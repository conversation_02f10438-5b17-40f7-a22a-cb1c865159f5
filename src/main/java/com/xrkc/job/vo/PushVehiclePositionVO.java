package com.xrkc.job.vo;
import java.math.BigDecimal;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/vo/PushVehiclePositionVO.class */
public class PushVehiclePositionVO {
    private String cardNo;
    private String vehicleNo;
    private String vehicleName;
    private String vehicleType;
    private String vehicleTypeName;
    private String vehicleAttribute;
    private String driver;
    private String phone;
    private String company;
    private Integer deviceElectric;
    private Long timestamp;
    private BigDecimal longitude;
    private BigDecimal latitude;
    private Integer speed;
    private Integer alt;
    private Integer dir;
    private List<String> alarmType;
    private Boolean isAlarm;
    public String getVehicleNo() {
        return this.vehicleNo;
    }
    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }
    public String getVehicleName() {
        return this.vehicleName;
    }
    public void setVehicleName(String vehicleName) {
        this.vehicleName = vehicleName;
    }
    public String getVehicleType() {
        return this.vehicleType;
    }
    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }
    public String getVehicleTypeName() {
        return this.vehicleTypeName;
    }
    public void setVehicleTypeName(String vehicleTypeName) {
        this.vehicleTypeName = vehicleTypeName;
    }
    public String getVehicleAttribute() {
        return this.vehicleAttribute;
    }
    public void setVehicleAttribute(String vehicleAttribute) {
        this.vehicleAttribute = vehicleAttribute;
    }
    public String getDriver() {
        return this.driver;
    }
    public void setDriver(String driver) {
        this.driver = driver;
    }
    public String getPhone() {
        return this.phone;
    }
    public void setPhone(String phone) {
        this.phone = phone;
    }
    public String getCompany() {
        return this.company;
    }
    public void setCompany(String company) {
        this.company = company;
    }
    public Integer getDeviceElectric() {
        return this.deviceElectric;
    }
    public void setDeviceElectric(Integer deviceElectric) {
        this.deviceElectric = deviceElectric;
    }
    public Long getTimestamp() {
        return this.timestamp;
    }
    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
    public BigDecimal getLongitude() {
        return this.longitude;
    }
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    public BigDecimal getLatitude() {
        return this.latitude;
    }
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    public Integer getSpeed() {
        return this.speed;
    }
    public void setSpeed(Integer speed) {
        this.speed = speed;
    }
    public Integer getAlt() {
        return this.alt;
    }
    public void setAlt(Integer alt) {
        this.alt = alt;
    }
    public List<String> getAlarmType() {
        return this.alarmType;
    }
    public void setAlarmType(List<String> alarmType) {
        this.alarmType = alarmType;
    }
    public Boolean getAlarm() {
        return this.isAlarm;
    }
    public void setAlarm(Boolean alarm) {
        this.isAlarm = alarm;
    }
    public String getCardNo() {
        return this.cardNo;
    }
    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }
    public Integer getDir() {
        return this.dir;
    }
    public void setDir(Integer dir) {
        this.dir = dir;
    }
}
