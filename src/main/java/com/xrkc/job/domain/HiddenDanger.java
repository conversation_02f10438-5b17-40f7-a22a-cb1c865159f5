package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
import java.time.LocalDateTime;
import org.springframework.web.servlet.tags.BindTag;
@TableName("hidden_danger")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/HiddenDanger.class */
public class HiddenDanger implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId("hidden_danger_id")
    private Long hiddenDangerId;
    @TableField(BindTag.STATUS_VARIABLE_NAME)
    private Integer status;
    @TableField("hidden_danger_result")
    private Integer hiddenDangerResult;
    @TableField("audit_id")
    private Long auditId;
    @TableField("abarbeitung_id")
    private Long abarbeitungId;
    @TableField("acceptance_id")
    private Long acceptanceId;
    @TableField("road_name")
    private String roadName;
    @TableField("road_leader_id")
    private Long roadLeaderId;
    @TableField("road_leader")
    private String roadLeader;
    @TableField("location_id")
    private Long locationId;
    @TableField("location_name")
    private String locationName;
    @TableField("team_name")
    private String teamName;
    @TableField("inspect_person_id")
    private Long inspectPersonId;
    @TableField("inspect_person_name")
    private String inspectPersonName;
    @TableField("location_code")
    private String locationCode;
    @TableField("create_time")
    private LocalDateTime createTime;
    @TableField("create_by")
    private String createBy;
    @TableField("update_time")
    private LocalDateTime updateTime;
    @TableField("update_by")
    private String updateBy;
    @TableField("remark")
    private String remark;
    public HiddenDanger setHiddenDangerId(Long hiddenDangerId) {
        this.hiddenDangerId = hiddenDangerId;
        return this;
    }
    public HiddenDanger setStatus(Integer status) {
        this.status = status;
        return this;
    }
    public HiddenDanger setHiddenDangerResult(Integer hiddenDangerResult) {
        this.hiddenDangerResult = hiddenDangerResult;
        return this;
    }
    public HiddenDanger setAuditId(Long auditId) {
        this.auditId = auditId;
        return this;
    }
    public HiddenDanger setAbarbeitungId(Long abarbeitungId) {
        this.abarbeitungId = abarbeitungId;
        return this;
    }
    public HiddenDanger setAcceptanceId(Long acceptanceId) {
        this.acceptanceId = acceptanceId;
        return this;
    }
    public HiddenDanger setRoadName(String roadName) {
        this.roadName = roadName;
        return this;
    }
    public HiddenDanger setRoadLeaderId(Long roadLeaderId) {
        this.roadLeaderId = roadLeaderId;
        return this;
    }
    public HiddenDanger setRoadLeader(String roadLeader) {
        this.roadLeader = roadLeader;
        return this;
    }
    public HiddenDanger setLocationId(Long locationId) {
        this.locationId = locationId;
        return this;
    }
    public HiddenDanger setLocationName(String locationName) {
        this.locationName = locationName;
        return this;
    }
    public HiddenDanger setTeamName(String teamName) {
        this.teamName = teamName;
        return this;
    }
    public HiddenDanger setInspectPersonId(Long inspectPersonId) {
        this.inspectPersonId = inspectPersonId;
        return this;
    }
    public HiddenDanger setInspectPersonName(String inspectPersonName) {
        this.inspectPersonName = inspectPersonName;
        return this;
    }
    public HiddenDanger setLocationCode(String locationCode) {
        this.locationCode = locationCode;
        return this;
    }
    public HiddenDanger setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
        return this;
    }
    public HiddenDanger setCreateBy(String createBy) {
        this.createBy = createBy;
        return this;
    }
    public HiddenDanger setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
        return this;
    }
    public HiddenDanger setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
        return this;
    }
    public HiddenDanger setRemark(String remark) {
        this.remark = remark;
        return this;
    }
    public String toString() {
        return "HiddenDanger(hiddenDangerId=" + getHiddenDangerId() + ", status=" + getStatus() + ", hiddenDangerResult=" + getHiddenDangerResult() + ", auditId=" + getAuditId() + ", abarbeitungId=" + getAbarbeitungId() + ", acceptanceId=" + getAcceptanceId() + ", roadName=" + getRoadName() + ", roadLeaderId=" + getRoadLeaderId() + ", roadLeader=" + getRoadLeader() + ", locationId=" + getLocationId() + ", locationName=" + getLocationName() + ", teamName=" + getTeamName() + ", inspectPersonId=" + getInspectPersonId() + ", inspectPersonName=" + getInspectPersonName() + ", locationCode=" + getLocationCode() + ", createTime=" + getCreateTime() + ", createBy=" + getCreateBy() + ", updateTime=" + getUpdateTime() + ", updateBy=" + getUpdateBy() + ", remark=" + getRemark() + StringPool.RIGHT_BRACKET;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof HiddenDanger)) {
            return false;
        }
        HiddenDanger other = (HiddenDanger) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$hiddenDangerId = getHiddenDangerId();
        Object other$hiddenDangerId = other.getHiddenDangerId();
        if (this$hiddenDangerId == null) {
            if (other$hiddenDangerId != null) {
                return false;
            }
        } else if (!this$hiddenDangerId.equals(other$hiddenDangerId)) {
            return false;
        }
        Object this$status = getStatus();
        Object other$status = other.getStatus();
        if (this$status == null) {
            if (other$status != null) {
                return false;
            }
        } else if (!this$status.equals(other$status)) {
            return false;
        }
        Object this$hiddenDangerResult = getHiddenDangerResult();
        Object other$hiddenDangerResult = other.getHiddenDangerResult();
        if (this$hiddenDangerResult == null) {
            if (other$hiddenDangerResult != null) {
                return false;
            }
        } else if (!this$hiddenDangerResult.equals(other$hiddenDangerResult)) {
            return false;
        }
        Object this$auditId = getAuditId();
        Object other$auditId = other.getAuditId();
        if (this$auditId == null) {
            if (other$auditId != null) {
                return false;
            }
        } else if (!this$auditId.equals(other$auditId)) {
            return false;
        }
        Object this$abarbeitungId = getAbarbeitungId();
        Object other$abarbeitungId = other.getAbarbeitungId();
        if (this$abarbeitungId == null) {
            if (other$abarbeitungId != null) {
                return false;
            }
        } else if (!this$abarbeitungId.equals(other$abarbeitungId)) {
            return false;
        }
        Object this$acceptanceId = getAcceptanceId();
        Object other$acceptanceId = other.getAcceptanceId();
        if (this$acceptanceId == null) {
            if (other$acceptanceId != null) {
                return false;
            }
        } else if (!this$acceptanceId.equals(other$acceptanceId)) {
            return false;
        }
        Object this$roadLeaderId = getRoadLeaderId();
        Object other$roadLeaderId = other.getRoadLeaderId();
        if (this$roadLeaderId == null) {
            if (other$roadLeaderId != null) {
                return false;
            }
        } else if (!this$roadLeaderId.equals(other$roadLeaderId)) {
            return false;
        }
        Object this$locationId = getLocationId();
        Object other$locationId = other.getLocationId();
        if (this$locationId == null) {
            if (other$locationId != null) {
                return false;
            }
        } else if (!this$locationId.equals(other$locationId)) {
            return false;
        }
        Object this$inspectPersonId = getInspectPersonId();
        Object other$inspectPersonId = other.getInspectPersonId();
        if (this$inspectPersonId == null) {
            if (other$inspectPersonId != null) {
                return false;
            }
        } else if (!this$inspectPersonId.equals(other$inspectPersonId)) {
            return false;
        }
        Object this$roadName = getRoadName();
        Object other$roadName = other.getRoadName();
        if (this$roadName == null) {
            if (other$roadName != null) {
                return false;
            }
        } else if (!this$roadName.equals(other$roadName)) {
            return false;
        }
        Object this$roadLeader = getRoadLeader();
        Object other$roadLeader = other.getRoadLeader();
        if (this$roadLeader == null) {
            if (other$roadLeader != null) {
                return false;
            }
        } else if (!this$roadLeader.equals(other$roadLeader)) {
            return false;
        }
        Object this$locationName = getLocationName();
        Object other$locationName = other.getLocationName();
        if (this$locationName == null) {
            if (other$locationName != null) {
                return false;
            }
        } else if (!this$locationName.equals(other$locationName)) {
            return false;
        }
        Object this$teamName = getTeamName();
        Object other$teamName = other.getTeamName();
        if (this$teamName == null) {
            if (other$teamName != null) {
                return false;
            }
        } else if (!this$teamName.equals(other$teamName)) {
            return false;
        }
        Object this$inspectPersonName = getInspectPersonName();
        Object other$inspectPersonName = other.getInspectPersonName();
        if (this$inspectPersonName == null) {
            if (other$inspectPersonName != null) {
                return false;
            }
        } else if (!this$inspectPersonName.equals(other$inspectPersonName)) {
            return false;
        }
        Object this$locationCode = getLocationCode();
        Object other$locationCode = other.getLocationCode();
        if (this$locationCode == null) {
            if (other$locationCode != null) {
                return false;
            }
        } else if (!this$locationCode.equals(other$locationCode)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        if (this$createTime == null) {
            if (other$createTime != null) {
                return false;
            }
        } else if (!this$createTime.equals(other$createTime)) {
            return false;
        }
        Object this$createBy = getCreateBy();
        Object other$createBy = other.getCreateBy();
        if (this$createBy == null) {
            if (other$createBy != null) {
                return false;
            }
        } else if (!this$createBy.equals(other$createBy)) {
            return false;
        }
        Object this$updateTime = getUpdateTime();
        Object other$updateTime = other.getUpdateTime();
        if (this$updateTime == null) {
            if (other$updateTime != null) {
                return false;
            }
        } else if (!this$updateTime.equals(other$updateTime)) {
            return false;
        }
        Object this$updateBy = getUpdateBy();
        Object other$updateBy = other.getUpdateBy();
        if (this$updateBy == null) {
            if (other$updateBy != null) {
                return false;
            }
        } else if (!this$updateBy.equals(other$updateBy)) {
            return false;
        }
        Object this$remark = getRemark();
        Object other$remark = other.getRemark();
        return this$remark == null ? other$remark == null : this$remark.equals(other$remark);
    }
    protected boolean canEqual(Object other) {
        return other instanceof HiddenDanger;
    }
    public int hashCode() {
        Object $hiddenDangerId = getHiddenDangerId();
        int result = (1 * 59) + ($hiddenDangerId == null ? 43 : $hiddenDangerId.hashCode());
        Object $status = getStatus();
        int result2 = (result * 59) + ($status == null ? 43 : $status.hashCode());
        Object $hiddenDangerResult = getHiddenDangerResult();
        int result3 = (result2 * 59) + ($hiddenDangerResult == null ? 43 : $hiddenDangerResult.hashCode());
        Object $auditId = getAuditId();
        int result4 = (result3 * 59) + ($auditId == null ? 43 : $auditId.hashCode());
        Object $abarbeitungId = getAbarbeitungId();
        int result5 = (result4 * 59) + ($abarbeitungId == null ? 43 : $abarbeitungId.hashCode());
        Object $acceptanceId = getAcceptanceId();
        int result6 = (result5 * 59) + ($acceptanceId == null ? 43 : $acceptanceId.hashCode());
        Object $roadLeaderId = getRoadLeaderId();
        int result7 = (result6 * 59) + ($roadLeaderId == null ? 43 : $roadLeaderId.hashCode());
        Object $locationId = getLocationId();
        int result8 = (result7 * 59) + ($locationId == null ? 43 : $locationId.hashCode());
        Object $inspectPersonId = getInspectPersonId();
        int result9 = (result8 * 59) + ($inspectPersonId == null ? 43 : $inspectPersonId.hashCode());
        Object $roadName = getRoadName();
        int result10 = (result9 * 59) + ($roadName == null ? 43 : $roadName.hashCode());
        Object $roadLeader = getRoadLeader();
        int result11 = (result10 * 59) + ($roadLeader == null ? 43 : $roadLeader.hashCode());
        Object $locationName = getLocationName();
        int result12 = (result11 * 59) + ($locationName == null ? 43 : $locationName.hashCode());
        Object $teamName = getTeamName();
        int result13 = (result12 * 59) + ($teamName == null ? 43 : $teamName.hashCode());
        Object $inspectPersonName = getInspectPersonName();
        int result14 = (result13 * 59) + ($inspectPersonName == null ? 43 : $inspectPersonName.hashCode());
        Object $locationCode = getLocationCode();
        int result15 = (result14 * 59) + ($locationCode == null ? 43 : $locationCode.hashCode());
        Object $createTime = getCreateTime();
        int result16 = (result15 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $createBy = getCreateBy();
        int result17 = (result16 * 59) + ($createBy == null ? 43 : $createBy.hashCode());
        Object $updateTime = getUpdateTime();
        int result18 = (result17 * 59) + ($updateTime == null ? 43 : $updateTime.hashCode());
        Object $updateBy = getUpdateBy();
        int result19 = (result18 * 59) + ($updateBy == null ? 43 : $updateBy.hashCode());
        Object $remark = getRemark();
        return (result19 * 59) + ($remark == null ? 43 : $remark.hashCode());
    }
    public Long getHiddenDangerId() {
        return this.hiddenDangerId;
    }
    public Integer getStatus() {
        return this.status;
    }
    public Integer getHiddenDangerResult() {
        return this.hiddenDangerResult;
    }
    public Long getAuditId() {
        return this.auditId;
    }
    public Long getAbarbeitungId() {
        return this.abarbeitungId;
    }
    public Long getAcceptanceId() {
        return this.acceptanceId;
    }
    public String getRoadName() {
        return this.roadName;
    }
    public Long getRoadLeaderId() {
        return this.roadLeaderId;
    }
    public String getRoadLeader() {
        return this.roadLeader;
    }
    public Long getLocationId() {
        return this.locationId;
    }
    public String getLocationName() {
        return this.locationName;
    }
    public String getTeamName() {
        return this.teamName;
    }
    public Long getInspectPersonId() {
        return this.inspectPersonId;
    }
    public String getInspectPersonName() {
        return this.inspectPersonName;
    }
    public String getLocationCode() {
        return this.locationCode;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public String getCreateBy() {
        return this.createBy;
    }
    public LocalDateTime getUpdateTime() {
        return this.updateTime;
    }
    public String getUpdateBy() {
        return this.updateBy;
    }
    public String getRemark() {
        return this.remark;
    }
}
