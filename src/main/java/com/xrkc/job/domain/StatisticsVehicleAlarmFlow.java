package com.xrkc.job.domain;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/StatisticsVehicleAlarmFlow.class */
public class StatisticsVehicleAlarmFlow implements Serializable {
    private static final long serialVersionUID = 1;
    private Long statisticsId;
    private LocalDate statisticsDate;
    private String alarmType;
    private String alarmTypeName;
    private Long alarmCount;
    private LocalDateTime createTime;
    public Long getStatisticsId() {
        return this.statisticsId;
    }
    public void setStatisticsId(Long statisticsId) {
        this.statisticsId = statisticsId;
    }
    public LocalDate getStatisticsDate() {
        return this.statisticsDate;
    }
    public void setStatisticsDate(LocalDate statisticsDate) {
        this.statisticsDate = statisticsDate;
    }
    public String getAlarmType() {
        return this.alarmType;
    }
    public void setAlarmType(String alarmType) {
        this.alarmType = alarmType;
    }
    public String getAlarmTypeName() {
        return this.alarmTypeName;
    }
    public void setAlarmTypeName(String alarmTypeName) {
        this.alarmTypeName = alarmTypeName;
    }
    public Long getAlarmCount() {
        return this.alarmCount;
    }
    public void setAlarmCount(Long alarmCount) {
        this.alarmCount = alarmCount;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}
