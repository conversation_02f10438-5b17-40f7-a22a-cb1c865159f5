package com.xrkc.job.domain;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/AlarmVehicle.class */
public class AlarmVehicle implements Serializable {
    private static final long serialVersionUID = 1;
    private Long alarmId;
    private String alarmName;
    private String alarmDesc;
    private String alarmStatus;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime acceptTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    private Long areaId;
    private String areaName;
    private String alarmType;
    private String alarmTypeName;
    private Long rule;
    private LocalDate validBeginDate;
    private LocalDate validEndDate;
    private LocalTime validBeginTime;
    private LocalTime validEndTime;
    private List<Long> railIds;
    private List<Long> deptIds;
    private List<Long> personIds;
    private List<String> railScopeList;
    private Long railId;
    private String railName;
    private String layerId;
    private Integer layerHeight;
    private String railScope;
    private Long alarmNoticeId;
    private String alarmNoticeName;
    private String noticeType;
    private String firstPersonId;
    private String secondPersonId;
    private Long secondIntervalSecond;
    private String thirdPersonId;
    private Long thirdIntervalSecond;
    private Integer beaconId;
    private Double distance;
    private Long cardId;
    private BigDecimal longitude;
    private BigDecimal latitude;
    private Long personId;
    private String personType;
    private String staffType;
    private String realName;
    private Long deptId;
    private Long postId;
    private String personPhoto;
    private String deptName;
    private String postName;
    private String jobNumber;
    private Long contractorId;
    private String contractorName;
    private String createBy;
    private Integer alarmCount;
    public LocalDateTime getAcceptTime() {
        return this.acceptTime;
    }
    public void setAcceptTime(LocalDateTime acceptTime) {
        this.acceptTime = acceptTime;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public Long getAreaId() {
        return this.areaId;
    }
    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }
    public String getAreaName() {
        return this.areaName;
    }
    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }
    public String getAlarmType() {
        return this.alarmType;
    }
    public void setAlarmType(String alarmType) {
        this.alarmType = alarmType;
    }
    public Long getRule() {
        return this.rule;
    }
    public void setRule(Long rule) {
        this.rule = rule;
    }
    public LocalDate getValidBeginDate() {
        return this.validBeginDate;
    }
    public void setValidBeginDate(LocalDate validBeginDate) {
        this.validBeginDate = validBeginDate;
    }
    public LocalDate getValidEndDate() {
        return this.validEndDate;
    }
    public void setValidEndDate(LocalDate validEndDate) {
        this.validEndDate = validEndDate;
    }
    public LocalTime getValidBeginTime() {
        return this.validBeginTime;
    }
    public void setValidBeginTime(LocalTime validBeginTime) {
        this.validBeginTime = validBeginTime;
    }
    public LocalTime getValidEndTime() {
        return this.validEndTime;
    }
    public void setValidEndTime(LocalTime validEndTime) {
        this.validEndTime = validEndTime;
    }
    public List<Long> getRailIds() {
        return this.railIds;
    }
    public void setRailIds(List<Long> railIds) {
        this.railIds = railIds;
    }
    public List<Long> getDeptIds() {
        return this.deptIds;
    }
    public void setDeptIds(List<Long> deptIds) {
        this.deptIds = deptIds;
    }
    public List<Long> getPersonIds() {
        return this.personIds;
    }
    public void setPersonIds(List<Long> personIds) {
        this.personIds = personIds;
    }
    public List<String> getRailScopeList() {
        return this.railScopeList;
    }
    public void setRailScopeList(List<String> railScopeList) {
        this.railScopeList = railScopeList;
    }
    public Long getRailId() {
        return this.railId;
    }
    public void setRailId(Long railId) {
        this.railId = railId;
    }
    public String getRailName() {
        return this.railName;
    }
    public void setRailName(String railName) {
        this.railName = railName;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
    public Integer getLayerHeight() {
        return this.layerHeight;
    }
    public void setLayerHeight(Integer layerHeight) {
        this.layerHeight = layerHeight;
    }
    public String getRailScope() {
        return this.railScope;
    }
    public void setRailScope(String railScope) {
        this.railScope = railScope;
    }
    public Long getAlarmNoticeId() {
        return this.alarmNoticeId;
    }
    public void setAlarmNoticeId(Long alarmNoticeId) {
        this.alarmNoticeId = alarmNoticeId;
    }
    public String getAlarmNoticeName() {
        return this.alarmNoticeName;
    }
    public void setAlarmNoticeName(String alarmNoticeName) {
        this.alarmNoticeName = alarmNoticeName;
    }
    public String getNoticeType() {
        return this.noticeType;
    }
    public void setNoticeType(String noticeType) {
        this.noticeType = noticeType;
    }
    public String getFirstPersonId() {
        return this.firstPersonId;
    }
    public void setFirstPersonId(String firstPersonId) {
        this.firstPersonId = firstPersonId;
    }
    public String getSecondPersonId() {
        return this.secondPersonId;
    }
    public void setSecondPersonId(String secondPersonId) {
        this.secondPersonId = secondPersonId;
    }
    public Long getSecondIntervalSecond() {
        return this.secondIntervalSecond;
    }
    public void setSecondIntervalSecond(Long secondIntervalSecond) {
        this.secondIntervalSecond = secondIntervalSecond;
    }
    public String getThirdPersonId() {
        return this.thirdPersonId;
    }
    public void setThirdPersonId(String thirdPersonId) {
        this.thirdPersonId = thirdPersonId;
    }
    public Long getThirdIntervalSecond() {
        return this.thirdIntervalSecond;
    }
    public void setThirdIntervalSecond(Long thirdIntervalSecond) {
        this.thirdIntervalSecond = thirdIntervalSecond;
    }
    public Integer getBeaconId() {
        return this.beaconId;
    }
    public void setBeaconId(Integer beaconId) {
        this.beaconId = beaconId;
    }
    public Double getDistance() {
        return this.distance;
    }
    public void setDistance(Double distance) {
        this.distance = distance;
    }
    public Long getCardId() {
        return this.cardId;
    }
    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }
    public BigDecimal getLongitude() {
        return this.longitude;
    }
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    public BigDecimal getLatitude() {
        return this.latitude;
    }
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    public Long getPersonId() {
        return this.personId;
    }
    public void setPersonId(Long personId) {
        this.personId = personId;
    }
    public String getPersonType() {
        return this.personType;
    }
    public void setPersonType(String personType) {
        this.personType = personType;
    }
    public String getStaffType() {
        return this.staffType;
    }
    public void setStaffType(String staffType) {
        this.staffType = staffType;
    }
    public String getRealName() {
        return this.realName;
    }
    public void setRealName(String realName) {
        this.realName = realName;
    }
    public Long getDeptId() {
        return this.deptId;
    }
    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
    public Long getPostId() {
        return this.postId;
    }
    public void setPostId(Long postId) {
        this.postId = postId;
    }
    public String getPersonPhoto() {
        return this.personPhoto;
    }
    public void setPersonPhoto(String personPhoto) {
        this.personPhoto = personPhoto;
    }
    public String getDeptName() {
        return this.deptName;
    }
    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    public String getPostName() {
        return this.postName;
    }
    public void setPostName(String postName) {
        this.postName = postName;
    }
    public String getJobNumber() {
        return this.jobNumber;
    }
    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }
    public Long getContractorId() {
        return this.contractorId;
    }
    public void setContractorId(Long contractorId) {
        this.contractorId = contractorId;
    }
    public String getContractorName() {
        return this.contractorName;
    }
    public void setContractorName(String contractorName) {
        this.contractorName = contractorName;
    }
    public Long getAlarmId() {
        return this.alarmId;
    }
    public void setAlarmId(Long alarmId) {
        this.alarmId = alarmId;
    }
    public String getAlarmName() {
        return this.alarmName;
    }
    public void setAlarmName(String alarmName) {
        this.alarmName = alarmName;
    }
    public String getAlarmDesc() {
        return this.alarmDesc;
    }
    public void setAlarmDesc(String alarmDesc) {
        this.alarmDesc = alarmDesc;
    }
    public String getAlarmTypeName() {
        return this.alarmTypeName;
    }
    public void setAlarmTypeName(String alarmTypeName) {
        this.alarmTypeName = alarmTypeName;
    }
    public String getCreateBy() {
        return this.createBy;
    }
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public String getAlarmStatus() {
        return this.alarmStatus;
    }
    public void setAlarmStatus(String alarmStatus) {
        this.alarmStatus = alarmStatus;
    }
    public Integer getAlarmCount() {
        return this.alarmCount;
    }
    public void setAlarmCount(Integer alarmCount) {
        this.alarmCount = alarmCount;
    }
}
