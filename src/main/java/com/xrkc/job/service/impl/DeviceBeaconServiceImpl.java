package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.job.domain.DeviceBeacon;
import com.xrkc.job.mapper.DeviceBeaconMapper;
import com.xrkc.job.service.IDeviceBeaconService;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/DeviceBeaconServiceImpl.class */
public class DeviceBeaconServiceImpl extends ServiceImpl<DeviceBeaconMapper, DeviceBeacon> implements IDeviceBeaconService {
    @Autowired
    private DeviceBeaconMapper mapper;
    @Override // com.xrkc.job.service.IDeviceBeaconService
    public void resetBeaconHeart() {
        this.mapper.resetBeaconHeart();
    }
    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.xrkc.job.service.IDeviceBeaconService
    public List<Integer> selectBeaconIdByRssi(String beaconEnable) {
        return  this.mapper.selectList(new LambdaQueryWrapper<DeviceBeacon>().eq((v0) -> {
            return v0.getBeaconEnable();
        }, beaconEnable)).stream().map((v0) -> {
            return v0.getBeaconId();
        }).collect(Collectors.toList());
    }
}
