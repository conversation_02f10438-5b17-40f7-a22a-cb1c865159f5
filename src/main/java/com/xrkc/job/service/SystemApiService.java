package com.xrkc.job.service;
import com.xrkc.core.domain.system.SystemApi;
import java.time.LocalDateTime;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/SystemApiService.class */
public interface SystemApiService {
    SystemApi getByApiKey(String str);
    void updateByApiKey(LocalDateTime localDateTime, LocalDateTime localDateTime2, String str, Long l);
    void updateEquipmentNumber(String str, Long l);
    void updateTimeByApiKey(LocalDateTime localDateTime, Long l);
}
