package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
@TableName("core_alarm")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/Alarm.class */
public class Alarm implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId(type = IdType.ASSIGN_ID)
    private Long alarmId;
    private String alarmName;
    private String alarmDesc;
    private String alarmStatus;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime acceptTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    private String createBy;
    private LocalDateTime updateTime;
    private String updateBy;
    private LocalDateTime disposeTime;
    private String disposeFeedback;
    private String disposeBy;
    private Long areaId;
    private String areaName;
    private String alarmType;
    private String alarmTypeName;
    @TableField(exist = false)
    private LocalDate validBeginDate;
    @TableField(exist = false)
    private LocalDate validEndDate;
    @TableField(exist = false)
    private LocalTime validBeginTime;
    @TableField(exist = false)
    private LocalTime validEndTime;
    @TableField(exist = false)
    private List<Long> railIds;
    @TableField(exist = false)
    private List<Long> deptIds;
    @TableField(exist = false)
    private List<Long> personIds;
    @TableField(exist = false)
    private List<String> railScopeList;
    private Long railId;
    private String railName;
    private Integer railHeight;
    private String layerId;
    @TableField(exist = false)
    private String layerName;
    private Integer layerHeight;
    private String railScope;
    @TableField(exist = false)
    private Long alarmNoticeId;
    @TableField(exist = false)
    private Long secondAlarmNoticeId;
    @TableField(exist = false)
    private Long thirdAlarmNoticeId;
    private Integer beaconId;
    @TableField(exist = false)
    private Double distance;
    private Long cardId;
    private BigDecimal longitude;
    private BigDecimal latitude;
    private Long personId;
    private String personType;
    private String staffType;
    private String realName;
    private String administratorName;
    private String administratorPhone;
    private Long deptId;
    private Long postId;
    @TableField(exist = false)
    private String personPhoto;
    private String deptName;
    private String postName;
    private String jobNumber;
    private Long contractorId;
    private String contractorName;
    @TableField(exist = false)
    private Integer alarmCount;
    @TableField(exist = false)
    private Integer timeCount;
    private Integer drawType;
    private LocalDateTime endTime;
    private String alarmLevel;
    private String highestAlarmLevel;
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String buildingName;
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Long buildingId;
    public void setAlarmId(Long alarmId) {
        this.alarmId = alarmId;
    }
    public void setAlarmName(String alarmName) {
        this.alarmName = alarmName;
    }
    public void setAlarmDesc(String alarmDesc) {
        this.alarmDesc = alarmDesc;
    }
    public void setAlarmStatus(String alarmStatus) {
        this.alarmStatus = alarmStatus;
    }
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public void setAcceptTime(LocalDateTime acceptTime) {
        this.acceptTime = acceptTime;
    }
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public void setDisposeTime(LocalDateTime disposeTime) {
        this.disposeTime = disposeTime;
    }
    public void setDisposeFeedback(String disposeFeedback) {
        this.disposeFeedback = disposeFeedback;
    }
    public void setDisposeBy(String disposeBy) {
        this.disposeBy = disposeBy;
    }
    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }
    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }
    public void setAlarmType(String alarmType) {
        this.alarmType = alarmType;
    }
    public void setAlarmTypeName(String alarmTypeName) {
        this.alarmTypeName = alarmTypeName;
    }
    public void setValidBeginDate(LocalDate validBeginDate) {
        this.validBeginDate = validBeginDate;
    }
    public void setValidEndDate(LocalDate validEndDate) {
        this.validEndDate = validEndDate;
    }
    public void setValidBeginTime(LocalTime validBeginTime) {
        this.validBeginTime = validBeginTime;
    }
    public void setValidEndTime(LocalTime validEndTime) {
        this.validEndTime = validEndTime;
    }
    public void setRailIds(List<Long> railIds) {
        this.railIds = railIds;
    }
    public void setDeptIds(List<Long> deptIds) {
        this.deptIds = deptIds;
    }
    public void setPersonIds(List<Long> personIds) {
        this.personIds = personIds;
    }
    public void setRailScopeList(List<String> railScopeList) {
        this.railScopeList = railScopeList;
    }
    public void setRailId(Long railId) {
        this.railId = railId;
    }
    public void setRailName(String railName) {
        this.railName = railName;
    }
    public void setRailHeight(Integer railHeight) {
        this.railHeight = railHeight;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
    public void setLayerName(String layerName) {
        this.layerName = layerName;
    }
    public void setLayerHeight(Integer layerHeight) {
        this.layerHeight = layerHeight;
    }
    public void setRailScope(String railScope) {
        this.railScope = railScope;
    }
    public void setAlarmNoticeId(Long alarmNoticeId) {
        this.alarmNoticeId = alarmNoticeId;
    }
    public void setSecondAlarmNoticeId(Long secondAlarmNoticeId) {
        this.secondAlarmNoticeId = secondAlarmNoticeId;
    }
    public void setThirdAlarmNoticeId(Long thirdAlarmNoticeId) {
        this.thirdAlarmNoticeId = thirdAlarmNoticeId;
    }
    public void setBeaconId(Integer beaconId) {
        this.beaconId = beaconId;
    }
    public void setDistance(Double distance) {
        this.distance = distance;
    }
    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    public void setPersonId(Long personId) {
        this.personId = personId;
    }
    public void setPersonType(String personType) {
        this.personType = personType;
    }
    public void setStaffType(String staffType) {
        this.staffType = staffType;
    }
    public void setRealName(String realName) {
        this.realName = realName;
    }
    public void setAdministratorName(String administratorName) {
        this.administratorName = administratorName;
    }
    public void setAdministratorPhone(String administratorPhone) {
        this.administratorPhone = administratorPhone;
    }
    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
    public void setPostId(Long postId) {
        this.postId = postId;
    }
    public void setPersonPhoto(String personPhoto) {
        this.personPhoto = personPhoto;
    }
    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    public void setPostName(String postName) {
        this.postName = postName;
    }
    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }
    public void setContractorId(Long contractorId) {
        this.contractorId = contractorId;
    }
    public void setContractorName(String contractorName) {
        this.contractorName = contractorName;
    }
    public void setAlarmCount(Integer alarmCount) {
        this.alarmCount = alarmCount;
    }
    public void setTimeCount(Integer timeCount) {
        this.timeCount = timeCount;
    }
    public void setDrawType(Integer drawType) {
        this.drawType = drawType;
    }
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    public void setAlarmLevel(String alarmLevel) {
        this.alarmLevel = alarmLevel;
    }
    public void setHighestAlarmLevel(String highestAlarmLevel) {
        this.highestAlarmLevel = highestAlarmLevel;
    }
    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }
    public void setBuildingId(Long buildingId) {
        this.buildingId = buildingId;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof Alarm)) {
            return false;
        }
        Alarm other = (Alarm) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$alarmId = getAlarmId();
        Object other$alarmId = other.getAlarmId();
        if (this$alarmId == null) {
            if (other$alarmId != null) {
                return false;
            }
        } else if (!this$alarmId.equals(other$alarmId)) {
            return false;
        }
        Object this$areaId = getAreaId();
        Object other$areaId = other.getAreaId();
        if (this$areaId == null) {
            if (other$areaId != null) {
                return false;
            }
        } else if (!this$areaId.equals(other$areaId)) {
            return false;
        }
        Object this$railId = getRailId();
        Object other$railId = other.getRailId();
        if (this$railId == null) {
            if (other$railId != null) {
                return false;
            }
        } else if (!this$railId.equals(other$railId)) {
            return false;
        }
        Object this$railHeight = getRailHeight();
        Object other$railHeight = other.getRailHeight();
        if (this$railHeight == null) {
            if (other$railHeight != null) {
                return false;
            }
        } else if (!this$railHeight.equals(other$railHeight)) {
            return false;
        }
        Object this$layerHeight = getLayerHeight();
        Object other$layerHeight = other.getLayerHeight();
        if (this$layerHeight == null) {
            if (other$layerHeight != null) {
                return false;
            }
        } else if (!this$layerHeight.equals(other$layerHeight)) {
            return false;
        }
        Object this$alarmNoticeId = getAlarmNoticeId();
        Object other$alarmNoticeId = other.getAlarmNoticeId();
        if (this$alarmNoticeId == null) {
            if (other$alarmNoticeId != null) {
                return false;
            }
        } else if (!this$alarmNoticeId.equals(other$alarmNoticeId)) {
            return false;
        }
        Object this$secondAlarmNoticeId = getSecondAlarmNoticeId();
        Object other$secondAlarmNoticeId = other.getSecondAlarmNoticeId();
        if (this$secondAlarmNoticeId == null) {
            if (other$secondAlarmNoticeId != null) {
                return false;
            }
        } else if (!this$secondAlarmNoticeId.equals(other$secondAlarmNoticeId)) {
            return false;
        }
        Object this$thirdAlarmNoticeId = getThirdAlarmNoticeId();
        Object other$thirdAlarmNoticeId = other.getThirdAlarmNoticeId();
        if (this$thirdAlarmNoticeId == null) {
            if (other$thirdAlarmNoticeId != null) {
                return false;
            }
        } else if (!this$thirdAlarmNoticeId.equals(other$thirdAlarmNoticeId)) {
            return false;
        }
        Object this$beaconId = getBeaconId();
        Object other$beaconId = other.getBeaconId();
        if (this$beaconId == null) {
            if (other$beaconId != null) {
                return false;
            }
        } else if (!this$beaconId.equals(other$beaconId)) {
            return false;
        }
        Object this$distance = getDistance();
        Object other$distance = other.getDistance();
        if (this$distance == null) {
            if (other$distance != null) {
                return false;
            }
        } else if (!this$distance.equals(other$distance)) {
            return false;
        }
        Object this$cardId = getCardId();
        Object other$cardId = other.getCardId();
        if (this$cardId == null) {
            if (other$cardId != null) {
                return false;
            }
        } else if (!this$cardId.equals(other$cardId)) {
            return false;
        }
        Object this$personId = getPersonId();
        Object other$personId = other.getPersonId();
        if (this$personId == null) {
            if (other$personId != null) {
                return false;
            }
        } else if (!this$personId.equals(other$personId)) {
            return false;
        }
        Object this$deptId = getDeptId();
        Object other$deptId = other.getDeptId();
        if (this$deptId == null) {
            if (other$deptId != null) {
                return false;
            }
        } else if (!this$deptId.equals(other$deptId)) {
            return false;
        }
        Object this$postId = getPostId();
        Object other$postId = other.getPostId();
        if (this$postId == null) {
            if (other$postId != null) {
                return false;
            }
        } else if (!this$postId.equals(other$postId)) {
            return false;
        }
        Object this$contractorId = getContractorId();
        Object other$contractorId = other.getContractorId();
        if (this$contractorId == null) {
            if (other$contractorId != null) {
                return false;
            }
        } else if (!this$contractorId.equals(other$contractorId)) {
            return false;
        }
        Object this$alarmCount = getAlarmCount();
        Object other$alarmCount = other.getAlarmCount();
        if (this$alarmCount == null) {
            if (other$alarmCount != null) {
                return false;
            }
        } else if (!this$alarmCount.equals(other$alarmCount)) {
            return false;
        }
        Object this$timeCount = getTimeCount();
        Object other$timeCount = other.getTimeCount();
        if (this$timeCount == null) {
            if (other$timeCount != null) {
                return false;
            }
        } else if (!this$timeCount.equals(other$timeCount)) {
            return false;
        }
        Object this$drawType = getDrawType();
        Object other$drawType = other.getDrawType();
        if (this$drawType == null) {
            if (other$drawType != null) {
                return false;
            }
        } else if (!this$drawType.equals(other$drawType)) {
            return false;
        }
        Object this$buildingId = getBuildingId();
        Object other$buildingId = other.getBuildingId();
        if (this$buildingId == null) {
            if (other$buildingId != null) {
                return false;
            }
        } else if (!this$buildingId.equals(other$buildingId)) {
            return false;
        }
        Object this$alarmName = getAlarmName();
        Object other$alarmName = other.getAlarmName();
        if (this$alarmName == null) {
            if (other$alarmName != null) {
                return false;
            }
        } else if (!this$alarmName.equals(other$alarmName)) {
            return false;
        }
        Object this$alarmDesc = getAlarmDesc();
        Object other$alarmDesc = other.getAlarmDesc();
        if (this$alarmDesc == null) {
            if (other$alarmDesc != null) {
                return false;
            }
        } else if (!this$alarmDesc.equals(other$alarmDesc)) {
            return false;
        }
        Object this$alarmStatus = getAlarmStatus();
        Object other$alarmStatus = other.getAlarmStatus();
        if (this$alarmStatus == null) {
            if (other$alarmStatus != null) {
                return false;
            }
        } else if (!this$alarmStatus.equals(other$alarmStatus)) {
            return false;
        }
        Object this$acceptTime = getAcceptTime();
        Object other$acceptTime = other.getAcceptTime();
        if (this$acceptTime == null) {
            if (other$acceptTime != null) {
                return false;
            }
        } else if (!this$acceptTime.equals(other$acceptTime)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        if (this$createTime == null) {
            if (other$createTime != null) {
                return false;
            }
        } else if (!this$createTime.equals(other$createTime)) {
            return false;
        }
        Object this$createBy = getCreateBy();
        Object other$createBy = other.getCreateBy();
        if (this$createBy == null) {
            if (other$createBy != null) {
                return false;
            }
        } else if (!this$createBy.equals(other$createBy)) {
            return false;
        }
        Object this$updateTime = getUpdateTime();
        Object other$updateTime = other.getUpdateTime();
        if (this$updateTime == null) {
            if (other$updateTime != null) {
                return false;
            }
        } else if (!this$updateTime.equals(other$updateTime)) {
            return false;
        }
        Object this$updateBy = getUpdateBy();
        Object other$updateBy = other.getUpdateBy();
        if (this$updateBy == null) {
            if (other$updateBy != null) {
                return false;
            }
        } else if (!this$updateBy.equals(other$updateBy)) {
            return false;
        }
        Object this$disposeTime = getDisposeTime();
        Object other$disposeTime = other.getDisposeTime();
        if (this$disposeTime == null) {
            if (other$disposeTime != null) {
                return false;
            }
        } else if (!this$disposeTime.equals(other$disposeTime)) {
            return false;
        }
        Object this$disposeFeedback = getDisposeFeedback();
        Object other$disposeFeedback = other.getDisposeFeedback();
        if (this$disposeFeedback == null) {
            if (other$disposeFeedback != null) {
                return false;
            }
        } else if (!this$disposeFeedback.equals(other$disposeFeedback)) {
            return false;
        }
        Object this$disposeBy = getDisposeBy();
        Object other$disposeBy = other.getDisposeBy();
        if (this$disposeBy == null) {
            if (other$disposeBy != null) {
                return false;
            }
        } else if (!this$disposeBy.equals(other$disposeBy)) {
            return false;
        }
        Object this$areaName = getAreaName();
        Object other$areaName = other.getAreaName();
        if (this$areaName == null) {
            if (other$areaName != null) {
                return false;
            }
        } else if (!this$areaName.equals(other$areaName)) {
            return false;
        }
        Object this$alarmType = getAlarmType();
        Object other$alarmType = other.getAlarmType();
        if (this$alarmType == null) {
            if (other$alarmType != null) {
                return false;
            }
        } else if (!this$alarmType.equals(other$alarmType)) {
            return false;
        }
        Object this$alarmTypeName = getAlarmTypeName();
        Object other$alarmTypeName = other.getAlarmTypeName();
        if (this$alarmTypeName == null) {
            if (other$alarmTypeName != null) {
                return false;
            }
        } else if (!this$alarmTypeName.equals(other$alarmTypeName)) {
            return false;
        }
        Object this$validBeginDate = getValidBeginDate();
        Object other$validBeginDate = other.getValidBeginDate();
        if (this$validBeginDate == null) {
            if (other$validBeginDate != null) {
                return false;
            }
        } else if (!this$validBeginDate.equals(other$validBeginDate)) {
            return false;
        }
        Object this$validEndDate = getValidEndDate();
        Object other$validEndDate = other.getValidEndDate();
        if (this$validEndDate == null) {
            if (other$validEndDate != null) {
                return false;
            }
        } else if (!this$validEndDate.equals(other$validEndDate)) {
            return false;
        }
        Object this$validBeginTime = getValidBeginTime();
        Object other$validBeginTime = other.getValidBeginTime();
        if (this$validBeginTime == null) {
            if (other$validBeginTime != null) {
                return false;
            }
        } else if (!this$validBeginTime.equals(other$validBeginTime)) {
            return false;
        }
        Object this$validEndTime = getValidEndTime();
        Object other$validEndTime = other.getValidEndTime();
        if (this$validEndTime == null) {
            if (other$validEndTime != null) {
                return false;
            }
        } else if (!this$validEndTime.equals(other$validEndTime)) {
            return false;
        }
        Object this$railIds = getRailIds();
        Object other$railIds = other.getRailIds();
        if (this$railIds == null) {
            if (other$railIds != null) {
                return false;
            }
        } else if (!this$railIds.equals(other$railIds)) {
            return false;
        }
        Object this$deptIds = getDeptIds();
        Object other$deptIds = other.getDeptIds();
        if (this$deptIds == null) {
            if (other$deptIds != null) {
                return false;
            }
        } else if (!this$deptIds.equals(other$deptIds)) {
            return false;
        }
        Object this$personIds = getPersonIds();
        Object other$personIds = other.getPersonIds();
        if (this$personIds == null) {
            if (other$personIds != null) {
                return false;
            }
        } else if (!this$personIds.equals(other$personIds)) {
            return false;
        }
        Object this$railScopeList = getRailScopeList();
        Object other$railScopeList = other.getRailScopeList();
        if (this$railScopeList == null) {
            if (other$railScopeList != null) {
                return false;
            }
        } else if (!this$railScopeList.equals(other$railScopeList)) {
            return false;
        }
        Object this$railName = getRailName();
        Object other$railName = other.getRailName();
        if (this$railName == null) {
            if (other$railName != null) {
                return false;
            }
        } else if (!this$railName.equals(other$railName)) {
            return false;
        }
        Object this$layerId = getLayerId();
        Object other$layerId = other.getLayerId();
        if (this$layerId == null) {
            if (other$layerId != null) {
                return false;
            }
        } else if (!this$layerId.equals(other$layerId)) {
            return false;
        }
        Object this$layerName = getLayerName();
        Object other$layerName = other.getLayerName();
        if (this$layerName == null) {
            if (other$layerName != null) {
                return false;
            }
        } else if (!this$layerName.equals(other$layerName)) {
            return false;
        }
        Object this$railScope = getRailScope();
        Object other$railScope = other.getRailScope();
        if (this$railScope == null) {
            if (other$railScope != null) {
                return false;
            }
        } else if (!this$railScope.equals(other$railScope)) {
            return false;
        }
        Object this$longitude = getLongitude();
        Object other$longitude = other.getLongitude();
        if (this$longitude == null) {
            if (other$longitude != null) {
                return false;
            }
        } else if (!this$longitude.equals(other$longitude)) {
            return false;
        }
        Object this$latitude = getLatitude();
        Object other$latitude = other.getLatitude();
        if (this$latitude == null) {
            if (other$latitude != null) {
                return false;
            }
        } else if (!this$latitude.equals(other$latitude)) {
            return false;
        }
        Object this$personType = getPersonType();
        Object other$personType = other.getPersonType();
        if (this$personType == null) {
            if (other$personType != null) {
                return false;
            }
        } else if (!this$personType.equals(other$personType)) {
            return false;
        }
        Object this$staffType = getStaffType();
        Object other$staffType = other.getStaffType();
        if (this$staffType == null) {
            if (other$staffType != null) {
                return false;
            }
        } else if (!this$staffType.equals(other$staffType)) {
            return false;
        }
        Object this$realName = getRealName();
        Object other$realName = other.getRealName();
        if (this$realName == null) {
            if (other$realName != null) {
                return false;
            }
        } else if (!this$realName.equals(other$realName)) {
            return false;
        }
        Object this$administratorName = getAdministratorName();
        Object other$administratorName = other.getAdministratorName();
        if (this$administratorName == null) {
            if (other$administratorName != null) {
                return false;
            }
        } else if (!this$administratorName.equals(other$administratorName)) {
            return false;
        }
        Object this$administratorPhone = getAdministratorPhone();
        Object other$administratorPhone = other.getAdministratorPhone();
        if (this$administratorPhone == null) {
            if (other$administratorPhone != null) {
                return false;
            }
        } else if (!this$administratorPhone.equals(other$administratorPhone)) {
            return false;
        }
        Object this$personPhoto = getPersonPhoto();
        Object other$personPhoto = other.getPersonPhoto();
        if (this$personPhoto == null) {
            if (other$personPhoto != null) {
                return false;
            }
        } else if (!this$personPhoto.equals(other$personPhoto)) {
            return false;
        }
        Object this$deptName = getDeptName();
        Object other$deptName = other.getDeptName();
        if (this$deptName == null) {
            if (other$deptName != null) {
                return false;
            }
        } else if (!this$deptName.equals(other$deptName)) {
            return false;
        }
        Object this$postName = getPostName();
        Object other$postName = other.getPostName();
        if (this$postName == null) {
            if (other$postName != null) {
                return false;
            }
        } else if (!this$postName.equals(other$postName)) {
            return false;
        }
        Object this$jobNumber = getJobNumber();
        Object other$jobNumber = other.getJobNumber();
        if (this$jobNumber == null) {
            if (other$jobNumber != null) {
                return false;
            }
        } else if (!this$jobNumber.equals(other$jobNumber)) {
            return false;
        }
        Object this$contractorName = getContractorName();
        Object other$contractorName = other.getContractorName();
        if (this$contractorName == null) {
            if (other$contractorName != null) {
                return false;
            }
        } else if (!this$contractorName.equals(other$contractorName)) {
            return false;
        }
        Object this$endTime = getEndTime();
        Object other$endTime = other.getEndTime();
        if (this$endTime == null) {
            if (other$endTime != null) {
                return false;
            }
        } else if (!this$endTime.equals(other$endTime)) {
            return false;
        }
        Object this$alarmLevel = getAlarmLevel();
        Object other$alarmLevel = other.getAlarmLevel();
        if (this$alarmLevel == null) {
            if (other$alarmLevel != null) {
                return false;
            }
        } else if (!this$alarmLevel.equals(other$alarmLevel)) {
            return false;
        }
        Object this$highestAlarmLevel = getHighestAlarmLevel();
        Object other$highestAlarmLevel = other.getHighestAlarmLevel();
        if (this$highestAlarmLevel == null) {
            if (other$highestAlarmLevel != null) {
                return false;
            }
        } else if (!this$highestAlarmLevel.equals(other$highestAlarmLevel)) {
            return false;
        }
        Object this$buildingName = getBuildingName();
        Object other$buildingName = other.getBuildingName();
        return this$buildingName == null ? other$buildingName == null : this$buildingName.equals(other$buildingName);
    }
    protected boolean canEqual(Object other) {
        return other instanceof Alarm;
    }
    public int hashCode() {
        Object $alarmId = getAlarmId();
        int result = (1 * 59) + ($alarmId == null ? 43 : $alarmId.hashCode());
        Object $areaId = getAreaId();
        int result2 = (result * 59) + ($areaId == null ? 43 : $areaId.hashCode());
        Object $railId = getRailId();
        int result3 = (result2 * 59) + ($railId == null ? 43 : $railId.hashCode());
        Object $railHeight = getRailHeight();
        int result4 = (result3 * 59) + ($railHeight == null ? 43 : $railHeight.hashCode());
        Object $layerHeight = getLayerHeight();
        int result5 = (result4 * 59) + ($layerHeight == null ? 43 : $layerHeight.hashCode());
        Object $alarmNoticeId = getAlarmNoticeId();
        int result6 = (result5 * 59) + ($alarmNoticeId == null ? 43 : $alarmNoticeId.hashCode());
        Object $secondAlarmNoticeId = getSecondAlarmNoticeId();
        int result7 = (result6 * 59) + ($secondAlarmNoticeId == null ? 43 : $secondAlarmNoticeId.hashCode());
        Object $thirdAlarmNoticeId = getThirdAlarmNoticeId();
        int result8 = (result7 * 59) + ($thirdAlarmNoticeId == null ? 43 : $thirdAlarmNoticeId.hashCode());
        Object $beaconId = getBeaconId();
        int result9 = (result8 * 59) + ($beaconId == null ? 43 : $beaconId.hashCode());
        Object $distance = getDistance();
        int result10 = (result9 * 59) + ($distance == null ? 43 : $distance.hashCode());
        Object $cardId = getCardId();
        int result11 = (result10 * 59) + ($cardId == null ? 43 : $cardId.hashCode());
        Object $personId = getPersonId();
        int result12 = (result11 * 59) + ($personId == null ? 43 : $personId.hashCode());
        Object $deptId = getDeptId();
        int result13 = (result12 * 59) + ($deptId == null ? 43 : $deptId.hashCode());
        Object $postId = getPostId();
        int result14 = (result13 * 59) + ($postId == null ? 43 : $postId.hashCode());
        Object $contractorId = getContractorId();
        int result15 = (result14 * 59) + ($contractorId == null ? 43 : $contractorId.hashCode());
        Object $alarmCount = getAlarmCount();
        int result16 = (result15 * 59) + ($alarmCount == null ? 43 : $alarmCount.hashCode());
        Object $timeCount = getTimeCount();
        int result17 = (result16 * 59) + ($timeCount == null ? 43 : $timeCount.hashCode());
        Object $drawType = getDrawType();
        int result18 = (result17 * 59) + ($drawType == null ? 43 : $drawType.hashCode());
        Object $buildingId = getBuildingId();
        int result19 = (result18 * 59) + ($buildingId == null ? 43 : $buildingId.hashCode());
        Object $alarmName = getAlarmName();
        int result20 = (result19 * 59) + ($alarmName == null ? 43 : $alarmName.hashCode());
        Object $alarmDesc = getAlarmDesc();
        int result21 = (result20 * 59) + ($alarmDesc == null ? 43 : $alarmDesc.hashCode());
        Object $alarmStatus = getAlarmStatus();
        int result22 = (result21 * 59) + ($alarmStatus == null ? 43 : $alarmStatus.hashCode());
        Object $acceptTime = getAcceptTime();
        int result23 = (result22 * 59) + ($acceptTime == null ? 43 : $acceptTime.hashCode());
        Object $createTime = getCreateTime();
        int result24 = (result23 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $createBy = getCreateBy();
        int result25 = (result24 * 59) + ($createBy == null ? 43 : $createBy.hashCode());
        Object $updateTime = getUpdateTime();
        int result26 = (result25 * 59) + ($updateTime == null ? 43 : $updateTime.hashCode());
        Object $updateBy = getUpdateBy();
        int result27 = (result26 * 59) + ($updateBy == null ? 43 : $updateBy.hashCode());
        Object $disposeTime = getDisposeTime();
        int result28 = (result27 * 59) + ($disposeTime == null ? 43 : $disposeTime.hashCode());
        Object $disposeFeedback = getDisposeFeedback();
        int result29 = (result28 * 59) + ($disposeFeedback == null ? 43 : $disposeFeedback.hashCode());
        Object $disposeBy = getDisposeBy();
        int result30 = (result29 * 59) + ($disposeBy == null ? 43 : $disposeBy.hashCode());
        Object $areaName = getAreaName();
        int result31 = (result30 * 59) + ($areaName == null ? 43 : $areaName.hashCode());
        Object $alarmType = getAlarmType();
        int result32 = (result31 * 59) + ($alarmType == null ? 43 : $alarmType.hashCode());
        Object $alarmTypeName = getAlarmTypeName();
        int result33 = (result32 * 59) + ($alarmTypeName == null ? 43 : $alarmTypeName.hashCode());
        Object $validBeginDate = getValidBeginDate();
        int result34 = (result33 * 59) + ($validBeginDate == null ? 43 : $validBeginDate.hashCode());
        Object $validEndDate = getValidEndDate();
        int result35 = (result34 * 59) + ($validEndDate == null ? 43 : $validEndDate.hashCode());
        Object $validBeginTime = getValidBeginTime();
        int result36 = (result35 * 59) + ($validBeginTime == null ? 43 : $validBeginTime.hashCode());
        Object $validEndTime = getValidEndTime();
        int result37 = (result36 * 59) + ($validEndTime == null ? 43 : $validEndTime.hashCode());
        Object $railIds = getRailIds();
        int result38 = (result37 * 59) + ($railIds == null ? 43 : $railIds.hashCode());
        Object $deptIds = getDeptIds();
        int result39 = (result38 * 59) + ($deptIds == null ? 43 : $deptIds.hashCode());
        Object $personIds = getPersonIds();
        int result40 = (result39 * 59) + ($personIds == null ? 43 : $personIds.hashCode());
        Object $railScopeList = getRailScopeList();
        int result41 = (result40 * 59) + ($railScopeList == null ? 43 : $railScopeList.hashCode());
        Object $railName = getRailName();
        int result42 = (result41 * 59) + ($railName == null ? 43 : $railName.hashCode());
        Object $layerId = getLayerId();
        int result43 = (result42 * 59) + ($layerId == null ? 43 : $layerId.hashCode());
        Object $layerName = getLayerName();
        int result44 = (result43 * 59) + ($layerName == null ? 43 : $layerName.hashCode());
        Object $railScope = getRailScope();
        int result45 = (result44 * 59) + ($railScope == null ? 43 : $railScope.hashCode());
        Object $longitude = getLongitude();
        int result46 = (result45 * 59) + ($longitude == null ? 43 : $longitude.hashCode());
        Object $latitude = getLatitude();
        int result47 = (result46 * 59) + ($latitude == null ? 43 : $latitude.hashCode());
        Object $personType = getPersonType();
        int result48 = (result47 * 59) + ($personType == null ? 43 : $personType.hashCode());
        Object $staffType = getStaffType();
        int result49 = (result48 * 59) + ($staffType == null ? 43 : $staffType.hashCode());
        Object $realName = getRealName();
        int result50 = (result49 * 59) + ($realName == null ? 43 : $realName.hashCode());
        Object $administratorName = getAdministratorName();
        int result51 = (result50 * 59) + ($administratorName == null ? 43 : $administratorName.hashCode());
        Object $administratorPhone = getAdministratorPhone();
        int result52 = (result51 * 59) + ($administratorPhone == null ? 43 : $administratorPhone.hashCode());
        Object $personPhoto = getPersonPhoto();
        int result53 = (result52 * 59) + ($personPhoto == null ? 43 : $personPhoto.hashCode());
        Object $deptName = getDeptName();
        int result54 = (result53 * 59) + ($deptName == null ? 43 : $deptName.hashCode());
        Object $postName = getPostName();
        int result55 = (result54 * 59) + ($postName == null ? 43 : $postName.hashCode());
        Object $jobNumber = getJobNumber();
        int result56 = (result55 * 59) + ($jobNumber == null ? 43 : $jobNumber.hashCode());
        Object $contractorName = getContractorName();
        int result57 = (result56 * 59) + ($contractorName == null ? 43 : $contractorName.hashCode());
        Object $endTime = getEndTime();
        int result58 = (result57 * 59) + ($endTime == null ? 43 : $endTime.hashCode());
        Object $alarmLevel = getAlarmLevel();
        int result59 = (result58 * 59) + ($alarmLevel == null ? 43 : $alarmLevel.hashCode());
        Object $highestAlarmLevel = getHighestAlarmLevel();
        int result60 = (result59 * 59) + ($highestAlarmLevel == null ? 43 : $highestAlarmLevel.hashCode());
        Object $buildingName = getBuildingName();
        return (result60 * 59) + ($buildingName == null ? 43 : $buildingName.hashCode());
    }
    public String toString() {
        return "Alarm(alarmId=" + getAlarmId() + ", alarmName=" + getAlarmName() + ", alarmDesc=" + getAlarmDesc() + ", alarmStatus=" + getAlarmStatus() + ", acceptTime=" + getAcceptTime() + ", createTime=" + getCreateTime() + ", createBy=" + getCreateBy() + ", updateTime=" + getUpdateTime() + ", updateBy=" + getUpdateBy() + ", disposeTime=" + getDisposeTime() + ", disposeFeedback=" + getDisposeFeedback() + ", disposeBy=" + getDisposeBy() + ", areaId=" + getAreaId() + ", areaName=" + getAreaName() + ", alarmType=" + getAlarmType() + ", alarmTypeName=" + getAlarmTypeName() + ", validBeginDate=" + getValidBeginDate() + ", validEndDate=" + getValidEndDate() + ", validBeginTime=" + getValidBeginTime() + ", validEndTime=" + getValidEndTime() + ", railIds=" + getRailIds() + ", deptIds=" + getDeptIds() + ", personIds=" + getPersonIds() + ", railScopeList=" + getRailScopeList() + ", railId=" + getRailId() + ", railName=" + getRailName() + ", railHeight=" + getRailHeight() + ", layerId=" + getLayerId() + ", layerName=" + getLayerName() + ", layerHeight=" + getLayerHeight() + ", railScope=" + getRailScope() + ", alarmNoticeId=" + getAlarmNoticeId() + ", secondAlarmNoticeId=" + getSecondAlarmNoticeId() + ", thirdAlarmNoticeId=" + getThirdAlarmNoticeId() + ", beaconId=" + getBeaconId() + ", distance=" + getDistance() + ", cardId=" + getCardId() + ", longitude=" + getLongitude() + ", latitude=" + getLatitude() + ", personId=" + getPersonId() + ", personType=" + getPersonType() + ", staffType=" + getStaffType() + ", realName=" + getRealName() + ", administratorName=" + getAdministratorName() + ", administratorPhone=" + getAdministratorPhone() + ", deptId=" + getDeptId() + ", postId=" + getPostId() + ", personPhoto=" + getPersonPhoto() + ", deptName=" + getDeptName() + ", postName=" + getPostName() + ", jobNumber=" + getJobNumber() + ", contractorId=" + getContractorId() + ", contractorName=" + getContractorName() + ", alarmCount=" + getAlarmCount() + ", timeCount=" + getTimeCount() + ", drawType=" + getDrawType() + ", endTime=" + getEndTime() + ", alarmLevel=" + getAlarmLevel() + ", highestAlarmLevel=" + getHighestAlarmLevel() + ", buildingName=" + getBuildingName() + ", buildingId=" + getBuildingId() + StringPool.RIGHT_BRACKET;
    }
    public Long getAlarmId() {
        return this.alarmId;
    }
    public String getAlarmName() {
        return this.alarmName;
    }
    public String getAlarmDesc() {
        return this.alarmDesc;
    }
    public String getAlarmStatus() {
        return this.alarmStatus;
    }
    public LocalDateTime getAcceptTime() {
        return this.acceptTime;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public String getCreateBy() {
        return this.createBy;
    }
    public LocalDateTime getUpdateTime() {
        return this.updateTime;
    }
    public String getUpdateBy() {
        return this.updateBy;
    }
    public LocalDateTime getDisposeTime() {
        return this.disposeTime;
    }
    public String getDisposeFeedback() {
        return this.disposeFeedback;
    }
    public String getDisposeBy() {
        return this.disposeBy;
    }
    public Long getAreaId() {
        return this.areaId;
    }
    public String getAreaName() {
        return this.areaName;
    }
    public String getAlarmType() {
        return this.alarmType;
    }
    public String getAlarmTypeName() {
        return this.alarmTypeName;
    }
    public LocalDate getValidBeginDate() {
        return this.validBeginDate;
    }
    public LocalDate getValidEndDate() {
        return this.validEndDate;
    }
    public LocalTime getValidBeginTime() {
        return this.validBeginTime;
    }
    public LocalTime getValidEndTime() {
        return this.validEndTime;
    }
    public List<Long> getRailIds() {
        return this.railIds;
    }
    public List<Long> getDeptIds() {
        return this.deptIds;
    }
    public List<Long> getPersonIds() {
        return this.personIds;
    }
    public List<String> getRailScopeList() {
        return this.railScopeList;
    }
    public Long getRailId() {
        return this.railId;
    }
    public String getRailName() {
        return this.railName;
    }
    public Integer getRailHeight() {
        return this.railHeight;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public String getLayerName() {
        return this.layerName;
    }
    public Integer getLayerHeight() {
        return this.layerHeight;
    }
    public String getRailScope() {
        return this.railScope;
    }
    public Long getAlarmNoticeId() {
        return this.alarmNoticeId;
    }
    public Long getSecondAlarmNoticeId() {
        return this.secondAlarmNoticeId;
    }
    public Long getThirdAlarmNoticeId() {
        return this.thirdAlarmNoticeId;
    }
    public Integer getBeaconId() {
        return this.beaconId;
    }
    public Double getDistance() {
        return this.distance;
    }
    public Long getCardId() {
        return this.cardId;
    }
    public BigDecimal getLongitude() {
        return this.longitude;
    }
    public BigDecimal getLatitude() {
        return this.latitude;
    }
    public Long getPersonId() {
        return this.personId;
    }
    public String getPersonType() {
        return this.personType;
    }
    public String getStaffType() {
        return this.staffType;
    }
    public String getRealName() {
        return this.realName;
    }
    public String getAdministratorName() {
        return this.administratorName;
    }
    public String getAdministratorPhone() {
        return this.administratorPhone;
    }
    public Long getDeptId() {
        return this.deptId;
    }
    public Long getPostId() {
        return this.postId;
    }
    public String getPersonPhoto() {
        return this.personPhoto;
    }
    public String getDeptName() {
        return this.deptName;
    }
    public String getPostName() {
        return this.postName;
    }
    public String getJobNumber() {
        return this.jobNumber;
    }
    public Long getContractorId() {
        return this.contractorId;
    }
    public String getContractorName() {
        return this.contractorName;
    }
    public Integer getAlarmCount() {
        return this.alarmCount;
    }
    public Integer getTimeCount() {
        return this.timeCount;
    }
    public Integer getDrawType() {
        return this.drawType;
    }
    public LocalDateTime getEndTime() {
        return this.endTime;
    }
    public String getAlarmLevel() {
        return this.alarmLevel;
    }
    public String getHighestAlarmLevel() {
        return this.highestAlarmLevel;
    }
    public String getBuildingName() {
        return this.buildingName;
    }
    public Long getBuildingId() {
        return this.buildingId;
    }
}
