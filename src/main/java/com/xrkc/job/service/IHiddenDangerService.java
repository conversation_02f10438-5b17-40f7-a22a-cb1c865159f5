package com.xrkc.job.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xrkc.job.domain.HiddenDanger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IHiddenDangerService.class */
public interface IHiddenDangerService extends IService<HiddenDanger> {
    List<HiddenDanger> selectDangerList(LocalDate localDate, LocalDateTime localDateTime);
    List<HiddenDanger> selectPushDangerList();
}
