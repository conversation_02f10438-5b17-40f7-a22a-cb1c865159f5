package com.xrkc.job.util.direct;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/direct/LatLngBounds.class */
public class LatLngBounds {
    private Point southwest;
    private Point northeast;
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/direct/LatLngBounds$LatLngBoundsBuilder.class */
    public static class LatLngBoundsBuilder {
        private Point southwest;
        private Point northeast;
        LatLngBoundsBuilder() {
        }
        public LatLngBoundsBuilder southwest(Point southwest) {
            this.southwest = southwest;
            return this;
        }
        public LatLngBoundsBuilder northeast(Point northeast) {
            this.northeast = northeast;
            return this;
        }
        public LatLngBounds build() {
            return new LatLngBounds(this.southwest, this.northeast);
        }
        public String toString() {
            return "LatLngBounds.LatLngBoundsBuilder(southwest=" + this.southwest + ", northeast=" + this.northeast + StringPool.RIGHT_BRACKET;
        }
    }
    public void setSouthwest(Point southwest) {
        this.southwest = southwest;
    }
    public void setNortheast(Point northeast) {
        this.northeast = northeast;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof LatLngBounds)) {
            return false;
        }
        LatLngBounds other = (LatLngBounds) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$southwest = getSouthwest();
        Object other$southwest = other.getSouthwest();
        if (this$southwest == null) {
            if (other$southwest != null) {
                return false;
            }
        } else if (!this$southwest.equals(other$southwest)) {
            return false;
        }
        Object this$northeast = getNortheast();
        Object other$northeast = other.getNortheast();
        return this$northeast == null ? other$northeast == null : this$northeast.equals(other$northeast);
    }
    protected boolean canEqual(Object other) {
        return other instanceof LatLngBounds;
    }
    public int hashCode() {
        Object $southwest = getSouthwest();
        int result = (1 * 59) + ($southwest == null ? 43 : $southwest.hashCode());
        Object $northeast = getNortheast();
        return (result * 59) + ($northeast == null ? 43 : $northeast.hashCode());
    }
    public String toString() {
        return "LatLngBounds(southwest=" + getSouthwest() + ", northeast=" + getNortheast() + StringPool.RIGHT_BRACKET;
    }
    LatLngBounds(Point southwest, Point northeast) {
        this.southwest = southwest;
        this.northeast = northeast;
    }
    public static LatLngBoundsBuilder builder() {
        return new LatLngBoundsBuilder();
    }
    public Point getSouthwest() {
        return this.southwest;
    }
    public Point getNortheast() {
        return this.northeast;
    }
    public boolean contains(Point point) {
        return checkLatitude(point.getLatitude()) && checkLongitude(point.getLongitude());
    }
    private boolean checkLatitude(double latitude) {
        return this.southwest.getLatitude() <= latitude && latitude <= this.northeast.getLatitude();
    }
    private boolean checkLongitude(double longitude) {
        return this.southwest.getLongitude() <= longitude && longitude <= this.northeast.getLongitude();
    }
}
