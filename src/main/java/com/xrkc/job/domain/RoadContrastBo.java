package com.xrkc.job.domain;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/RoadContrastBo.class */
public class RoadContrastBo {
    private Integer roadCount;
    private Integer roadAllCount;
    public void setRoadCount(Integer roadCount) {
        this.roadCount = roadCount;
    }
    public void setRoadAllCount(Integer roadAllCount) {
        this.roadAllCount = roadAllCount;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof RoadContrastBo)) {
            return false;
        }
        RoadContrastBo other = (RoadContrastBo) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$roadCount = getRoadCount();
        Object other$roadCount = other.getRoadCount();
        if (this$roadCount == null) {
            if (other$roadCount != null) {
                return false;
            }
        } else if (!this$roadCount.equals(other$roadCount)) {
            return false;
        }
        Object this$roadAllCount = getRoadAllCount();
        Object other$roadAllCount = other.getRoadAllCount();
        return this$roadAllCount == null ? other$roadAllCount == null : this$roadAllCount.equals(other$roadAllCount);
    }
    protected boolean canEqual(Object other) {
        return other instanceof RoadContrastBo;
    }
    public int hashCode() {
        Object $roadCount = getRoadCount();
        int result = (1 * 59) + ($roadCount == null ? 43 : $roadCount.hashCode());
        Object $roadAllCount = getRoadAllCount();
        return (result * 59) + ($roadAllCount == null ? 43 : $roadAllCount.hashCode());
    }
    public String toString() {
        return "RoadContrastBo(roadCount=" + getRoadCount() + ", roadAllCount=" + getRoadAllCount() + StringPool.RIGHT_BRACKET;
    }
    public Integer getRoadCount() {
        return this.roadCount;
    }
    public Integer getRoadAllCount() {
        return this.roadAllCount;
    }
}
