package com.xrkc.job.mapper;
import com.xrkc.core.domain.inspect.InspectRecord;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/InspectRecordHistoryMapper.class */
public interface InspectRecordHistoryMapper {
    List<InspectRecord> selectCopyHistory(@Param("months") LocalDateTime localDateTime);
    int batchDelRecord(List<Long> list);
    int batchDelRecordLocation(List<Long> list);
    int batchDelRecordItem(List<Long> list);
    int batchDelRecordParam(List<Long> list);
    int batchBakHistory(List<InspectRecord> list);
    int delHistory(List<Long> list);
    List<Long> findDelRecordIds(@Param("minusYears") LocalDateTime localDateTime);
}
