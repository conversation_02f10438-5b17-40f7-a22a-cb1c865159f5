package com.xrkc.job.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xrkc.job.domain.AlarmNoticeRecordPerson;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IAlarmNoticeRecordPersonService.class */
public interface IAlarmNoticeRecordPersonService extends IService<AlarmNoticeRecordPerson> {
    List<AlarmNoticeRecordPerson> getListByRecordId(Long l);
    void updateByRecordId(AlarmNoticeRecordPerson alarmNoticeRecordPerson);
    void updateFinishByRecordId(Long l, String str);
}
