package com.xrkc.job.util.direct;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/direct/Point.class */
public class Point implements Serializable {
    private double longitude;
    private double latitude;
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/direct/Point$PointBuilder.class */
    public static class PointBuilder {
        private double longitude;
        private double latitude;
        PointBuilder() {
        }
        public PointBuilder longitude(double longitude) {
            this.longitude = longitude;
            return this;
        }
        public PointBuilder latitude(double latitude) {
            this.latitude = latitude;
            return this;
        }
        public Point build() {
            return new Point(this.longitude, this.latitude);
        }
        public String toString() {
            return "Point.PointBuilder(longitude=" + this.longitude + ", latitude=" + this.latitude + StringPool.RIGHT_BRACKET;
        }
    }
    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }
    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof Point)) {
            return false;
        }
        Point other = (Point) o;
        return other.canEqual(this) && Double.compare(getLongitude(), other.getLongitude()) == 0 && Double.compare(getLatitude(), other.getLatitude()) == 0;
    }
    protected boolean canEqual(Object other) {
        return other instanceof Point;
    }
    public int hashCode() {
        long $longitude = Double.doubleToLongBits(getLongitude());
        int result = (1 * 59) + ((int) (($longitude >>> 32) ^ $longitude));
        long $latitude = Double.doubleToLongBits(getLatitude());
        return (result * 59) + ((int) (($latitude >>> 32) ^ $latitude));
    }
    public String toString() {
        return "Point(longitude=" + getLongitude() + ", latitude=" + getLatitude() + StringPool.RIGHT_BRACKET;
    }
    Point(double longitude, double latitude) {
        this.longitude = longitude;
        this.latitude = latitude;
    }
    public static PointBuilder builder() {
        return new PointBuilder();
    }
    public double getLongitude() {
        return this.longitude;
    }
    public double getLatitude() {
        return this.latitude;
    }
}
