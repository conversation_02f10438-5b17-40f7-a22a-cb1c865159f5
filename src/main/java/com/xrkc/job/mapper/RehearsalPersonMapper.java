package com.xrkc.job.mapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xrkc.core.mybatisplus.BaseMapperX;
import com.xrkc.job.domain.RehearsalPerson;
import java.util.List;
public interface RehearsalPersonMapper extends BaseMapperX<RehearsalPerson> {
    List<RehearsalPerson> findRehearsalPersonList(List<Long> list);
    List<RehearsalPerson> findUnCompletePersonByRehearsalId(Long l);
    default int batchCompleteUpdate(List<Long> list) {
        LambdaUpdateWrapper<RehearsalPerson> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set((v0) -> v0.getPositionStatus(), "Y");
        wrapper.in((v0) -> v0.getId(), list);
        return update(null, wrapper);
    }
}
