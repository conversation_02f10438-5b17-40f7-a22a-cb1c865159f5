package com.xrkc.job.service;
import com.xrkc.job.domain.PositionDataCalc;
import com.xrkc.job.domain.PositionVO;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IPositionDataCalcService.class */
public interface IPositionDataCalcService {
    int replaceVO(PositionVO positionVO);
    void batchDelete(int i);
    void insertByCurrent(PositionVO positionVO);
    List<PositionDataCalc> selectList(int i);
}
