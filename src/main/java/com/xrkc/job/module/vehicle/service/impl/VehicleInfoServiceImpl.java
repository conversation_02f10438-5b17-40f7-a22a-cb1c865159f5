package com.xrkc.job.module.vehicle.service.impl;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xrkc.core.domain.vehicle.VehicleInfo;
import com.xrkc.job.module.vehicle.mapper.VehicleInfoMapper;
import com.xrkc.job.module.vehicle.service.IVehicleInfoService;
import com.xrkc.job.service.IDeviceCardService;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/module/vehicle/service/impl/VehicleInfoServiceImpl.class */
public class VehicleInfoServiceImpl implements IVehicleInfoService {
    private final VehicleInfoMapper mapper;
    private final IDeviceCardService deviceCardService;
    @Autowired
    public VehicleInfoServiceImpl(VehicleInfoMapper mapper, IDeviceCardService deviceCardService) {
        this.mapper = mapper;
        this.deviceCardService = deviceCardService;
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehicleInfoService
    public Map<Long, VehicleInfo> findCardIdEntityMap() {
        return (Map) this.mapper.selectList().stream().filter(f -> {
            return Objects.nonNull(f.getCardId());
        }).collect(Collectors.toMap((v0) -> {
            return v0.getCardId();
        }, Function.identity()));
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehicleInfoService
    public List<VehicleInfo> selectListByIdList(List<Long> vehicleIdList) {
        return this.mapper.selectBatchIds(vehicleIdList);
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehicleInfoService
    public List<VehicleInfo> selectLowBatteryList() {
        List<VehicleInfo> list = this.mapper.selectNotNullCardIdList();
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList();
        }
        List<Long> lowBatteryCardIdList = (List) this.deviceCardService.selectListByCardIdList((List) list.stream().map((v0) -> {
            return v0.getCardId();
        }).collect(Collectors.toList())).stream().filter(f -> {
            return Objects.nonNull(f.getCardPower()) && f.getCardPower().compareTo((Integer) 20) < 0;
        }).map((v0) -> {
            return v0.getCardId();
        }).collect(Collectors.toList());
        list.removeIf(r -> {
            return !lowBatteryCardIdList.contains(r.getCardId());
        });
        return list;
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehicleInfoService
    public List<Long> findCardIdList() {
        return (List) this.mapper.selectList().stream().filter(f -> {
            return Objects.nonNull(f.getCardId());
        }).map((v0) -> {
            return v0.getCardId();
        }).collect(Collectors.toList());
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehicleInfoService
    public List<VehicleInfo> selectList() {
        return this.mapper.selectList();
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehicleInfoService
    public Long count() {
        return this.mapper.selectCount();
    }
    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.xrkc.job.module.vehicle.service.IVehicleInfoService
    public List<VehicleInfo> selectToFaceVehicleList() {
        return this.mapper.selectList((Wrapper) new LambdaQueryWrapper<VehicleInfo>().isNotNull((v0) -> {
            return v0.getDriverPhoto();
        }));
    }
}
