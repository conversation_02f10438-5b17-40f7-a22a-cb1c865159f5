package com.xrkc.job.domain;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/VehicleCurrentPosition.class */
public class VehicleCurrentPosition implements Serializable {
    private static final long serialVersionUID = 1;
    private Long vehicleId;
    private String vehicleSn;
    private Long carId;
    private String carNo;
    private String vehicleType;
    private String vehicleTypeName;
    private String vehicleAttribute;
    private String vehicleName;
    private String driverName;
    private String driverTel;
    private String vehicleStatus;
    private Integer speedLimit;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime acceptTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime heartTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime staticTime;
    private Integer speed;
    private Integer online;
    private Integer dir;
    private Integer vehiclePower;
    private Integer pointType;
    private BigDecimal longitude;
    private BigDecimal latitude;
    private String companyName;
    private Long undisposedAlarmCount;
    public String getVehicleSn() {
        return this.vehicleSn;
    }
    public void setVehicleSn(String vehicleSn) {
        this.vehicleSn = vehicleSn;
    }
    public Integer getSpeed() {
        return this.speed;
    }
    public void setSpeed(Integer speed) {
        this.speed = speed;
    }
    public BigDecimal getLongitude() {
        return this.longitude;
    }
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    public BigDecimal getLatitude() {
        return this.latitude;
    }
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    public Integer getVehiclePower() {
        return this.vehiclePower;
    }
    public void setVehiclePower(Integer vehiclePower) {
        this.vehiclePower = vehiclePower;
    }
    public Integer getDir() {
        return this.dir;
    }
    public void setDir(Integer dir) {
        this.dir = dir;
    }
    public LocalDateTime getAcceptTime() {
        return this.acceptTime;
    }
    public void setAcceptTime(LocalDateTime acceptTime) {
        this.acceptTime = acceptTime;
    }
    public LocalDateTime getHeartTime() {
        return this.heartTime;
    }
    public void setHeartTime(LocalDateTime heartTime) {
        this.heartTime = heartTime;
    }
    public Integer getOnline() {
        return this.online;
    }
    public void setOnline(Integer online) {
        this.online = online;
    }
    public Long getVehicleId() {
        return this.vehicleId;
    }
    public void setVehicleId(Long vehicleId) {
        this.vehicleId = vehicleId;
    }
    public Long getCarId() {
        return this.carId;
    }
    public void setCarId(Long carId) {
        this.carId = carId;
    }
    public String getCarNo() {
        return this.carNo;
    }
    public void setCarNo(String carNo) {
        this.carNo = carNo;
    }
    public String getVehicleType() {
        return this.vehicleType;
    }
    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }
    public String getVehicleTypeName() {
        return this.vehicleTypeName;
    }
    public void setVehicleTypeName(String vehicleTypeName) {
        this.vehicleTypeName = vehicleTypeName;
    }
    public String getVehicleName() {
        return this.vehicleName;
    }
    public void setVehicleName(String vehicleName) {
        this.vehicleName = vehicleName;
    }
    public String getDriverName() {
        return this.driverName;
    }
    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }
    public String getDriverTel() {
        return this.driverTel;
    }
    public void setDriverTel(String driverTel) {
        this.driverTel = driverTel;
    }
    public String getVehicleStatus() {
        return this.vehicleStatus;
    }
    public void setVehicleStatus(String vehicleStatus) {
        this.vehicleStatus = vehicleStatus;
    }
    public Integer getSpeedLimit() {
        return this.speedLimit;
    }
    public void setSpeedLimit(Integer speedLimit) {
        this.speedLimit = speedLimit;
    }
    public LocalDateTime getStaticTime() {
        return this.staticTime;
    }
    public void setStaticTime(LocalDateTime staticTime) {
        this.staticTime = staticTime;
    }
    public Integer getPointType() {
        return this.pointType;
    }
    public void setPointType(Integer pointType) {
        this.pointType = pointType;
    }
    public String getVehicleAttribute() {
        return this.vehicleAttribute;
    }
    public void setVehicleAttribute(String vehicleAttribute) {
        this.vehicleAttribute = vehicleAttribute;
    }
    public String getCompanyName() {
        return this.companyName;
    }
    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
    public Long getUndisposedAlarmCount() {
        return this.undisposedAlarmCount;
    }
    public void setUndisposedAlarmCount(Long undisposedAlarmCount) {
        this.undisposedAlarmCount = undisposedAlarmCount;
    }
}
