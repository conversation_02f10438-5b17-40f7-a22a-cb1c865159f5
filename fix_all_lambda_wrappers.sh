#!/bin/bash

# 批量修复项目中所有类型的 LambdaQueryWrapper 泛型类型问题

echo "开始批量修复项目中所有类型的 LambdaQueryWrapper 泛型类型问题..."

# 统计修复的文件数量
total_count=0

# 1. 修复 new LambdaQueryWrapper() 的使用
echo "1. 修复 new LambdaQueryWrapper() 的使用..."
files=$(find src/main/java -name "*.java" -exec grep -l "new LambdaQueryWrapper()" {} \; 2>/dev/null)
if [ ! -z "$files" ]; then
    for file in $files; do
        echo "  - 修复文件: $file"
        
        # 备份原文件
        cp "$file" "$file.backup"
        
        # 获取文件中的实体类名称
        entity_class=$(grep -o "extends BaseMapperX<[^>]*>" "$file" | head -1 | sed 's/.*<\([^>]*\)>.*/\1/')
        
        if [ -z "$entity_class" ]; then
            # 尝试从导入语句中获取实体类
            entity_class=$(grep "^import.*\." "$file" | grep -v "import.*\*" | grep -E "(Entity|Domain|Model|VO|DTO)" | head -1 | sed 's/.*\.//')
        fi
        
        if [ -z "$entity_class" ]; then
            # 尝试从文件名推断
            filename=$(basename "$file" .java)
            if [[ "$filename" == *"Mapper" ]]; then
                entity_class=$(echo "$filename" | sed 's/Mapper$//')
            elif [[ "$filename" == *"ServiceImpl" ]]; then
                entity_class=$(echo "$filename" | sed 's/ServiceImpl$//' | sed 's/^I//')
            fi
        fi
        
        echo "    - 推断的实体类: $entity_class"
        
        # 替换 new LambdaQueryWrapper() 为 new LambdaQueryWrapper<EntityClass>()
        if [ ! -z "$entity_class" ]; then
            sed -i '' "s/new LambdaQueryWrapper()/new LambdaQueryWrapper<$entity_class>()/g" "$file"
            echo "    - 已添加泛型类型: $entity_class"
            total_count=$((total_count + 1))
        else
            echo "    - 警告: 无法推断实体类，跳过此文件"
        fi
    done
fi

# 2. 修复 new LambdaUpdateWrapper() 的使用
echo "2. 修复 new LambdaUpdateWrapper() 的使用..."
files=$(find src/main/java -name "*.java" -exec grep -l "new LambdaUpdateWrapper()" {} \; 2>/dev/null)
if [ ! -z "$files" ]; then
    for file in $files; do
        echo "  - 修复文件: $file"
        
        # 备份原文件
        cp "$file" "$file.backup"
        
        # 获取文件中的实体类名称
        entity_class=$(grep -o "extends BaseMapperX<[^>]*>" "$file" | head -1 | sed 's/.*<\([^>]*\)>.*/\1/')
        
        if [ -z "$entity_class" ]; then
            # 尝试从导入语句中获取实体类
            entity_class=$(grep "^import.*\." "$file" | grep -v "import.*\*" | grep -E "(Entity|Domain|Model|VO|DTO)" | head -1 | sed 's/.*\.//')
        fi
        
        if [ -z "$entity_class" ]; then
            # 尝试从文件名推断
            filename=$(basename "$file" .java)
            if [[ "$filename" == *"Mapper" ]]; then
                entity_class=$(echo "$filename" | sed 's/Mapper$//')
            elif [[ "$filename" == *"ServiceImpl" ]]; then
                entity_class=$(echo "$filename" | sed 's/ServiceImpl$//' | sed 's/^I//')
            fi
        fi
        
        echo "    - 推断的实体类: $entity_class"
        
        # 替换 new LambdaUpdateWrapper() 为 new LambdaUpdateWrapper<EntityClass>()
        if [ ! -z "$entity_class" ]; then
            sed -i '' "s/new LambdaUpdateWrapper()/new LambdaUpdateWrapper<$entity_class>()/g" "$file"
            echo "    - 已添加泛型类型: $entity_class"
            total_count=$((total_count + 1))
        else
            echo "    - 警告: 无法推断实体类，跳过此文件"
        fi
    done
fi

# 3. 修复 new QueryWrapper() 的使用
echo "3. 修复 new QueryWrapper() 的使用..."
files=$(find src/main/java -name "*.java" -exec grep -l "new QueryWrapper()" {} \; 2>/dev/null)
if [ ! -z "$files" ]; then
    for file in $files; do
        echo "  - 修复文件: $file"
        
        # 备份原文件
        cp "$file" "$file.backup"
        
        # 获取文件中的实体类名称
        entity_class=$(grep -o "extends BaseMapperX<[^>]*>" "$file" | head -1 | sed 's/.*<\([^>]*\)>.*/\1/')
        
        if [ -z "$entity_class" ]; then
            # 尝试从导入语句中获取实体类
            entity_class=$(grep "^import.*\." "$file" | grep -v "import.*\*" | grep -E "(Entity|Domain|Model|VO|DTO)" | head -1 | sed 's/.*\.//')
        fi
        
        if [ -z "$entity_class" ]; then
            # 尝试从文件名推断
            filename=$(basename "$file" .java)
            if [[ "$filename" == *"Mapper" ]]; then
                entity_class=$(echo "$filename" | sed 's/Mapper$//')
            elif [[ "$filename" == *"ServiceImpl" ]]; then
                entity_class=$(echo "$filename" | sed 's/ServiceImpl$//' | sed 's/^I//')
            fi
        fi
        
        echo "    - 推断的实体类: $entity_class"
        
        # 替换 new QueryWrapper() 为 new QueryWrapper<EntityClass>()
        if [ ! -z "$entity_class" ]; then
            sed -i '' "s/new QueryWrapper()/new QueryWrapper<$entity_class>()/g" "$file"
            echo "    - 已添加泛型类型: $entity_class"
            total_count=$((total_count + 1))
        else
            echo "    - 警告: 无法推断实体类，跳过此文件"
        fi
    done
fi

# 4. 修复 new UpdateWrapper() 的使用
echo "4. 修复 new UpdateWrapper() 的使用..."
files=$(find src/main/java -name "*.java" -exec grep -l "new UpdateWrapper()" {} \; 2>/dev/null)
if [ ! -z "$files" ]; then
    for file in $files; do
        echo "  - 修复文件: $file"
        
        # 备份原文件
        cp "$file" "$file.backup"
        
        # 获取文件中的实体类名称
        entity_class=$(grep -o "extends BaseMapperX<[^>]*>" "$file" | head -1 | sed 's/.*<\([^>]*\)>.*/\1/')
        
        if [ -z "$entity_class" ]; then
            # 尝试从导入语句中获取实体类
            entity_class=$(grep "^import.*\." "$file" | grep -v "import.*\*" | grep -E "(Entity|Domain|Model|VO|DTO)" | head -1 | sed 's/.*\.//')
        fi
        
        if [ -z "$entity_class" ]; then
            # 尝试从文件名推断
            filename=$(basename "$file" .java)
            if [[ "$filename" == *"Mapper" ]]; then
                entity_class=$(echo "$filename" | sed 's/Mapper$//')
            elif [[ "$filename" == *"ServiceImpl" ]]; then
                entity_class=$(echo "$filename" | sed 's/ServiceImpl$//' | sed 's/^I//')
            fi
        fi
        
        echo "    - 推断的实体类: $entity_class"
        
        # 替换 new UpdateWrapper() 为 new UpdateWrapper<EntityClass>()
        if [ ! -z "$entity_class" ]; then
            sed -i '' "s/new UpdateWrapper()/new UpdateWrapper<$entity_class>()/g" "$file"
            echo "    - 已添加泛型类型: $entity_class"
            total_count=$((total_count + 1))
        else
            echo "    - 警告: 无法推断实体类，跳过此文件"
        fi
    done
fi

# 5. 修复 Wrappers.lambdaQuery() 的使用
echo "5. 修复 Wrappers.lambdaQuery() 的使用..."
files=$(find src/main/java -name "*.java" -exec grep -l "Wrappers\.lambdaQuery()" {} \; 2>/dev/null)
if [ ! -z "$files" ]; then
    for file in $files; do
        echo "  - 修复文件: $file"
        
        # 备份原文件
        cp "$file" "$file.backup"
        
        # 获取文件中的实体类名称
        entity_class=$(grep -o "extends BaseMapperX<[^>]*>" "$file" | head -1 | sed 's/.*<\([^>]*\)>.*/\1/')
        
        if [ -z "$entity_class" ]; then
            # 尝试从导入语句中获取实体类
            entity_class=$(grep "^import.*\." "$file" | grep -v "import.*\*" | grep -E "(Entity|Domain|Model|VO|DTO)" | head -1 | sed 's/.*\.//')
        fi
        
        if [ -z "$entity_class" ]; then
            # 尝试从文件名推断
            filename=$(basename "$file" .java)
            if [[ "$filename" == *"Mapper" ]]; then
                entity_class=$(echo "$filename" | sed 's/Mapper$//')
            elif [[ "$filename" == *"ServiceImpl" ]]; then
                entity_class=$(echo "$filename" | sed 's/ServiceImpl$//' | sed 's/^I//')
            fi
        fi
        
        echo "    - 推断的实体类: $entity_class"
        
        # 替换 Wrappers.lambdaQuery() 为 Wrappers.lambdaQuery(EntityClass.class)
        if [ ! -z "$entity_class" ]; then
            sed -i '' "s/Wrappers\.lambdaQuery()/Wrappers.lambdaQuery($entity_class.class)/g" "$file"
            echo "    - 已添加泛型类型: $entity_class"
            total_count=$((total_count + 1))
        else
            echo "    - 警告: 无法推断实体类，跳过此文件"
        fi
    done
fi

# 6. 修复 Wrappers.lambdaUpdate() 的使用
echo "6. 修复 Wrappers.lambdaUpdate() 的使用..."
files=$(find src/main/java -name "*.java" -exec grep -l "Wrappers\.lambdaUpdate()" {} \; 2>/dev/null)
if [ ! -z "$files" ]; then
    for file in $files; do
        echo "  - 修复文件: $file"
        
        # 备份原文件
        cp "$file" "$file.backup"
        
        # 获取文件中的实体类名称
        entity_class=$(grep -o "extends BaseMapperX<[^>]*>" "$file" | head -1 | sed 's/.*<\([^>]*\)>.*/\1/')
        
        if [ -z "$entity_class" ]; then
            # 尝试从导入语句中获取实体类
            entity_class=$(grep "^import.*\." "$file" | grep -v "import.*\*" | grep -E "(Entity|Domain|Model|VO|DTO)" | head -1 | sed 's/.*\.//')
        fi
        
        if [ -z "$entity_class" ]; then
            # 尝试从文件名推断
            filename=$(basename "$file" .java)
            if [[ "$filename" == *"Mapper" ]]; then
                entity_class=$(echo "$filename" | sed 's/Mapper$//')
            elif [[ "$filename" == *"ServiceImpl" ]]; then
                entity_class=$(echo "$filename" | sed 's/ServiceImpl$//' | sed 's/^I//')
            fi
        fi
        
        echo "    - 推断的实体类: $entity_class"
        
        # 替换 Wrappers.lambdaUpdate() 为 Wrappers.lambdaUpdate(EntityClass.class)
        if [ ! -z "$entity_class" ]; then
            sed -i '' "s/Wrappers\.lambdaUpdate()/Wrappers.lambdaUpdate($entity_class.class)/g" "$file"
            echo "    - 已添加泛型类型: $entity_class"
            total_count=$((total_count + 1))
        else
            echo "    - 警告: 无法推断实体类，跳过此文件"
        fi
    done
fi

echo ""
echo "批量修复完成！"
echo "总共修复了 $total_count 个文件"
echo ""
echo "修复内容："
echo "1. 修复了 new LambdaQueryWrapper() 的使用"
echo "2. 修复了 new LambdaUpdateWrapper() 的使用"
echo "3. 修复了 new QueryWrapper() 的使用"
echo "4. 修复了 new UpdateWrapper() 的使用"
echo "5. 修复了 Wrappers.lambdaQuery() 的使用"
echo "6. 修复了 Wrappers.lambdaUpdate() 的使用"
echo ""
echo "注意：所有修复都基于 Mapper 接口的泛型参数自动推断实体类" 