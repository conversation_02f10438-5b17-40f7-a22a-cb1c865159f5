package com.xrkc.job.util;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.sun.jna.platform.win32.WinError;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import org.osgeo.proj4j.CRSFactory;
import org.osgeo.proj4j.CoordinateReferenceSystem;
import org.osgeo.proj4j.CoordinateTransform;
import org.osgeo.proj4j.CoordinateTransformFactory;
import org.osgeo.proj4j.Proj4jException;
import org.osgeo.proj4j.ProjCoordinate;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/CoordinateSystemUtils.class */
public class CoordinateSystemUtils {
    public static void main(String[] args) throws Proj4jException {
        BigDecimal longitude3857 = BigDecimal.valueOf(1.3093179583693873E7d);
        BigDecimal latitude3857 = BigDecimal.valueOf(4680528.996017617d);
        BigDecimal[] G4326 = Geo3857Transform4326(longitude3857, latitude3857);
        BigDecimal[] G3857 = Geo4326Transform3857(G4326[0], G4326[1]);
        BigDecimal.valueOf(117.61803337753878d);
        BigDecimal.valueOf(38.71219201031723d);
        System.out.println(JSONObject.toJSONString(G4326, new JSONWriter.Feature[0]));
        System.out.println(JSONObject.toJSONString(G3857, new JSONWriter.Feature[0]));
        System.out.println(JSONObject.toJSONString(Geo3857Transform4326(G3857[0], G3857[1]), new JSONWriter.Feature[0]));
    }
    public static BigDecimal[] Geo3857Transform4326(BigDecimal longitude, BigDecimal latitude) throws Proj4jException {
        double[] array = transform(3857, Integer.valueOf(WinError.ERROR_UNABLE_TO_INVENTORY_SLOT), longitude.doubleValue(), latitude.doubleValue());
        BigDecimal lon = new BigDecimal(array[0]).setScale(14, 4);
        BigDecimal lat = new BigDecimal(array[1]).setScale(14, 4);
        return new BigDecimal[]{lon, lat};
    }
    public static BigDecimal[] Geo4326Transform3857(BigDecimal longitude, BigDecimal latitude) throws Proj4jException {
        double[] array = transform(Integer.valueOf(WinError.ERROR_UNABLE_TO_INVENTORY_SLOT), 3857, longitude.doubleValue(), latitude.doubleValue());
        BigDecimal lon = new BigDecimal(array[0]).setScale(9, 4);
        BigDecimal lat = new BigDecimal(array[1]).setScale(9, 4);
        return new BigDecimal[]{lon, lat};
    }
    public static double[] transform(Integer sourceEPSG, Integer targetEPSG, double x, double y) throws Proj4jException {
        CoordinateTransformFactory transformFactory = new CoordinateTransformFactory();
        CRSFactory crsFactory = new CRSFactory();
        CoordinateReferenceSystem source = crsFactory.createFromName("EPSG:" + sourceEPSG);
        CoordinateReferenceSystem target = crsFactory.createFromName("EPSG:" + targetEPSG);
        CoordinateTransform transform = transformFactory.createTransform(source, target);
        ProjCoordinate minCoordinate = new ProjCoordinate(x, y);
        transform.transform(minCoordinate, minCoordinate);
        double[] box = {minCoordinate.x, minCoordinate.y};
        return box;
    }
    public static double[] translateExtent(Integer sourceEPSG, Integer targetEPSG, double xmin, double ymin) throws Proj4jException {
        CoordinateTransformFactory transformFactory = new CoordinateTransformFactory();
        CRSFactory crsFactory = new CRSFactory();
        CoordinateReferenceSystem source = crsFactory.createFromName("EPSG:" + sourceEPSG);
        CoordinateReferenceSystem target = crsFactory.createFromName("EPSG:" + targetEPSG);
        CoordinateTransform transform = transformFactory.createTransform(source, target);
        ProjCoordinate minCoordinate = new ProjCoordinate(xmin, ymin);
        transform.transform(minCoordinate, minCoordinate);
        double[] box = {minCoordinate.x, minCoordinate.y};
        return box;
    }
    public static List<Double> translateExtent(Integer sourceEPSG, Integer targetEPSG, List<BigDecimal> data) throws Proj4jException {
        if (data == null || data.size() < 2) {
            return null;
        }
        List<Double> list = new ArrayList<>();
        CoordinateTransformFactory transformFactory = new CoordinateTransformFactory();
        CRSFactory crsFactory = new CRSFactory();
        CoordinateReferenceSystem source = crsFactory.createFromName("EPSG:" + sourceEPSG);
        CoordinateReferenceSystem target = crsFactory.createFromName("EPSG:" + targetEPSG);
        CoordinateTransform transform = transformFactory.createTransform(source, target);
        if (data.size() == 2) {
            ProjCoordinate minCoordinate = new ProjCoordinate(data.get(0).doubleValue(), data.get(1).doubleValue());
            transform.transform(minCoordinate, minCoordinate);
            list.add(Double.valueOf(minCoordinate.x));
            list.add(Double.valueOf(minCoordinate.y));
            return list;
        }
        if (data.size() == 3) {
            ProjCoordinate minCoordinate2 = new ProjCoordinate(data.get(0).doubleValue(), data.get(1).doubleValue(), data.get(2).doubleValue());
            transform.transform(minCoordinate2, minCoordinate2);
            list.add(Double.valueOf(minCoordinate2.x));
            list.add(Double.valueOf(minCoordinate2.y));
            list.add(Double.valueOf(minCoordinate2.z));
            return list;
        }
        return list;
    }
    public static List<Double> translateExtentByDouble(Integer sourceEPSG, Integer targetEPSG, List<Double> data) throws Proj4jException {
        if (data == null || data.size() < 2) {
            return null;
        }
        List<Double> list = new ArrayList<>();
        CoordinateTransformFactory transformFactory = new CoordinateTransformFactory();
        CRSFactory crsFactory = new CRSFactory();
        CoordinateReferenceSystem source = crsFactory.createFromName("EPSG:" + sourceEPSG);
        CoordinateReferenceSystem target = crsFactory.createFromName("EPSG:" + targetEPSG);
        CoordinateTransform transform = transformFactory.createTransform(source, target);
        if (data.size() == 2) {
            ProjCoordinate minCoordinate = new ProjCoordinate(data.get(0).doubleValue(), data.get(1).doubleValue());
            transform.transform(minCoordinate, minCoordinate);
            list.add(Double.valueOf(minCoordinate.x));
            list.add(Double.valueOf(minCoordinate.y));
            return list;
        }
        if (data.size() == 3) {
            ProjCoordinate minCoordinate2 = new ProjCoordinate(data.get(0).doubleValue(), data.get(1).doubleValue(), data.get(2).doubleValue());
            transform.transform(minCoordinate2, minCoordinate2);
            list.add(Double.valueOf(minCoordinate2.x));
            list.add(Double.valueOf(minCoordinate2.y));
            list.add(Double.valueOf(minCoordinate2.z));
            return list;
        }
        return list;
    }
}
