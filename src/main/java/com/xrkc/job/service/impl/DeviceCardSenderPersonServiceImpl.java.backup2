package com.xrkc.job.service.impl;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.xrkc.core.domain.cardDispenser.CardDispenserPerson;
import com.xrkc.job.domain.AreaAlarmTempVO;
import com.xrkc.job.mapper.DeviceCardSenderPersonMapper;
import com.xrkc.job.service.IDeviceCardSenderPersonService;
import com.xrkc.job.service.IPersonService;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/DeviceCardSenderPersonServiceImpl.class */
public class DeviceCardSenderPersonServiceImpl implements IDeviceCardSenderPersonService {
    @Autowired
    private DeviceCardSenderPersonMapper deviceCardSenderPersonMapper;
    @Autowired
    private IPersonService personService;
    @Override // com.xrkc.job.service.IDeviceCardSenderPersonService
    public List<CardDispenserPerson> selectTimeoutList(List<String> notDeviceSnList, List<String> inDeviceSnList, int timeout) {
        return this.deviceCardSenderPersonMapper.selectTimeoutList(notDeviceSnList, inDeviceSnList, timeout);
    }
    @Override // com.xrkc.job.service.IDeviceCardSenderPersonService
    public void batchUpdate(List<CardDispenserPerson> list) {
        if (CollUtil.isEmpty((Collection<?>) list)) {
            return;
        }
        this.deviceCardSenderPersonMapper.updateBatch(list, 1000);
    }
    @Override // com.xrkc.job.service.IDeviceCardSenderPersonService
    public List<CardDispenserPerson> selectNoReturnAlarmList(Long alarmSecond, AreaAlarmTempVO areaVO) {
        List<CardDispenserPerson> list = this.deviceCardSenderPersonMapper.selectNoReturnAlarmList(alarmSecond, areaVO);
        if (CollUtil.isNotEmpty((Collection<?>) list)) {
            List<Long> postIds = areaVO.getPostIds();
            if (CollUtil.isNotEmpty((Collection<?>) postIds)) {
                List<Long> personIds = (List) list.stream().map((v0) -> {
                    return v0.getPersonId();
                }).collect(Collectors.toList());
                List<Long> effectivePostPersonId = this.personService.getEffectivePost(personIds, postIds);
                list = (List) list.stream().filter(t -> {
                    return effectivePostPersonId.contains(t.getPersonId());
                }).collect(Collectors.toList());
            }
        }
        return list;
    }
    @Override // com.xrkc.job.service.IDeviceCardSenderPersonService
    public List<CardDispenserPerson> timeWithinSuccessCardPerson(Long minutes) {
        CardDispenserPerson cardDispenserPerson = new CardDispenserPerson();
        cardDispenserPerson.setCreateTime(LocalDateTime.now().minusMinutes(minutes.longValue()));
        cardDispenserPerson.setCardSenderType(1);
        cardDispenserPerson.setResult("成功");
        return this.deviceCardSenderPersonMapper.timeWithinSuccessCardPerson(cardDispenserPerson);
    }
    @Override // com.xrkc.job.service.IDeviceCardSenderPersonService
    public void updateOfflineStatus(List<Long> successPersonIds) {
        CardDispenserPerson cardDispenserPerson = new CardDispenserPerson();
        cardDispenserPerson.setOnline("0");
        cardDispenserPerson.setCardSenderType(1);
        cardDispenserPerson.setResult("成功");
        this.deviceCardSenderPersonMapper.updateOnlineStatus(successPersonIds, cardDispenserPerson);
    }
    @Override // com.xrkc.job.service.IDeviceCardSenderPersonService
    public void updateOnlineStatus(List<Long> personIds) {
        CardDispenserPerson cardDispenserPerson = new CardDispenserPerson();
        cardDispenserPerson.setOnline("1");
        cardDispenserPerson.setCardSenderType(1);
        cardDispenserPerson.setResult("成功");
        this.deviceCardSenderPersonMapper.updateOnlineStatus(personIds, cardDispenserPerson);
    }
    @Override // com.xrkc.job.service.IDeviceCardSenderPersonService
    public LambdaQueryChainWrapper<CardDispenserPerson> lambdaSelect() {
        return ChainWrappers.lambdaQueryChain((BaseMapper) this.deviceCardSenderPersonMapper, CardDispenserPerson.class);
    }
    @Override // com.xrkc.job.service.IDeviceCardSenderPersonService
    public LambdaUpdateChainWrapper<CardDispenserPerson> lambdaUpdate() {
        return ChainWrappers.lambdaUpdateChain(this.deviceCardSenderPersonMapper);
    }
}
