package com.xrkc.job.module.vehicle.service;
import com.xrkc.core.domain.vehicle.VehicleInfo;
import java.util.List;
import java.util.Map;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/module/vehicle/service/IVehicleInfoService.class */
public interface IVehicleInfoService {
    Map<Long, VehicleInfo> findCardIdEntityMap();
    List<VehicleInfo> selectListByIdList(List<Long> list);
    List<VehicleInfo> selectLowBatteryList();
    List<Long> findCardIdList();
    List<VehicleInfo> selectList();
    Long count();
    List<VehicleInfo> selectToFaceVehicleList();
}
