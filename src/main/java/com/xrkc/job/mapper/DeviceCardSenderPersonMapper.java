package com.xrkc.job.mapper;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xrkc.core.domain.cardDispenser.CardDispenserPerson;
import com.xrkc.core.mybatisplus.BaseMapperX;
import com.xrkc.core.mybatisplus.LambdaQueryWrapperX;
import com.xrkc.job.domain.AreaAlarmTempVO;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/DeviceCardSenderPersonMapper.class */
public interface DeviceCardSenderPersonMapper extends BaseMapperX<CardDispenserPerson> {
    default LambdaQueryWrapperX<CardDispenserPerson> privateWrapper(List<String> notDeviceSnList, List<String> inDeviceSnList, int timeout) {
        return new LambdaQueryWrapperX<CardDispenserPerson>().eqIfPresent((v0) -> {
            return v0.getCardSenderType();
        }, 3).eqIfPresent((v0) -> {
            return v0.getResult();
        }, "成功").ltIfPresent((v0) -> {
            return v0.getCommandTime();
        }, LocalDateTime.now().minusSeconds(timeout)).notInIfPresent((v0) -> {
            return v0.getDeviceSn();
        }, notDeviceSnList).inIfPresent((v0) -> {
            return v0.getDeviceSn();
        }, inDeviceSnList).orderByDesc((v0) -> {
            return v0.getId();
        });
    }
    default List<CardDispenserPerson> selectTimeoutList(List<String> notDeviceSnList, List<String> inDeviceSnList, int timeout) {
        return selectList(privateWrapper(notDeviceSnList, inDeviceSnList, timeout));
    }
    default LambdaQueryWrapperX<CardDispenserPerson> privateAlarmWrapper(Long alarmSecond, AreaAlarmTempVO areaVO) {
        return new LambdaQueryWrapperX<CardDispenserPerson>().eqIfPresent((v0) -> {
            return v0.getCardSenderType();
        }, 1).eqIfPresent((v0) -> {
            return v0.getResult();
        }, "成功").ltIfPresent((v0) -> {
            return v0.getCreateTime();
        }, LocalDateTime.now().minusSeconds(alarmSecond.longValue())).notInIfPresent((v0) -> {
            return v0.getPersonId();
        }, areaVO.getPersonIds()).inIfPresent((v0) -> {
            return v0.getPersonId();
        }, areaVO.getAlarmPersonIds()).inIfPresent((v0) -> {
            return v0.getDeptId();
        }, areaVO.getDeptIds()).orderByDesc((v0) -> {
            return v0.getId();
        });
    }
    default List<CardDispenserPerson> selectNoReturnAlarmList(Long alarmSecond, AreaAlarmTempVO areaVO) {
        return selectList(privateAlarmWrapper(alarmSecond, areaVO));
    }
    default List<CardDispenserPerson> timeWithinSuccessCardPerson(CardDispenserPerson cardDispenserPerson) {
        return selectList(timeWithinSuccessCardPersonWrapper(cardDispenserPerson));
    }
    default LambdaQueryWrapperX<CardDispenserPerson> timeWithinSuccessCardPersonWrapper(CardDispenserPerson cardDispenserPerson) {
        return new LambdaQueryWrapperX<CardDispenserPerson>().eqIfPresent((v0) -> {
            return v0.getCardSenderType();
        }, cardDispenserPerson.getCardSenderType()).eqIfPresent((v0) -> {
            return v0.getResult();
        }, cardDispenserPerson.getResult()).ltIfPresent((v0) -> {
            return v0.getCreateTime();
        }, cardDispenserPerson.getCreateTime());
    }
    default void updateOnlineStatus(List<Long> successPersonIds, CardDispenserPerson cardDispenserPerson) {
        update(updateOnlineStatusWrapper(successPersonIds, cardDispenserPerson));
    }
    /* JADX WARN: Multi-variable type inference failed */
    default LambdaUpdateWrapper<CardDispenserPerson> updateOnlineStatusWrapper(List<Long> successPersonIds, CardDispenserPerson cardDispenserPerson) {
        LambdaUpdateWrapper<CardDispenserPerson> updateWrapperX = (LambdaUpdateWrapper) ((LambdaUpdateWrapper) new LambdaUpdateWrapper<CardDispenserPerson>().set((v0) -> {
            return v0.getOnline();
        }, cardDispenserPerson.getOnline()).eq((v0) -> {
            return v0.getCardSenderType();
        }, cardDispenserPerson.getCardSenderType())).eq((v0) -> {
            return v0.getResult();
        }, cardDispenserPerson.getResult());
        if (CollUtil.isNotEmpty((Collection<?>) successPersonIds)) {
            updateWrapperX.in((LambdaUpdateWrapper<CardDispenserPerson>) (v0) -> {
                return v0.getPersonId();
            }, successPersonIds);
        }
        return updateWrapperX;
    }
}
