package com.xrkc.job.task;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.job.service.IDeviceBeaconService;
import com.xrkc.job.service.IDeviceCardService;
import com.xrkc.job.service.ISystemConfigService;
import com.xrkc.redis.utils.RedisCacheUtils;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
@Component("deviceTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/DeviceTask.class */
public class DeviceTask {
    @Autowired
    private IDeviceCardService deviceCardService;
    @Autowired
    private IDeviceBeaconService deviceBeaconService;
    @Autowired
    private ISystemConfigService systemConfigService;
    private static final Logger log = LoggerFactory.getLogger((Class<?>) DeviceTask.class);
    private static final DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public void resetDeviceHeart() {
        LocalDate today = LocalDate.now();
        String reset_card_heart = RedisCacheUtils.getConfigRedisCache().get(ConfigValue.CONFIG_KEY_RESET_CARD_HEART);
        if (StringUtils.isNotBlank(reset_card_heart) && Integer.parseInt(reset_card_heart) > 0) {
            String remark = this.systemConfigService.selectRemarkByKey(ConfigValue.CONFIG_KEY_RESET_CARD_HEART);
            if (StringUtils.isNotBlank(remark)) {
                LocalDate lastDay = LocalDate.parse(remark, df);
                long until = today.until(lastDay, ChronoUnit.DAYS);
                if (until > Long.valueOf(reset_card_heart).longValue()) {
                    resetCardHeart(today.format(df));
                }
            } else {
                resetCardHeart(today.format(df));
            }
        }
        String reset_beacon_heart = RedisCacheUtils.getConfigRedisCache().get(ConfigValue.CONFIG_KEY_RESET_BEACON_HEART);
        if (StringUtils.isNotBlank(reset_beacon_heart) && Integer.parseInt(reset_beacon_heart) > 0) {
            String remark2 = this.systemConfigService.selectRemarkByKey(ConfigValue.CONFIG_KEY_RESET_BEACON_HEART);
            if (StringUtils.isNotBlank(remark2)) {
                LocalDate lastDay2 = LocalDate.parse(remark2, df);
                long until2 = today.until(lastDay2, ChronoUnit.DAYS);
                if (until2 > Long.valueOf(reset_beacon_heart).longValue()) {
                    resetBeaconHeart(today.format(df));
                    return;
                }
                return;
            }
            resetBeaconHeart(today.format(df));
        }
    }
    private void resetCardHeart(String today) {
        this.deviceCardService.resetCardHeart();
        this.systemConfigService.updateRemarkByKey(ConfigValue.CONFIG_KEY_RESET_CARD_HEART, today);
    }
    private void resetBeaconHeart(String today) {
        this.deviceBeaconService.resetBeaconHeart();
        this.systemConfigService.updateRemarkByKey(ConfigValue.CONFIG_KEY_RESET_BEACON_HEART, today);
    }
}
