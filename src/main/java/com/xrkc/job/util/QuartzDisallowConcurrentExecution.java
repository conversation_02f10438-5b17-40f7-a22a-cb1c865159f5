package com.xrkc.job.util;
import com.xrkc.job.domain.SystemJob;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
@DisallowConcurrentExecution
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/QuartzDisallowConcurrentExecution.class */
public class QuartzDisallowConcurrentExecution extends AbstractQuartzJob {
    @Override // com.xrkc.job.util.AbstractQuartzJob
    protected void doExecute(JobExecutionContext context, SystemJob sysJob) throws Exception {
        JobInvokeUtil.invokeMethod(sysJob);
    }
}
