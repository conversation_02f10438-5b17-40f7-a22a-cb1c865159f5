package com.xrkc.job.service.impl;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.domain.person.Person;
import com.xrkc.core.domain.statistics.StatisticsDeptOnlineLog;
import com.xrkc.job.domain.PositionCurrent;
import com.xrkc.job.mapper.PersonMapper;
import com.xrkc.job.mapper.SysConfigMapper;
import com.xrkc.job.service.IDeviceCardService;
import com.xrkc.job.service.IPersonService;
import com.xrkc.redis.utils.RedisCacheUtils;
import java.beans.Transient;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/PersonServiceImpl.class */
public class PersonServiceImpl implements IPersonService {
    @Autowired
    private PersonMapper personMapper;
    @Autowired
    private PositionCurrentServiceImpl positionCurrentService;
    @Autowired
    private IDeviceCardService deviceCardService;
    @Autowired
    private SysConfigMapper sysConfigMapper;
    @Override // com.xrkc.job.service.IPersonService
    public List<Person> selectList() {
        return this.personMapper.selectList();
    }
    @Override // com.xrkc.job.service.IPersonService
    public Long insertEntity(Person person) {
        person.setCreateTime(LocalDateTime.now());
        person.setCreateBy("task");
        this.personMapper.insert( person);
        return person.getPersonId();
    }
    @Override // com.xrkc.job.service.IPersonService
    public int updateEntity(Person person) {
        return this.personMapper.updatePerson(person);
    }
    @Override // com.xrkc.job.service.IPersonService
    public List<StatisticsDeptOnlineLog> findDeptPersonTotal() {
        return this.personMapper.findDeptPersonTotal();
    }
    @Override // com.xrkc.job.service.IPersonService
    public Person getPersonById(Long personId) {
        return this.personMapper.getPersonById(personId);
    }
    @Override // com.xrkc.job.service.IPersonService
    public List<Person> getPersonsByIds(List<Long> personIds) {
        return this.personMapper.getPersonByIds(personIds);
    }
    @Override // com.xrkc.job.service.IPersonService
    public void unbindingCard(Long personId, String personType, Long cardId) {
        if ("visitor".equals(personType)) {
            this.personMapper.unBindingCard(personId, cardId, "N");
        }
        this.personMapper.unbindingCard(personId);
        this.deviceCardService.updateUseStatus(cardId, "N");
        this.positionCurrentService.offlineByCard(cardId, null);
    }
    @Override // com.xrkc.job.service.IPersonService
    public List<Person> selectPersonList(Map<String, Object> map) {
        return this.personMapper.selectPersonList(map);
    }
    @Override // com.xrkc.job.service.IPersonService
    @Transient
    public void updateHikvisionPerson(List<Person> list) {
        list.stream().forEach(s -> {
            this.personMapper.updateHikvisionPerson(s);
        });
    }
    @Override // com.xrkc.job.service.IPersonService
    public void deleteByPersonId(Long personId) {
        this.personMapper.deleteByPersonId(personId);
    }
    @Override // com.xrkc.job.service.IPersonService
    public int countOnJob() {
        return this.personMapper.countOnJob();
    }
    @Override // com.xrkc.job.service.IPersonService
    public PositionCurrent findPersonByDeptId(List<Long> deptIds, List<Long> excludePersonIds, List<Long> alarmPersonIds, List<Long> postIds) {
        boolean parameterCondition = true;
        if (CollUtil.isEmpty((Collection<?>) deptIds) && CollUtil.isEmpty((Collection<?>) alarmPersonIds) && CollUtil.isEmpty((Collection<?>) postIds)) {
            parameterCondition = false;
        }
        Integer intervalSecond = this.sysConfigMapper.getByIntervalSecond();
        LocalDateTime minusSeconds = LocalDateTime.now().minusSeconds(intervalSecond.intValue());
        Map<String, String> config = RedisCacheUtils.getConfigRedisCache();
        String configurationRelation = config.getOrDefault(ConfigValue.CONFIGURATION_RELATION, "1");
        Person person = this.personMapper.findPersonByDeptId(deptIds, excludePersonIds, alarmPersonIds, postIds, Boolean.valueOf(parameterCondition), minusSeconds, configurationRelation);
        return (PositionCurrent) BeanUtil.copyProperties((Object) person, PositionCurrent.class, new String[0]);
    }
    @Override // com.xrkc.job.service.IPersonService
    public List<Long> getEffectivePost(List<Long> personIds, List<Long> postIds) {
        return this.personMapper.getEffectivePost(personIds, postIds);
    }
    @Override // com.xrkc.job.service.IPersonService
    public Map<Long, Person> findPersonMap() {
        return (Map) this.personMapper.selectPersonListFilter().stream().collect(Collectors.toMap((v0) -> {
            return v0.getPersonId();
        }, Function.identity()));
    }
    @Override // com.xrkc.job.service.IPersonService
    public Map<Long, Person> findNoticeMap() {
        return (Map) this.personMapper.selectNoticeList().stream().collect(Collectors.toMap((v0) -> {
            return v0.getPersonId();
        }, Function.identity()));
    }
}
