package com.xrkc.job.service;
import com.xrkc.job.domain.InspectRecord;
import com.xrkc.job.domain.InspectRecordItem;
import com.xrkc.job.domain.InspectRecordItemParam;
import com.xrkc.job.domain.InspectRecordLocation;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IInspectRecordService.class */
public interface IInspectRecordService {
    List<InspectRecord> selectEverydayInspectTaskList(String str);
    List<InspectRecord> selectNotEverydayInspectTaskList(String str);
    int existInspectTask(InspectRecord inspectRecord);
    int insertInspectRecord(InspectRecord inspectRecord);
    List<InspectRecordLocation> selectInspectLocationList(List<Long> list);
    int batchInsertLocation(List<InspectRecordLocation> list);
    List<InspectRecordItem> selectInspectItemList(List<Long> list);
    int batchInsertItem(List<InspectRecordItem> list);
    int batchInsertItemParam(List<InspectRecordItemParam> list);
    List<InspectRecord> selectInspectRecord();
    int updateInspectRecord(InspectRecord inspectRecord);
    List<InspectRecord> selectYesterdayRecord(LocalDate localDate, LocalDateTime localDateTime);
    List<InspectRecordItem> selectInspectItem(Long l);
    List<InspectRecordItemParam> selectInspectItemParam(Long l);
}
