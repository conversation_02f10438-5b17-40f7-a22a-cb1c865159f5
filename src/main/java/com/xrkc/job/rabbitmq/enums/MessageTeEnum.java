package com.xrkc.job.rabbitmq.enums;
import com.alibaba.nacos.client.config.common.ConfigConstants;
import com.xrkc.core.constant.Constants;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/rabbitmq/enums/MessageTeEnum.class */
public enum MessageTeEnum {
    INSPECT_ALARM(Constants.ALARM_TYPE_95, "尊敬的@NAME您好，人员报警中@SOS_WARN发生报警，请及时处理"),
    INSPECT_VEHICLE_ALARM("20", "尊敬的@NAME您好，围栏产生@WARN，请及时处理");
    private String title;
    private String content;
    public static Map<String, MessageTeEnum> catchs = new HashMap();
    public static List<Map<String, Object>> list = new ArrayList(values().length);
    MessageTeEnum(String title, String content) {
        this.title = title;
        this.content = content;
    }
    static {
        MessageTeEnum[] values = values();
        for (MessageTeEnum v : values) {
            catchs.put(v.title, v);
            Map<String, Object> map = new HashMap<>(2);
            map.put("title", v.title);
            map.put(ConfigConstants.CONTENT, v.content);
            list.add(map);
        }
    }
    public String getTitle() {
        return this.title;
    }
    public String getContent() {
        return this.content;
    }
}
