package com.xrkc.job.mapper;
import com.xrkc.core.mybatisplus.BaseMapperX;
import com.xrkc.job.domain.StatisticsPersonFlow;
import java.time.LocalDate;
import java.util.List;
import org.apache.ibatis.annotations.Select;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/StatisticsPersonFlowMapper.class */
public interface StatisticsPersonFlowMapper extends BaseMapperX<StatisticsPersonFlow> {
    @Select({"select person_type from statistics_person_flow where statistics_date = #{statisticsDate};"})
    List<StatisticsPersonFlow> selectByStatisticsDate(LocalDate localDate);
    int updateByParams(StatisticsPersonFlow statisticsPersonFlow);
}
