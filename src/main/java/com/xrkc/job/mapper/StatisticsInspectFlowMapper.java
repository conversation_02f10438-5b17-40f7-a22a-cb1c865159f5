package com.xrkc.job.mapper;
import com.xrkc.core.mybatisplus.BaseMapperX;
import com.xrkc.job.domain.StatisticsInspectFlow;
import java.time.LocalDate;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/StatisticsInspectFlowMapper.class */
public interface StatisticsInspectFlowMapper extends BaseMapperX<StatisticsInspectFlow> {
    @Select({"select inspect_type,statistics_date from statistics_inspect_flow where statistics_date=#{statisticsDate};"})
    List<StatisticsInspectFlow> selectByStatisticsDate(LocalDate localDate);
}
