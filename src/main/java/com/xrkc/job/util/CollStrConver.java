package com.xrkc.job.util;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/CollStrConver.class */
public class CollStrConver {
    public static String coll2String(Collection<?> collection) {
        if (null == collection || collection.isEmpty()) {
            return "";
        }
        Iterator<?> it = collection.iterator();
        StringBuilder sb = new StringBuilder();
        while (true) {
            sb.append(it.next());
            if (!it.hasNext()) {
                return sb.toString();
            }
            sb.append((char) 12289);
        }
    }
    public static List<Long> string2coll(String Str) {
        if (!StringUtils.hasText(Str)) {
            return Collections.emptyList();
        }
        String[] rects = Str.split("、");
        List<Long> list = (List) Arrays.stream(rects).filter((v0) -> {
            return org.apache.commons.lang3.StringUtils.isNotBlank(v0);
        }).map(p -> {
            return Long.valueOf(p);
        }).collect(Collectors.toList());
        return list;
    }
    public static String dictTypeName(Collection<?> collection, Map<String, String> aiTypeMap) {
        if (null == collection || collection.isEmpty()) {
            return "";
        }
        Iterator<?> it = collection.iterator();
        StringBuilder sb = new StringBuilder();
        while (true) {
            String var = aiTypeMap.get(it.next().toString().trim());
            if (!StringUtils.isEmpty(var)) {
                sb.append(var);
            }
            if (!it.hasNext()) {
                return sb.toString();
            }
            sb.append(',');
        }
    }
    public static String dictTypeByIdGetName(Collection<?> collection, Map<Long, String> aiTypeMap) {
        if (null == collection || collection.isEmpty()) {
            return "";
        }
        Iterator<?> it = collection.iterator();
        StringBuilder sb = new StringBuilder();
        while (true) {
            String var = aiTypeMap.get(it.next());
            if (!StringUtils.isEmpty(var)) {
                sb.append(var);
            }
            if (!it.hasNext()) {
                return sb.toString();
            }
            sb.append(',');
        }
    }
    public static String dictTypeNameId(Collection<?> collection, Map<String, Long> aiTypeMap) {
        if (null == collection || collection.isEmpty()) {
            return "";
        }
        Iterator<?> it = collection.iterator();
        StringBuilder sb = new StringBuilder();
        while (true) {
            Long var = aiTypeMap.get(it.next().toString().trim());
            if (!Objects.isNull(var)) {
                sb.append(var);
            }
            if (!it.hasNext()) {
                return sb.toString();
            }
            sb.append(',');
        }
    }
    public static Integer[] str2intArr(String eventRects) {
        if (!StringUtils.hasText(eventRects)) {
            return new Integer[0];
        }
        String[] rects = eventRects.split(",");
        List<Integer> list = (List) Arrays.stream(rects).filter((v0) -> {
            return org.apache.commons.lang3.StringUtils.isNotBlank(v0);
        }).map(p -> {
            return Integer.valueOf(p);
        }).collect(Collectors.toList());
        return (Integer[]) list.toArray(new Integer[0]);
    }
    public static String[] str2Arr(String eventRects, String split) {
        if (!StringUtils.hasText(eventRects)) {
            return new String[0];
        }
        String[] rects = eventRects.split(split);
        List<String> list = (List) Arrays.stream(rects).filter((v0) -> {
            return org.apache.commons.lang3.StringUtils.isNotBlank(v0);
        }).collect(Collectors.toList());
        return (String[]) list.toArray(new String[0]);
    }
    public static List<Integer> str2intList(String eventRects) {
        if (!StringUtils.hasText(eventRects)) {
            return Arrays.asList(new Integer[0]);
        }
        String[] rects = eventRects.split(",");
        List<Integer> list = (List) Arrays.stream(rects).filter((v0) -> {
            return org.apache.commons.lang3.StringUtils.isNotBlank(v0);
        }).map(p -> {
            return Integer.valueOf(p);
        }).collect(Collectors.toList());
        return list;
    }
    public static List<Long> str2longList(String eventRects) {
        if (!StringUtils.hasText(eventRects)) {
            return Collections.emptyList();
        }
        String[] rects = eventRects.split(",");
        List<Long> list = (List) Arrays.stream(rects).filter((v0) -> {
            return org.apache.commons.lang3.StringUtils.isNotBlank(v0);
        }).map(p -> {
            return Long.valueOf(p);
        }).collect(Collectors.toList());
        return list;
    }
}
