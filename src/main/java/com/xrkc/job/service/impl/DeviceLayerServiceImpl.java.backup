package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.job.domain.DeviceLayer;
import com.xrkc.job.mapper.DeviceLayerMapper;
import com.xrkc.job.service.IDeviceLayerService;
import java.lang.invoke.SerializedLambda;
import java.math.BigDecimal;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/DeviceLayerServiceImpl.class */
public class DeviceLayerServiceImpl extends ServiceImpl<DeviceLayerMapper, DeviceLayer> implements IDeviceLayerService {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) DeviceLayerServiceImpl.class);
    @Autowired
    private DeviceLayerMapper deviceLayerMapper;
    @Override // com.xrkc.job.service.IDeviceLayerService
    public DeviceLayer findOneLayer() {
        return this.deviceLayerMapper.findOneLayer();
    }
    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.xrkc.job.service.IDeviceLayerService
    public DeviceLayer queryLayerExist(String layerId) {
        LambdaQueryWrapper<DeviceLayer> wrapper = (LambdaQueryWrapper) Wrappers.lambdaQuery(DeviceLayer.class).eq((v0) -> {
            return v0.getLayerId();
        }, layerId);
        return this.deviceLayerMapper.selectOne(wrapper);
    }
    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.xrkc.job.service.IDeviceLayerService
    public void deleteByLayerId(String layerId) {
        LambdaQueryWrapper<DeviceLayer> wrapper = (LambdaQueryWrapper) Wrappers.lambdaQuery(DeviceLayer.class).eq((v0) -> {
            return v0.getLayerId();
        }, layerId);
        remove(wrapper);
    }
    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.xrkc.job.service.IDeviceLayerService
    public boolean queryLayerOneExist(String layerId) {
        LambdaQueryWrapper<DeviceLayer> wrapper = (LambdaQueryWrapper) Wrappers.lambdaQuery(DeviceLayer.class).eq((v0) -> {
            return v0.getLayerId();
        }, layerId);
        return this.deviceLayerMapper.selectCount(wrapper).longValue() > 0;
    }
    @Override // com.xrkc.job.service.IDeviceLayerService
    public void updateByLayerId(DeviceLayer layer) {
        LambdaUpdateWrapper<DeviceLayer> updateWrapper = Wrappers.lambdaUpdate(DeviceLayer.class);
        updateWrapper.eq((v0) -> {
            return v0.getLayerId();
        }, layer.getLayerId());
        this.deviceLayerMapper.update(layer, updateWrapper);
    }
    @Override // com.xrkc.job.service.IDeviceLayerService
    public Boolean inOneLayer(BigDecimal longitude, BigDecimal latitude) {
        if (Objects.isNull(longitude) || Objects.isNull(latitude)) {
            log.error("判断是否在厂区的经纬度为空");
            return false;
        }
        DeviceLayer deviceLayer = findOneLayer();
        if (Objects.isNull(deviceLayer)) {
            log.error("系统未配置全景楼层");
            return false;
        }
        if (Objects.isNull(deviceLayer.getMaxLongitude()) || Objects.isNull(deviceLayer.getMinLongitude()) || Objects.isNull(deviceLayer.getMaxLatitude()) || Objects.isNull(deviceLayer.getMinLatitude())) {
            log.error("厂区最大最小经纬度坐标未完整配置");
            return false;
        }
        if (longitude.compareTo(deviceLayer.getMinLongitude()) > -1 && longitude.compareTo(deviceLayer.getMaxLongitude()) < 1 && latitude.compareTo(deviceLayer.getMinLatitude()) > -1 && latitude.compareTo(deviceLayer.getMaxLatitude()) < 1) {
            return true;
        }
        log.error("经纬度{},{}不在厂区{},{};{},{}", longitude, latitude, deviceLayer.getMinLongitude(), deviceLayer.getMaxLongitude(), deviceLayer.getMinLatitude(), deviceLayer.getMaxLatitude());
        return false;
    }
}
