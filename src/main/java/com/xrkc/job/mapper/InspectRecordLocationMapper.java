package com.xrkc.job.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xrkc.job.domain.InspectRecordLocation;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.postgresql.jdbc.EscapedFunctions;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/InspectRecordLocationMapper.class */
public interface InspectRecordLocationMapper extends BaseMapper<InspectRecordLocation> {
    @Select({"SELECT\n*\nFROM inspect_record_location\nWHERE create_time > #{now}\nand create_time < #{nowTime}"})
    List<InspectRecordLocation> selectLocationList(@Param(EscapedFunctions.NOW_FUNC) LocalDate localDate, @Param("nowTime") LocalDateTime localDateTime);
}
