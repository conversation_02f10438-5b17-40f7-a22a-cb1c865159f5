package com.xrkc.job.service;
import com.xrkc.core.domain.person.Person;
import com.xrkc.core.domain.statistics.StatisticsDeptOnlineLog;
import com.xrkc.job.domain.PositionCurrent;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IPersonService.class */
public interface IPersonService {
    List<Person> selectList();
    Long insertEntity(Person person);
    int updateEntity(Person person);
    List<StatisticsDeptOnlineLog> findDeptPersonTotal();
    Person getPersonById(@Param("personId") Long l);
    List<Person> getPersonsByIds(List<Long> list);
    void unbindingCard(Long l, String str, Long l2);
    List<Person> selectPersonList(Map<String, Object> map);
    void updateHikvisionPerson(List<Person> list);
    void deleteByPersonId(@Param("personId") Long l);
    int countOnJob();
    PositionCurrent findPersonByDeptId(List<Long> list, List<Long> list2, List<Long> list3, List<Long> list4);
    List<Long> getEffectivePost(List<Long> list, List<Long> list2);
    Map<Long, Person> findNoticeMap();
    Map<Long, Person> findPersonMap();
}
