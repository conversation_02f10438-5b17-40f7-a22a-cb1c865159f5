package com.xrkc.job.domain;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.time.LocalDateTime;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/VehicleAlarmVO.class */
public class VehicleAlarmVO implements Serializable {
    private static final long serialVersionUID = 1;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime alarmTime;
    private String alarmId;
    private String alarmTypeName;
    private Long carId;
    private String carNo;
    public LocalDateTime getAlarmTime() {
        return this.alarmTime;
    }
    public void setAlarmTime(LocalDateTime alarmTime) {
        this.alarmTime = alarmTime;
    }
    public String getAlarmId() {
        return this.alarmId;
    }
    public void setAlarmId(String alarmId) {
        this.alarmId = alarmId;
    }
    public String getAlarmTypeName() {
        return this.alarmTypeName;
    }
    public void setAlarmTypeName(String alarmTypeName) {
        this.alarmTypeName = alarmTypeName;
    }
    public Long getCarId() {
        return this.carId;
    }
    public void setCarId(Long carId) {
        this.carId = carId;
    }
    public String getCarNo() {
        return this.carNo;
    }
    public void setCarNo(String carNo) {
        this.carNo = carNo;
    }
}
