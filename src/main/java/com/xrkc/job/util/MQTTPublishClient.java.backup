package com.xrkc.job.util;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mysql.cj.CharsetMapping;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.domain.system.SystemApi;
import com.xrkc.job.domain.DeviceBeacon;
import com.xrkc.job.domain.DeviceCard;
import com.xrkc.job.domain.DeviceLayer;
import com.xrkc.job.service.IDeviceBeaconService;
import com.xrkc.job.service.IDeviceCardService;
import com.xrkc.job.service.IDeviceLayerService;
import com.xrkc.job.service.SystemApiService;
import com.xrkc.redis.utils.RedisCacheUtils;
import java.io.UnsupportedEncodingException;
import java.lang.invoke.SerializedLambda;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.MqttTopic;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/MQTTPublishClient.class */
public class MQTTPublishClient {
    @Autowired
    private MQTTPublishClient mqttClient;
    @Autowired
    private IDeviceLayerService layerService;
    @Autowired
    private IDeviceBeaconService beaconService;
    @Autowired
    private IDeviceCardService deviceCardService;
    private static final Integer NORMAL_BEACON_STATUS = 1;
    private MqttClient client;
    @Autowired
    private SystemApiService systemApiService;
    private Logger log = LoggerFactory.getLogger(getClass());
    private final String defaultCharset = CharsetMapping.MYSQL_CHARSET_NAME_utf8;
    private String baseTopic = "";
    private final Lock mqttClientConnectLock = new ReentrantLock();
    private MqttClient createClient() {
        if (this.client == null || !this.client.isConnected()) {
            SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_MQTT);
            if (Objects.isNull(systemApi)) {
                return null;
            }
            this.baseTopic = systemApi.getBasicTopic();
            String clientId = this.baseTopic.replaceAll("/", "_") + "_" + MqttClient.generateClientId();
            String serverURI = systemApi.getHost();
            try {
                this.client = new MqttClient(serverURI, clientId, new MemoryPersistence());
                MqttConnectOptions options = new MqttConnectOptions();
                options.setCleanSession(true);
                options.setConnectionTimeout(5);
                options.setKeepAliveInterval(15);
                options.setAutomaticReconnect(true);
                options.setUserName(systemApi.getUserName());
                if (org.apache.commons.lang3.StringUtils.isNotBlank(systemApi.getPassword())) {
                    options.setPassword(systemApi.getPassword().toCharArray());
                }
                this.log.info("创建mqtt连接中：{},{},{}", serverURI, systemApi.getUserName(), systemApi.getPassword());
                this.client.connect(options);
                if (this.client.isConnected()) {
                    this.log.info("mqtt连接成功：{}", serverURI);
                }
                uploadData();
            } catch (Exception e) {
                this.log.info("mqtt连接失败:{}", e.getMessage());
                this.log.error(e.getMessage(), (Throwable) e);
                if (this.client != null) {
                    try {
                        this.client.close();
                    } catch (MqttException e2) {
                        this.log.error(e.getMessage(), (Throwable) e);
                    }
                }
            }
        }
        return this.client;
    }
    public MqttClient getClient() {
        if (this.client == null || !this.client.isConnected()) {
            if (this.mqttClientConnectLock.tryLock()) {
                try {
                    if (this.client != null) {
                        this.client.close();
                    }
                    this.client = createClient();
                } catch (MqttException e) {
                    this.log.error(e.getMessage(), (Throwable) e);
                    this.client = null;
                } finally {
                    this.mqttClientConnectLock.unlock();
                }
            } else {
                return this.client;
            }
        }
        return this.client;
    }
    public Boolean publish(MqttClient mqttClient, String topic, String msg) throws IllegalStateException {
        if (mqttClient == null) {
            mqttClient = getClient();
        }
        if (mqttClient == null) {
            return false;
        }
        try {
            if (!mqttClient.isConnected()) {
                this.client = null;
                mqttClient.close(true);
                mqttClient = getClient();
            }
            String[] baseTopicArr = this.baseTopic.split(",");
            for (String bTopic : baseTopicArr) {
                topic = bTopic + topic;
                MqttTopic mqttTopic = mqttClient.getTopic(topic);
                MqttMessage message = new MqttMessage();
                message.setQos(2);
                try {
                    message.setPayload(msg.getBytes(CharsetMapping.MYSQL_CHARSET_NAME_utf8));
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                MqttDeliveryToken token = mqttTopic.publish(message);
                token.waitForCompletion(2000L);
                this.log.info("推送完成:{},messageId:{},推送的topic为:{},推送的数据为:{}", Boolean.valueOf(token.isComplete()), Integer.valueOf(token.getMessageId()), topic, msg);
            }
            return true;
        } catch (MqttException e2) {
            this.log.error("mqtt推送失败，请检查配置信息，err:{}", e2.getMessage());
            return false;
        }
    }
    public void uploadData() {
        try {
            subscribeLayer();
        } catch (Exception e) {
            this.log.error(e.getMessage(), (Throwable) e);
            this.log.error("subscribeLayer错误", (Throwable) e);
        }
        try {
            subscribeCard();
        } catch (Exception e2) {
            this.log.error(e2.getMessage(), (Throwable) e2);
            this.log.error("subscribeCard错误", (Throwable) e2);
        }
        try {
            subscribeBeacon();
        } catch (Exception e3) {
            this.log.error(e3.getMessage(), (Throwable) e3);
            this.log.error("subscribeBeacon错误", (Throwable) e3);
        }
    }
    private void subscribeCard() throws MqttException, IllegalArgumentException {
        if (this.client != null && this.client.isConnected()) {
            String topic = this.baseTopic + "/card";
            this.client.subscribe(topic, 2, (topic1, message) -> {
                String msg = new String(message.getPayload(), CharsetMapping.MYSQL_CHARSET_NAME_utf8);
                this.log.info(" topic:{} 接受到的消息为：{}", topic, message);
                StringBuilder error = new StringBuilder();
                JSONObject json = JSONObject.parseObject(msg);
                String uniqueId = json.getLong("cardId") + "";
                try {
                    try {
                        DeviceCard card = new DeviceCard();
                        card.setCardId(json.getLong("cardId"));
                        card.setIcCardId(json.getString("icCardId"));
                        card.setCreateTime(LocalDateTime.now());
                        Integer operateType = json.getInteger("operateType");
                        if (Objects.isNull(operateType) || (!operateType.equals(1) && !operateType.equals(2) && !operateType.equals(3))) {
                            error.append("操作类型未提供或提供的值不合法").append(";");
                        }
                        if (operateType.equals(1)) {
                            if (Objects.isNull(card.getCardId())) {
                                error.append("定位卡卡号未提供").append(";");
                            }
                        } else if (Objects.isNull(card.getCardId())) {
                            error.append("定位卡CardId未提供").append(";");
                        }
                        if (error.length() == 0) {
                            if (operateType.equals(1)) {
                                card.setCardType("card");
                                LambdaQueryWrapper<DeviceCard> wrapper = (LambdaQueryWrapper) Wrappers.<DeviceCard>lambdaQuery().eq((v0) -> {
                                    return v0.getCardId();
                                }, card.getCardId());
                                long count = this.deviceCardService.count(wrapper);
                                if (count > 0) {
                                    error.append("当前定位卡标识已存在，新增失败cardId: " + card.getCardId()).append(";");
                                    if (error.length() > 0) {
                                        publishErrorMsg(uniqueId, error.toString());
                                        return;
                                    }
                                    return;
                                }
                                this.deviceCardService.save(card);
                            } else if (operateType.equals(2)) {
                                LambdaUpdateWrapper<DeviceCard> updateWrapper = (LambdaUpdateWrapper) Wrappers.<DeviceCard> lambdaUpdate().eq((v0) -> {
                                    return v0.getCardId();
                                }, card.getCardId());
                                this.deviceCardService.update(card, updateWrapper);
                            } else {
                                LambdaQueryWrapper<DeviceCard> wrapper2 = (LambdaQueryWrapper) Wrappers.<DeviceCard> lambdaQuery().eq((v0) -> {
                                    return v0.getCardId();
                                }, card.getCardId());
                                this.deviceCardService.remove(wrapper2);
                            }
                        }
                        if (error.length() > 0) {
                            publishErrorMsg(uniqueId, error.toString());
                        }
                    } catch (JSONException e) {
                        error.append("json处理出错，").append(e.getMessage()).append(";");
                        if (error.length() > 0) {
                            publishErrorMsg(uniqueId, error.toString());
                        }
                    } catch (Exception e2) {
                        error.append("服务器处理出错，").append(e2.getMessage()).append(";");
                        if (error.length() > 0) {
                            publishErrorMsg(uniqueId, error.toString());
                        }
                    }
                } catch (Throwable th) {
                    if (error.length() > 0) {
                        publishErrorMsg(uniqueId, error.toString());
                    }
                    throw th;
                }
            });
        } else {
            this.client = this.mqttClient.getClient();
        }
    }
    private void subscribeBeacon() throws MqttException, IllegalArgumentException {
        if (this.client != null && this.client.isConnected()) {
            String topic = this.baseTopic + "/beacon";
            this.client.subscribe(topic, 2, (topic1, message) -> {
                String msg = new String(message.getPayload(), CharsetMapping.MYSQL_CHARSET_NAME_utf8);
                this.log.info(" topic:{} 接受到的消息为：{}", topic, message);
                StringBuilder error = new StringBuilder();
                try {
                    try {
                        JSONObject json = JSONObject.parseObject(msg);
                        String uniqueId = UUID.randomUUID().toString();
                        DeviceBeacon beacon = new DeviceBeacon();
                        beacon.setCreateTime(LocalDateTime.now());
                        beacon.setBeaconId(json.getInteger("beaconId"));
                        beacon.setLayerId(json.getString("layerId"));
                        beacon.setLayerHeight(json.getInteger("layerHeight"));
                        beacon.setLongitude(json.getBigDecimal("longitude"));
                        beacon.setLatitude(json.getBigDecimal("latitude"));
                        beacon.setBeaconStatus(NORMAL_BEACON_STATUS);
                        Integer operateType = json.getInteger("operateType");
                        if (Objects.isNull(operateType) || (!operateType.equals(1) && !operateType.equals(2) && !operateType.equals(3))) {
                            error.append("操作类型未提供或提供的值不合法").append(";");
                        }
                        if (operateType.equals(1)) {
                            if (Objects.isNull(beacon.getBeaconId())) {
                                error.append("信标beaconId编号未提供").append(";");
                            }
                            if (org.apache.commons.lang3.StringUtils.isBlank(beacon.getLayerId())) {
                                error.append("楼层未提供").append(";");
                            }
                            if (Objects.isNull(beacon.getLayerHeight())) {
                                error.append("楼高未提供").append(";");
                            }
                            if (Objects.isNull(beacon.getLongitude())) {
                                error.append("信标经度未提供").append(";");
                            }
                            if (Objects.isNull(beacon.getLatitude())) {
                                error.append("信标纬度未提供").append(";");
                            }
                        } else if (Objects.isNull(beacon.getBeaconId())) {
                            error.append("删除信标的唯一标识未提供").append(";");
                        }
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(beacon.getLayerId())) {
                            DeviceLayer layer = this.layerService.queryLayerExist(beacon.getLayerId());
                            if (Objects.isNull(layer)) {
                                error.append("信标楼层提供有误，查无此层").append(";");
                            }
                            beacon.setLayerHeight(layer.getLayerHeight());
                        }
                        if (error.length() == 0) {
                            if (operateType.equals(1)) {
                                long count = this.beaconService.count((Wrapper) Wrappers.<DeviceBeacon>lambdaQuery().eq((v0) -> {
                                    return v0.getBeaconId();
                                }, beacon.getBeaconId()));
                                if (count > 0) {
                                    error.append("该信标id已存在，新增失败beaconId: " + beacon.getBeaconId()).append(";");
                                    if (error.length() > 0) {
                                        publishErrorMsg(uniqueId, error.toString());
                                        return;
                                    }
                                    return;
                                }
                                this.beaconService.save(beacon);
                            } else if (operateType.equals(2)) {
                                LambdaUpdateWrapper<DeviceBeacon> updateWrapper = (LambdaUpdateWrapper) Wrappers.<DeviceBeacon>lambdaUpdate().eq((v0) -> {
                                    return v0.getBeaconId();
                                }, beacon.getBeaconId());
                                this.beaconService.update(beacon, updateWrapper);
                            } else {
                                LambdaQueryWrapper<DeviceBeacon> wrapper = (LambdaQueryWrapper) Wrappers.<DeviceBeacon>lambdaQuery().eq((v0) -> {
                                    return v0.getBeaconId();
                                }, beacon.getBeaconId());
                                this.beaconService.remove(wrapper);
                            }
                        }
                        if (error.length() > 0) {
                            publishErrorMsg(uniqueId, error.toString());
                        }
                    } catch (JSONException e) {
                        error.append("json处理出错，").append(e.getMessage()).append(";");
                        if (error.length() > 0) {
                            publishErrorMsg("", error.toString());
                        }
                    } catch (Exception e2) {
                        error.append("服务器处理出错，").append(e2.getMessage()).append(";");
                        if (error.length() > 0) {
                            publishErrorMsg("", error.toString());
                        }
                    }
                } catch (Throwable th) {
                    if (error.length() > 0) {
                        publishErrorMsg("", error.toString());
                    }
                    throw th;
                }
            });
        } else {
            this.client = this.mqttClient.getClient();
        }
    }
    private void subscribeLayer() throws MqttException, IllegalArgumentException {
        if (this.client != null && this.client.isConnected()) {
            String topic = this.baseTopic + "/layer";
            this.client.subscribe(topic, 2, (topic1, message) -> {
                String msg = new String(message.getPayload(), CharsetMapping.MYSQL_CHARSET_NAME_utf8);
                this.log.info(" topic:{} 接受到的消息为：{}", topic, message);
                StringBuilder error = new StringBuilder();
                JSONObject json = JSONObject.parseObject(msg);
                String uniqueId = json.getString("layerId") + "";
                try {
                    try {
                        try {
                            DeviceLayer layer = new DeviceLayer();
                            layer.setLayerId(json.getString("layerId")).setMinLatitude(json.getBigDecimal("minLatitude")).setMinLongitude(json.getBigDecimal("minLongitude")).setMaxLatitude(json.getBigDecimal("maxLatitude")).setMaxLongitude(json.getBigDecimal("maxLongitude")).setBuildingId(json.getLong("buildingId")).setMapType(json.getString("fileMapType")).setLayerHeight(json.getInteger("layerHeight"));
                            String file = json.getString("fileMap");
                            Map<String, String> configMap = RedisCacheUtils.getConfigRedisCache();
                            configMap.get(ConfigValue.CONFIG_KEY_STATIC_PATH);
                            Integer operateType = json.getInteger("operateType");
                            if (operateType == null || (operateType.intValue() != 1 && operateType.intValue() != 2 && operateType.intValue() != 3)) {
                                error.append("操作类型未提供或提供的值不合法").append(";");
                            }
                            if (operateType.equals(1)) {
                                if (layer.getLayerId() == null) {
                                    error.append("楼层编号未提供").append(";");
                                }
                                if (layer.getMinLatitude() == null) {
                                    error.append("楼层的最小纬度未提供").append(";");
                                }
                                if (layer.getMinLongitude() == null) {
                                    error.append("楼层的最小经度未提供").append(";");
                                }
                                if (layer.getMaxLongitude() == null) {
                                    error.append("楼层的最大经度未提供").append(";");
                                }
                                if (layer.getMaxLatitude() == null) {
                                    error.append("楼层的最大纬度未提供").append(";");
                                }
                                if (file == null) {
                                    error.append("楼层的地图未提供").append(";");
                                }
                            } else if (Objects.isNull(layer.getLayerId())) {
                                error.append("楼层layerId未提供").append(";");
                            }
                            if (error.length() == 0) {
                                if (operateType.equals(1)) {
                                    String url = FileUtil.saveBase64UpLoadFile(file, "/xrkc/", "xrkc");
                                    layer.setMapUrl(url);
                                    layer.setCreateTime(LocalDateTime.now());
                                    if (this.layerService.queryLayerOneExist(layer.getLayerId())) {
                                        error.append("当前楼层已存在，新增楼层失败layerId: " + layer.getLayerId()).append(";");
                                        publishErrorMsg(uniqueId, error.toString());
                                        if (error.length() > 0) {
                                            publishErrorMsg(uniqueId, error.toString());
                                            return;
                                        }
                                        return;
                                    }
                                    this.layerService.save(layer);
                                } else if (operateType.equals(2)) {
                                    this.layerService.updateByLayerId(layer);
                                } else {
                                    this.layerService.deleteByLayerId(layer.getLayerId());
                                }
                            }
                            if (error.length() > 0) {
                                publishErrorMsg(uniqueId, error.toString());
                            }
                        } catch (Exception e) {
                            error.append("服务器处理出错，").append(e.getMessage()).append(";");
                            if (error.length() > 0) {
                                publishErrorMsg(uniqueId, error.toString());
                            }
                        }
                    } catch (JSONException e2) {
                        error.append("json处理出错，").append(e2.getMessage()).append(";");
                        if (error.length() > 0) {
                            publishErrorMsg(uniqueId, error.toString());
                        }
                    }
                } catch (Throwable th) {
                    if (error.length() > 0) {
                        publishErrorMsg(uniqueId, error.toString());
                    }
                    throw th;
                }
            });
        } else {
            this.client = this.mqttClient.getClient();
        }
    }
    private void publishErrorMsg(String uuid, String errorMsg) throws IllegalStateException, UnsupportedEncodingException {
        JSONObject msg = new JSONObject();
        msg.put("uniqueId", uuid);
        msg.put("msg", errorMsg);
        String topic = this.baseTopic + "/error";
        this.mqttClient.publish(this.client, topic, msg.toString());
    }
}
