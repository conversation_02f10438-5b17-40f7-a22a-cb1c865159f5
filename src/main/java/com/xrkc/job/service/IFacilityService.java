package com.xrkc.job.service;
import com.xrkc.job.domain.Facility;
import com.xrkc.job.domain.FacilityRail;
import com.xrkc.job.domain.FacilityRailVO;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IFacilityService.class */
public interface IFacilityService {
    List<FacilityRailVO> selectFacilityRailVOList();
    List<FacilityRail> selectFacilityRailList();
    List<Facility> selectFacilityAndRailList();
    List<Facility> selectFacilityList();
    List<FacilityRailVO> getRailScopes(Long l);
}
