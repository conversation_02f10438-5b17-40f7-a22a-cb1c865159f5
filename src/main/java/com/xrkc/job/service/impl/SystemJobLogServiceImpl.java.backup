package com.xrkc.job.service.impl;
import com.xrkc.job.domain.SystemJobLog;
import com.xrkc.job.mapper.SystemJobLogMapper;
import com.xrkc.job.service.ISystemJobLogService;
import java.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/SystemJobLogServiceImpl.class */
public class SystemJobLogServiceImpl implements ISystemJobLogService {
    @Autowired(required = false)
    private SystemJobLogMapper systemJobLogMapper;
    @Override // com.xrkc.job.service.ISystemJobLogService
    public int addJobLog(SystemJobLog sysJobLog) {
        sysJobLog.setCreateTime(LocalDateTime.now());
        return this.systemJobLogMapper.insert((SystemJobLogMapper) sysJobLog);
    }
    /* JADX WARN: Incorrect condition in loop: B:28:0x0023 */
    @Override // com.xrkc.job.service.ISystemJobLogService
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void batchDelete(java.lang.String r6, int r7) {
        /*
            r5 = this;
            java.time.LocalDateTime r0 = java.time.LocalDateTime.now()
            r1 = r7
            long r1 = (long) r1
            java.time.LocalDateTime r0 = r0.minusMinutes(r1)
            r8 = r0
            r0 = r5
            com.xrkc.job.mapper.SystemJobLogMapper r0 = r0.systemJobLogMapper
            r1 = r8
            r2 = r6
            java.lang.Long r0 = r0.findMaxId(r1, r2)
            r9 = r0
            r0 = r9
            boolean r0 = java.util.Objects.nonNull(r0)
            if (r0 == 0) goto L3a
            r0 = 1
            r10 = r0
        L21:
            r0 = r10
            if (r0 <= 0) goto L3a
            r0 = r5
            com.xrkc.job.mapper.SystemJobLogMapper r0 = r0.systemJobLogMapper
            r1 = r9
            r2 = 1000(0x3e8, float:1.401E-42)
            r3 = r6
            int r0 = r0.delByMaxId(r1, r2, r3)
            r10 = r0
            goto L21
        L3a:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: com.xrkc.job.service.impl.SystemJobLogServiceImpl.batchDelete(java.lang.String, int):void");
    }
    @Override // com.xrkc.job.service.ISystemJobLogService
    public int truncateTable() {
        return this.systemJobLogMapper.truncateTable();
    }
}
