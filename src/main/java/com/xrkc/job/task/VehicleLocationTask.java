package com.xrkc.job.task;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.domain.basic.JsonResult;
import com.xrkc.core.domain.vehicle.VehicleHistoryCalendar;
import com.xrkc.core.domain.vehicle.VehiclePositionCalc;
import com.xrkc.core.domain.vehicle.VehiclePositionCurrent;
import com.xrkc.core.domain.vehicle.VehiclePositionHistory;
import com.xrkc.job.controller.WebSocketServer;
import com.xrkc.job.domain.Constants;
import com.xrkc.job.domain.SystemDict;
import com.xrkc.job.domain.SystemUser;
import com.xrkc.job.module.vehicle.mapper.VehicleHistoryCalendarMapper;
import com.xrkc.job.module.vehicle.service.IVehicleAlarmService;
import com.xrkc.job.module.vehicle.service.IVehicleInfoService;
import com.xrkc.job.module.vehicle.service.IVehiclePositionCalcService;
import com.xrkc.job.module.vehicle.service.IVehiclePositionCurrentService;
import com.xrkc.job.module.vehicle.service.IVehiclePositionHistoryService;
import com.xrkc.job.service.ISystemDictService;
import com.xrkc.job.service.ISystemUserService;
import com.xrkc.job.util.CoordinateSystemUtils;
import com.xrkc.job.util.MQTTPublishClient;
import com.xrkc.job.vo.PushVehiclePositionVO;
import com.xrkc.job.vo.TypeCountVo;
import com.xrkc.redis.utils.RedisCacheUtils;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import net.jodah.expiringmap.ExpiringMap;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.tags.form.AbstractHtmlElementTag;
@Component("vehicleLocationTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/VehicleLocationTask.class */
public class VehicleLocationTask {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) VehicleLocationTask.class);
    @Autowired
    private IVehiclePositionCurrentService vehiclePositionCurrentService;
    @Autowired
    private IVehiclePositionCalcService vehiclePositionCalcService;
    @Autowired
    private IVehiclePositionHistoryService vehiclePositionHistoryService;
    @Autowired
    private ISystemUserService systemUserService;
    @Autowired
    private WebSocketServer webSocketServer;
    @Autowired
    private MQTTPublishClient mqttPublishClient;
    @Autowired
    private IVehicleAlarmService vehicleAlarmService;
    @Autowired
    private IVehicleInfoService vehicleInfoService;
    @Autowired
    private ISystemDictService systemDictService;
    @Autowired
    private VehicleHistoryCalendarMapper vehicleHistoryCalendarMapper;
    @Value("${spring.datasource.driver-class-name}")
    private String driverClassName;
    ExpiringMap<String, LocalDateTime> vehicleMqttSendedMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    ExpiringMap<String, LocalDateTime> vehiclePositionMqttSendedMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    private static final int second = 240;
    /* JADX WARN: Multi-variable type inference failed */
    public void generateHistoryPosition() {
        List<VehiclePositionCalc> list = this.vehiclePositionCalcService.selectListOfHistory(240);
        if (!CollectionUtils.isEmpty(list)) {
            LocalDate today = LocalDate.now();
            String baseTableName = this.driverClassName.contains("dm") ? "VEHICLE_POSITION_HISTORY_" : "vehicle_position_history_";
            String tableName = baseTableName + today.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            List<VehiclePositionHistory> historyList = BeanUtil.copyToList(list, VehiclePositionHistory.class);
            this.vehiclePositionHistoryService.batchBakHistory(tableName, historyList);
            List<VehicleHistoryCalendar> calendarList = this.vehicleHistoryCalendarMapper.selectList((Wrapper) new LambdaQueryWrapper<VehicleHistoryCalendar>().eq((v0) -> {
                return v0.getDay();
            }, today));
            List<Long> vehicleIds = (List) calendarList.stream().map((v0) -> {
                return v0.getVehicleId();
            }).collect(Collectors.toList());
            historyList.removeIf(r -> {
                return vehicleIds.contains(r.getVehicleId());
            });
            if (!CollectionUtils.isEmpty(historyList)) {
                LocalDateTime now = LocalDateTime.now();
                Map<Long, VehiclePositionHistory> groupedItems = (Map) historyList.stream().collect(Collectors.groupingBy((v0) -> {
                    return v0.getVehicleId();
                }, Collectors.collectingAndThen(Collectors.toList(), value -> {
                    return (VehiclePositionHistory) value.get(0);
                })));
                ArrayList arrayList = new ArrayList();
                groupedItems.forEach((vehicleId, item) -> {
                    VehicleHistoryCalendar p = VehicleHistoryCalendar.builder().day(today).createTime(now).vehicleId(vehicleId).vehicleCategory(item.getVehicleCategory()).vehicleName(item.getVehicleName()).build();
                    arrayList.add(p);
                });
                this.vehicleHistoryCalendarMapper.insertBatch(arrayList, 1000);
            }
        }
    }
    public void pushCurrentPosition() {
        if (CollectionUtils.isEmpty(WebSocketServer.systemSessionMap)) {
            return;
        }
        Set<Long> wsUserIds = new HashSet<>();
        WebSocketServer.systemSessionMap.forEach((socketKey, session) -> {
            String userId = socketKey.split("_")[2];
            if (NumberUtil.isLong(userId)) {
                wsUserIds.add(Long.valueOf(userId));
            }
        });
        List<SystemUser> systemUsers = this.systemUserService.findByUserIds(wsUserIds);
        systemUsers.removeIf(r -> {
            return !wsUserIds.contains(r.getUserId());
        });
        Set<Long> pushUserIds = (Set) systemUsers.stream().map((v0) -> {
            return v0.getUserId();
        }).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(pushUserIds)) {
            return;
        }
        List<VehiclePositionCurrent> list = this.vehiclePositionCurrentService.selectCurrentList();
        JSONObject pushJson = new JSONObject();
        pushJson.put("msgType", "currentVehicleLocation");
        pushJson.put(JsonResult.TOTAL, Integer.valueOf(list.size()));
        pushJson.put("data", list);
        Map<String, Long> CategoryMap = (Map) list.stream().filter(f -> {
            return StringUtils.isNotBlank(f.getVehicleCategory());
        }).collect(Collectors.groupingBy((v0) -> {
            return v0.getVehicleCategory();
        }, Collectors.counting()));
        List<TypeCountVo> typeCountVoList = new ArrayList<>();
        Map<String, SystemDict> dictMap = this.systemDictService.findValEntityMap("vehicle_category");
        dictMap.forEach((k, v) -> {
            if ("Y".equals(v.getDictEnable())) {
                TypeCountVo typeCountVo = TypeCountVo.builder().type(v.getDictValue()).label(v.getDictLabel()).count(CategoryMap.containsKey(k) ? (Long) CategoryMap.get(k) : 0L).build();
                typeCountVoList.add(typeCountVo);
            }
        });
        pushJson.put("categoryStatistics", typeCountVoList);
        pushJson.put("vehicleCount", this.vehicleInfoService.count());
        this.webSocketServer.sendMessageForXR(null, JSONObject.toJSONString(pushJson, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible));
    }
    public void pushApiCurrentPosition() {
        if (CollectionUtils.isEmpty(WebSocketServer.apiSessionMap)) {
            return;
        }
        List<PushVehiclePositionVO> pushList = new ArrayList<>();
        JSONObject msgJson = new JSONObject();
        msgJson.put("msgType", "vehiclePosition");
        List<VehiclePositionCurrent> list = this.vehiclePositionCurrentService.selectCurrentList();
        if (CollectionUtils.isEmpty(list)) {
            msgJson.put("data", pushList);
            this.webSocketServer.sendMessageForAPI(null, JSONObject.toJSONString(msgJson, JSONWriter.Feature.WriteMapNullValue));
            return;
        }
        Map<Long, List<String>> carAlarmType = this.vehicleAlarmService.selectUnDisposeAlarmTypeMap((List) list.stream().map((v0) -> {
            return v0.getVehicleId();
        }).collect(Collectors.toList()));
        Map<String, String> configMap = RedisCacheUtils.getConfigRedisCache();
        String coordinateSystem = configMap.get(ConfigValue.API_KEY_WS_PUSH_COORDINATE_SYSTEM);
        list.stream().forEach(position -> {
            PushVehiclePositionVO vo = new PushVehiclePositionVO();
            if (StrUtil.equals(coordinateSystem, Constants.COORDINATE_SYSTEM_WGS84, true)) {
                vo.setLongitude(position.getLongitude());
                vo.setLatitude(position.getLatitude());
            } else {
                BigDecimal[] geo = CoordinateSystemUtils.Geo4326Transform3857(position.getLongitude(), position.getLatitude());
                vo.setLongitude(geo[0]);
                vo.setLatitude(geo[1]);
            }
            vo.setCardNo(Objects.nonNull(position.getCardId()) ? position.getCardId().toString() : null);
            vo.setVehicleNo(position.getLicensePlateNumber());
            vo.setVehicleName(position.getVehicleName());
            vo.setVehicleType(position.getVehicleType());
            vo.setVehicleTypeName(position.getVehicleTypeName());
            String attr = position.getVehicleAttribute();
            if (StringUtils.isNotBlank(attr)) {
                attr = attr.replaceAll("/init/", "/dingshi/");
            }
            vo.setVehicleAttribute(attr);
            vo.setDriver(position.getDriverName());
            vo.setPhone(position.getDriverTel());
            vo.setCompany(position.getCompanyName());
            vo.setDeviceElectric(position.getElectricity());
            vo.setTimestamp(Long.valueOf(System.currentTimeMillis()));
            vo.setSpeed(position.getSpeed());
            vo.setDir(position.getDirection());
            vo.setAlarm(Boolean.valueOf(carAlarmType.containsKey(position.getVehicleId())));
            vo.setAlarmType((List) carAlarmType.get(position.getVehicleId()));
            pushList.add(vo);
        });
        msgJson.put("data", pushList);
        this.webSocketServer.sendMessageForAPI(null, JSONObject.toJSONString(msgJson, JSONWriter.Feature.WriteMapNullValue));
    }
    @Deprecated
    public void vehicleMqttPush() {
        if (this.mqttPublishClient.getClient() != null && this.mqttPublishClient.getClient().isConnected()) {
            String topic = "/vehiclePosition";
            List<VehiclePositionCurrent> pushList = this.vehiclePositionCurrentService.selectCurrentList();
            pushList.forEach(vehicle -> {
                String key = "vehicle_" + vehicle.getCardId();
                if (!this.vehiclePositionMqttSendedMap.containsKey(key) || this.vehiclePositionMqttSendedMap.get(key).isBefore(vehicle.getAcceptTime())) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("vehicleId", vehicle.getVehicleId());
                    jsonObject.put("carNo", vehicle.getLicensePlateNumber());
                    jsonObject.put("companyName", vehicle.getCompanyName());
                    jsonObject.put(AbstractHtmlElementTag.DIR_ATTRIBUTE, vehicle.getDirection());
                    jsonObject.put("driverName", vehicle.getDriverName());
                    jsonObject.put("driverTel", vehicle.getDriverTel());
                    jsonObject.put("latitude", vehicle.getLatitude());
                    jsonObject.put("longitude", vehicle.getLongitude());
                    jsonObject.put("online", "1");
                    jsonObject.put("speed", vehicle.getSpeed());
                    jsonObject.put("vehicleName", vehicle.getVehicleName());
                    jsonObject.put("vehiclePower", vehicle.getElectricity());
                    jsonObject.put("vehicleSn", vehicle.getCardId());
                    jsonObject.put("acceptTime", vehicle.getAcceptTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    jsonObject.put("vehicleType", vehicle.getVehicleType());
                    jsonObject.put("vehicleTypeName", vehicle.getVehicleType());
                    String msg = JSON.toJSONString(jsonObject, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible);
                    log.info("[兼容]准备推送车辆实时数据：{}", msg);
                    if (this.mqttPublishClient.publish(null, topic, msg).booleanValue()) {
                        this.vehiclePositionMqttSendedMap.put(key, vehicle.getAcceptTime());
                    }
                }
            });
        }
    }
    public void vehiclePositionMqttPush() {
        if (this.mqttPublishClient.getClient() != null && this.mqttPublishClient.getClient().isConnected()) {
            String topic = "/vehicle/position";
            List<VehiclePositionCurrent> pushList = this.vehiclePositionCurrentService.selectCurrentList();
            pushList.forEach(vehicle -> {
                String key = "vehicle_" + vehicle.getCardId();
                if (!this.vehicleMqttSendedMap.containsKey(key) || this.vehicleMqttSendedMap.get(key).isBefore(vehicle.getAcceptTime())) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("uniqueId", UUID.randomUUID().toString());
                    jsonObject.put("vehicleId", vehicle.getVehicleId());
                    jsonObject.put("licensePlateNumber", vehicle.getLicensePlateNumber());
                    jsonObject.put("companyName", vehicle.getCompanyName());
                    jsonObject.put("direction", vehicle.getDirection());
                    jsonObject.put("driverName", vehicle.getDriverName());
                    jsonObject.put("driverTel", vehicle.getDriverTel());
                    jsonObject.put("latitude", vehicle.getLatitude());
                    jsonObject.put("longitude", vehicle.getLongitude());
                    jsonObject.put("speed", vehicle.getSpeed());
                    jsonObject.put("vehicleName", vehicle.getVehicleName());
                    jsonObject.put("cardId", vehicle.getCardId());
                    jsonObject.put("acceptTime", vehicle.getAcceptTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    jsonObject.put("vehicleType", vehicle.getVehicleType());
                    jsonObject.put("electricity", vehicle.getElectricity());
                    jsonObject.put("layerId", vehicle.getLayerId());
                    jsonObject.put("pointType", vehicle.getPointType());
                    jsonObject.put("beaconId", vehicle.getBeaconId());
                    jsonObject.put("layerHeight", vehicle.getLayerHeight());
                    String msg = JSON.toJSONString(jsonObject, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible);
                    log.info("准备推送车辆实时数据：{}", msg);
                    if (this.mqttPublishClient.publish(null, topic, msg).booleanValue()) {
                        this.vehicleMqttSendedMap.put(key, vehicle.getAcceptTime());
                    }
                }
            });
        }
    }
}
