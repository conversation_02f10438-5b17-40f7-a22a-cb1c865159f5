package com.xrkc.job.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xrkc.job.domain.HiddenDangerAbarbeitung;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.postgresql.jdbc.EscapedFunctions;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/HiddenDangerAbarbeitungMapper.class */
public interface HiddenDangerAbarbeitungMapper extends BaseMapper<HiddenDangerAbarbeitung> {
    @Select({"select id from hidden_danger_abarbeitung where abarbeitung_deadline <= #{now} "})
    List<Long> findExpireRectifyDeadline(@Param(EscapedFunctions.NOW_FUNC) LocalDateTime localDateTime);
}
