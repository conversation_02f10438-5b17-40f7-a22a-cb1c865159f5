package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.job.domain.InspectRecordLocation;
import com.xrkc.job.mapper.InspectRecordLocationMapper;
import com.xrkc.job.service.IInspectRecordLocationService;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/InspectRecordLocationServiceImpl.class */
public class InspectRecordLocationServiceImpl extends ServiceImpl<InspectRecordLocationMapper, InspectRecordLocation> implements IInspectRecordLocationService {
    @Autowired
    private InspectRecordLocationMapper inspectRecordLocationMapper;
    @Override // com.xrkc.job.service.IInspectRecordLocationService
    public List<InspectRecordLocation> selectLocationList(LocalDate now, LocalDateTime nowTime) {
        return this.inspectRecordLocationMapper.selectLocationList(now, nowTime);
    }
}
