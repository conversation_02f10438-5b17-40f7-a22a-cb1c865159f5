package com.xrkc.job.domain;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/HikvisionSyncPerson.class */
public class HikvisionSyncPerson implements Serializable {
    private static final long serialVersionUID = 1;
    private String userId;
    private String personId;
    private String personCode;
    private String personGivenName;
    private String personFamilyName;
    private Integer gender;
    private String orgIndexCode;
    private String phoneNo;
    private String email;
    private List<Face> faces;
    private String fingerPrint;
    private String remark;
    private String beginTime;
    private String endTime;
    public void setUserId(String userId) {
        this.userId = userId;
    }
    public void setPersonId(String personId) {
        this.personId = personId;
    }
    public void setPersonCode(String personCode) {
        this.personCode = personCode;
    }
    public void setPersonGivenName(String personGivenName) {
        this.personGivenName = personGivenName;
    }
    public void setPersonFamilyName(String personFamilyName) {
        this.personFamilyName = personFamilyName;
    }
    public void setGender(Integer gender) {
        this.gender = gender;
    }
    public void setOrgIndexCode(String orgIndexCode) {
        this.orgIndexCode = orgIndexCode;
    }
    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }
    public void setEmail(String email) {
        this.email = email;
    }
    public void setFaces(List<Face> faces) {
        this.faces = faces;
    }
    public void setFingerPrint(String fingerPrint) {
        this.fingerPrint = fingerPrint;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }
    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof HikvisionSyncPerson)) {
            return false;
        }
        HikvisionSyncPerson other = (HikvisionSyncPerson) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$gender = getGender();
        Object other$gender = other.getGender();
        if (this$gender == null) {
            if (other$gender != null) {
                return false;
            }
        } else if (!this$gender.equals(other$gender)) {
            return false;
        }
        Object this$userId = getUserId();
        Object other$userId = other.getUserId();
        if (this$userId == null) {
            if (other$userId != null) {
                return false;
            }
        } else if (!this$userId.equals(other$userId)) {
            return false;
        }
        Object this$personId = getPersonId();
        Object other$personId = other.getPersonId();
        if (this$personId == null) {
            if (other$personId != null) {
                return false;
            }
        } else if (!this$personId.equals(other$personId)) {
            return false;
        }
        Object this$personCode = getPersonCode();
        Object other$personCode = other.getPersonCode();
        if (this$personCode == null) {
            if (other$personCode != null) {
                return false;
            }
        } else if (!this$personCode.equals(other$personCode)) {
            return false;
        }
        Object this$personGivenName = getPersonGivenName();
        Object other$personGivenName = other.getPersonGivenName();
        if (this$personGivenName == null) {
            if (other$personGivenName != null) {
                return false;
            }
        } else if (!this$personGivenName.equals(other$personGivenName)) {
            return false;
        }
        Object this$personFamilyName = getPersonFamilyName();
        Object other$personFamilyName = other.getPersonFamilyName();
        if (this$personFamilyName == null) {
            if (other$personFamilyName != null) {
                return false;
            }
        } else if (!this$personFamilyName.equals(other$personFamilyName)) {
            return false;
        }
        Object this$orgIndexCode = getOrgIndexCode();
        Object other$orgIndexCode = other.getOrgIndexCode();
        if (this$orgIndexCode == null) {
            if (other$orgIndexCode != null) {
                return false;
            }
        } else if (!this$orgIndexCode.equals(other$orgIndexCode)) {
            return false;
        }
        Object this$phoneNo = getPhoneNo();
        Object other$phoneNo = other.getPhoneNo();
        if (this$phoneNo == null) {
            if (other$phoneNo != null) {
                return false;
            }
        } else if (!this$phoneNo.equals(other$phoneNo)) {
            return false;
        }
        Object this$email = getEmail();
        Object other$email = other.getEmail();
        if (this$email == null) {
            if (other$email != null) {
                return false;
            }
        } else if (!this$email.equals(other$email)) {
            return false;
        }
        Object this$faces = getFaces();
        Object other$faces = other.getFaces();
        if (this$faces == null) {
            if (other$faces != null) {
                return false;
            }
        } else if (!this$faces.equals(other$faces)) {
            return false;
        }
        Object this$fingerPrint = getFingerPrint();
        Object other$fingerPrint = other.getFingerPrint();
        if (this$fingerPrint == null) {
            if (other$fingerPrint != null) {
                return false;
            }
        } else if (!this$fingerPrint.equals(other$fingerPrint)) {
            return false;
        }
        Object this$remark = getRemark();
        Object other$remark = other.getRemark();
        if (this$remark == null) {
            if (other$remark != null) {
                return false;
            }
        } else if (!this$remark.equals(other$remark)) {
            return false;
        }
        Object this$beginTime = getBeginTime();
        Object other$beginTime = other.getBeginTime();
        if (this$beginTime == null) {
            if (other$beginTime != null) {
                return false;
            }
        } else if (!this$beginTime.equals(other$beginTime)) {
            return false;
        }
        Object this$endTime = getEndTime();
        Object other$endTime = other.getEndTime();
        return this$endTime == null ? other$endTime == null : this$endTime.equals(other$endTime);
    }
    protected boolean canEqual(Object other) {
        return other instanceof HikvisionSyncPerson;
    }
    public int hashCode() {
        Object $gender = getGender();
        int result = (1 * 59) + ($gender == null ? 43 : $gender.hashCode());
        Object $userId = getUserId();
        int result2 = (result * 59) + ($userId == null ? 43 : $userId.hashCode());
        Object $personId = getPersonId();
        int result3 = (result2 * 59) + ($personId == null ? 43 : $personId.hashCode());
        Object $personCode = getPersonCode();
        int result4 = (result3 * 59) + ($personCode == null ? 43 : $personCode.hashCode());
        Object $personGivenName = getPersonGivenName();
        int result5 = (result4 * 59) + ($personGivenName == null ? 43 : $personGivenName.hashCode());
        Object $personFamilyName = getPersonFamilyName();
        int result6 = (result5 * 59) + ($personFamilyName == null ? 43 : $personFamilyName.hashCode());
        Object $orgIndexCode = getOrgIndexCode();
        int result7 = (result6 * 59) + ($orgIndexCode == null ? 43 : $orgIndexCode.hashCode());
        Object $phoneNo = getPhoneNo();
        int result8 = (result7 * 59) + ($phoneNo == null ? 43 : $phoneNo.hashCode());
        Object $email = getEmail();
        int result9 = (result8 * 59) + ($email == null ? 43 : $email.hashCode());
        Object $faces = getFaces();
        int result10 = (result9 * 59) + ($faces == null ? 43 : $faces.hashCode());
        Object $fingerPrint = getFingerPrint();
        int result11 = (result10 * 59) + ($fingerPrint == null ? 43 : $fingerPrint.hashCode());
        Object $remark = getRemark();
        int result12 = (result11 * 59) + ($remark == null ? 43 : $remark.hashCode());
        Object $beginTime = getBeginTime();
        int result13 = (result12 * 59) + ($beginTime == null ? 43 : $beginTime.hashCode());
        Object $endTime = getEndTime();
        return (result13 * 59) + ($endTime == null ? 43 : $endTime.hashCode());
    }
    public String toString() {
        return "HikvisionSyncPerson(userId=" + getUserId() + ", personId=" + getPersonId() + ", personCode=" + getPersonCode() + ", personGivenName=" + getPersonGivenName() + ", personFamilyName=" + getPersonFamilyName() + ", gender=" + getGender() + ", orgIndexCode=" + getOrgIndexCode() + ", phoneNo=" + getPhoneNo() + ", email=" + getEmail() + ", faces=" + getFaces() + ", fingerPrint=" + getFingerPrint() + ", remark=" + getRemark() + ", beginTime=" + getBeginTime() + ", endTime=" + getEndTime() + StringPool.RIGHT_BRACKET;
    }
    public String getUserId() {
        return this.userId;
    }
    public String getPersonId() {
        return this.personId;
    }
    public String getPersonCode() {
        return this.personCode;
    }
    public String getPersonGivenName() {
        return this.personGivenName;
    }
    public String getPersonFamilyName() {
        return this.personFamilyName;
    }
    public Integer getGender() {
        return this.gender;
    }
    public String getOrgIndexCode() {
        return this.orgIndexCode;
    }
    public String getPhoneNo() {
        return this.phoneNo;
    }
    public String getEmail() {
        return this.email;
    }
    public List<Face> getFaces() {
        return this.faces;
    }
    public String getFingerPrint() {
        return this.fingerPrint;
    }
    public String getRemark() {
        return this.remark;
    }
    public String getBeginTime() {
        return this.beginTime;
    }
    public String getEndTime() {
        return this.endTime;
    }
}
