package com.xrkc.job.util;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.xrkc.core.domain.system.SystemApi;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import org.postgresql.jdbc.EscapedFunctions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/ApiUtils.class */
public class ApiUtils {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) ApiUtils.class);
    private static final List<Integer> VALID_PWD_CHARS = new ArrayList();
    private static Map<String, String> alarmTypeMap = new HashMap();
    private static final String ALARM_TYPE_01 = "01";
    private static final String ALARM_TYPE_09 = "09";
    private static final String ALARM_TYPE_03 = "03";
    private static final String ALARM_TYPE_02 = "02";
    private static final String ALARM_TYPE_05 = "05";
    private static final String ALARM_TYPE_04 = "04";
    private static final String ALARM_TYPE_06 = "06";
    private static final String ALARM_TYPE_07 = "07";
    private static final String ALARM_TYPE_95 = "95";
    private static final String ALARM_TYPE_10 = "10";
    private static final String ALARM_TYPE_20 = "20";
    private static final String ALARM_TYPE_30 = "30";
    private static final String ALARM_TYPE_40 = "40";
    private static final String ALARM_TYPE_50 = "50";
    private static final String ALARM_TYPE_70 = "70";
    private static final String VEHICLE_ALARM_TYPE_5 = "5";
    private static final String HMAC_SHA1_ALGORITHM = "HmacSHA1";
    static {
        IntStream intStreamRangeClosed = IntStream.rangeClosed(48, 57);
        List<Integer> list = VALID_PWD_CHARS;
        list.getClass();
        intStreamRangeClosed.forEach((v1) -> {
            r1.add(v1);
        });
        IntStream intStreamRangeClosed2 = IntStream.rangeClosed(97, 122);
        List<Integer> list2 = VALID_PWD_CHARS;
        list2.getClass();
        intStreamRangeClosed2.forEach((v1) -> {
            r1.add(v1);
        });
    }
    public static String getAlarmType(String alarmType) {
        alarmTypeMap.put("95", ALARM_TYPE_01);
        alarmTypeMap.put("10", ALARM_TYPE_09);
        alarmTypeMap.put("20", ALARM_TYPE_03);
        alarmTypeMap.put("30", ALARM_TYPE_02);
        alarmTypeMap.put("40", ALARM_TYPE_05);
        alarmTypeMap.put("50", ALARM_TYPE_04);
        alarmTypeMap.put("70", ALARM_TYPE_06);
        alarmTypeMap.put("5", ALARM_TYPE_07);
        return alarmTypeMap.get(alarmType);
    }
    /* JADX WARN: Multi-variable type inference failed */
    public static String pushData(SystemApi systemApi, MultiValueMap<String, Object> linkedMap, String route) {
        try {
            String appid = systemApi.getUserName();
            String appSecret = systemApi.getPassword();
            String time = "" + System.currentTimeMillis();
            String nonceStr = CommonUtil.GetRandomString(10);
            String str = appid + "&" + time + "&" + nonceStr;
            String sign = genHMAC(str, appSecret);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            headers.set("appid", appid);
            headers.set("time", time);
            headers.set("nonceStr", nonceStr);
            headers.set(EscapedFunctions.SIGN_FUNC, sign);
            RestTemplate restTemplate = new RestTemplate();
            String url = systemApi.getHost() + route;
            HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(linkedMap, headers);
            JSONObject result = (JSONObject) restTemplate.postForEntity(url, httpEntity, JSONObject.class, new Object[0]).getBody();
            log.info("【数据推送】{},url:{},推送结果：{}", systemApi.getApiKey(), url, result.toJSONString(new JSONWriter.Feature[0]));
            if (0 == result.getIntValue("code")) {
                return "success";
            }
            return result.getString("msg");
        } catch (Exception e) {
            log.error("【数据推送】推送失败：{},接口：{}", e.getMessage(), route);
            return e.getMessage();
        }
    }
    public static String genHMAC(String data, String key) throws IllegalStateException, NoSuchAlgorithmException, InvalidKeyException {
        try {
            SecretKeySpec signinKey = new SecretKeySpec(key.getBytes(), HMAC_SHA1_ALGORITHM);
            Mac mac = Mac.getInstance(HMAC_SHA1_ALGORITHM);
            mac.init(signinKey);
            byte[] rawHmac = mac.doFinal(data.getBytes());
            String result = Base64.encode(rawHmac);
            return result;
        } catch (InvalidKeyException e) {
            return null;
        } catch (NoSuchAlgorithmException e2) {
            return null;
        }
    }
}
