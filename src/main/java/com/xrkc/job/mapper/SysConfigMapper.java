package com.xrkc.job.mapper;
import com.xrkc.core.domain.system.SystemConfig;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/SysConfigMapper.class */
public interface SysConfigMapper {
    String findConfigVal(String str);
    String selectRemarkByKey(String str);
    int updateRemarkByKey(@Param("configKey") String str, @Param("today") String str2);
    Integer getByIntervalSecond();
    Integer getNewByIntervalSecond();
    Integer getByIntervalDay();
    @Select({" SELECT config_key,config_value FROM system_config;"})
    List<SystemConfig> configList();
}
