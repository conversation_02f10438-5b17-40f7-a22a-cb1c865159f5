package com.xrkc.job.service.impl;
import com.xrkc.core.domain.cardDispenser.DeviceCardSenderVehicle;
import com.xrkc.job.mapper.DeviceCardSenderVehicleMapper;
import com.xrkc.job.service.IDeviceCardSenderVehicleService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/DeviceCardSenderVehicleServiceImpl.class */
public class DeviceCardSenderVehicleServiceImpl implements IDeviceCardSenderVehicleService {
    @Autowired
    private DeviceCardSenderVehicleMapper deviceCardSenderVehicleMapper;
    @Override // com.xrkc.job.service.IDeviceCardSenderVehicleService
    public List<DeviceCardSenderVehicle> selectTimeoutList(List<String> notDeviceSnList, List<String> inDeviceSnList, int timeout) {
        return this.deviceCardSenderVehicleMapper.selectTimeoutList(notDeviceSnList, inDeviceSnList, timeout);
    }
    @Override // com.xrkc.job.service.IDeviceCardSenderVehicleService
    public void batchUpdate(List<DeviceCardSenderVehicle> list) {
        this.deviceCardSenderVehicleMapper.updateBatch(list, 1000);
    }
}
