package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.job.domain.AlarmNoticeRecordPerson;
import com.xrkc.job.mapper.AlarmNoticeRecordPersonMapper;
import com.xrkc.job.service.IAlarmNoticeRecordPersonService;
import java.lang.invoke.SerializedLambda;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/AlarmNoticeRecordPersonServiceImpl.class */
public class AlarmNoticeRecordPersonServiceImpl extends ServiceImpl<AlarmNoticeRecordPersonMapper, AlarmNoticeRecordPerson> implements IAlarmNoticeRecordPersonService {
    @Autowired
    private AlarmNoticeRecordPersonMapper alarmNoticeRecordPersonMapper;
    @Override // com.xrkc.job.service.IAlarmNoticeRecordPersonService
    public List<AlarmNoticeRecordPerson> getListByRecordId(Long recordId) {
        LambdaQueryWrapper<AlarmNoticeRecordPerson> wrapper = Wrappers.lambdaQuery(AlarmNoticeRecordPerson.class);
        wrapper.eq((v0) -> {
            return v0.getRecordId();
        }, recordId);
        wrapper.eq((v0) -> {
            return v0.getNoticeStatic();
        }, 20);
        return this.alarmNoticeRecordPersonMapper.selectList(wrapper);
    }
    @Override // com.xrkc.job.service.IAlarmNoticeRecordPersonService
    public void updateByRecordId(AlarmNoticeRecordPerson recordPerson) {
        this.alarmNoticeRecordPersonMapper.updateByRecordId(recordPerson);
    }
    @Override // com.xrkc.job.service.IAlarmNoticeRecordPersonService
    public void updateFinishByRecordId(Long recordId, String noticeResult) {
        this.alarmNoticeRecordPersonMapper.updateFinishByRecordId(recordId, noticeResult);
    }
}
