package com.xrkc.job.module.vehicle.service;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.xrkc.core.domain.vehicle.VehicleAlarm;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/module/vehicle/service/IVehicleAlarmService.class */
public interface IVehicleAlarmService {
    void batchInsert(List<VehicleAlarm> list);
    void batchCalcInsert(List<VehicleAlarm> list);
    List<VehicleAlarm> selectCurrentUnDisposeList();
    Long countUnDisposed();
    Map<Long, List<String>> selectUnDisposeAlarmTypeMap(List<Long> list);
    Map<String, Long> statisticsAlarmTypeMap(LocalDate localDate);
    Long insert(VehicleAlarm vehicleAlarm);
    LambdaQueryChainWrapper<VehicleAlarm> lambdaSelectList();
    Map<Long, Long> findUndisposedMap();
    List<Long> selectDisposedVehicleAlarmId(List<Long> list);
    List<VehicleAlarm> selectUnDisposedVehicleAlarmRail();
}
