package com.xrkc.job.service;
import com.xrkc.core.domain.rail.RailDrawType;
import com.xrkc.core.domain.statistics.StatisticsFacilityCurrentPersonList;
import com.xrkc.job.domain.AreaAlarmTempVO;
import com.xrkc.job.domain.PositionCurrent;
import com.xrkc.job.domain.PositionVO;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IPositionCurrentService.class */
public interface IPositionCurrentService {
    int deleteByOfflineBeacons(String str);
    int delDataByOfflineBeacons(String str);
    List<String> countCurrentPerson(String str, String str2, String str3);
    int countCurrentFacilityPerson(List<RailDrawType> list);
    int replaceVO(PositionVO positionVO);
    List<PositionCurrent> selectList();
    void notMovePosition(PositionVO positionVO);
    List<PositionCurrent> selectAlarmPerson(AreaAlarmTempVO areaAlarmTempVO);
    List<PositionCurrent> selectStopAlarmPerson(AreaAlarmTempVO areaAlarmTempVO, Long l);
    List<Long> selectStopAlarmPositionId(AreaAlarmTempVO areaAlarmTempVO, Long l);
    List<PositionCurrent> selectRailAlarmPerson(AreaAlarmTempVO areaAlarmTempVO);
    int deleteByOnlineMinute();
    List<PositionCurrent> selectAlarmPersonPosition(AreaAlarmTempVO areaAlarmTempVO);
    int offlineByCard(Long l, List<Long> list);
    List<StatisticsFacilityCurrentPersonList> getCurrentPersonListByRails(String str, String str2, String str3);
    List<Long> selectAllOnlinePersonId(List<Long> list);
}
