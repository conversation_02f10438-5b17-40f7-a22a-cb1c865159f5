package com.xrkc.job.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
import java.time.LocalDateTime;

@TableName("device_card_sender_log")
public class DeviceCardSenderLog implements Serializable {
    private static final long serialVersionUID = 1;

    @TableId("id")
    private Long id;

    @TableField("card_id")
    private Long cardId;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("person_id")
    private Long personId;

    @TableField("real_name")
    private String realName;

    @TableField("job_number")
    private String jobNumber;

    @TableField("person_photo")
    private String personPhoto;

    @TableField("dept_id")
    private Long deptId;

    @TableField("dept_name")
    private String deptName;

    @TableField("device_sn")
    private String deviceSn;

    @TableField("device_aims")
    private Integer deviceAims;

    @TableField("device_num")
    private Integer deviceNum;

    @TableField("card_sender_type")
    private Integer cardSenderType;

  //  @TableField(CacheOperationExpressionEvaluator.RESULT_VARIABLE)
    private String result;

    @TableField("remark")
    private String remark;

    @TableField(exist = false)
    private String personType;

    @TableField("command_time")
    private LocalDateTime commandTime;

    @TableField("closed_time")
    private LocalDateTime closedTime;

    @TableField("notify_status")
    private String notifyStatus;

    public void setId(Long id) {
        this.id = id;
    }

    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public void setPersonId(Long personId) {
        this.personId = personId;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public void setPersonPhoto(String personPhoto) {
        this.personPhoto = personPhoto;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public void setDeviceSn(String deviceSn) {
        this.deviceSn = deviceSn;
    }

    public void setDeviceAims(Integer deviceAims) {
        this.deviceAims = deviceAims;
    }

    public void setDeviceNum(Integer deviceNum) {
        this.deviceNum = deviceNum;
    }

    public void setCardSenderType(Integer cardSenderType) {
        this.cardSenderType = cardSenderType;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public void setPersonType(String personType) {
        this.personType = personType;
    }

    public void setCommandTime(LocalDateTime commandTime) {
        this.commandTime = commandTime;
    }

    public void setClosedTime(LocalDateTime closedTime) {
        this.closedTime = closedTime;
    }

    public void setNotifyStatus(String notifyStatus) {
        this.notifyStatus = notifyStatus;
    }

    public String toString() {
        return "DeviceCardSenderLog(id=" + getId() + ", cardId=" + getCardId() + ", createTime=" + getCreateTime() + ", personId=" + getPersonId() + ", realName=" + getRealName() + ", jobNumber=" + getJobNumber() + ", personPhoto=" + getPersonPhoto() + ", deptId=" + getDeptId() + ", deptName=" + getDeptName() + ", deviceSn=" + getDeviceSn() + ", deviceAims=" + getDeviceAims() + ", deviceNum=" + getDeviceNum() + ", cardSenderType=" + getCardSenderType() + ", result=" + getResult() + ", remark=" + getRemark() + ", personType=" + getPersonType() + ", commandTime=" + getCommandTime() + ", closedTime=" + getClosedTime() + ", notifyStatus=" + getNotifyStatus() + StringPool.RIGHT_BRACKET;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof DeviceCardSenderLog)) {
            return false;
        }
        DeviceCardSenderLog other = (DeviceCardSenderLog) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$id = getId();
        Object other$id = other.getId();
        if (this$id == null) {
            if (other$id != null) {
                return false;
            }
        } else if (!this$id.equals(other$id)) {
            return false;
        }
        Object this$cardId = getCardId();
        Object other$cardId = other.getCardId();
        if (this$cardId == null) {
            if (other$cardId != null) {
                return false;
            }
        } else if (!this$cardId.equals(other$cardId)) {
            return false;
        }
        Object this$personId = getPersonId();
        Object other$personId = other.getPersonId();
        if (this$personId == null) {
            if (other$personId != null) {
                return false;
            }
        } else if (!this$personId.equals(other$personId)) {
            return false;
        }
        Object this$deptId = getDeptId();
        Object other$deptId = other.getDeptId();
        if (this$deptId == null) {
            if (other$deptId != null) {
                return false;
            }
        } else if (!this$deptId.equals(other$deptId)) {
            return false;
        }
        Object this$deviceAims = getDeviceAims();
        Object other$deviceAims = other.getDeviceAims();
        if (this$deviceAims == null) {
            if (other$deviceAims != null) {
                return false;
            }
        } else if (!this$deviceAims.equals(other$deviceAims)) {
            return false;
        }
        Object this$deviceNum = getDeviceNum();
        Object other$deviceNum = other.getDeviceNum();
        if (this$deviceNum == null) {
            if (other$deviceNum != null) {
                return false;
            }
        } else if (!this$deviceNum.equals(other$deviceNum)) {
            return false;
        }
        Object this$cardSenderType = getCardSenderType();
        Object other$cardSenderType = other.getCardSenderType();
        if (this$cardSenderType == null) {
            if (other$cardSenderType != null) {
                return false;
            }
        } else if (!this$cardSenderType.equals(other$cardSenderType)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        if (this$createTime == null) {
            if (other$createTime != null) {
                return false;
            }
        } else if (!this$createTime.equals(other$createTime)) {
            return false;
        }
        Object this$realName = getRealName();
        Object other$realName = other.getRealName();
        if (this$realName == null) {
            if (other$realName != null) {
                return false;
            }
        } else if (!this$realName.equals(other$realName)) {
            return false;
        }
        Object this$jobNumber = getJobNumber();
        Object other$jobNumber = other.getJobNumber();
        if (this$jobNumber == null) {
            if (other$jobNumber != null) {
                return false;
            }
        } else if (!this$jobNumber.equals(other$jobNumber)) {
            return false;
        }
        Object this$personPhoto = getPersonPhoto();
        Object other$personPhoto = other.getPersonPhoto();
        if (this$personPhoto == null) {
            if (other$personPhoto != null) {
                return false;
            }
        } else if (!this$personPhoto.equals(other$personPhoto)) {
            return false;
        }
        Object this$deptName = getDeptName();
        Object other$deptName = other.getDeptName();
        if (this$deptName == null) {
            if (other$deptName != null) {
                return false;
            }
        } else if (!this$deptName.equals(other$deptName)) {
            return false;
        }
        Object this$deviceSn = getDeviceSn();
        Object other$deviceSn = other.getDeviceSn();
        if (this$deviceSn == null) {
            if (other$deviceSn != null) {
                return false;
            }
        } else if (!this$deviceSn.equals(other$deviceSn)) {
            return false;
        }
        Object this$result = getResult();
        Object other$result = other.getResult();
        if (this$result == null) {
            if (other$result != null) {
                return false;
            }
        } else if (!this$result.equals(other$result)) {
            return false;
        }
        Object this$remark = getRemark();
        Object other$remark = other.getRemark();
        if (this$remark == null) {
            if (other$remark != null) {
                return false;
            }
        } else if (!this$remark.equals(other$remark)) {
            return false;
        }
        Object this$personType = getPersonType();
        Object other$personType = other.getPersonType();
        if (this$personType == null) {
            if (other$personType != null) {
                return false;
            }
        } else if (!this$personType.equals(other$personType)) {
            return false;
        }
        Object this$commandTime = getCommandTime();
        Object other$commandTime = other.getCommandTime();
        if (this$commandTime == null) {
            if (other$commandTime != null) {
                return false;
            }
        } else if (!this$commandTime.equals(other$commandTime)) {
            return false;
        }
        Object this$closedTime = getClosedTime();
        Object other$closedTime = other.getClosedTime();
        if (this$closedTime == null) {
            if (other$closedTime != null) {
                return false;
            }
        } else if (!this$closedTime.equals(other$closedTime)) {
            return false;
        }
        Object this$notifyStatus = getNotifyStatus();
        Object other$notifyStatus = other.getNotifyStatus();
        return this$notifyStatus == null ? other$notifyStatus == null : this$notifyStatus.equals(other$notifyStatus);
    }

    protected boolean canEqual(Object other) {
        return other instanceof DeviceCardSenderLog;
    }

    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $cardId = getCardId();
        int result2 = (result * 59) + ($cardId == null ? 43 : $cardId.hashCode());
        Object $personId = getPersonId();
        int result3 = (result2 * 59) + ($personId == null ? 43 : $personId.hashCode());
        Object $deptId = getDeptId();
        int result4 = (result3 * 59) + ($deptId == null ? 43 : $deptId.hashCode());
        Object $deviceAims = getDeviceAims();
        int result5 = (result4 * 59) + ($deviceAims == null ? 43 : $deviceAims.hashCode());
        Object $deviceNum = getDeviceNum();
        int result6 = (result5 * 59) + ($deviceNum == null ? 43 : $deviceNum.hashCode());
        Object $cardSenderType = getCardSenderType();
        int result7 = (result6 * 59) + ($cardSenderType == null ? 43 : $cardSenderType.hashCode());
        Object $createTime = getCreateTime();
        int result8 = (result7 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $realName = getRealName();
        int result9 = (result8 * 59) + ($realName == null ? 43 : $realName.hashCode());
        Object $jobNumber = getJobNumber();
        int result10 = (result9 * 59) + ($jobNumber == null ? 43 : $jobNumber.hashCode());
        Object $personPhoto = getPersonPhoto();
        int result11 = (result10 * 59) + ($personPhoto == null ? 43 : $personPhoto.hashCode());
        Object $deptName = getDeptName();
        int result12 = (result11 * 59) + ($deptName == null ? 43 : $deptName.hashCode());
        Object $deviceSn = getDeviceSn();
        int result13 = (result12 * 59) + ($deviceSn == null ? 43 : $deviceSn.hashCode());
        Object $result = getResult();
        int result14 = (result13 * 59) + ($result == null ? 43 : $result.hashCode());
        Object $remark = getRemark();
        int result15 = (result14 * 59) + ($remark == null ? 43 : $remark.hashCode());
        Object $personType = getPersonType();
        int result16 = (result15 * 59) + ($personType == null ? 43 : $personType.hashCode());
        Object $commandTime = getCommandTime();
        int result17 = (result16 * 59) + ($commandTime == null ? 43 : $commandTime.hashCode());
        Object $closedTime = getClosedTime();
        int result18 = (result17 * 59) + ($closedTime == null ? 43 : $closedTime.hashCode());
        Object $notifyStatus = getNotifyStatus();
        return (result18 * 59) + ($notifyStatus == null ? 43 : $notifyStatus.hashCode());
    }

    public Long getId() {
        return this.id;
    }

    public Long getCardId() {
        return this.cardId;
    }

    public LocalDateTime getCreateTime() {
        return this.createTime;
    }

    public Long getPersonId() {
        return this.personId;
    }

    public String getRealName() {
        return this.realName;
    }

    public String getJobNumber() {
        return this.jobNumber;
    }

    public String getPersonPhoto() {
        return this.personPhoto;
    }

    public Long getDeptId() {
        return this.deptId;
    }

    public String getDeptName() {
        return this.deptName;
    }

    public String getDeviceSn() {
        return this.deviceSn;
    }

    public Integer getDeviceAims() {
        return this.deviceAims;
    }

    public Integer getDeviceNum() {
        return this.deviceNum;
    }

    public Integer getCardSenderType() {
        return this.cardSenderType;
    }

    public String getResult() {
        return this.result;
    }

    public String getRemark() {
        return this.remark;
    }

    public String getPersonType() {
        return this.personType;
    }

    public LocalDateTime getCommandTime() {
        return this.commandTime;
    }

    public LocalDateTime getClosedTime() {
        return this.closedTime;
    }

    public String getNotifyStatus() {
        return this.notifyStatus;
    }
}
