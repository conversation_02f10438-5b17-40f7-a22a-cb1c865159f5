#!/bin/bash

# 删除项目中剩余的 $deserializeLambda$ 方法

echo "开始删除项目中剩余的 \$deserializeLambda\$ 方法..."

# 统计删除的文件数量
total_count=0

# 查找所有包含 $deserializeLambda$ 方法的文件
files=$(find src/main/java -name "*.java" -exec grep -l "private static.*synthetic.*Object.*deserializeLambda" {} \;)

if [ -z "$files" ]; then
    echo "没有找到包含 \$deserializeLambda\$ 方法的文件"
    exit 0
fi

echo "找到以下文件需要删除 \$deserializeLambda\$ 方法："
echo "$files"
echo ""

# 遍历每个文件进行删除
for file in $files; do
    echo "正在删除文件: $file"
    
    # 备份原文件
    cp "$file" "$file.backup"
    
    # 删除 $deserializeLambda$ 方法
    # 使用 sed 删除从 "private static /* synthetic */ Object $deserializeLambda$(SerializedLambda lambda) {" 开始到下一个 "}" 结束的整个方法
    sed -i '' '/private static.*synthetic.*Object.*deserializeLambda/,/^    }$/d' "$file"
    
    # 删除相关的 import 语句
    sed -i '' '/import.*SerializedLambda/d' "$file"
    
    total_count=$((total_count + 1))
    echo "  - 删除完成"
done

echo ""
echo "批量删除完成！"
echo "总共删除了 $total_count 个文件中的 \$deserializeLambda\$ 方法"
echo ""
echo "删除的文件："
for file in $files; do
    echo "  - $file"
done
echo ""
echo "注意：原文件已备份为 .backup 文件，如需恢复请手动操作" 