package com.xrkc.job.util.direct;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/direct/Cluster.class */
public class Cluster implements Serializable {
    LatLngBounds latLngBounds;
    private Point centerPoint;
    private List<Point> points;
    private int count;
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/direct/Cluster$ClusterBuilder.class */
    public static class ClusterBuilder {
        private LatLngBounds latLngBounds;
        private Point centerPoint;
        private List<Point> points;
        private int count;
        ClusterBuilder() {
        }
        public ClusterBuilder latLngBounds(LatLngBounds latLngBounds) {
            this.latLngBounds = latLngBounds;
            return this;
        }
        public ClusterBuilder centerPoint(Point centerPoint) {
            this.centerPoint = centerPoint;
            return this;
        }
        public ClusterBuilder points(List<Point> points) {
            this.points = points;
            return this;
        }
        public ClusterBuilder count(int count) {
            this.count = count;
            return this;
        }
        public Cluster build() {
            return new Cluster(this.latLngBounds, this.centerPoint, this.points, this.count);
        }
        public String toString() {
            return "Cluster.ClusterBuilder(latLngBounds=" + this.latLngBounds + ", centerPoint=" + this.centerPoint + ", points=" + this.points + ", count=" + this.count + StringPool.RIGHT_BRACKET;
        }
    }
    public void setLatLngBounds(LatLngBounds latLngBounds) {
        this.latLngBounds = latLngBounds;
    }
    public void setCenterPoint(Point centerPoint) {
        this.centerPoint = centerPoint;
    }
    public void setPoints(List<Point> points) {
        this.points = points;
    }
    public void setCount(int count) {
        this.count = count;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof Cluster)) {
            return false;
        }
        Cluster other = (Cluster) o;
        if (!other.canEqual(this) || getCount() != other.getCount()) {
            return false;
        }
        Object this$latLngBounds = getLatLngBounds();
        Object other$latLngBounds = other.getLatLngBounds();
        if (this$latLngBounds == null) {
            if (other$latLngBounds != null) {
                return false;
            }
        } else if (!this$latLngBounds.equals(other$latLngBounds)) {
            return false;
        }
        Object this$centerPoint = getCenterPoint();
        Object other$centerPoint = other.getCenterPoint();
        if (this$centerPoint == null) {
            if (other$centerPoint != null) {
                return false;
            }
        } else if (!this$centerPoint.equals(other$centerPoint)) {
            return false;
        }
        Object this$points = getPoints();
        Object other$points = other.getPoints();
        return this$points == null ? other$points == null : this$points.equals(other$points);
    }
    protected boolean canEqual(Object other) {
        return other instanceof Cluster;
    }
    public int hashCode() {
        int result = (1 * 59) + getCount();
        Object $latLngBounds = getLatLngBounds();
        int result2 = (result * 59) + ($latLngBounds == null ? 43 : $latLngBounds.hashCode());
        Object $centerPoint = getCenterPoint();
        int result3 = (result2 * 59) + ($centerPoint == null ? 43 : $centerPoint.hashCode());
        Object $points = getPoints();
        return (result3 * 59) + ($points == null ? 43 : $points.hashCode());
    }
    public String toString() {
        return "Cluster(latLngBounds=" + getLatLngBounds() + ", centerPoint=" + getCenterPoint() + ", points=" + getPoints() + ", count=" + getCount() + StringPool.RIGHT_BRACKET;
    }
    Cluster(LatLngBounds latLngBounds, Point centerPoint, List<Point> points, int count) {
        this.latLngBounds = latLngBounds;
        this.centerPoint = centerPoint;
        this.points = points;
        this.count = count;
    }
    public static ClusterBuilder builder() {
        return new ClusterBuilder();
    }
    public LatLngBounds getLatLngBounds() {
        return this.latLngBounds;
    }
    public Point getCenterPoint() {
        return this.centerPoint;
    }
    public List<Point> getPoints() {
        return this.points;
    }
    public int getCount() {
        return this.count;
    }
    public void addCount() {
        this.count++;
    }
}
