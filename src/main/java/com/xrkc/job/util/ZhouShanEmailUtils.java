package com.xrkc.job.util;
import com.alibaba.fastjson2.JSONObject;
import java.net.URI;
import java.util.Arrays;
import microsoft.exchange.webservices.data.core.ExchangeService;
import microsoft.exchange.webservices.data.core.enumeration.misc.ExchangeVersion;
import microsoft.exchange.webservices.data.core.enumeration.property.BodyType;
import microsoft.exchange.webservices.data.core.service.item.EmailMessage;
import microsoft.exchange.webservices.data.credential.ExchangeCredentials;
import microsoft.exchange.webservices.data.credential.WebCredentials;
import microsoft.exchange.webservices.data.property.complex.MessageBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/ZhouShanEmailUtils.class */
public class ZhouShanEmailUtils {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) ZhouShanEmailUtils.class);
    public static JSONObject sendEmailBySinochem(String subject, String content, String to, String host, String userName, String password) {
        JSONObject js = new JSONObject();
        js.put("result", "N");
        js.put("bizMsg", "");
        try {
            ExchangeService service = new ExchangeService(ExchangeVersion.Exchange2010_SP2);
            ExchangeCredentials credentials = new WebCredentials(userName, password);
            service.setCredentials(credentials);
            service.setUrl(new URI(host));
            service.setTraceEnabled(true);
            EmailMessage msg = new EmailMessage(service);
            msg.setSubject(subject);
            MessageBody messageBody = new MessageBody(BodyType.Text, content);
            msg.setBody(messageBody);
            Arrays.asList(to.split(",")).forEach(recipients -> {
                try {
                    msg.getToRecipients().add(recipients);
                } catch (Exception e) {
                    log.error("添加邮件收件人失败：{}", recipients);
                }
            });
            msg.sendAndSaveCopy();
            js.put("result", "Y");
        } catch (Exception e) {
            log.error("邮件发送失败：{}", e.getMessage());
            js.put("bizMsg", e.getMessage().length() > 64 ? e.getMessage().substring(0, 64) : e.getMessage());
        }
        return js;
    }
    public static void main(String[] args) throws Exception {
        ExchangeService service = new ExchangeService(ExchangeVersion.Exchange2010_SP2);
        ExchangeCredentials credentials = new WebCredentials("<EMAIL>", "#wGYIg2Db3wPyMEEXmgU12345");
        service.setCredentials(credentials);
        service.setUrl(new URI("https://email.sinochem.com/EWS/Exchange.asmx"));
        service.setTraceEnabled(true);
        log.info("邮件地址：" + service.getUrl().toString());
        EmailMessage msg = new EmailMessage(service);
        msg.setSubject("物流港邮件发送测试");
        MessageBody messageBody = new MessageBody(BodyType.HTML, "<span data-v-670f8dae=''><p>储运子系统-数量管理-计量单管理-立式罐计量单<br></p><p>当测量信息输入模块下，输入水高为0mm时，水容积(m³)也应显示为0m³，但实际显示2.415m³。</p><p>与实际不符，建议修正</p><p>（内在逻辑为：检尺未检出有水，即认为储罐无水）</p><p><br></p></span>");
        msg.setBody(messageBody);
        msg.getAttachments().addFileAttachment("物流港测试附件.txt", "文件内容".getBytes());
        msg.getToRecipients().add("<EMAIL>");
        msg.getToRecipients().add("<EMAIL>");
        msg.sendAndSaveCopy();
    }
}
