package com.xrkc.job.mapper;
import com.xrkc.core.domain.person.Person;
import com.xrkc.core.domain.statistics.StatisticsDeptOnlineLog;
import com.xrkc.core.mybatisplus.BaseMapperX;
import com.xrkc.job.domain.FaceDispenserPersonVO;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
@Mapper
@Repository
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/PersonMapper.class */
public interface PersonMapper extends BaseMapperX<Person> {
    int updatePerson(Person person);
    List<FaceDispenserPersonVO> selectToFacePersonList();
    List<StatisticsDeptOnlineLog> findDeptPersonTotal();
    @Select({"select * from core_person where person_id=#{personId}"})
    Person getPersonById(@Param("personId") Long l);
    List<Person> getPersonByIds(@Param("list") List<Long> list);
    void unBindingCard(@Param("personId") Long l, @Param("cardId") Long l2, @Param("visitorStatus") String str);
    void unbindingCard(@Param("personId") Long l);
    List<Person> selectPersonList(Map<String, Object> map);
    int updateHikvisionPerson(Person person);
    int existByParams(Map<String, Object> map);
    Person findByPersonId(Map<String, Object> map);
    String findPersonName(@Param("personId") Long l);
    @Select({"select card_id from core_person where person_id=#{personId}"})
    Long findCardIdByPersonId(@Param("personId") Long l);
    void deleteByPersonId(@Param("personId") Long l);
    @Select({"select COUNT(*) from core_person where job_status='Y'"})
    int countOnJob();
    Person findPersonByDeptId(@Param("deptIds") List<Long> list, @Param("excludePersonIds") List<Long> list2, @Param("alarmPersonIds") List<Long> list3, @Param("postIds") List<Long> list4, @Param("parameterCondition") Boolean bool, @Param("minusSeconds") LocalDateTime localDateTime, @Param("configurationRelation") String str);
    List<Long> getEffectivePost(List<Long> list, @Param("deptIds") List<Long> list2);
    @Select({"SELECT person_id,email,phone FROM core_person WHERE email!='' OR phone!=''"})
    List<Person> selectPersonListFilter();
    @Select({"SELECT person_id,person_type,real_name,email,phone,sex FROM core_person"})
    List<Person> selectNoticeList();
}
