package com.xrkc.job.domain;
import java.io.Serializable;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/VehicleRail.class */
public class VehicleRail implements Serializable {
    private Integer vehicleRailId;
    private String vehicleRailName;
    private String vehicleRailScope;
    private String vehicleAlarmType;
    private Integer bindingVehicleCount;
    public Integer getVehicleRailId() {
        return this.vehicleRailId;
    }
    public void setVehicleRailId(Integer vehicleRailId) {
        this.vehicleRailId = vehicleRailId;
    }
    public String getVehicleRailName() {
        return this.vehicleRailName;
    }
    public void setVehicleRailName(String vehicleRailName) {
        this.vehicleRailName = vehicleRailName;
    }
    public String getVehicleRailScope() {
        return this.vehicleRailScope;
    }
    public void setVehicleRailScope(String vehicleRailScope) {
        this.vehicleRailScope = vehicleRailScope;
    }
    public String getVehicleAlarmType() {
        return this.vehicleAlarmType;
    }
    public void setVehicleAlarmType(String vehicleAlarmType) {
        this.vehicleAlarmType = vehicleAlarmType;
    }
    public Integer getBindingVehicleCount() {
        return this.bindingVehicleCount;
    }
    public void setBindingVehicleCount(Integer bindingVehicleCount) {
        this.bindingVehicleCount = bindingVehicleCount;
    }
}
