package com.xrkc.job.domain;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.time.LocalDateTime;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/InspectRecordLocation.class */
public class InspectRecordLocation implements Serializable {
    private static final long serialVersionUID = 1;
    private Long recordId;
    private Long roadId;
    private Long locationId;
    private String locationCode;
    private String locationName;
    private Integer locationSort;
    private String locationStatus;
    private String abnormalStatus;
    private LocalDateTime locationFinishTime;
    private LocalDateTime locationStartTime;
    private LocalDateTime inspectBeginTime;
    public Long getRoadId() {
        return this.roadId;
    }
    public void setRoadId(Long roadId) {
        this.roadId = roadId;
    }
    public Long getRecordId() {
        return this.recordId;
    }
    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }
    public Long getLocationId() {
        return this.locationId;
    }
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }
    public String getLocationCode() {
        return this.locationCode;
    }
    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }
    public String getLocationName() {
        return this.locationName;
    }
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }
    public Integer getLocationSort() {
        return this.locationSort;
    }
    public void setLocationSort(Integer locationSort) {
        this.locationSort = locationSort;
    }
    public String getLocationStatus() {
        return this.locationStatus;
    }
    public void setLocationStatus(String locationStatus) {
        this.locationStatus = locationStatus;
    }
    public String getAbnormalStatus() {
        return this.abnormalStatus;
    }
    public void setAbnormalStatus(String abnormalStatus) {
        this.abnormalStatus = abnormalStatus;
    }
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public LocalDateTime getLocationFinishTime() {
        return this.locationFinishTime;
    }
    public void setLocationFinishTime(LocalDateTime locationFinishTime) {
        this.locationFinishTime = locationFinishTime;
    }
    public LocalDateTime getLocationStartTime() {
        return this.locationStartTime;
    }
    public void setLocationStartTime(LocalDateTime locationStartTime) {
        this.locationStartTime = locationStartTime;
    }
    public LocalDateTime getInspectBeginTime() {
        return this.inspectBeginTime;
    }
    public void setInspectBeginTime(LocalDateTime inspectBeginTime) {
        this.inspectBeginTime = inspectBeginTime;
    }
}
