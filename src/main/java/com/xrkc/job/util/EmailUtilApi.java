package com.xrkc.job.util;
import com.alibaba.fastjson2.JSONObject;
import com.xrkc.core.domain.system.SystemApi;
import com.xrkc.core.enums.NoticeType;
import com.xrkc.job.domain.AlarmNoticeRecord;
import com.xrkc.job.domain.LogSms;
import com.xrkc.job.mapper.LogSmsMapper;
import com.xrkc.job.service.IAlarmNoticeRecordPersonService;
import com.xrkc.job.service.IAlarmNoticeRecordService;
import com.xrkc.job.task.MessageTask;
import com.xrkc.job.util.sm4.SM4Utils;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Properties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.MailException;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.stereotype.Component;
@Component
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/EmailUtilApi.class */
public class EmailUtilApi {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) EmailUtilApi.class);
    @Autowired
    private LogSmsMapper messageMapper;
    @Autowired
    private IAlarmNoticeRecordService alarmNoticeRecordService;
    @Autowired
    private IAlarmNoticeRecordPersonService alarmNoticeRecordPersonService;
    private static JavaMailSenderImpl mailSenderIml;
    public static void setInitData(String host, String username, String passwd, String apiPort) {
        mailSenderIml = new JavaMailSenderImpl();
        mailSenderIml.setHost(host);
        mailSenderIml.setUsername(username);
        mailSenderIml.setPassword(passwd);
        mailSenderIml.setPort(Integer.valueOf(apiPort).intValue());
        Properties javaMailProperties = new Properties();
        javaMailProperties.put("mail.smtp.auth", true);
        javaMailProperties.put("mail.smtp.starttls.enable", true);
        javaMailProperties.put("mail.smtp.timeout", 5000);
        javaMailProperties.setProperty("mail.smtp.socketFactory.fallback", "false");
        javaMailProperties.setProperty("mail.smtp.socketFactory.class", "com.sun.mail.util.MailSSLSocketFactory");
        mailSenderIml.setJavaMailProperties(javaMailProperties);
        log.info("报警邮件初始化成功");
    }
    public void sendMail(SystemApi systemApi, AlarmNoticeRecord alarmNoticeRecord, String source) {
        if (Objects.isNull(systemApi)) {
            log.error("未配置邮件发送");
            return;
        }
        if (org.apache.commons.lang3.StringUtils.isAnyBlank(systemApi.getHost(), systemApi.getUserName(), systemApi.getPassword())) {
            log.error("邮件配置缺少必要参数");
            return;
        }
        StringBuilder content = new StringBuilder("报警类型:").append(alarmNoticeRecord.getAlarmTypeName()).append("；楼层：").append(alarmNoticeRecord.getLayerId());
        if (MessageTask.source.equals(source)) {
            if (CommonUtil.areaAlarmType.contains(alarmNoticeRecord.getAlarmType())) {
                content.append("；报警区域名：").append(alarmNoticeRecord.getAreaName());
            } else {
                content.append("；姓名：").append(alarmNoticeRecord.getRealName());
                content.append("；部门：").append(alarmNoticeRecord.getDeptName());
            }
        } else {
            content.append("；报警区域名：").append(alarmNoticeRecord.getAreaName());
            content.append("；车辆名称：").append(alarmNoticeRecord.getVehicleName());
            content.append("；车牌号：").append(alarmNoticeRecord.getLicensePlateNumber());
            content.append("；司机姓名：").append(alarmNoticeRecord.getRealName());
        }
        content.append("；请及时处理");
        LogSms logSms = new LogSms();
        logSms.setAlarmId(alarmNoticeRecord.getAlarmId());
        logSms.setAlarmType(alarmNoticeRecord.getAlarmType());
        logSms.setAlarmTypeName(alarmNoticeRecord.getAlarmTypeName());
        logSms.setCreateTime(LocalDateTime.now());
        logSms.setPhoneNumbers(alarmNoticeRecord.getPhoneNumbers());
        logSms.setPhoneRealNames(alarmNoticeRecord.getPhoneRealNames());
        logSms.setContent(content.toString());
        logSms.setNoticeType(NoticeType.email.getKey());
        logSms.setSource(source);
        StringBuilder subject = new StringBuilder();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(systemApi.getBasicTopic())) {
            subject.append("【").append(systemApi.getBasicTopic()).append("】");
        }
        subject.append(source).append("触发").append(alarmNoticeRecord.getAlarmTypeName()).append(",请及时处理");
        JSONObject js = commonSend(subject.toString(), content.toString(), alarmNoticeRecord.getPhoneNumbers(), systemApi.getHost(), systemApi.getUserName(), systemApi.getPassword(), systemApi.getApiOrgSn());
        logSms.setResult(js.getString("result"));
        String bizMsg = js.getString("bizMsg");
        logSms.setBizMsg(bizMsg.length() > 64 ? bizMsg.substring(0, 64) : bizMsg);
        logSms.setSendTime(LocalDateTime.now());
        this.messageMapper.insert( logSms);
        alarmNoticeRecord.setRemark("OK");
        alarmNoticeRecord.setNoticeStatic("50");
        this.alarmNoticeRecordService.updateById(alarmNoticeRecord);
        this.alarmNoticeRecordPersonService.updateFinishByRecordId(alarmNoticeRecord.getRecordId(), "OK");
    }
    public JSONObject commonSend(String subject, String text, String to, String host, String userName, String password, String port) {
        JSONObject js = new JSONObject();
        js.put("result", "N");
        js.put("bizMsg", "");
        if (host.contains("sinochem")) {
            return ZhouShanEmailUtils.sendEmailBySinochem(subject, text, to, host, userName, password);
        }
        try {
            String password2 = SM4Utils.decryptData_CBC(password);
            if (org.apache.commons.lang3.StringUtils.isEmpty(password2)) {
                log.error("邮件密钥配置有误，请检查发送邮件api配置");
                js.put("bizMsg", "邮件密钥配置有误，请检查发送邮件api配置");
                return js;
            }
            js.put("result", send(subject, text, to, host, userName, password2, port));
            return js;
        } catch (Exception e) {
            log.info("邮件发送失败:{}", e.getMessage());
            log.error(e.getMessage(), (Throwable) e);
            js.put("bizMsg", "邮件发送失败：" + e.getMessage());
            return js;
        }
    }
    private String send(String subject, String text, String to, String host, String userName, String password, String port) throws MailException {
        initMailSend(host, userName, password, port);
        SimpleMailMessage message = new SimpleMailMessage();
        message.setFrom(userName);
        message.setTo(to.split(","));
        message.setSubject(subject);
        message.setText(text);
        mailSenderIml.send(message);
        return "Y";
    }
    private void initMailSend(String host, String userName, String password, String port) {
        if (Objects.isNull(mailSenderIml)) {
            setInitData(host, userName, password, port);
        }
        StringBuilder currentMailConfig = new StringBuilder();
        currentMailConfig.append(mailSenderIml.getHost()).append("_").append(mailSenderIml.getUsername()).append("_").append(mailSenderIml.getPassword()).append("_").append(mailSenderIml.getPort());
        StringBuilder sysMailConfig = new StringBuilder();
        sysMailConfig.append(host).append("_").append(userName).append("_").append(password).append("_").append(port);
        if (!sysMailConfig.toString().equals(currentMailConfig.toString())) {
            setInitData(host, userName, password, port);
        }
    }
}
