package com.xrkc.job.service.impl;
import cn.hutool.core.collection.CollUtil;
import com.xrkc.core.domain.statistics.StatisticsFacilityCurrentPersonList;
import com.xrkc.job.mapper.StatisticsFacilityCurrentPersonListMapper;
import com.xrkc.job.service.IStatisticsFacilityCurrentPersonListService;
import java.util.Collection;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/StatisticsFacilityCurrentPersonListServiceImpl.class */
public class StatisticsFacilityCurrentPersonListServiceImpl implements IStatisticsFacilityCurrentPersonListService {
    @Autowired
    private StatisticsFacilityCurrentPersonListMapper mapper;
    @Override // com.xrkc.job.service.IStatisticsFacilityCurrentPersonListService
    public void batchInsert(List<StatisticsFacilityCurrentPersonList> facilityCurrentPersonList) {
        this.mapper.deleteAll();
        if (CollUtil.isNotEmpty((Collection<?>) facilityCurrentPersonList)) {
            this.mapper.insertBatch(facilityCurrentPersonList);
        }
    }
    @Override // com.xrkc.job.service.IStatisticsFacilityCurrentPersonListService
    public void deleteAll() {
        this.mapper.deleteAll();
    }
}
