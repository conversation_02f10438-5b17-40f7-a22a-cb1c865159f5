package com.xrkc.job.util;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.file.FileNameUtil;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.redis.utils.RedisCacheUtils;
import java.awt.image.BufferedImage;
import java.awt.image.ImageObserver;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.OpenOption;
import java.nio.file.Paths;
import java.util.Base64;
import javax.imageio.ImageIO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/ImageCompressUtils.class */
public class ImageCompressUtils {
    private static final Logger log;
    private static final int faceDispenser_targetPixel = 800;
    private static final int byte_size = 1024;
    private static final int min_kb_size = 200;
    static final /* synthetic */ boolean $assertionsDisabled;
    static {
        $assertionsDisabled = !ImageCompressUtils.class.desiredAssertionStatus();
        log = LoggerFactory.getLogger((Class<?>) ImageCompressUtils.class);
    }
    public static void main(String[] args) {
        try {
            File originalImageFile = new File("D:/微信图片_20240615110006 - 副本 - 副本 - 副本.jpg");
            BufferedImage originalImage = ImageIO.read(originalImageFile);
            int currentWidth = originalImage.getWidth();
            int currentHeight = originalImage.getHeight();
            if (currentWidth > 800 || currentHeight > 800) {
                String originalName = FileNameUtil.getName(originalImageFile);
                String tempName = "temp_" + originalName;
                "D:/微信图片_20240615110006 - 副本 - 副本 - 副本.jpg".replace(originalName, tempName);
                BufferedImage compressedImage = compressImageSize(originalImage, 800, 800);
                ImageIO.write(compressedImage, "jpg", originalImageFile);
            }
            System.out.println("图片压缩成功！");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    public static String faceDispenserImgCompressToBase64(String personPhoto) throws IOException {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(personPhoto)) {
            String staticPath = RedisCacheUtils.getConfigRedisCache().get(ConfigValue.CONFIG_KEY_STATIC_PATH);
            if (org.apache.commons.lang3.StringUtils.isBlank(staticPath)) {
                log.error("请在[系统配置]中配置静态资源路径");
                return null;
            }
            String pathname = staticPath + personPhoto;
            File originalImageFile = new File(pathname);
            if (originalImageFile.exists()) {
                InputStream in = null;
                try {
                    try {
                        long fileSizeInKB = originalImageFile.length() / 1024;
                        if (fileSizeInKB > 200) {
                            try {
                                BufferedImage originalImage = ImageIO.read(originalImageFile);
                                int currentWidth = originalImage.getWidth();
                                int currentHeight = originalImage.getHeight();
                                if (currentWidth > 800 || currentHeight > 800) {
                                    String originalName = FileNameUtil.getName(originalImageFile);
                                    String tempName = "temp_" + originalName;
                                    String tempPathname = pathname.replace(originalName, tempName);
                                    new File(tempPathname);
                                    BufferedImage compressedImage = compressImageSize(originalImage, 800, 800);
                                    ImageIO.write(compressedImage, "jpg", originalImageFile);
                                }
                            } catch (Exception e) {
                                log.error("压缩图片失败：{}", e.getMessage());
                            }
                        }
                        in = Files.newInputStream(Paths.get(pathname, new String[0]), new OpenOption[0]);
                        byte[] data = new byte[in.available()];
                        in.read(data);
                        Base64.Encoder encoder = java.util.Base64.getEncoder();
                        String strEncodeToString = encoder.encodeToString(data);
                        if (!$assertionsDisabled && in == null) {
                            throw new AssertionError();
                        }
                        in.close();
                        return strEncodeToString;
                    } catch (IOException e2) {
                        log.error("[人脸机]图片压缩并获取base64失败,url:{},msg:：{}", personPhoto, e2.getMessage());
                        try {
                            if (!$assertionsDisabled && in == null) {
                                throw new AssertionError();
                            }
                            in.close();
                            return null;
                        } catch (Exception e3) {
                            return null;
                        }
                    }
                } catch (Throwable th) {
                    if (!$assertionsDisabled && in == null) {
                        throw new AssertionError();
                    }
                    in.close();
                    throw th;
                }
            }
            return null;
        }
        return null;
    }
    private static BufferedImage compressImageSize(BufferedImage image, int targetWidth, int targetHeight) throws IOException {
        int currentWidth = image.getWidth();
        int currentHeight = image.getHeight();
        double widthRatio = targetWidth / currentWidth;
        double heightRatio = targetHeight / currentHeight;
        double ratio = Math.min(widthRatio, heightRatio);
        int newWidth = (int) (currentWidth * ratio);
        int newHeight = (int) (currentHeight * ratio);
        if (currentWidth > currentHeight) {
            image = ImgUtil.toBufferedImage(ImgUtil.rotate(image, 90));
            newWidth = (int) (currentHeight * ratio);
            newHeight = (int) (currentWidth * ratio);
        }
        BufferedImage resizedImage = new BufferedImage(newWidth, newHeight, image.getType());
        resizedImage.createGraphics().drawImage(image.getScaledInstance(newWidth, newHeight, 4), 0, 0, (ImageObserver) null);
        return resizedImage;
    }
}
