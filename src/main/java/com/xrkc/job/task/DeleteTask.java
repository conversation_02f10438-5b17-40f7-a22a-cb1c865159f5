package com.xrkc.job.task;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xrkc.core.domain.alarm.CoreAlarmPeople;
import com.xrkc.core.domain.log.LogLogin;
import com.xrkc.core.domain.log.LogOperate;
import com.xrkc.core.domain.statistics.StatisticsLamps;
import com.xrkc.core.domain.vehicle.PositionHistoryCalendar;
import com.xrkc.core.domain.vehicle.VehicleHistoryCalendar;
import com.xrkc.job.domain.*;
import com.xrkc.job.module.log.service.LogLoginService;
import com.xrkc.job.module.log.service.LogOperateService;
import com.xrkc.job.module.vehicle.mapper.PositionHistoryCalendarMapper;
import com.xrkc.job.module.vehicle.mapper.VehicleHistoryCalendarMapper;
import com.xrkc.job.module.vehicle.service.IVehiclePositionCalcService;
import com.xrkc.job.service.IAlarmPeopleService;
import com.xrkc.job.service.IAlarmService;
import com.xrkc.job.service.ICoreAlarmLampsService;
import com.xrkc.job.service.IDeviceBeaconHistoryService;
import com.xrkc.job.service.IDeviceCardSenderLogService;
import com.xrkc.job.service.IDeviceCardSenderVehicleLogService;
import com.xrkc.job.service.IDeviceLampsStatusService;
import com.xrkc.job.service.IFacilityAccessRecordService;
import com.xrkc.job.service.IPositionCurrentService;
import com.xrkc.job.service.IPositionDataCalcService;
import com.xrkc.job.service.IPositionHistoryService;
import com.xrkc.job.service.IRssiService;
import com.xrkc.job.service.IStatisticsFacilityStayService;
import com.xrkc.job.service.IStatisticsLampsService;
import com.xrkc.job.service.ISystemJobLogService;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
@Component("deleteTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/DeleteTask.class */
public class DeleteTask {
    @Autowired
    private IPositionCurrentService positionCurrentService;
    @Autowired
    private ISystemJobLogService systemJobLogService;
    @Autowired
    private IRssiService rssiService;
    @Autowired
    private IPositionHistoryService positionHistoryService;
    @Autowired
    private IStatisticsFacilityStayService statisticsFacilityStayService;
    @Autowired
    private IDeviceLampsStatusService deviceLampsStatusService;
    @Autowired
    private IStatisticsLampsService statisticsLampsService;
    @Autowired
    private ICoreAlarmLampsService coreAlarmLampsService;
    @Autowired
    private IDeviceCardSenderLogService deviceCardSenderLogService;
    @Autowired
    private IDeviceCardSenderVehicleLogService deviceCardSenderVehicleLogService;
    @Autowired
    private IDeviceBeaconHistoryService deviceBeaconHistoryService;
    @Autowired
    private IAlarmService alarmService;
    @Autowired
    private IAlarmPeopleService alarmPeopleService;
    @Autowired
    private LogLoginService logLoginService;
    @Autowired
    private LogOperateService logOperateService;
    @Autowired
    private IFacilityAccessRecordService facilityAccessRecordService;
    @Autowired
    private PositionHistoryCalendarMapper positionHistoryCalendarMapper;
    @Autowired
    private VehicleHistoryCalendarMapper vehicleHistoryCalendarMapper;
    @Autowired
    private IPositionDataCalcService positionDataCalcService;
    @Autowired
    private IVehiclePositionCalcService vehiclePositionCalcService;
    /* JADX WARN: Multi-variable type inference failed */
    public void planDelete() {
        this.systemJobLogService.batchDelete("0", 30);
        this.systemJobLogService.batchDelete("1", 60);
        this.statisticsFacilityStayService.batchDelete(30);
        this.rssiService.batchDeleteBak(8);
        this.deviceLampsStatusService.remove( new LambdaQueryWrapper<DeviceLampsStatus>().le((v0) -> {
            return v0.getHeartTime();
        }, LocalDateTime.now().minusHours(4L)));
    }
    /* JADX WARN: Multi-variable type inference failed */
    public void weeHoursDelete() {
        this.systemJobLogService.truncateTable();
        this.positionCurrentService.deleteByOnlineMinute();
        List<String> tableNames = this.positionHistoryService.selectDeleteHistoryTable();
        if (!CollectionUtils.isEmpty(tableNames)) {
            tableNames.stream().forEach(tableName -> {
                this.positionHistoryService.deleteTable(tableName);
            });
        }
        this.deviceCardSenderLogService.batchDelete(31);
        this.deviceCardSenderVehicleLogService.remove((Wrapper) new LambdaQueryWrapper<DeviceCardSenderVehicleLog>().le((v0) -> {
            return v0.getCreateTime();
        }, LocalDateTime.now().minusDays(31L)));
        this.deviceBeaconHistoryService.batchDelete(180);
        this.alarmService.remove((Wrapper) new LambdaQueryWrapper<Alarm>().le((v0) -> {
            return v0.getAcceptTime();
        }, LocalDateTime.now().minusYears(1L)));
        this.alarmPeopleService.remove((Wrapper) new LambdaQueryWrapper<CoreAlarmPeople>().le((v0) -> {
            return v0.getAcceptTime();
        }, LocalDateTime.now().minusMonths(3L)));
        this.logLoginService.remove((Wrapper) new LambdaQueryWrapper<LogLogin>().le((v0) -> {
            return v0.getCreateTime();
        }, LocalDateTime.now().minusMonths(6L)));
        this.logOperateService.remove((Wrapper) new LambdaQueryWrapper<LogOperate>().le((v0) -> {
            return v0.getCreateTime();
        }, LocalDateTime.now().minusMonths(6L)));
        this.statisticsLampsService.remove((Wrapper) new LambdaQueryWrapper<StatisticsLamps>().le((v0) -> {
            return v0.getStatisticsDate();
        }, LocalDateTime.now().minusYears(1L)));
        this.coreAlarmLampsService.remove((Wrapper) new LambdaQueryWrapper<CoreAlarmLamps>().le((v0) -> {
            return v0.getAcceptTime();
        }, LocalDateTime.now().minusYears(1L)));
        this.facilityAccessRecordService.remove((Wrapper) new LambdaQueryWrapper<FacilityAccessRecord>().le((v0) -> {
            return v0.getAcceptTime();
        }, LocalDateTime.now().minusYears(1L)));
    }
    /* JADX WARN: Multi-variable type inference failed */
    public void weeHoursPlanDelete() {
        this.positionHistoryCalendarMapper.delete((Wrapper) new LambdaQueryWrapper<PositionHistoryCalendar>().lt((v0) -> {
            return v0.getDay();
        }, LocalDate.now().minusYears(1L)));
        this.vehicleHistoryCalendarMapper.delete((Wrapper) new LambdaQueryWrapper<VehicleHistoryCalendar>().lt((v0) -> {
            return v0.getDay();
        }, LocalDate.now().minusYears(1L)));
    }
    public void minutePlanDelete() {
        this.positionDataCalcService.batchDelete(60);
        this.vehiclePositionCalcService.delData();
    }
}
