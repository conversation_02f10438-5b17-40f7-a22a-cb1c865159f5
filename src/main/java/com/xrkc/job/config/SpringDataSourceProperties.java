package com.xrkc.job.config;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
@ConfigurationProperties(prefix = "spring.datasource")
@RefreshScope
@Configuration
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/config/SpringDataSourceProperties.class */
public class SpringDataSourceProperties {
    private String url;
    private String username;
    private String password;
    public String getUrl() {
        return this.url;
    }
    public void setUrl(String url) {
        this.url = url;
    }
    public String getUsername() {
        return this.username;
    }
    public void setUsername(String username) {
        this.username = username;
    }
    public String getPassword() {
        return this.password;
    }
    public void setPassword(String password) {
        this.password = password;
    }
}
