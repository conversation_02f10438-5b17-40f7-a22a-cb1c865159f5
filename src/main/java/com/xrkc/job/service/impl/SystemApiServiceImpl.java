package com.xrkc.job.service.impl;
import com.xrkc.core.domain.system.SystemApi;
import com.xrkc.job.mapper.SystemApiMapper;
import com.xrkc.job.service.SystemApiService;
import java.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/SystemApiServiceImpl.class */
public class SystemApiServiceImpl implements SystemApiService {
    @Autowired
    private SystemApiMapper systemApiMapper;
    @Override // com.xrkc.job.service.SystemApiService
    public SystemApi getByApiKey(String apiKey) {
        return this.systemApiMapper.selectOne(apiKey);
    }
    @Override // com.xrkc.job.service.SystemApiService
    public void updateByApiKey(LocalDateTime createTime, LocalDateTime updateTime, String orgIndexCode, Long apiId) {
        this.systemApiMapper.updateByApiKey(createTime, updateTime, orgIndexCode, apiId);
    }
    @Override // com.xrkc.job.service.SystemApiService
    public void updateEquipmentNumber(String password, Long apiId) {
        this.systemApiMapper.updateEquipmentNumber(password, apiId);
    }
    @Override // com.xrkc.job.service.SystemApiService
    public void updateTimeByApiKey(LocalDateTime updateTime, Long apiId) {
        this.systemApiMapper.updateTimeByApiKey(updateTime, apiId);
    }
}
