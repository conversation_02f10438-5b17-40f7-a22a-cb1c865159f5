package com.xrkc.job.config;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/config/PersonSynchronizerConstants.class */
public class PersonSynchronizerConstants {
    public static final String BASIS_PATH = "/artemis/api";
    public static final String ORG_ADD = "/artemis/api/resource/v1/org/single/add";
    public static final String PERSON_TO_ORG_ADD = "/artemis/api/resource/v1/person/single/add";
    public static final String FACE_CHECK = "/artemis/api/acs/v1/faceCheck";
    public static final String ADD_PESONS_PRIVILEGE = "/artemis/api/acs/v1/privilege/group/single/addPersons";
    public static final String SYNC_PERMISSIONS = "/artemis/api/visitor/v1/auth/reapplication";
    public static final String DELETE_PESONS_PRIVILEGE = "/artemis/api/acs/v1/privilege/group/single/deletePersons";
    public static final String PERSON_TO_ORG_update = "/artemis/api/resource/v1/person/single/update";
    public static final String updateFaceData = "/artemis/api/resource/v1/person/face/update";
    public static final String getFaceData = "/artemis/api/resource/v1/person/picture_data";
    public static final String ORG_NAME = "All Persons";
    public static final String havePermissionGroup = "1";
    public static final String notHavePermissionGroup = "0";
    public static final String parentIndexCode = "0";
    public static final int idNumberLength = 16;
    public static final int type = 1;
    public static final String historicalAccessRecord = "/artemis/api/acs/v1/door/events";
    public static final String notupdate = "notupdate";
    public static String acsDevIndexCode = "";
    public static final Integer pageSize = 100;
}
