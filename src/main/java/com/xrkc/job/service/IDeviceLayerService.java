package com.xrkc.job.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xrkc.job.domain.DeviceLayer;
import java.math.BigDecimal;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IDeviceLayerService.class */
public interface IDeviceLayerService extends IService<DeviceLayer> {
    DeviceLayer findOneLayer();
    DeviceLayer queryLayerExist(String str);
    void deleteByLayerId(String str);
    boolean queryLayerOneExist(String str);
    void updateByLayerId(DeviceLayer deviceLayer);
    Boolean inOneLayer(BigDecimal bigDecimal, BigDecimal bigDecimal2);
}
