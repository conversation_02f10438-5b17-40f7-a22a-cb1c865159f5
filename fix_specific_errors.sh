#!/bin/bash

# 精确修复项目中的具体编译错误

echo "开始精确修复项目中的编译错误..."

# 统计修复的文件数量
total_count=0

# 1. 修复 ApiUtils.java 中的 r1 变量问题
echo "1. 修复 ApiUtils.java 中的 r1 变量问题..."
if [ -f "src/main/java/com/xrkc/job/util/ApiUtils.java" ]; then
    echo "  - 修复文件: src/main/java/com/xrkc/job/util/ApiUtils.java"
    cp "src/main/java/com/xrkc/job/util/ApiUtils.java" "src/main/java/com/xrkc/job/util/ApiUtils.java.backup"
    
    # 修复 r1 变量为 result
    sed -i '' 's/r1/result/g' "src/main/java/com/xrkc/job/util/ApiUtils.java"
    
    total_count=$((total_count + 1))
    echo "    - 修复完成"
fi

# 2. 修复 StatisticsPersonFlowServiceImpl.java 中的 Mapper 参数错误
echo "2. 修复 StatisticsPersonFlowServiceImpl.java 中的 Mapper 参数错误..."
if [ -f "src/main/java/com/xrkc/job/service/impl/StatisticsPersonFlowServiceImpl.java" ]; then
    echo "  - 修复文件: src/main/java/com/xrkc/job/service/impl/StatisticsPersonFlowServiceImpl.java"
    cp "src/main/java/com/xrkc/job/service/impl/StatisticsPersonFlowServiceImpl.java" "src/main/java/com/xrkc/job/service/impl/StatisticsPersonFlowServiceImpl.java.backup"
    
    # 修复 insert(mapper) 为 insert(entity)
    sed -i '' 's/insert(statisticsPersonFlowMapper)/insert(statisticsPersonFlow)/g' "src/main/java/com/xrkc/job/service/impl/StatisticsPersonFlowServiceImpl.java"
    
    total_count=$((total_count + 1))
    echo "    - 修复完成"
fi

# 3. 修复 StatisticsDeptOnlineLogServiceImpl.java 中的 Mapper 参数错误
echo "3. 修复 StatisticsDeptOnlineLogServiceImpl.java 中的 Mapper 参数错误..."
if [ -f "src/main/java/com/xrkc/job/service/impl/StatisticsDeptOnlineLogServiceImpl.java" ]; then
    echo "  - 修复文件: src/main/java/com/xrkc/job/service/impl/StatisticsDeptOnlineLogServiceImpl.java"
    cp "src/main/java/com/xrkc/job/service/impl/StatisticsDeptOnlineLogServiceImpl.java" "src/main/java/com/xrkc/job/service/impl/StatisticsDeptOnlineLogServiceImpl.java.backup"
    
    # 修复 insert(mapper) 为 insert(entity)
    sed -i '' 's/insert(statisticsDeptOnlineLogMapper)/insert(statisticsDeptOnlineLog)/g' "src/main/java/com/xrkc/job/service/impl/StatisticsDeptOnlineLogServiceImpl.java"
    sed -i '' 's/updateById(statisticsDeptOnlineLogMapper)/updateById(statisticsDeptOnlineLog)/g' "src/main/java/com/xrkc/job/service/impl/StatisticsDeptOnlineLogServiceImpl.java"
    
    total_count=$((total_count + 1))
    echo "    - 修复完成"
fi

# 4. 修复 AlarmServiceImpl.java 中的 Mapper 参数错误
echo "4. 修复 AlarmServiceImpl.java 中的 Mapper 参数错误..."
if [ -f "src/main/java/com/xrkc/job/service/impl/AlarmServiceImpl.java" ]; then
    echo "  - 修复文件: src/main/java/com/xrkc/job/service/impl/AlarmServiceImpl.java"
    cp "src/main/java/com/xrkc/job/service/impl/AlarmServiceImpl.java" "src/main/java/com/xrkc/job/service/impl/AlarmServiceImpl.java.backup"
    
    # 修复 insert(mapper) 为 insert(entity)
    sed -i '' 's/insert(alarmMapper)/insert(alarm)/g' "src/main/java/com/xrkc/job/service/impl/AlarmServiceImpl.java"
    sed -i '' 's/updateById(alarmMapper)/updateById(alarm)/g' "src/main/java/com/xrkc/job/service/impl/AlarmServiceImpl.java"
    
    total_count=$((total_count + 1))
    echo "    - 修复完成"
fi

# 5. 修复 EmailUtilApi.java 中的 Mapper 参数错误
echo "5. 修复 EmailUtilApi.java 中的 Mapper 参数错误..."
if [ -f "src/main/java/com/xrkc/job/util/EmailUtilApi.java" ]; then
    echo "  - 修复文件: src/main/java/com/xrkc/job/util/EmailUtilApi.java"
    cp "src/main/java/com/xrkc/job/util/EmailUtilApi.java" "src/main/java/com/xrkc/job/util/EmailUtilApi.java.backup"
    
    # 修复 insert(mapper) 为 insert(entity)
    sed -i '' 's/insert(logSmsMapper)/insert(logSms)/g' "src/main/java/com/xrkc/job/util/EmailUtilApi.java"
    
    total_count=$((total_count + 1))
    echo "    - 修复完成"
fi

# 6. 修复 SystemJobLogServiceImpl.java 中的 Mapper 参数错误
echo "6. 修复 SystemJobLogServiceImpl.java 中的 Mapper 参数错误..."
if [ -f "src/main/java/com/xrkc/job/service/impl/SystemJobLogServiceImpl.java" ]; then
    echo "  - 修复文件: src/main/java/com/xrkc/job/service/impl/SystemJobLogServiceImpl.java"
    cp "src/main/java/com/xrkc/job/service/impl/SystemJobLogServiceImpl.java" "src/main/java/com/xrkc/job/service/impl/SystemJobLogServiceImpl.java.backup"
    
    # 修复 insert(mapper) 为 insert(entity)
    sed -i '' 's/insert(systemJobLogMapper)/insert(systemJobLog)/g' "src/main/java/com/xrkc/job/service/impl/SystemJobLogServiceImpl.java"
    
    total_count=$((total_count + 1))
    echo "    - 修复完成"
fi

# 7. 修复 PersonServiceImpl.java 中的 Mapper 参数错误
echo "7. 修复 PersonServiceImpl.java 中的 Mapper 参数错误..."
if [ -f "src/main/java/com/xrkc/job/service/impl/PersonServiceImpl.java" ]; then
    echo "  - 修复文件: src/main/java/com/xrkc/job/service/impl/PersonServiceImpl.java"
    cp "src/main/java/com/xrkc/job/service/impl/PersonServiceImpl.java" "src/main/java/com/xrkc/job/service/impl/PersonServiceImpl.java.backup"
    
    # 修复 insert(mapper) 为 insert(entity)
    sed -i '' 's/insert(personMapper)/insert(person)/g' "src/main/java/com/xrkc/job/service/impl/PersonServiceImpl.java"
    
    total_count=$((total_count + 1))
    echo "    - 修复完成"
fi

# 8. 修复 PositionCurrentServiceImpl.java 中的 Mapper 参数错误
echo "8. 修复 PositionCurrentServiceImpl.java 中的 Mapper 参数错误..."
if [ -f "src/main/java/com/xrkc/job/service/impl/PositionCurrentServiceImpl.java" ]; then
    echo "  - 修复文件: src/main/java/com/xrkc/job/service/impl/PositionCurrentServiceImpl.java"
    cp "src/main/java/com/xrkc/job/service/impl/PositionCurrentServiceImpl.java" "src/main/java/com/xrkc/job/service/impl/PositionCurrentServiceImpl.java.backup"
    
    # 修复 insert(mapper) 为 insert(entity)
    sed -i '' 's/insert(positionCurrentMapper)/insert(positionCurrent)/g' "src/main/java/com/xrkc/job/service/impl/PositionCurrentServiceImpl.java"
    
    total_count=$((total_count + 1))
    echo "    - 修复完成"
fi

# 9. 修复 AlarmNoticeRecordServiceImpl.java 中的 Mapper 参数错误
echo "9. 修复 AlarmNoticeRecordServiceImpl.java 中的 Mapper 参数错误..."
if [ -f "src/main/java/com/xrkc/job/service/impl/AlarmNoticeRecordServiceImpl.java" ]; then
    echo "  - 修复文件: src/main/java/com/xrkc/job/service/impl/AlarmNoticeRecordServiceImpl.java"
    cp "src/main/java/com/xrkc/job/service/impl/AlarmNoticeRecordServiceImpl.java" "src/main/java/com/xrkc/job/service/impl/AlarmNoticeRecordServiceImpl.java.backup"
    
    # 修复 insert(mapper) 为 insert(entity)
    sed -i '' 's/insert(alarmNoticeRecordMapper)/insert(alarmNoticeRecord)/g' "src/main/java/com/xrkc/job/service/impl/AlarmNoticeRecordServiceImpl.java"
    sed -i '' 's/updateById(alarmNoticeRecordMapper)/updateById(alarmNoticeRecord)/g' "src/main/java/com/xrkc/job/service/impl/AlarmNoticeRecordServiceImpl.java"
    
    total_count=$((total_count + 1))
    echo "    - 修复完成"
fi

# 10. 修复 StatisticsTask.java 中的 Mapper 参数错误
echo "10. 修复 StatisticsTask.java 中的 Mapper 参数错误..."
if [ -f "src/main/java/com/xrkc/job/task/StatisticsTask.java" ]; then
    echo "  - 修复文件: src/main/java/com/xrkc/job/task/StatisticsTask.java"
    cp "src/main/java/com/xrkc/job/task/StatisticsTask.java" "src/main/java/com/xrkc/job/task/StatisticsTask.java.backup"
    
    # 修复 insert(mapper) 为 insert(entity)
    sed -i '' 's/insert(statisticsAlarmDisposeMapper)/insert(statisticsAlarmDispose)/g' "src/main/java/com/xrkc/job/task/StatisticsTask.java"
    sed -i '' 's/updateById(statisticsAlarmDisposeMapper)/updateById(statisticsAlarmDispose)/g' "src/main/java/com/xrkc/job/task/StatisticsTask.java"
    
    total_count=$((total_count + 1))
    echo "    - 修复完成"
fi

# 11. 修复 CertificationNoticeTask.java 中的类名问题
echo "11. 修复 CertificationNoticeTask.java 中的类名问题..."
if [ -f "src/main/java/com/xrkc/job/task/CertificationNoticeTask.java" ]; then
    echo "  - 修复文件: src/main/java/com/xrkc/job/task/CertificationNoticeTask.java"
    cp "src/main/java/com/xrkc/job/task/CertificationNoticeTask.java" "src/main/java/com/xrkc/job/task/CertificationNoticeTask.java.backup"
    
    # 修复 CertificationNoticeObj 为 CertificationNotice
    sed -i '' 's/CertificationNoticeObj/CertificationNotice/g' "src/main/java/com/xrkc/job/task/CertificationNoticeTask.java"
    
    total_count=$((total_count + 1))
    echo "    - 修复完成"
fi

# 12. 修复 DeviceCardSenderPersonServiceImpl.java 中的 lambdaQueryChain 问题
echo "12. 修复 DeviceCardSenderPersonServiceImpl.java 中的 lambdaQueryChain 问题..."
if [ -f "src/main/java/com/xrkc/job/service/impl/DeviceCardSenderPersonServiceImpl.java" ]; then
    echo "  - 修复文件: src/main/java/com/xrkc/job/service/impl/DeviceCardSenderPersonServiceImpl.java"
    cp "src/main/java/com/xrkc/job/service/impl/DeviceCardSenderPersonServiceImpl.java" "src/main/java/com/xrkc/job/service/impl/DeviceCardSenderPersonServiceImpl.java.backup"
    
    # 修复 lambdaQueryChain 调用
    sed -i '' 's/lambdaQueryChain(\([^,]*\),\([^)]*\))/lambdaQueryChain(\1,\2.class)/g' "src/main/java/com/xrkc/job/service/impl/DeviceCardSenderPersonServiceImpl.java"
    
    total_count=$((total_count + 1))
    echo "    - 修复完成"
fi

echo ""
echo "精确修复完成！"
echo "总共修复了 $total_count 个文件"
echo ""
echo "修复内容："
echo "1. 修复了 ApiUtils.java 中的 r1 变量问题"
echo "2. 修复了 StatisticsPersonFlowServiceImpl.java 中的 Mapper 参数错误"
echo "3. 修复了 StatisticsDeptOnlineLogServiceImpl.java 中的 Mapper 参数错误"
echo "4. 修复了 AlarmServiceImpl.java 中的 Mapper 参数错误"
echo "5. 修复了 EmailUtilApi.java 中的 Mapper 参数错误"
echo "6. 修复了 SystemJobLogServiceImpl.java 中的 Mapper 参数错误"
echo "7. 修复了 PersonServiceImpl.java 中的 Mapper 参数错误"
echo "8. 修复了 PositionCurrentServiceImpl.java 中的 Mapper 参数错误"
echo "9. 修复了 AlarmNoticeRecordServiceImpl.java 中的 Mapper 参数错误"
echo "10. 修复了 StatisticsTask.java 中的 Mapper 参数错误"
echo "11. 修复了 CertificationNoticeTask.java 中的类名问题"
echo "12. 修复了 DeviceCardSenderPersonServiceImpl.java 中的 lambdaQueryChain 问题"
echo ""
echo "注意：所有修复都基于具体的编译错误，已备份原文件" 