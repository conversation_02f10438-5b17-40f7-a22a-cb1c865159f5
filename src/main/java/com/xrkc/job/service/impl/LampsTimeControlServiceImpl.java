package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.job.domain.LampsTimeControl;
import com.xrkc.job.mapper.LampsTimeControlMapper;
import com.xrkc.job.service.ILampsTimeControlService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/LampsTimeControlServiceImpl.class */
public class LampsTimeControlServiceImpl extends ServiceImpl<LampsTimeControlMapper, LampsTimeControl> implements ILampsTimeControlService {
    @Autowired
    private LampsTimeControlMapper lampsTimeControlMapper;
    @Override // com.xrkc.job.service.ILampsTimeControlService
    public List<Long> findGroupIdByTimeId(Long lampsTimeId) {
        return this.lampsTimeControlMapper.findGroupIdByTimeId(lampsTimeId);
    }
}
