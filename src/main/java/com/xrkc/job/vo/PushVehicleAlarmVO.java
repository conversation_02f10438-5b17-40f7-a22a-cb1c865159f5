package com.xrkc.job.vo;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.math.BigDecimal;
import java.time.LocalDateTime;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/vo/PushVehicleAlarmVO.class */
public class PushVehicleAlarmVO {
    private Long id;
    private Long alarmId;
    private String alarmStatus;
    private LocalDateTime alarmTime;
    private String alarmType;
    private String alarmTypeName;
    private String carNo;
    private BigDecimal longitude;
    private BigDecimal latitude;
    private String remark;
    private Integer speed;
    private Integer dir;
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/vo/PushVehicleAlarmVO$PushVehicleAlarmVOBuilder.class */
    public static class PushVehicleAlarmVOBuilder {
        private Long id;
        private Long alarmId;
        private String alarmStatus;
        private LocalDateTime alarmTime;
        private String alarmType;
        private String alarmTypeName;
        private String carNo;
        private BigDecimal longitude;
        private BigDecimal latitude;
        private String remark;
        private Integer speed;
        private Integer dir;
        PushVehicleAlarmVOBuilder() {
        }
        public PushVehicleAlarmVOBuilder id(Long id) {
            this.id = id;
            return this;
        }
        public PushVehicleAlarmVOBuilder alarmId(Long alarmId) {
            this.alarmId = alarmId;
            return this;
        }
        public PushVehicleAlarmVOBuilder alarmStatus(String alarmStatus) {
            this.alarmStatus = alarmStatus;
            return this;
        }
        public PushVehicleAlarmVOBuilder alarmTime(LocalDateTime alarmTime) {
            this.alarmTime = alarmTime;
            return this;
        }
        public PushVehicleAlarmVOBuilder alarmType(String alarmType) {
            this.alarmType = alarmType;
            return this;
        }
        public PushVehicleAlarmVOBuilder alarmTypeName(String alarmTypeName) {
            this.alarmTypeName = alarmTypeName;
            return this;
        }
        public PushVehicleAlarmVOBuilder carNo(String carNo) {
            this.carNo = carNo;
            return this;
        }
        public PushVehicleAlarmVOBuilder longitude(BigDecimal longitude) {
            this.longitude = longitude;
            return this;
        }
        public PushVehicleAlarmVOBuilder latitude(BigDecimal latitude) {
            this.latitude = latitude;
            return this;
        }
        public PushVehicleAlarmVOBuilder remark(String remark) {
            this.remark = remark;
            return this;
        }
        public PushVehicleAlarmVOBuilder speed(Integer speed) {
            this.speed = speed;
            return this;
        }
        public PushVehicleAlarmVOBuilder dir(Integer dir) {
            this.dir = dir;
            return this;
        }
        public PushVehicleAlarmVO build() {
            return new PushVehicleAlarmVO(this.id, this.alarmId, this.alarmStatus, this.alarmTime, this.alarmType, this.alarmTypeName, this.carNo, this.longitude, this.latitude, this.remark, this.speed, this.dir);
        }
        public String toString() {
            return "PushVehicleAlarmVO.PushVehicleAlarmVOBuilder(id=" + this.id + ", alarmId=" + this.alarmId + ", alarmStatus=" + this.alarmStatus + ", alarmTime=" + this.alarmTime + ", alarmType=" + this.alarmType + ", alarmTypeName=" + this.alarmTypeName + ", carNo=" + this.carNo + ", longitude=" + this.longitude + ", latitude=" + this.latitude + ", remark=" + this.remark + ", speed=" + this.speed + ", dir=" + this.dir + StringPool.RIGHT_BRACKET;
        }
    }
    public void setId(Long id) {
        this.id = id;
    }
    public void setAlarmId(Long alarmId) {
        this.alarmId = alarmId;
    }
    public void setAlarmStatus(String alarmStatus) {
        this.alarmStatus = alarmStatus;
    }
    public void setAlarmTime(LocalDateTime alarmTime) {
        this.alarmTime = alarmTime;
    }
    public void setAlarmType(String alarmType) {
        this.alarmType = alarmType;
    }
    public void setAlarmTypeName(String alarmTypeName) {
        this.alarmTypeName = alarmTypeName;
    }
    public void setCarNo(String carNo) {
        this.carNo = carNo;
    }
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }
    public void setSpeed(Integer speed) {
        this.speed = speed;
    }
    public void setDir(Integer dir) {
        this.dir = dir;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof PushVehicleAlarmVO)) {
            return false;
        }
        PushVehicleAlarmVO other = (PushVehicleAlarmVO) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$id = getId();
        Object other$id = other.getId();
        if (this$id == null) {
            if (other$id != null) {
                return false;
            }
        } else if (!this$id.equals(other$id)) {
            return false;
        }
        Object this$alarmId = getAlarmId();
        Object other$alarmId = other.getAlarmId();
        if (this$alarmId == null) {
            if (other$alarmId != null) {
                return false;
            }
        } else if (!this$alarmId.equals(other$alarmId)) {
            return false;
        }
        Object this$speed = getSpeed();
        Object other$speed = other.getSpeed();
        if (this$speed == null) {
            if (other$speed != null) {
                return false;
            }
        } else if (!this$speed.equals(other$speed)) {
            return false;
        }
        Object this$dir = getDir();
        Object other$dir = other.getDir();
        if (this$dir == null) {
            if (other$dir != null) {
                return false;
            }
        } else if (!this$dir.equals(other$dir)) {
            return false;
        }
        Object this$alarmStatus = getAlarmStatus();
        Object other$alarmStatus = other.getAlarmStatus();
        if (this$alarmStatus == null) {
            if (other$alarmStatus != null) {
                return false;
            }
        } else if (!this$alarmStatus.equals(other$alarmStatus)) {
            return false;
        }
        Object this$alarmTime = getAlarmTime();
        Object other$alarmTime = other.getAlarmTime();
        if (this$alarmTime == null) {
            if (other$alarmTime != null) {
                return false;
            }
        } else if (!this$alarmTime.equals(other$alarmTime)) {
            return false;
        }
        Object this$alarmType = getAlarmType();
        Object other$alarmType = other.getAlarmType();
        if (this$alarmType == null) {
            if (other$alarmType != null) {
                return false;
            }
        } else if (!this$alarmType.equals(other$alarmType)) {
            return false;
        }
        Object this$alarmTypeName = getAlarmTypeName();
        Object other$alarmTypeName = other.getAlarmTypeName();
        if (this$alarmTypeName == null) {
            if (other$alarmTypeName != null) {
                return false;
            }
        } else if (!this$alarmTypeName.equals(other$alarmTypeName)) {
            return false;
        }
        Object this$carNo = getCarNo();
        Object other$carNo = other.getCarNo();
        if (this$carNo == null) {
            if (other$carNo != null) {
                return false;
            }
        } else if (!this$carNo.equals(other$carNo)) {
            return false;
        }
        Object this$longitude = getLongitude();
        Object other$longitude = other.getLongitude();
        if (this$longitude == null) {
            if (other$longitude != null) {
                return false;
            }
        } else if (!this$longitude.equals(other$longitude)) {
            return false;
        }
        Object this$latitude = getLatitude();
        Object other$latitude = other.getLatitude();
        if (this$latitude == null) {
            if (other$latitude != null) {
                return false;
            }
        } else if (!this$latitude.equals(other$latitude)) {
            return false;
        }
        Object this$remark = getRemark();
        Object other$remark = other.getRemark();
        return this$remark == null ? other$remark == null : this$remark.equals(other$remark);
    }
    protected boolean canEqual(Object other) {
        return other instanceof PushVehicleAlarmVO;
    }
    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $alarmId = getAlarmId();
        int result2 = (result * 59) + ($alarmId == null ? 43 : $alarmId.hashCode());
        Object $speed = getSpeed();
        int result3 = (result2 * 59) + ($speed == null ? 43 : $speed.hashCode());
        Object $dir = getDir();
        int result4 = (result3 * 59) + ($dir == null ? 43 : $dir.hashCode());
        Object $alarmStatus = getAlarmStatus();
        int result5 = (result4 * 59) + ($alarmStatus == null ? 43 : $alarmStatus.hashCode());
        Object $alarmTime = getAlarmTime();
        int result6 = (result5 * 59) + ($alarmTime == null ? 43 : $alarmTime.hashCode());
        Object $alarmType = getAlarmType();
        int result7 = (result6 * 59) + ($alarmType == null ? 43 : $alarmType.hashCode());
        Object $alarmTypeName = getAlarmTypeName();
        int result8 = (result7 * 59) + ($alarmTypeName == null ? 43 : $alarmTypeName.hashCode());
        Object $carNo = getCarNo();
        int result9 = (result8 * 59) + ($carNo == null ? 43 : $carNo.hashCode());
        Object $longitude = getLongitude();
        int result10 = (result9 * 59) + ($longitude == null ? 43 : $longitude.hashCode());
        Object $latitude = getLatitude();
        int result11 = (result10 * 59) + ($latitude == null ? 43 : $latitude.hashCode());
        Object $remark = getRemark();
        return (result11 * 59) + ($remark == null ? 43 : $remark.hashCode());
    }
    public String toString() {
        return "PushVehicleAlarmVO(id=" + getId() + ", alarmId=" + getAlarmId() + ", alarmStatus=" + getAlarmStatus() + ", alarmTime=" + getAlarmTime() + ", alarmType=" + getAlarmType() + ", alarmTypeName=" + getAlarmTypeName() + ", carNo=" + getCarNo() + ", longitude=" + getLongitude() + ", latitude=" + getLatitude() + ", remark=" + getRemark() + ", speed=" + getSpeed() + ", dir=" + getDir() + StringPool.RIGHT_BRACKET;
    }
    PushVehicleAlarmVO(Long id, Long alarmId, String alarmStatus, LocalDateTime alarmTime, String alarmType, String alarmTypeName, String carNo, BigDecimal longitude, BigDecimal latitude, String remark, Integer speed, Integer dir) {
        this.id = id;
        this.alarmId = alarmId;
        this.alarmStatus = alarmStatus;
        this.alarmTime = alarmTime;
        this.alarmType = alarmType;
        this.alarmTypeName = alarmTypeName;
        this.carNo = carNo;
        this.longitude = longitude;
        this.latitude = latitude;
        this.remark = remark;
        this.speed = speed;
        this.dir = dir;
    }
    public static PushVehicleAlarmVOBuilder builder() {
        return new PushVehicleAlarmVOBuilder();
    }
    public Long getId() {
        return this.id;
    }
    public Long getAlarmId() {
        return this.alarmId;
    }
    public String getAlarmStatus() {
        return this.alarmStatus;
    }
    public LocalDateTime getAlarmTime() {
        return this.alarmTime;
    }
    public String getAlarmType() {
        return this.alarmType;
    }
    public String getAlarmTypeName() {
        return this.alarmTypeName;
    }
    public String getCarNo() {
        return this.carNo;
    }
    public BigDecimal getLongitude() {
        return this.longitude;
    }
    public BigDecimal getLatitude() {
        return this.latitude;
    }
    public String getRemark() {
        return this.remark;
    }
    public Integer getSpeed() {
        return this.speed;
    }
    public Integer getDir() {
        return this.dir;
    }
}
