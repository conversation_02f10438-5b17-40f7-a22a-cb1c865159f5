package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.job.domain.DeviceCard;
import com.xrkc.job.mapper.DeviceCardMapper;
import com.xrkc.job.service.IDeviceCardService;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/DeviceCardServiceImpl.class */
public class DeviceCardServiceImpl extends ServiceImpl<DeviceCardMapper, DeviceCard> implements IDeviceCardService {
    @Autowired
    private DeviceCardMapper deviceCardMapper;
    @Override // com.xrkc.job.service.IDeviceCardService
    public List<DeviceCard> selectLowPowerCard() {
        return this.deviceCardMapper.selectLowPowerCard();
    }
    @Override // com.xrkc.job.service.IDeviceCardService
    public List<DeviceCard> selectForUDP() {
        return this.deviceCardMapper.selectForUDP();
    }
    @Override // com.xrkc.job.service.IDeviceCardService
    public void updateUseStatus(Long cardId, String useStatus) {
        this.deviceCardMapper.updateUseStatus(cardId, useStatus);
    }
    @Override // com.xrkc.job.service.IDeviceCardService
    public int updateUseStatus(Long cardId, String useStatus, List<Long> cardIds) {
        Map<String, Object> query = new HashMap<>();
        query.put("cardId", cardId);
        query.put("useStatus", useStatus);
        query.put("cardIds", cardIds);
        return this.deviceCardMapper.updateUseStatus2(query);
    }
    @Override // com.xrkc.job.service.IDeviceCardService
    public void resetCardHeart() {
        this.deviceCardMapper.resetCardHeart();
    }
    @Override // com.xrkc.job.service.IDeviceCardService
    public DeviceCard findByCardId(Long cardId) {
        if (Objects.nonNull(cardId)) {
            return this.deviceCardMapper.findByCardId(cardId);
        }
        return null;
    }
    @Override // com.xrkc.job.service.IDeviceCardService
    public boolean getExistByCardIdEqPersonId(Long cardId, Long personId) {
        Long existPersonId = this.deviceCardMapper.getPersonId(cardId);
        if (existPersonId == null || Objects.equals(existPersonId, personId)) {
            return false;
        }
        return true;
    }
    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.xrkc.job.service.IDeviceCardService
    public List<DeviceCard> selectListByCardIdList(List<Long> cardIdList) {
        if (CollectionUtils.isEmpty(cardIdList)) {
            return null;
        }
        return this.deviceCardMapper.selectList((Wrapper) new LambdaQueryWrapper<DeviceCard>().in((LambdaQueryWrapper) (v0) -> {
            return v0.getCardId();
        }, (Collection<?>) cardIdList));
    }
    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.xrkc.job.service.IDeviceCardService
    public List<Long> selectCardIdByRssi(String cardEnable) {
        return (List) this.deviceCardMapper.selectList((Wrapper) new LambdaQueryWrapper<DeviceCard>().eq((v0) -> {
            return v0.getCardEnable();
        }, cardEnable)).stream().map((v0) -> {
            return v0.getCardId();
        }).collect(Collectors.toList());
    }
}
