#!/bin/bash

# 修复剩余的编译错误

echo "开始修复剩余的编译错误..."

# 统计修复的文件数量
total_count=0

# 1. 修复 ApiUtils.java 中的 result 变量问题
echo "1. 修复 ApiUtils.java 中的 result 变量问题..."
if [ -f "src/main/java/com/xrkc/job/util/ApiUtils.java" ]; then
    echo "  - 修复文件: src/main/java/com/xrkc/job/util/ApiUtils.java"
    cp "src/main/java/com/xrkc/job/util/ApiUtils.java" "src/main/java/com/xrkc/job/util/ApiUtils.java.backup2"
    
    # 查看文件内容，找到具体的变量名
    grep -n "result" "src/main/java/com/xrkc/job/util/ApiUtils.java"
    
    # 修复变量名问题
    sed -i '' 's/result/r1/g' "src/main/java/com/xrkc/job/util/ApiUtils.java"
    
    total_count=$((total_count + 1))
    echo "    - 修复完成"
fi

# 2. 修复 VehiclePositionCurrentServiceImpl.java 中的 LambdaQueryWrapperX 问题
echo "2. 修复 VehiclePositionCurrentServiceImpl.java 中的 LambdaQueryWrapperX 问题..."
if [ -f "src/main/java/com/xrkc/job/module/vehicle/service/impl/VehiclePositionCurrentServiceImpl.java" ]; then
    echo "  - 修复文件: src/main/java/com/xrkc/job/module/vehicle/service/impl/VehiclePositionCurrentServiceImpl.java"
    cp "src/main/java/com/xrkc/job/module/vehicle/service/impl/VehiclePositionCurrentServiceImpl.java" "src/main/java/com/xrkc/job/module/vehicle/service/impl/VehiclePositionCurrentServiceImpl.java.backup2"
    
    # 修复 LambdaQueryWrapperX 的使用
    sed -i '' 's/new LambdaQueryWrapperX<VehiclePositionCurrent>().in((LambdaQueryWrapperX<VehiclePositionCurrent>) (v0) -> {/new LambdaQueryWrapperX<VehiclePositionCurrent>().in(VehiclePositionCurrent::getVehicleId, v0 -> {/g' "src/main/java/com/xrkc/job/module/vehicle/service/impl/VehiclePositionCurrentServiceImpl.java"
    
    total_count=$((total_count + 1))
    echo "    - 修复完成"
fi

# 3. 修复 DeviceCardSenderPersonServiceImpl.java 中的 lambdaQueryChain 问题
echo "3. 修复 DeviceCardSenderPersonServiceImpl.java 中的 lambdaQueryChain 问题..."
if [ -f "src/main/java/com/xrkc/job/service/impl/DeviceCardSenderPersonServiceImpl.java" ]; then
    echo "  - 修复文件: src/main/java/com/xrkc/job/service/impl/DeviceCardSenderPersonServiceImpl.java"
    cp "src/main/java/com/xrkc/job/service/impl/DeviceCardSenderPersonServiceImpl.java" "src/main/java/com/xrkc/job/service/impl/DeviceCardSenderPersonServiceImpl.java.backup2"
    
    # 修复 lambdaQueryChain 调用
    sed -i '' 's/ChainWrappers.lambdaQueryChain((BaseMapper) this.deviceCardSenderPersonMapper, CardDispenserPerson.class);/ChainWrappers.lambdaQueryChain(this.deviceCardSenderPersonMapper);/g' "src/main/java/com/xrkc/job/service/impl/DeviceCardSenderPersonServiceImpl.java"
    
    total_count=$((total_count + 1))
    echo "    - 修复完成"
fi

# 4. 修复 VehiclePushAlarmTask.java 中的泛型问题
echo "4. 修复 VehiclePushAlarmTask.java 中的泛型问题..."
if [ -f "src/main/java/com/xrkc/job/task/VehiclePushAlarmTask.java" ]; then
    echo "  - 修复文件: src/main/java/com/xrkc/job/task/VehiclePushAlarmTask.java"
    cp "src/main/java/com/xrkc/job/task/VehiclePushAlarmTask.java" "src/main/java/com/xrkc/job/task/VehiclePushAlarmTask.java.backup2"
    
    # 修复泛型 T 的问题
    sed -i '' 's/List<T>/List<Object>/g' "src/main/java/com/xrkc/job/task/VehiclePushAlarmTask.java"
    
    total_count=$((total_count + 1))
    echo "    - 修复完成"
fi

# 5. 修复 PositionTask.java 中的 getDay() 方法问题
echo "5. 修复 PositionTask.java 中的 getDay() 方法问题..."
if [ -f "src/main/java/com/xrkc/job/task/PositionTask.java" ]; then
    echo "  - 修复文件: src/main/java/com/xrkc/job/task/PositionTask.java"
    cp "src/main/java/com/xrkc/job/task/PositionTask.java" "src/main/java/com/xrkc/job/task/PositionTask.java.backup2"
    
    # 查看第252行附近的内容
    sed -n '250,255p' "src/main/java/com/xrkc/job/task/PositionTask.java"
    
    # 修复 getDay() 方法调用
    sed -i '' 's/v0.getDay()/v0.getDate()/g' "src/main/java/com/xrkc/job/task/PositionTask.java"
    
    total_count=$((total_count + 1))
    echo "    - 修复完成"
fi

# 6. 修复 VehiclePositionHistoryServiceImpl.java 中的类型问题
echo "6. 修复 VehiclePositionHistoryServiceImpl.java 中的类型问题..."
if [ -f "src/main/java/com/xrkc/job/module/vehicle/service/impl/VehiclePositionHistoryServiceImpl.java" ]; then
    echo "  - 修复文件: src/main/java/com/xrkc/job/module/vehicle/service/impl/VehiclePositionHistoryServiceImpl.java"
    cp "src/main/java/com/xrkc/job/module/vehicle/service/impl/VehiclePositionHistoryServiceImpl.java" "src/main/java/com/xrkc/job/module/vehicle/service/impl/VehiclePositionHistoryServiceImpl.java.backup2"
    
    # 修复 Object 类型的方法调用
    sed -i '' 's/f3.getCardId()/((VehiclePositionHistory)f3).getCardId()/g' "src/main/java/com/xrkc/job/module/vehicle/service/impl/VehiclePositionHistoryServiceImpl.java"
    sed -i '' 's/f3.getAcceptTime()/((VehiclePositionHistory)f3).getAcceptTime()/g' "src/main/java/com/xrkc/job/module/vehicle/service/impl/VehiclePositionHistoryServiceImpl.java"
    sed -i '' 's/f3.getVehicleId()/((VehiclePositionHistory)f3).getVehicleId()/g' "src/main/java/com/xrkc/job/module/vehicle/service/impl/VehiclePositionHistoryServiceImpl.java"
    sed -i '' 's/f4.getCardId()/((VehiclePositionHistory)f4).getCardId()/g' "src/main/java/com/xrkc/job/module/vehicle/service/impl/VehiclePositionHistoryServiceImpl.java"
    sed -i '' 's/f4.getAcceptTime()/((VehiclePositionHistory)f4).getAcceptTime()/g' "src/main/java/com/xrkc/job/module/vehicle/service/impl/VehiclePositionHistoryServiceImpl.java"
    sed -i '' 's/f4.getVehicleId()/((VehiclePositionHistory)f4).getVehicleId()/g' "src/main/java/com/xrkc/job/module/vehicle/service/impl/VehiclePositionHistoryServiceImpl.java"
    
    total_count=$((total_count + 1))
    echo "    - 修复完成"
fi

# 7. 修复 PushLocationTask.java 中的类型问题
echo "7. 修复 PushLocationTask.java 中的类型问题..."
if [ -f "src/main/java/com/xrkc/job/task/PushLocationTask.java" ]; then
    echo "  - 修复文件: src/main/java/com/xrkc/job/task/PushLocationTask.java"
    cp "src/main/java/com/xrkc/job/task/PushLocationTask.java" "src/main/java/com/xrkc/job/task/PushLocationTask.java.backup2"
    
    # 修复 Object 类型的方法调用
    sed -i '' 's/v0.getPersonId()/((Person)v0).getPersonId()/g' "src/main/java/com/xrkc/job/task/PushLocationTask.java"
    
    total_count=$((total_count + 1))
    echo "    - 修复完成"
fi

# 8. 修复 InspectStatisticTask.java 中的类型问题
echo "8. 修复 InspectStatisticTask.java 中的类型问题..."
if [ -f "src/main/java/com/xrkc/job/task/InspectStatisticTask.java" ]; then
    echo "  - 修复文件: src/main/java/com/xrkc/job/task/InspectStatisticTask.java"
    cp "src/main/java/com/xrkc/job/task/InspectStatisticTask.java" "src/main/java/com/xrkc/job/task/InspectStatisticTask.java.backup2"
    
    # 修复 Object 类型的方法调用
    sed -i '' 's/v0.getAbarbeitungId()/((HiddenDangerAbarbeitung)v0).getAbarbeitungId()/g' "src/main/java/com/xrkc/job/task/InspectStatisticTask.java"
    
    total_count=$((total_count + 1))
    echo "    - 修复完成"
fi

# 9. 修复 CertificationNoticeTask.java 中的类型问题
echo "9. 修复 CertificationNoticeTask.java 中的类型问题..."
if [ -f "src/main/java/com/xrkc/job/task/CertificationNoticeTask.java" ]; then
    echo "  - 修复文件: src/main/java/com/xrkc/job/task/CertificationNoticeTask.java"
    cp "src/main/java/com/xrkc/job/task/CertificationNoticeTask.java" "src/main/java/com/xrkc/job/task/CertificationNoticeTask.java.backup2"
    
    # 修复 Object 类型的方法调用
    sed -i '' 's/v0.getPersonId()/((CertificationNotice)v0).getPersonId()/g' "src/main/java/com/xrkc/job/task/CertificationNoticeTask.java"
    
    total_count=$((total_count + 1))
    echo "    - 修复完成"
fi

# 10. 修复 StatisticsTask.java 中的类型问题
echo "10. 修复 StatisticsTask.java 中的类型问题..."
if [ -f "src/main/java/com/xrkc/job/task/StatisticsTask.java" ]; then
    echo "  - 修复文件: src/main/java/com/xrkc/job/task/StatisticsTask.java"
    cp "src/main/java/com/xrkc/job/task/StatisticsTask.java" "src/main/java/com/xrkc/job/task/StatisticsTask.java.backup2"
    
    # 修复 Object 类型的方法调用
    sed -i '' 's/v0.getPersonId()/((Person)v0).getPersonId()/g' "src/main/java/com/xrkc/job/task/StatisticsTask.java"
    sed -i '' 's/facilityRailListChild.removeIf/((List)facilityRailListChild).removeIf/g' "src/main/java/com/xrkc/job/task/StatisticsTask.java"
    sed -i '' 's/CollectionUtils.isEmpty(facilityRailListChild)/CollectionUtils.isEmpty((Collection)facilityRailListChild)/g' "src/main/java/com/xrkc/job/task/StatisticsTask.java"
    sed -i '' 's/facilityRailListChild.stream()/((List)facilityRailListChild).stream()/g' "src/main/java/com/xrkc/job/task/StatisticsTask.java"
    sed -i '' 's/facilityAccessRecord.getFacilityId()/((FacilityAccessRecord)facilityAccessRecord).getFacilityId()/g' "src/main/java/com/xrkc/job/task/StatisticsTask.java"
    sed -i '' 's/facilityAccessRecord.getPersonId()/((FacilityAccessRecord)facilityAccessRecord).getPersonId()/g' "src/main/java/com/xrkc/job/task/StatisticsTask.java"
    sed -i '' 's/facilityAccessRecord.getAcceptTime()/((FacilityAccessRecord)facilityAccessRecord).getAcceptTime()/g' "src/main/java/com/xrkc/job/task/StatisticsTask.java"
    sed -i '' 's/facilityAccessRecord.getRecordId()/((FacilityAccessRecord)facilityAccessRecord).getRecordId()/g' "src/main/java/com/xrkc/job/task/StatisticsTask.java"
    
    total_count=$((total_count + 1))
    echo "    - 修复完成"
fi

echo ""
echo "剩余错误修复完成！"
echo "总共修复了 $total_count 个文件"
echo ""
echo "修复内容："
echo "1. 修复了 ApiUtils.java 中的 result 变量问题"
echo "2. 修复了 VehiclePositionCurrentServiceImpl.java 中的 LambdaQueryWrapperX 问题"
echo "3. 修复了 DeviceCardSenderPersonServiceImpl.java 中的 lambdaQueryChain 问题"
echo "4. 修复了 VehiclePushAlarmTask.java 中的泛型问题"
echo "5. 修复了 PositionTask.java 中的 getDay() 方法问题"
echo "6. 修复了 VehiclePositionHistoryServiceImpl.java 中的类型问题"
echo "7. 修复了 PushLocationTask.java 中的类型问题"
echo "8. 修复了 InspectStatisticTask.java 中的类型问题"
echo "9. 修复了 CertificationNoticeTask.java 中的类型问题"
echo "10. 修复了 StatisticsTask.java 中的类型问题"
echo ""
echo "注意：所有修复都基于具体的编译错误，已备份原文件" 