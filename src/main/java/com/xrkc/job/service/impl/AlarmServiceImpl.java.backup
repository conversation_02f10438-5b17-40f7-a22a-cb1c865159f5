package com.xrkc.job.service.impl;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.job.domain.Alarm;
import com.xrkc.job.mapper.AlarmMapper;
import com.xrkc.job.service.IAlarmPeopleService;
import com.xrkc.job.service.IAlarmService;
import com.xrkc.redis.utils.RedisCacheUtils;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/AlarmServiceImpl.class */
public class AlarmServiceImpl extends ServiceImpl<AlarmMapper, Alarm> implements IAlarmService {
    @Autowired
    private AlarmMapper alarmMapper;
    @Autowired
    private IAlarmPeopleService alarmPeopleService;
    @Override // com.xrkc.job.service.IAlarmService
    public int insertAlarm(Alarm alarm) {
        Map<String, Object> map = new HashMap<>();
        map.put("alarmType", alarm.getAlarmType());
        map.put("areaId", alarm.getAreaId());
        map.put("cardId", alarm.getCardId());
        map.put("endTime", LocalDateTime.now());
        alarm.setAlarmStatus("15");
        alarm.setCreateTime(LocalDateTime.now());
        if (existAlarmByParams(map)) {
            return 0;
        }
        return this.alarmMapper.insert((AlarmMapper) alarm);
    }
    @Override // com.xrkc.job.service.IAlarmService
    public int insertAreaAlarm(Alarm alarm) {
        Map<String, Object> map = new HashMap<>();
        map.put("alarmType", alarm.getAlarmType());
        map.put("areaId", alarm.getAreaId());
        alarm.setAlarmStatus("15");
        alarm.setCreateTime(LocalDateTime.now());
        if (existAlarmByParams(map)) {
            return 0;
        }
        return this.alarmMapper.insert((AlarmMapper) alarm);
    }
    @Override // com.xrkc.job.service.IAlarmService
    public void insert50AreaAlarm(Alarm alarm) {
        Map<String, Object> map = new HashMap<>();
        map.put("alarmType", alarm.getAlarmType());
        map.put("areaId", alarm.getAreaId());
        alarm.setAlarmStatus("15");
        alarm.setCreateTime(LocalDateTime.now());
        Alarm oldAlarm = getAlarmIdByParams(map);
        if (Objects.isNull(oldAlarm)) {
            this.alarmMapper.insert((AlarmMapper) alarm);
            return;
        }
        if (!oldAlarm.getAlarmDesc().equals(alarm.getAlarmDesc())) {
            oldAlarm.setAlarmDesc(alarm.getAlarmDesc());
            this.alarmMapper.updateById((AlarmMapper) oldAlarm);
        }
        alarm.setAlarmId(oldAlarm.getAlarmId());
    }
    public Alarm getAlarmIdByParams(Map<String, Object> map) {
        Map<String, String> configMap = RedisCacheUtils.getConfigRedisCache();
        String alarm_monitor_interval_type = configMap.get(ConfigValue.CONFIG_KEY_ALARM_MONITOR_INTERVAL_TYPE);
        if (ConfigValue.DEFAULT_ALARM_INTERVAL_TYPE_2.equals(alarm_monitor_interval_type)) {
            map.put("alarmStatus", "15");
        }
        String alarm_monitor_interval = configMap.get("alarm_monitor_interval_" + map.get("alarmType"));
        Integer intervalSecond = Integer.valueOf(StringUtils.isNotBlank(alarm_monitor_interval) ? Integer.parseInt(alarm_monitor_interval) * 60 : ConfigValue.DEFAULT_ALARM_INTERVAL.intValue() * 60);
        LocalDateTime minusSeconds = LocalDateTime.now().minusSeconds(intervalSecond.intValue());
        map.put("minusSeconds", minusSeconds);
        return this.alarmMapper.getAlarmIdByParams(map);
    }
    @Override // com.xrkc.job.service.IAlarmService
    public Alarm selectOne(Long alarmId) {
        return this.alarmMapper.selectById(alarmId);
    }
    @Override // com.xrkc.job.service.IAlarmService
    public List<Alarm> selectUnDisposedAlarm(Integer intervalSecond) {
        LocalDateTime seconds = LocalDateTime.now();
        if (!ObjectUtil.isEmpty(intervalSecond)) {
            seconds = seconds.minusSeconds(intervalSecond.intValue());
        }
        return this.alarmMapper.selectUnDisposedAlarm(intervalSecond, seconds);
    }
    @Override // com.xrkc.job.service.IAlarmService
    public List<Alarm> statisticsByAlarm(Map<String, Object> map) {
        return this.alarmMapper.statisticsByAlarm(map);
    }
    @Override // com.xrkc.job.service.IAlarmService
    public List<Alarm> statisticsAlarmArea(Map<String, Object> map) {
        return this.alarmMapper.statisticsAlarmArea(map);
    }
    @Override // com.xrkc.job.service.IAlarmService
    public List<Alarm> selectUnDisposedAlarmCard(List<Long> list) {
        return this.alarmMapper.selectUnDisposedAlarmCard(list);
    }
    @Override // com.xrkc.job.service.IAlarmService
    public boolean existAlarmByParams(Map<String, Object> map) {
        Map<String, String> configMap = RedisCacheUtils.getConfigRedisCache();
        String alarm_monitor_interval_type = configMap.get(ConfigValue.CONFIG_KEY_ALARM_MONITOR_INTERVAL_TYPE);
        if (ConfigValue.DEFAULT_ALARM_INTERVAL_TYPE_2.toString().equals(alarm_monitor_interval_type)) {
            map.put("alarmStatus", "15");
        }
        String alarm_monitor_interval = configMap.get("alarm_monitor_interval_" + map.get("alarmType"));
        Integer intervalSecond = Integer.valueOf(StringUtils.isNotBlank(alarm_monitor_interval) ? Integer.parseInt(alarm_monitor_interval) * 60 : ConfigValue.DEFAULT_ALARM_INTERVAL.intValue() * 60);
        LocalDateTime minusSeconds = LocalDateTime.now().minusSeconds(intervalSecond.intValue());
        map.put("minusSeconds", minusSeconds);
        int i = this.alarmMapper.existAlarmByParams(map);
        return i > 0;
    }
    @Override // com.xrkc.job.service.IAlarmService
    public List<Alarm> selectList(Map<String, Object> map) {
        Integer disposeTimeInterval = (Integer) map.get("disposeTimeInterval");
        LocalDateTime disposeMinutes = LocalDateTime.now().minusMinutes(disposeTimeInterval.intValue());
        map.put("disposeMinutes", disposeMinutes);
        return this.alarmMapper.selectCustomList(map);
    }
    @Override // com.xrkc.job.service.IAlarmService
    public List<Alarm> selectUnDisposedAlarmRail() {
        LocalDateTime seconds = LocalDateTime.now().minusSeconds(60L);
        return this.alarmMapper.selectUnDisposedAlarmRail(seconds);
    }
    @Override // com.xrkc.job.service.IAlarmService
    public List<Alarm> selectUnDisposedAlarmSOS() {
        LocalDateTime seconds = LocalDateTime.now().minusSeconds(60L);
        return this.alarmMapper.selectUnDisposedAlarmSOS(seconds);
    }
    @Override // com.xrkc.job.service.IAlarmService
    public List<Long> selectDisposedAlarmId(List<Long> alarmIds) {
        return this.alarmMapper.selectDisposedAlarmId(alarmIds);
    }
    @Override // com.xrkc.job.service.IAlarmService
    public void closeAlarmEndTime(String alarmType, Map<Long, Set<Long>> map, Set<Long> closeAreaAlarm, Set<Long> perSonIds) {
        LocalDateTime closeAlarmEndTime;
        closeAlarmEndTime = LocalDateTime.now();
        switch (alarmType) {
            case "50":
                close50_AlarmEndTime(alarmType, map, closeAreaAlarm, closeAlarmEndTime);
                break;
            case "40":
                close40_AlarmEndTime(alarmType, map, closeAreaAlarm, closeAlarmEndTime);
                break;
            case "10":
            case "20":
            case "30":
            case "70":
            case "90":
                close10_20AlarmEndTime(alarmType, map, closeAreaAlarm, closeAlarmEndTime);
                break;
            case "3":
                close3AlarmEndTime(alarmType, perSonIds, closeAlarmEndTime);
                break;
            case "80":
                close80AlarmEndTime(alarmType, closeAreaAlarm, perSonIds, closeAlarmEndTime);
                break;
            case "81":
                close81AlarmEndTime(perSonIds, closeAlarmEndTime);
                break;
            case "keep_50":
                close50_KeepAlarmEndTime(map, closeAreaAlarm, closeAlarmEndTime);
                break;
        }
    }
    public void close50_KeepAlarmEndTime(Map<Long, Set<Long>> map, Set<Long> closeRailAlarm, LocalDateTime time) {
        if (CollUtil.isNotEmpty(map)) {
            this.alarmPeopleService.updatepeopleAlarm(map, "50", time);
            this.alarmMapper.closeAlarmEndTime("50", time);
        }
        if (!CollUtil.isEmpty((Collection<?>) closeRailAlarm)) {
            Integer count = this.alarmMapper.selectClose50_KeepAlarmEndTime(closeRailAlarm, "50");
            if (Objects.nonNull(count) && count.intValue() > 0) {
                this.alarmMapper.close50_KeepAlarmEndTime(closeRailAlarm, "50", time);
            }
        }
    }
    public void close50_AlarmEndTime(String alarmType, Map<Long, Set<Long>> map, Set<Long> closeAreaAlarm, LocalDateTime time) {
        if (CollUtil.isNotEmpty(map)) {
            this.alarmPeopleService.updatepeopleAlarm(map, alarmType, time);
            this.alarmMapper.closeAlarmEndTime(alarmType, time);
        }
        Integer count = this.alarmMapper.selectClose50_AlarmEndTime(closeAreaAlarm, alarmType);
        if (Objects.nonNull(count) && count.intValue() > 0) {
            this.alarmMapper.close50_AlarmEndTime(closeAreaAlarm, alarmType, time);
        }
    }
    public void close40_AlarmEndTime(String alarmType, Map<Long, Set<Long>> map, Set<Long> closeAreaAlarm, LocalDateTime time) {
        if (CollUtil.isNotEmpty(map)) {
            this.alarmPeopleService.updatepeopleAlarm(map, alarmType, time);
            this.alarmMapper.closeAlarmEndTime(alarmType, time);
        }
        if (CollUtil.isNotEmpty((Collection<?>) closeAreaAlarm)) {
            this.alarmMapper.close40_AlarmEndTime(closeAreaAlarm, alarmType, time);
        }
    }
    public void close10_20AlarmEndTime(String alarmType, Map<Long, Set<Long>> map, Set<Long> closeAreaAlarm, LocalDateTime time) {
        if (CollUtil.isNotEmpty(map)) {
            map.forEach((areaId, list) -> {
                if (CollUtil.isNotEmpty((Collection<?>) list)) {
                    this.alarmMapper.close10_20_AlarmEndTime(alarmType, areaId, list, time);
                }
            });
        }
        Integer count = this.alarmMapper.selectClose10_20_AlarmEndTimeByAreaId(alarmType, closeAreaAlarm);
        if (Objects.nonNull(count) && count.intValue() > 0) {
            this.alarmMapper.close10_20_AlarmEndTimeByAreaId(alarmType, closeAreaAlarm, time);
        }
    }
    public void close3AlarmEndTime(String alarmType, Set<Long> lastCurrentPesonSets, LocalDateTime time) {
        Integer count = this.alarmMapper.select3AlarmEndTimeByAreaId(alarmType, lastCurrentPesonSets);
        if (Objects.nonNull(count) && count.intValue() > 0) {
            this.alarmMapper.close3AlarmEndTimeByAreaId(alarmType, lastCurrentPesonSets, time);
        }
    }
    public void close80AlarmEndTime(String alarmType, Set<Long> closeAreaAlarmId, Set<Long> closeAreaAlarmPersonId, LocalDateTime time) {
        List<Long> personIdList = new ArrayList<>();
        if (CollUtil.isNotEmpty((Collection<?>) closeAreaAlarmId)) {
            List<Long> list = this.alarmMapper.selectClose80_AlarmEndTime(closeAreaAlarmId, alarmType);
            if (CollUtil.isNotEmpty((Collection<?>) list)) {
                personIdList.addAll(list);
            }
        }
        if (CollUtil.isNotEmpty((Collection<?>) closeAreaAlarmPersonId)) {
            personIdList.addAll(closeAreaAlarmPersonId);
        }
        if (CollUtil.isNotEmpty((Collection<?>) personIdList)) {
            this.alarmMapper.close80AlarmEndTimeByPersonIds(alarmType, personIdList, time);
            this.alarmMapper.updateReturnCardStatus(personIdList);
        }
    }
    public void close81AlarmEndTime(Set<Long> list, LocalDateTime time) {
        if (CollUtil.isEmpty((Collection<?>) list)) {
            return;
        }
        this.alarmMapper.close81AlarmEndTime(list, time);
    }
    @Override // com.xrkc.job.service.IAlarmService
    public Set<Long> selectNoClose81AlarmEndTime() {
        Set<Long> personIds = this.alarmMapper.selectNoClose81AlarmEndTime();
        return Objects.isNull(personIds) ? new HashSet() : personIds;
    }
}
