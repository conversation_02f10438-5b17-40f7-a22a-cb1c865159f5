package com.xrkc.job.module.vehicle.mapper;
import com.xrkc.core.domain.vehicle.VehiclePositionHistory;
import com.xrkc.core.mybatisplus.BaseMapperX;
import com.xrkc.core.mybatisplus.LambdaQueryWrapperX;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/module/vehicle/mapper/VehiclePositionHistoryMapper.class */
public interface VehiclePositionHistoryMapper extends BaseMapperX<VehiclePositionHistory> {
    @Update({"CREATE TABLE ${tableName} LIKE vehicle_position_history;"})
    int createTable(@Param("tableName") String str);
    default List<VehiclePositionHistory> selectListBySecond(List<Long> cardIds, long second) {
        return selectList(new LambdaQueryWrapperX<VehiclePositionHistory>().inIfPresent((v0) -> {
            return v0.getCardId();
        }, cardIds).gtIfPresent((v0) -> {
            return v0.getAcceptTime();
        }, LocalDateTime.now().minusSeconds(second)));
    }
}
