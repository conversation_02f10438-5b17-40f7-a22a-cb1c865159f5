package com.xrkc.job.domain;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/Facility.class */
public class Facility {
    private Long facilityId;
    private String facilityName;
    private String statisticType;
    private List<FacilityRail> facilityRailList;
    public void setFacilityId(Long facilityId) {
        this.facilityId = facilityId;
    }
    public void setFacilityName(String facilityName) {
        this.facilityName = facilityName;
    }
    public void setStatisticType(String statisticType) {
        this.statisticType = statisticType;
    }
    public void setFacilityRailList(List<FacilityRail> facilityRailList) {
        this.facilityRailList = facilityRailList;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof Facility)) {
            return false;
        }
        Facility other = (Facility) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$facilityId = getFacilityId();
        Object other$facilityId = other.getFacilityId();
        if (this$facilityId == null) {
            if (other$facilityId != null) {
                return false;
            }
        } else if (!this$facilityId.equals(other$facilityId)) {
            return false;
        }
        Object this$facilityName = getFacilityName();
        Object other$facilityName = other.getFacilityName();
        if (this$facilityName == null) {
            if (other$facilityName != null) {
                return false;
            }
        } else if (!this$facilityName.equals(other$facilityName)) {
            return false;
        }
        Object this$statisticType = getStatisticType();
        Object other$statisticType = other.getStatisticType();
        if (this$statisticType == null) {
            if (other$statisticType != null) {
                return false;
            }
        } else if (!this$statisticType.equals(other$statisticType)) {
            return false;
        }
        Object this$facilityRailList = getFacilityRailList();
        Object other$facilityRailList = other.getFacilityRailList();
        return this$facilityRailList == null ? other$facilityRailList == null : this$facilityRailList.equals(other$facilityRailList);
    }
    protected boolean canEqual(Object other) {
        return other instanceof Facility;
    }
    public int hashCode() {
        Object $facilityId = getFacilityId();
        int result = (1 * 59) + ($facilityId == null ? 43 : $facilityId.hashCode());
        Object $facilityName = getFacilityName();
        int result2 = (result * 59) + ($facilityName == null ? 43 : $facilityName.hashCode());
        Object $statisticType = getStatisticType();
        int result3 = (result2 * 59) + ($statisticType == null ? 43 : $statisticType.hashCode());
        Object $facilityRailList = getFacilityRailList();
        return (result3 * 59) + ($facilityRailList == null ? 43 : $facilityRailList.hashCode());
    }
    public String toString() {
        return "Facility(facilityId=" + getFacilityId() + ", facilityName=" + getFacilityName() + ", statisticType=" + getStatisticType() + ", facilityRailList=" + getFacilityRailList() + StringPool.RIGHT_BRACKET;
    }
    public Long getFacilityId() {
        return this.facilityId;
    }
    public String getFacilityName() {
        return this.facilityName;
    }
    public String getStatisticType() {
        return this.statisticType;
    }
    public List<FacilityRail> getFacilityRailList() {
        return this.facilityRailList;
    }
}
