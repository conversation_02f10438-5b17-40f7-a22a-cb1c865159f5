package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
@TableName("core_alarm_notice_record_person")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/AlarmNoticeRecordPerson.class */
public class AlarmNoticeRecordPerson {
    private Long recordId;
    private Long personId;
    private String phone;
    private String realName;
    private LocalDateTime noticeTime;
    private String noticeStatic;
    private String noticeResult;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    public Long getRecordId() {
        return this.recordId;
    }
    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }
    public Long getPersonId() {
        return this.personId;
    }
    public void setPersonId(Long personId) {
        this.personId = personId;
    }
    public String getPhone() {
        return this.phone;
    }
    public void setPhone(String phone) {
        this.phone = phone;
    }
    public String getRealName() {
        return this.realName;
    }
    public void setRealName(String realName) {
        this.realName = realName;
    }
    public LocalDateTime getNoticeTime() {
        return this.noticeTime;
    }
    public void setNoticeTime(LocalDateTime noticeTime) {
        this.noticeTime = noticeTime;
    }
    public String getNoticeStatic() {
        return this.noticeStatic;
    }
    public void setNoticeStatic(String noticeStatic) {
        this.noticeStatic = noticeStatic;
    }
    public String getNoticeResult() {
        return this.noticeResult;
    }
    public void setNoticeResult(String noticeResult) {
        this.noticeResult = noticeResult;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getUpdateTime() {
        return this.updateTime;
    }
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}
