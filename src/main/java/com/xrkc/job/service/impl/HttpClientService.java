package com.xrkc.job.service.impl;
import com.alibaba.fastjson2.JSONObject;
import com.xrkc.core.constant.CacheConstants;
import com.xrkc.job.util.ApiUtils;
import com.xrkc.job.util.CommonUtil;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.InvalidKeyException;
import java.security.KeyStore;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.net.ssl.SSLContext;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpRequestRetryHandler;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.ssl.TrustStrategy;
import org.apache.http.util.EntityUtils;
import org.postgresql.jdbc.EscapedFunctions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/HttpClientService.class */
public class HttpClientService {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) HttpClientService.class);
    private static final String DEFAULT_CHARSET = "UTF-8";
    private HttpClientBuilder httpClientBuilder;
    private HttpClientService() {
        PoolingHttpClientConnectionManager commManager = new PoolingHttpClientConnectionManager();
        commManager.setMaxTotal(20);
        commManager.setDefaultMaxPerRoute(10);
        this.httpClientBuilder = HttpClients.custom().setDefaultRequestConfig(RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).build()).setConnectionManager(commManager).setRetryHandler(new DefaultHttpRequestRetryHandler(3, true));
    }
    public CloseableHttpClient getClient() {
        return this.httpClientBuilder.build();
    }
    public String sendPost(String url, Map<String, Object> reqParams) throws IOException {
        return sendPostWithCharset(url, reqParams, null);
    }
    public String sendPostWithCharset(String url, Map<String, Object> reqParams, String charset) throws IOException {
        CloseableHttpClient client = getClient();
        HttpPost post = new HttpPost(url);
        List<BasicNameValuePair> reqParamList = new ArrayList<>();
        for (Map.Entry<String, Object> entry : reqParams.entrySet()) {
            reqParamList.add(new BasicNameValuePair(entry.getKey(), entry.getValue().toString()));
        }
        if (reqParamList.size() > 0) {
            post.setEntity(new UrlEncodedFormEntity(reqParamList, "UTF-8"));
        }
        CloseableHttpResponse resp = null;
        try {
            CloseableHttpResponse resp2 = client.execute((HttpUriRequest) post);
            if (charset != null) {
                String string = EntityUtils.toString(resp2.getEntity(), charset);
                if (resp2 != null) {
                    resp2.close();
                }
                return string;
            }
            String string2 = EntityUtils.toString(resp2.getEntity());
            if (resp2 != null) {
                resp2.close();
            }
            return string2;
        } catch (Throwable th) {
            if (0 != 0) {
                resp.close();
            }
            throw th;
        }
    }
    public String doPost(String url, String jsonStrData) throws IOException {
        return doPostWithCharset(url, jsonStrData, null);
    }
    public String doGet(String url, Map<String, String> headerParm, int... args) throws IOException {
        CloseableHttpClient client = getClient();
        CloseableHttpResponse resp = null;
        HttpGet get = new HttpGet(url);
        if (args != null && args.length > 0) {
            get.setConfig(RequestConfig.custom().setSocketTimeout(args[0]).build());
        }
        if (headerParm != null && headerParm.size() > 0) {
            for (Map.Entry<String, String> entry : headerParm.entrySet()) {
                get.addHeader(entry.getKey(), entry.getValue());
            }
        }
        try {
            resp = client.execute((HttpUriRequest) get);
            String string = EntityUtils.toString(resp.getEntity());
            if (resp != null) {
                resp.close();
            }
            return string;
        } catch (Throwable th) {
            if (resp != null) {
                resp.close();
            }
            throw th;
        }
    }
    public String doGetIgnoreSSL(String url, Map<String, String> headerParm, int... args) throws Exception {
        CloseableHttpClient client = getIgnoreSSLClient();
        CloseableHttpResponse resp = null;
        HttpGet get = new HttpGet(url);
        if (args != null && args.length > 0) {
            get.setConfig(RequestConfig.custom().setSocketTimeout(args[0]).build());
        }
        if (headerParm != null && headerParm.size() > 0) {
            for (Map.Entry<String, String> entry : headerParm.entrySet()) {
                get.addHeader(entry.getKey(), entry.getValue());
            }
        }
        try {
            resp = client.execute((HttpUriRequest) get);
            String string = EntityUtils.toString(resp.getEntity());
            if (resp != null) {
                resp.close();
            }
            return string;
        } catch (Throwable th) {
            if (resp != null) {
                resp.close();
            }
            throw th;
        }
    }
    /* renamed from: com.xrkc.job.service.impl.HttpClientService$1 */
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/HttpClientService$1.class */
    class AnonymousClass1 implements TrustStrategy {
        AnonymousClass1() {
        }
        @Override // org.apache.http.ssl.TrustStrategy
        public boolean isTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
            return true;
        }
    }
    public CloseableHttpClient getIgnoreSSLClient() throws Exception {
        SSLContext sslContext = SSLContexts.custom().loadTrustMaterial((KeyStore) null, new TrustStrategy() { // from class: com.xrkc.job.service.impl.HttpClientService.1
            void AnonymousClass1() {
            }
            @Override // org.apache.http.ssl.TrustStrategy
            public boolean isTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
                return true;
            }
        }).build();
        CloseableHttpClient client = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
        return client;
    }
    public String doPostWithCharset(String url, String jsonStrData, String charset) throws IOException {
        String result = "";
        CloseableHttpClient client = getClient();
        CloseableHttpResponse response = null;
        HttpPost post = new HttpPost(url);
        try {
            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(3000).setSocketTimeout(3000).setMaxRedirects(1).build();
            HttpEntity param = new StringEntity(jsonStrData, "UTF-8");
            post.setEntity(param);
            post.setHeader("Content-type", "application/json");
            post.setConfig(requestConfig);
            CloseableHttpResponse response2 = client.execute((HttpUriRequest) post);
            HttpEntity entity = response2.getEntity();
            if (charset != null) {
                result = EntityUtils.toString(entity, charset);
            } else {
                result = EntityUtils.toString(entity);
            }
            if (response2 != null) {
                response2.close();
            }
        } catch (Exception e) {
            if (0 != 0) {
                response.close();
            }
        } catch (Throwable th) {
            if (0 != 0) {
                response.close();
            }
            throw th;
        }
        return result;
    }
    public String doPostAndAuth(String url, String jsonStrData, String token) throws IOException {
        return doPostWithCharsetAndAuth(url, jsonStrData, null, token);
    }
    public String doPostWithCharsetAndAuth(String url, String jsonStrData, String charset, String token) throws IOException {
        String result;
        CloseableHttpClient client = getClient();
        CloseableHttpResponse response = null;
        HttpPost post = new HttpPost(url);
        try {
            try {
                HttpEntity param = new StringEntity(jsonStrData, ContentType.create("application/json", "UTF-8"));
                post.setHeader("Authorization", CacheConstants.TOKEN_PREFIX + token);
                post.setEntity(param);
                response = client.execute((HttpUriRequest) post);
                HttpEntity entity = response.getEntity();
                if (charset != null) {
                    result = EntityUtils.toString(entity, charset);
                } else {
                    result = EntityUtils.toString(entity);
                }
                return result;
            } catch (Exception e) {
                throw e;
            }
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }
    public static String httpRequest(String url, String requestMethod, Map<String, String> headerMap, JSONObject contentMap) throws IOException {
        String result = "";
        try {
            URL restURL = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) restURL.openConnection();
            connection.setRequestMethod(requestMethod);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            for (Map.Entry<String, String> elem : headerMap.entrySet()) {
                connection.setRequestProperty(elem.getKey(), elem.getValue());
            }
            OutputStreamWriter outer = new OutputStreamWriter(connection.getOutputStream(), "UTF-8");
            outer.write(contentMap.toString());
            outer.flush();
            outer.close();
            InputStream ips = connection.getInputStream();
            BufferedReader in = new BufferedReader(new InputStreamReader(ips, "UTF-8"));
            StringBuffer buffer = new StringBuffer();
            while (true) {
                String line = in.readLine();
                if (line == null) {
                    break;
                }
                buffer.append(line);
                buffer.append("\r\n");
            }
            in.close();
            ips.close();
            connection.disconnect();
            result = buffer.toString();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }
    public static String sendPostFormData(String url, Map<String, String> headersMap, JSONObject contentMap) {
        try {
            if (CollectionUtils.isEmpty(headersMap)) {
                headersMap = new HashMap();
            }
            headersMap.put("Content-Type", "multipart/form-data");
            return httpRequest(url, "POST", headersMap, contentMap);
        } catch (Exception e) {
            log.error("HTTP请求失败，url：{}，msg：{}", url, e.getMessage());
            return "";
        }
    }
    public static String sendPostBody(String url, Map<String, String> headersMap, JSONObject contentMap) {
        try {
            if (CollectionUtils.isEmpty(headersMap)) {
                headersMap = new HashMap();
            }
            headersMap.put("Content-Type", "application/json; charset=utf-8");
            return httpRequest(url, "POST", headersMap, contentMap);
        } catch (Exception e) {
            log.error("HTTP请求失败，url：{}，msg：{}", url, e.getMessage());
            return "";
        }
    }
    public static void main(String[] args) throws IllegalStateException, NoSuchAlgorithmException, InvalidKeyException {
        test();
        try {
            Map<String, String> headersMap = new HashMap<>();
            headersMap.put("Content-Type", "application/x-www-form-urlencoded; charset=utf-8");
            headersMap.put("appmark", "应用标识");
            headersMap.put(EscapedFunctions.SIGN_FUNC, "加密后的签名");
            JSONObject contentMap = new JSONObject();
            contentMap.put("id", "3216520000000000218");
            contentMap.put("name", "张三");
            String post = httpRequest("http://localhost:8090/gateway-service/api-service/position/ignoreTest", "POST", headersMap, contentMap);
            System.err.println(post);
            test2();
        } catch (Exception e) {
            log.error("HTTP请求失败，url：{}，msg：{}", "http://localhost:8090/gateway-service/api-service/position/ignoreTest", e.getMessage());
        }
    }
    private static void test2() {
        try {
            Map<String, String> headersMap = new HashMap<>();
            headersMap.put("Content-Type", "form-data");
            headersMap.put("appmark", "应用标识");
            headersMap.put(EscapedFunctions.SIGN_FUNC, "加密后的签名");
            JSONObject contentMap = new JSONObject();
            contentMap.put("id", "1251");
            contentMap.put("name", "张三");
            String post = httpRequest("http://localhost:8090/gateway-service/api-service/position/ignoreTest", "POST", headersMap, contentMap);
            System.err.println(post);
        } catch (Exception e) {
            log.error("HTTP请求失败，url：{}，msg：{}", "http://localhost:8090/gateway-service/api-service/position/ignoreTest", e.getMessage());
        }
    }
    /* JADX WARN: Multi-variable type inference failed */
    public static void test() throws IllegalStateException, NoSuchAlgorithmException, InvalidKeyException {
        String time = "" + System.currentTimeMillis();
        String nonceStr = CommonUtil.GetRandomString(10);
        String str = "66b9bdf62a43be25212572f0e171c226&" + time + "&" + nonceStr;
        String sign = ApiUtils.genHMAC(str, "5e071833059a17fbce70e7f759f5fd4c");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.set("appid", "66b9bdf62a43be25212572f0e171c226");
        headers.set("time", time);
        headers.set("nonceStr", nonceStr);
        headers.set(EscapedFunctions.SIGN_FUNC, sign);
        RestTemplate restTemplate = new RestTemplate();
        MultiValueMap<String, Object> linkedMap = new LinkedMultiValueMap<>();
        linkedMap.add("alarmId", "1");
        linkedMap.add("alarmStatus", "1");
        linkedMap.add("alarmTime", "2022-06-30 13:04:01");
        linkedMap.add("alarmType", "01");
        linkedMap.add("deviceCode", "1");
        linkedMap.add("videoCode", "1");
        org.springframework.http.HttpEntity<MultiValueMap<String, Object>> httpEntity = new org.springframework.http.HttpEntity<>(linkedMap, headers);
        JSONObject result = (JSONObject) restTemplate.postForEntity("http://localhost:8090/gateway-service/api-service/position/ignoreTest", httpEntity, JSONObject.class, new Object[0]).getBody();
        System.out.println(result.toString());
    }
}
