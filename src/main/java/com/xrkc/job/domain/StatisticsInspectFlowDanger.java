package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
@TableName("statistics_inspect_flow_danger")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/StatisticsInspectFlowDanger.class */
public class StatisticsInspectFlowDanger implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId("statistics_id")
    private Long statisticsId;
    @TableField("statistics_date")
    private LocalDate statisticsDate;
    @TableField("danger_type")
    private String dangerType;
    @TableField("danger_type_name")
    private String dangerTypeName;
    @TableField("danger_count")
    private Integer dangerCount;
    @TableField("create_time")
    private LocalDateTime createTime;
    public void setStatisticsId(Long statisticsId) {
        this.statisticsId = statisticsId;
    }
    public void setStatisticsDate(LocalDate statisticsDate) {
        this.statisticsDate = statisticsDate;
    }
    public void setDangerType(String dangerType) {
        this.dangerType = dangerType;
    }
    public void setDangerTypeName(String dangerTypeName) {
        this.dangerTypeName = dangerTypeName;
    }
    public void setDangerCount(Integer dangerCount) {
        this.dangerCount = dangerCount;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof StatisticsInspectFlowDanger)) {
            return false;
        }
        StatisticsInspectFlowDanger other = (StatisticsInspectFlowDanger) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$statisticsId = getStatisticsId();
        Object other$statisticsId = other.getStatisticsId();
        if (this$statisticsId == null) {
            if (other$statisticsId != null) {
                return false;
            }
        } else if (!this$statisticsId.equals(other$statisticsId)) {
            return false;
        }
        Object this$dangerCount = getDangerCount();
        Object other$dangerCount = other.getDangerCount();
        if (this$dangerCount == null) {
            if (other$dangerCount != null) {
                return false;
            }
        } else if (!this$dangerCount.equals(other$dangerCount)) {
            return false;
        }
        Object this$statisticsDate = getStatisticsDate();
        Object other$statisticsDate = other.getStatisticsDate();
        if (this$statisticsDate == null) {
            if (other$statisticsDate != null) {
                return false;
            }
        } else if (!this$statisticsDate.equals(other$statisticsDate)) {
            return false;
        }
        Object this$dangerType = getDangerType();
        Object other$dangerType = other.getDangerType();
        if (this$dangerType == null) {
            if (other$dangerType != null) {
                return false;
            }
        } else if (!this$dangerType.equals(other$dangerType)) {
            return false;
        }
        Object this$dangerTypeName = getDangerTypeName();
        Object other$dangerTypeName = other.getDangerTypeName();
        if (this$dangerTypeName == null) {
            if (other$dangerTypeName != null) {
                return false;
            }
        } else if (!this$dangerTypeName.equals(other$dangerTypeName)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        return this$createTime == null ? other$createTime == null : this$createTime.equals(other$createTime);
    }
    protected boolean canEqual(Object other) {
        return other instanceof StatisticsInspectFlowDanger;
    }
    public int hashCode() {
        Object $statisticsId = getStatisticsId();
        int result = (1 * 59) + ($statisticsId == null ? 43 : $statisticsId.hashCode());
        Object $dangerCount = getDangerCount();
        int result2 = (result * 59) + ($dangerCount == null ? 43 : $dangerCount.hashCode());
        Object $statisticsDate = getStatisticsDate();
        int result3 = (result2 * 59) + ($statisticsDate == null ? 43 : $statisticsDate.hashCode());
        Object $dangerType = getDangerType();
        int result4 = (result3 * 59) + ($dangerType == null ? 43 : $dangerType.hashCode());
        Object $dangerTypeName = getDangerTypeName();
        int result5 = (result4 * 59) + ($dangerTypeName == null ? 43 : $dangerTypeName.hashCode());
        Object $createTime = getCreateTime();
        return (result5 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
    }
    public String toString() {
        return "StatisticsInspectFlowDanger(statisticsId=" + getStatisticsId() + ", statisticsDate=" + getStatisticsDate() + ", dangerType=" + getDangerType() + ", dangerTypeName=" + getDangerTypeName() + ", dangerCount=" + getDangerCount() + ", createTime=" + getCreateTime() + StringPool.RIGHT_BRACKET;
    }
    public Long getStatisticsId() {
        return this.statisticsId;
    }
    public LocalDate getStatisticsDate() {
        return this.statisticsDate;
    }
    public String getDangerType() {
        return this.dangerType;
    }
    public String getDangerTypeName() {
        return this.dangerTypeName;
    }
    public Integer getDangerCount() {
        return this.dangerCount;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
}
