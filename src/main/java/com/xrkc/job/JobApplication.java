package com.xrkc.job;
import com.xrkc.security.annotation.EnableRyFeignClients;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
@EnableRyFeignClients
@SpringBootApplication
@MapperScan({"com.xrkc.**.mapper.**"})
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/JobApplication.class */
public class JobApplication {
    public static void main(String[] args) {
        SpringApplication.run((Class<?>) JobApplication.class, args);
    }
}
