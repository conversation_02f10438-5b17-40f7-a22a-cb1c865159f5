package com.xrkc.job.mapper;
import com.xrkc.job.domain.StatisticsVehicleAlarmFlow;
import java.time.LocalDate;
import java.util.List;
import org.apache.ibatis.annotations.Select;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/StatisticsVehicleAlarmFlowMapper.class */
public interface StatisticsVehicleAlarmFlowMapper {
    int insert(StatisticsVehicleAlarmFlow statisticsVehicleAlarmFlow);
    int update(StatisticsVehicleAlarmFlow statisticsVehicleAlarmFlow);
    @Select({"select alarm_type from statistics_vehicle_alarm_flow where statistics_date =#{statisticsDate};"})
    List<StatisticsVehicleAlarmFlow> selectByStatisticsDate(LocalDate localDate);
}
