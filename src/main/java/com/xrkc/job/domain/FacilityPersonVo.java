package com.xrkc.job.domain;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.time.LocalDateTime;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/FacilityPersonVo.class */
public class FacilityPersonVo {
    private Long recordId;
    private Long personId;
    private LocalDateTime enterTime;
    private LocalDateTime outTime;
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/FacilityPersonVo$FacilityPersonVoBuilder.class */
    public static class FacilityPersonVoBuilder {
        private Long recordId;
        private Long personId;
        private LocalDateTime enterTime;
        private LocalDateTime outTime;
        FacilityPersonVoBuilder() {
        }
        public FacilityPersonVoBuilder recordId(Long recordId) {
            this.recordId = recordId;
            return this;
        }
        public FacilityPersonVoBuilder personId(Long personId) {
            this.personId = personId;
            return this;
        }
        public FacilityPersonVoBuilder enterTime(LocalDateTime enterTime) {
            this.enterTime = enterTime;
            return this;
        }
        public FacilityPersonVoBuilder outTime(LocalDateTime outTime) {
            this.outTime = outTime;
            return this;
        }
        public FacilityPersonVo build() {
            return new FacilityPersonVo(this.recordId, this.personId, this.enterTime, this.outTime);
        }
        public String toString() {
            return "FacilityPersonVo.FacilityPersonVoBuilder(recordId=" + this.recordId + ", personId=" + this.personId + ", enterTime=" + this.enterTime + ", outTime=" + this.outTime + StringPool.RIGHT_BRACKET;
        }
    }
    FacilityPersonVo(Long recordId, Long personId, LocalDateTime enterTime, LocalDateTime outTime) {
        this.recordId = recordId;
        this.personId = personId;
        this.enterTime = enterTime;
        this.outTime = outTime;
    }
    public static FacilityPersonVoBuilder builder() {
        return new FacilityPersonVoBuilder();
    }
    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }
    public void setPersonId(Long personId) {
        this.personId = personId;
    }
    public void setEnterTime(LocalDateTime enterTime) {
        this.enterTime = enterTime;
    }
    public void setOutTime(LocalDateTime outTime) {
        this.outTime = outTime;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FacilityPersonVo)) {
            return false;
        }
        FacilityPersonVo other = (FacilityPersonVo) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$recordId = getRecordId();
        Object other$recordId = other.getRecordId();
        if (this$recordId == null) {
            if (other$recordId != null) {
                return false;
            }
        } else if (!this$recordId.equals(other$recordId)) {
            return false;
        }
        Object this$personId = getPersonId();
        Object other$personId = other.getPersonId();
        if (this$personId == null) {
            if (other$personId != null) {
                return false;
            }
        } else if (!this$personId.equals(other$personId)) {
            return false;
        }
        Object this$enterTime = getEnterTime();
        Object other$enterTime = other.getEnterTime();
        if (this$enterTime == null) {
            if (other$enterTime != null) {
                return false;
            }
        } else if (!this$enterTime.equals(other$enterTime)) {
            return false;
        }
        Object this$outTime = getOutTime();
        Object other$outTime = other.getOutTime();
        return this$outTime == null ? other$outTime == null : this$outTime.equals(other$outTime);
    }
    protected boolean canEqual(Object other) {
        return other instanceof FacilityPersonVo;
    }
    public int hashCode() {
        Object $recordId = getRecordId();
        int result = (1 * 59) + ($recordId == null ? 43 : $recordId.hashCode());
        Object $personId = getPersonId();
        int result2 = (result * 59) + ($personId == null ? 43 : $personId.hashCode());
        Object $enterTime = getEnterTime();
        int result3 = (result2 * 59) + ($enterTime == null ? 43 : $enterTime.hashCode());
        Object $outTime = getOutTime();
        return (result3 * 59) + ($outTime == null ? 43 : $outTime.hashCode());
    }
    public String toString() {
        return "FacilityPersonVo(recordId=" + getRecordId() + ", personId=" + getPersonId() + ", enterTime=" + getEnterTime() + ", outTime=" + getOutTime() + StringPool.RIGHT_BRACKET;
    }
    public Long getRecordId() {
        return this.recordId;
    }
    public Long getPersonId() {
        return this.personId;
    }
    public LocalDateTime getEnterTime() {
        return this.enterTime;
    }
    public LocalDateTime getOutTime() {
        return this.outTime;
    }
}
