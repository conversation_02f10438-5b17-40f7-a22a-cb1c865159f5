package com.xrkc.job.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xrkc.job.domain.StatisticsInspectFlow;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IStatisticsInspectFlowService.class */
public interface IStatisticsInspectFlowService extends IService<StatisticsInspectFlow> {
    int batchReplace(List<StatisticsInspectFlow> list);
}
