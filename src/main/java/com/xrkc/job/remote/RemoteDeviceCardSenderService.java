package com.xrkc.job.remote;
import com.xrkc.core.constant.ServiceNameConstants;
import com.xrkc.core.domain.basic.JsonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
@FeignClient(contextId = "remoteDeviceCardSenderService", value = ServiceNameConstants.SYSTEM_SERVICE)
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/remote/RemoteDeviceCardSenderService.class */
public interface RemoteDeviceCardSenderService {
    @PostMapping({"/cardSender/syncWhiteList"})
    JsonResult syncWhiteList();
}
