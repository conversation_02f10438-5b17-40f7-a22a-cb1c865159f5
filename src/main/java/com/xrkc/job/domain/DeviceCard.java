package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
import java.time.LocalDateTime;
@TableName("device_card")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/DeviceCard.class */
public class DeviceCard implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId("id")
    private Long id;
    @TableField("card_id")
    private Long cardId;
    @TableField("card_type")
    private String cardType;
    private String cardModel;
    @TableField("ic_card_id")
    private String icCardId;
    @TableField("card_power")
    private Integer cardPower;
    @TableField("card_status")
    private Integer cardStatus;
    @TableField("card_version")
    private Integer cardVersion;
    @TableField("card_freq")
    private Integer cardFreq;
    @TableField("card_enable")
    private String cardEnable;
    @TableField("use_status")
    private String useStatus;
    @TableField("card_transfer")
    private Integer cardTransfer;
    @TableField("heart_time")
    private LocalDateTime heartTime;
    @TableField("create_time")
    private LocalDateTime createTime;
    @TableField("create_by")
    private String createBy;
    @TableField("update_time")
    private LocalDateTime updateTime;
    @TableField("update_by")
    private String updateBy;
    @TableField("remark")
    private String remark;
    @TableField(exist = false)
    private String realName;
    private LocalDateTime statusTime;
    public void setId(Long id) {
        this.id = id;
    }
    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }
    public void setCardType(String cardType) {
        this.cardType = cardType;
    }
    public void setCardModel(String cardModel) {
        this.cardModel = cardModel;
    }
    public void setIcCardId(String icCardId) {
        this.icCardId = icCardId;
    }
    public void setCardPower(Integer cardPower) {
        this.cardPower = cardPower;
    }
    public void setCardStatus(Integer cardStatus) {
        this.cardStatus = cardStatus;
    }
    public void setCardVersion(Integer cardVersion) {
        this.cardVersion = cardVersion;
    }
    public void setCardFreq(Integer cardFreq) {
        this.cardFreq = cardFreq;
    }
    public void setCardEnable(String cardEnable) {
        this.cardEnable = cardEnable;
    }
    public void setUseStatus(String useStatus) {
        this.useStatus = useStatus;
    }
    public void setCardTransfer(Integer cardTransfer) {
        this.cardTransfer = cardTransfer;
    }
    public void setHeartTime(LocalDateTime heartTime) {
        this.heartTime = heartTime;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }
    public void setRealName(String realName) {
        this.realName = realName;
    }
    public void setStatusTime(LocalDateTime statusTime) {
        this.statusTime = statusTime;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof DeviceCard)) {
            return false;
        }
        DeviceCard other = (DeviceCard) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$id = getId();
        Object other$id = other.getId();
        if (this$id == null) {
            if (other$id != null) {
                return false;
            }
        } else if (!this$id.equals(other$id)) {
            return false;
        }
        Object this$cardId = getCardId();
        Object other$cardId = other.getCardId();
        if (this$cardId == null) {
            if (other$cardId != null) {
                return false;
            }
        } else if (!this$cardId.equals(other$cardId)) {
            return false;
        }
        Object this$cardPower = getCardPower();
        Object other$cardPower = other.getCardPower();
        if (this$cardPower == null) {
            if (other$cardPower != null) {
                return false;
            }
        } else if (!this$cardPower.equals(other$cardPower)) {
            return false;
        }
        Object this$cardStatus = getCardStatus();
        Object other$cardStatus = other.getCardStatus();
        if (this$cardStatus == null) {
            if (other$cardStatus != null) {
                return false;
            }
        } else if (!this$cardStatus.equals(other$cardStatus)) {
            return false;
        }
        Object this$cardVersion = getCardVersion();
        Object other$cardVersion = other.getCardVersion();
        if (this$cardVersion == null) {
            if (other$cardVersion != null) {
                return false;
            }
        } else if (!this$cardVersion.equals(other$cardVersion)) {
            return false;
        }
        Object this$cardFreq = getCardFreq();
        Object other$cardFreq = other.getCardFreq();
        if (this$cardFreq == null) {
            if (other$cardFreq != null) {
                return false;
            }
        } else if (!this$cardFreq.equals(other$cardFreq)) {
            return false;
        }
        Object this$cardTransfer = getCardTransfer();
        Object other$cardTransfer = other.getCardTransfer();
        if (this$cardTransfer == null) {
            if (other$cardTransfer != null) {
                return false;
            }
        } else if (!this$cardTransfer.equals(other$cardTransfer)) {
            return false;
        }
        Object this$cardType = getCardType();
        Object other$cardType = other.getCardType();
        if (this$cardType == null) {
            if (other$cardType != null) {
                return false;
            }
        } else if (!this$cardType.equals(other$cardType)) {
            return false;
        }
        Object this$cardModel = getCardModel();
        Object other$cardModel = other.getCardModel();
        if (this$cardModel == null) {
            if (other$cardModel != null) {
                return false;
            }
        } else if (!this$cardModel.equals(other$cardModel)) {
            return false;
        }
        Object this$icCardId = getIcCardId();
        Object other$icCardId = other.getIcCardId();
        if (this$icCardId == null) {
            if (other$icCardId != null) {
                return false;
            }
        } else if (!this$icCardId.equals(other$icCardId)) {
            return false;
        }
        Object this$cardEnable = getCardEnable();
        Object other$cardEnable = other.getCardEnable();
        if (this$cardEnable == null) {
            if (other$cardEnable != null) {
                return false;
            }
        } else if (!this$cardEnable.equals(other$cardEnable)) {
            return false;
        }
        Object this$useStatus = getUseStatus();
        Object other$useStatus = other.getUseStatus();
        if (this$useStatus == null) {
            if (other$useStatus != null) {
                return false;
            }
        } else if (!this$useStatus.equals(other$useStatus)) {
            return false;
        }
        Object this$heartTime = getHeartTime();
        Object other$heartTime = other.getHeartTime();
        if (this$heartTime == null) {
            if (other$heartTime != null) {
                return false;
            }
        } else if (!this$heartTime.equals(other$heartTime)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        if (this$createTime == null) {
            if (other$createTime != null) {
                return false;
            }
        } else if (!this$createTime.equals(other$createTime)) {
            return false;
        }
        Object this$createBy = getCreateBy();
        Object other$createBy = other.getCreateBy();
        if (this$createBy == null) {
            if (other$createBy != null) {
                return false;
            }
        } else if (!this$createBy.equals(other$createBy)) {
            return false;
        }
        Object this$updateTime = getUpdateTime();
        Object other$updateTime = other.getUpdateTime();
        if (this$updateTime == null) {
            if (other$updateTime != null) {
                return false;
            }
        } else if (!this$updateTime.equals(other$updateTime)) {
            return false;
        }
        Object this$updateBy = getUpdateBy();
        Object other$updateBy = other.getUpdateBy();
        if (this$updateBy == null) {
            if (other$updateBy != null) {
                return false;
            }
        } else if (!this$updateBy.equals(other$updateBy)) {
            return false;
        }
        Object this$remark = getRemark();
        Object other$remark = other.getRemark();
        if (this$remark == null) {
            if (other$remark != null) {
                return false;
            }
        } else if (!this$remark.equals(other$remark)) {
            return false;
        }
        Object this$realName = getRealName();
        Object other$realName = other.getRealName();
        if (this$realName == null) {
            if (other$realName != null) {
                return false;
            }
        } else if (!this$realName.equals(other$realName)) {
            return false;
        }
        Object this$statusTime = getStatusTime();
        Object other$statusTime = other.getStatusTime();
        return this$statusTime == null ? other$statusTime == null : this$statusTime.equals(other$statusTime);
    }
    protected boolean canEqual(Object other) {
        return other instanceof DeviceCard;
    }
    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $cardId = getCardId();
        int result2 = (result * 59) + ($cardId == null ? 43 : $cardId.hashCode());
        Object $cardPower = getCardPower();
        int result3 = (result2 * 59) + ($cardPower == null ? 43 : $cardPower.hashCode());
        Object $cardStatus = getCardStatus();
        int result4 = (result3 * 59) + ($cardStatus == null ? 43 : $cardStatus.hashCode());
        Object $cardVersion = getCardVersion();
        int result5 = (result4 * 59) + ($cardVersion == null ? 43 : $cardVersion.hashCode());
        Object $cardFreq = getCardFreq();
        int result6 = (result5 * 59) + ($cardFreq == null ? 43 : $cardFreq.hashCode());
        Object $cardTransfer = getCardTransfer();
        int result7 = (result6 * 59) + ($cardTransfer == null ? 43 : $cardTransfer.hashCode());
        Object $cardType = getCardType();
        int result8 = (result7 * 59) + ($cardType == null ? 43 : $cardType.hashCode());
        Object $cardModel = getCardModel();
        int result9 = (result8 * 59) + ($cardModel == null ? 43 : $cardModel.hashCode());
        Object $icCardId = getIcCardId();
        int result10 = (result9 * 59) + ($icCardId == null ? 43 : $icCardId.hashCode());
        Object $cardEnable = getCardEnable();
        int result11 = (result10 * 59) + ($cardEnable == null ? 43 : $cardEnable.hashCode());
        Object $useStatus = getUseStatus();
        int result12 = (result11 * 59) + ($useStatus == null ? 43 : $useStatus.hashCode());
        Object $heartTime = getHeartTime();
        int result13 = (result12 * 59) + ($heartTime == null ? 43 : $heartTime.hashCode());
        Object $createTime = getCreateTime();
        int result14 = (result13 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $createBy = getCreateBy();
        int result15 = (result14 * 59) + ($createBy == null ? 43 : $createBy.hashCode());
        Object $updateTime = getUpdateTime();
        int result16 = (result15 * 59) + ($updateTime == null ? 43 : $updateTime.hashCode());
        Object $updateBy = getUpdateBy();
        int result17 = (result16 * 59) + ($updateBy == null ? 43 : $updateBy.hashCode());
        Object $remark = getRemark();
        int result18 = (result17 * 59) + ($remark == null ? 43 : $remark.hashCode());
        Object $realName = getRealName();
        int result19 = (result18 * 59) + ($realName == null ? 43 : $realName.hashCode());
        Object $statusTime = getStatusTime();
        return (result19 * 59) + ($statusTime == null ? 43 : $statusTime.hashCode());
    }
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/DeviceCard$DeviceCardBuilder.class */
    public static class DeviceCardBuilder {
        private Long id;
        private Long cardId;
        private String cardType;
        private String cardModel;
        private String icCardId;
        private Integer cardPower;
        private Integer cardStatus;
        private Integer cardVersion;
        private Integer cardFreq;
        private String cardEnable;
        private String useStatus;
        private Integer cardTransfer;
        private LocalDateTime heartTime;
        private LocalDateTime createTime;
        private String createBy;
        private LocalDateTime updateTime;
        private String updateBy;
        private String remark;
        private String realName;
        private LocalDateTime statusTime;
        DeviceCardBuilder() {
        }
        public DeviceCardBuilder id(Long id) {
            this.id = id;
            return this;
        }
        public DeviceCardBuilder cardId(Long cardId) {
            this.cardId = cardId;
            return this;
        }
        public DeviceCardBuilder cardType(String cardType) {
            this.cardType = cardType;
            return this;
        }
        public DeviceCardBuilder cardModel(String cardModel) {
            this.cardModel = cardModel;
            return this;
        }
        public DeviceCardBuilder icCardId(String icCardId) {
            this.icCardId = icCardId;
            return this;
        }
        public DeviceCardBuilder cardPower(Integer cardPower) {
            this.cardPower = cardPower;
            return this;
        }
        public DeviceCardBuilder cardStatus(Integer cardStatus) {
            this.cardStatus = cardStatus;
            return this;
        }
        public DeviceCardBuilder cardVersion(Integer cardVersion) {
            this.cardVersion = cardVersion;
            return this;
        }
        public DeviceCardBuilder cardFreq(Integer cardFreq) {
            this.cardFreq = cardFreq;
            return this;
        }
        public DeviceCardBuilder cardEnable(String cardEnable) {
            this.cardEnable = cardEnable;
            return this;
        }
        public DeviceCardBuilder useStatus(String useStatus) {
            this.useStatus = useStatus;
            return this;
        }
        public DeviceCardBuilder cardTransfer(Integer cardTransfer) {
            this.cardTransfer = cardTransfer;
            return this;
        }
        public DeviceCardBuilder heartTime(LocalDateTime heartTime) {
            this.heartTime = heartTime;
            return this;
        }
        public DeviceCardBuilder createTime(LocalDateTime createTime) {
            this.createTime = createTime;
            return this;
        }
        public DeviceCardBuilder createBy(String createBy) {
            this.createBy = createBy;
            return this;
        }
        public DeviceCardBuilder updateTime(LocalDateTime updateTime) {
            this.updateTime = updateTime;
            return this;
        }
        public DeviceCardBuilder updateBy(String updateBy) {
            this.updateBy = updateBy;
            return this;
        }
        public DeviceCardBuilder remark(String remark) {
            this.remark = remark;
            return this;
        }
        public DeviceCardBuilder realName(String realName) {
            this.realName = realName;
            return this;
        }
        public DeviceCardBuilder statusTime(LocalDateTime statusTime) {
            this.statusTime = statusTime;
            return this;
        }
        public DeviceCard build() {
            return new DeviceCard(this.id, this.cardId, this.cardType, this.cardModel, this.icCardId, this.cardPower, this.cardStatus, this.cardVersion, this.cardFreq, this.cardEnable, this.useStatus, this.cardTransfer, this.heartTime, this.createTime, this.createBy, this.updateTime, this.updateBy, this.remark, this.realName, this.statusTime);
        }
        public String toString() {
            return "DeviceCard.DeviceCardBuilder(id=" + this.id + ", cardId=" + this.cardId + ", cardType=" + this.cardType + ", cardModel=" + this.cardModel + ", icCardId=" + this.icCardId + ", cardPower=" + this.cardPower + ", cardStatus=" + this.cardStatus + ", cardVersion=" + this.cardVersion + ", cardFreq=" + this.cardFreq + ", cardEnable=" + this.cardEnable + ", useStatus=" + this.useStatus + ", cardTransfer=" + this.cardTransfer + ", heartTime=" + this.heartTime + ", createTime=" + this.createTime + ", createBy=" + this.createBy + ", updateTime=" + this.updateTime + ", updateBy=" + this.updateBy + ", remark=" + this.remark + ", realName=" + this.realName + ", statusTime=" + this.statusTime + StringPool.RIGHT_BRACKET;
        }
    }
    public String toString() {
        return "DeviceCard(super=" + super.toString() + ", id=" + getId() + ", cardId=" + getCardId() + ", cardType=" + getCardType() + ", cardModel=" + getCardModel() + ", icCardId=" + getIcCardId() + ", cardPower=" + getCardPower() + ", cardStatus=" + getCardStatus() + ", cardVersion=" + getCardVersion() + ", cardFreq=" + getCardFreq() + ", cardEnable=" + getCardEnable() + ", useStatus=" + getUseStatus() + ", cardTransfer=" + getCardTransfer() + ", heartTime=" + getHeartTime() + ", createTime=" + getCreateTime() + ", createBy=" + getCreateBy() + ", updateTime=" + getUpdateTime() + ", updateBy=" + getUpdateBy() + ", remark=" + getRemark() + ", realName=" + getRealName() + ", statusTime=" + getStatusTime() + StringPool.RIGHT_BRACKET;
    }
    public static DeviceCardBuilder builder() {
        return new DeviceCardBuilder();
    }
    public DeviceCard(Long id, Long cardId, String cardType, String cardModel, String icCardId, Integer cardPower, Integer cardStatus, Integer cardVersion, Integer cardFreq, String cardEnable, String useStatus, Integer cardTransfer, LocalDateTime heartTime, LocalDateTime createTime, String createBy, LocalDateTime updateTime, String updateBy, String remark, String realName, LocalDateTime statusTime) {
        this.id = id;
        this.cardId = cardId;
        this.cardType = cardType;
        this.cardModel = cardModel;
        this.icCardId = icCardId;
        this.cardPower = cardPower;
        this.cardStatus = cardStatus;
        this.cardVersion = cardVersion;
        this.cardFreq = cardFreq;
        this.cardEnable = cardEnable;
        this.useStatus = useStatus;
        this.cardTransfer = cardTransfer;
        this.heartTime = heartTime;
        this.createTime = createTime;
        this.createBy = createBy;
        this.updateTime = updateTime;
        this.updateBy = updateBy;
        this.remark = remark;
        this.realName = realName;
        this.statusTime = statusTime;
    }
    public DeviceCard() {
    }
    public Long getId() {
        return this.id;
    }
    public Long getCardId() {
        return this.cardId;
    }
    public String getCardType() {
        return this.cardType;
    }
    public String getCardModel() {
        return this.cardModel;
    }
    public String getIcCardId() {
        return this.icCardId;
    }
    public Integer getCardPower() {
        return this.cardPower;
    }
    public Integer getCardStatus() {
        return this.cardStatus;
    }
    public Integer getCardVersion() {
        return this.cardVersion;
    }
    public Integer getCardFreq() {
        return this.cardFreq;
    }
    public String getCardEnable() {
        return this.cardEnable;
    }
    public String getUseStatus() {
        return this.useStatus;
    }
    public Integer getCardTransfer() {
        return this.cardTransfer;
    }
    public LocalDateTime getHeartTime() {
        return this.heartTime;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public String getCreateBy() {
        return this.createBy;
    }
    public LocalDateTime getUpdateTime() {
        return this.updateTime;
    }
    public String getUpdateBy() {
        return this.updateBy;
    }
    public String getRemark() {
        return this.remark;
    }
    public String getRealName() {
        return this.realName;
    }
    public LocalDateTime getStatusTime() {
        return this.statusTime;
    }
}
