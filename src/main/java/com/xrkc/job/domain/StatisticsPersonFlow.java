package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
@TableName(value = "statistics_person_flow", autoResultMap = true)
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/StatisticsPersonFlow.class */
public class StatisticsPersonFlow implements Serializable {
    private static final long serialVersionUID = 1;
    private Long statisticsId;
    private LocalDate statisticsDate;
    private String personType;
    private String personTypeName;
    private Long personCount;
    private LocalDateTime createTime;
    public void setStatisticsId(Long statisticsId) {
        this.statisticsId = statisticsId;
    }
    public void setStatisticsDate(LocalDate statisticsDate) {
        this.statisticsDate = statisticsDate;
    }
    public void setPersonType(String personType) {
        this.personType = personType;
    }
    public void setPersonTypeName(String personTypeName) {
        this.personTypeName = personTypeName;
    }
    public void setPersonCount(Long personCount) {
        this.personCount = personCount;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof StatisticsPersonFlow)) {
            return false;
        }
        StatisticsPersonFlow other = (StatisticsPersonFlow) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$statisticsId = getStatisticsId();
        Object other$statisticsId = other.getStatisticsId();
        if (this$statisticsId == null) {
            if (other$statisticsId != null) {
                return false;
            }
        } else if (!this$statisticsId.equals(other$statisticsId)) {
            return false;
        }
        Object this$personCount = getPersonCount();
        Object other$personCount = other.getPersonCount();
        if (this$personCount == null) {
            if (other$personCount != null) {
                return false;
            }
        } else if (!this$personCount.equals(other$personCount)) {
            return false;
        }
        Object this$statisticsDate = getStatisticsDate();
        Object other$statisticsDate = other.getStatisticsDate();
        if (this$statisticsDate == null) {
            if (other$statisticsDate != null) {
                return false;
            }
        } else if (!this$statisticsDate.equals(other$statisticsDate)) {
            return false;
        }
        Object this$personType = getPersonType();
        Object other$personType = other.getPersonType();
        if (this$personType == null) {
            if (other$personType != null) {
                return false;
            }
        } else if (!this$personType.equals(other$personType)) {
            return false;
        }
        Object this$personTypeName = getPersonTypeName();
        Object other$personTypeName = other.getPersonTypeName();
        if (this$personTypeName == null) {
            if (other$personTypeName != null) {
                return false;
            }
        } else if (!this$personTypeName.equals(other$personTypeName)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        return this$createTime == null ? other$createTime == null : this$createTime.equals(other$createTime);
    }
    protected boolean canEqual(Object other) {
        return other instanceof StatisticsPersonFlow;
    }
    public int hashCode() {
        Object $statisticsId = getStatisticsId();
        int result = (1 * 59) + ($statisticsId == null ? 43 : $statisticsId.hashCode());
        Object $personCount = getPersonCount();
        int result2 = (result * 59) + ($personCount == null ? 43 : $personCount.hashCode());
        Object $statisticsDate = getStatisticsDate();
        int result3 = (result2 * 59) + ($statisticsDate == null ? 43 : $statisticsDate.hashCode());
        Object $personType = getPersonType();
        int result4 = (result3 * 59) + ($personType == null ? 43 : $personType.hashCode());
        Object $personTypeName = getPersonTypeName();
        int result5 = (result4 * 59) + ($personTypeName == null ? 43 : $personTypeName.hashCode());
        Object $createTime = getCreateTime();
        return (result5 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
    }
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/StatisticsPersonFlow$StatisticsPersonFlowBuilder.class */
    public static class StatisticsPersonFlowBuilder {
        private Long statisticsId;
        private LocalDate statisticsDate;
        private String personType;
        private String personTypeName;
        private Long personCount;
        private LocalDateTime createTime;
        StatisticsPersonFlowBuilder() {
        }
        public StatisticsPersonFlowBuilder statisticsId(Long statisticsId) {
            this.statisticsId = statisticsId;
            return this;
        }
        public StatisticsPersonFlowBuilder statisticsDate(LocalDate statisticsDate) {
            this.statisticsDate = statisticsDate;
            return this;
        }
        public StatisticsPersonFlowBuilder personType(String personType) {
            this.personType = personType;
            return this;
        }
        public StatisticsPersonFlowBuilder personTypeName(String personTypeName) {
            this.personTypeName = personTypeName;
            return this;
        }
        public StatisticsPersonFlowBuilder personCount(Long personCount) {
            this.personCount = personCount;
            return this;
        }
        public StatisticsPersonFlowBuilder createTime(LocalDateTime createTime) {
            this.createTime = createTime;
            return this;
        }
        public StatisticsPersonFlow build() {
            return new StatisticsPersonFlow(this.statisticsId, this.statisticsDate, this.personType, this.personTypeName, this.personCount, this.createTime);
        }
        public String toString() {
            return "StatisticsPersonFlow.StatisticsPersonFlowBuilder(statisticsId=" + this.statisticsId + ", statisticsDate=" + this.statisticsDate + ", personType=" + this.personType + ", personTypeName=" + this.personTypeName + ", personCount=" + this.personCount + ", createTime=" + this.createTime + StringPool.RIGHT_BRACKET;
        }
    }
    public String toString() {
        return "StatisticsPersonFlow(super=" + super.toString() + ", statisticsId=" + getStatisticsId() + ", statisticsDate=" + getStatisticsDate() + ", personType=" + getPersonType() + ", personTypeName=" + getPersonTypeName() + ", personCount=" + getPersonCount() + ", createTime=" + getCreateTime() + StringPool.RIGHT_BRACKET;
    }
    public static StatisticsPersonFlowBuilder builder() {
        return new StatisticsPersonFlowBuilder();
    }
    public StatisticsPersonFlow(Long statisticsId, LocalDate statisticsDate, String personType, String personTypeName, Long personCount, LocalDateTime createTime) {
        this.statisticsId = statisticsId;
        this.statisticsDate = statisticsDate;
        this.personType = personType;
        this.personTypeName = personTypeName;
        this.personCount = personCount;
        this.createTime = createTime;
    }
    public StatisticsPersonFlow() {
    }
    public Long getStatisticsId() {
        return this.statisticsId;
    }
    public LocalDate getStatisticsDate() {
        return this.statisticsDate;
    }
    public String getPersonType() {
        return this.personType;
    }
    public String getPersonTypeName() {
        return this.personTypeName;
    }
    public Long getPersonCount() {
        return this.personCount;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
}
