package com.xrkc.job.domain;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/SystemUserDataScopeVO.class */
public class SystemUserDataScopeVO implements Serializable {
    private static final long serialVersionUID = 1;
    private Long roleId;
    private Long userId;
    private String dataScope;
    private Long deptId;
    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    public void setDataScope(String dataScope) {
        this.dataScope = dataScope;
    }
    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof SystemUserDataScopeVO)) {
            return false;
        }
        SystemUserDataScopeVO other = (SystemUserDataScopeVO) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$roleId = getRoleId();
        Object other$roleId = other.getRoleId();
        if (this$roleId == null) {
            if (other$roleId != null) {
                return false;
            }
        } else if (!this$roleId.equals(other$roleId)) {
            return false;
        }
        Object this$userId = getUserId();
        Object other$userId = other.getUserId();
        if (this$userId == null) {
            if (other$userId != null) {
                return false;
            }
        } else if (!this$userId.equals(other$userId)) {
            return false;
        }
        Object this$deptId = getDeptId();
        Object other$deptId = other.getDeptId();
        if (this$deptId == null) {
            if (other$deptId != null) {
                return false;
            }
        } else if (!this$deptId.equals(other$deptId)) {
            return false;
        }
        Object this$dataScope = getDataScope();
        Object other$dataScope = other.getDataScope();
        return this$dataScope == null ? other$dataScope == null : this$dataScope.equals(other$dataScope);
    }
    protected boolean canEqual(Object other) {
        return other instanceof SystemUserDataScopeVO;
    }
    public int hashCode() {
        Object $roleId = getRoleId();
        int result = (1 * 59) + ($roleId == null ? 43 : $roleId.hashCode());
        Object $userId = getUserId();
        int result2 = (result * 59) + ($userId == null ? 43 : $userId.hashCode());
        Object $deptId = getDeptId();
        int result3 = (result2 * 59) + ($deptId == null ? 43 : $deptId.hashCode());
        Object $dataScope = getDataScope();
        return (result3 * 59) + ($dataScope == null ? 43 : $dataScope.hashCode());
    }
    public String toString() {
        return "SystemUserDataScopeVO(roleId=" + getRoleId() + ", userId=" + getUserId() + ", dataScope=" + getDataScope() + ", deptId=" + getDeptId() + StringPool.RIGHT_BRACKET;
    }
    public Long getRoleId() {
        return this.roleId;
    }
    public Long getUserId() {
        return this.userId;
    }
    public String getDataScope() {
        return this.dataScope;
    }
    public Long getDeptId() {
        return this.deptId;
    }
}
