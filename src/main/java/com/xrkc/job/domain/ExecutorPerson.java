package com.xrkc.job.domain;
import java.io.Serializable;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/ExecutorPerson.class */
public class ExecutorPerson implements Serializable {
    private static final long serialVersionUID = 1;
    private String deleteId;
    private String temporaryAllowId;
    private Long personId;
    private String hikvisionPersonIdId;
    private String updateFail;
    private String notUpdate;
    public String getNotUpdate() {
        return this.notUpdate;
    }
    public void setNotUpdate(String notUpdate) {
        this.notUpdate = notUpdate;
    }
    public String getUpdateFail() {
        return this.updateFail;
    }
    public void setUpdateFail(String updateFail) {
        this.updateFail = updateFail;
    }
    public String getDeleteId() {
        return this.deleteId;
    }
    public void setDeleteId(String deleteId) {
        this.deleteId = deleteId;
    }
    public String getTemporaryAllowId() {
        return this.temporaryAllowId;
    }
    public void setTemporaryAllowId(String temporaryAllowId) {
        this.temporaryAllowId = temporaryAllowId;
    }
    public Long getPersonId() {
        return this.personId;
    }
    public void setPersonId(Long personId) {
        this.personId = personId;
    }
    public String getHikvisionPersonIdId() {
        return this.hikvisionPersonIdId;
    }
    public void setHikvisionPersonIdId(String hikvisionPersonIdId) {
        this.hikvisionPersonIdId = hikvisionPersonIdId;
    }
}
