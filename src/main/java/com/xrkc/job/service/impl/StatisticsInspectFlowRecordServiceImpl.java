package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xrkc.job.domain.StatisticsInspectFlowRecord;
import com.xrkc.job.mapper.StatisticsInspectFlowRecordMapper;
import com.xrkc.job.service.IStatisticsInspectFlowRecordService;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/StatisticsInspectFlowRecordServiceImpl.class */
public class StatisticsInspectFlowRecordServiceImpl implements IStatisticsInspectFlowRecordService {
    @Autowired
    private StatisticsInspectFlowRecordMapper statisticsInspectFlowRecordMapper;
    @Override // com.xrkc.job.service.IStatisticsInspectFlowRecordService
    public int batchReplace(List<StatisticsInspectFlowRecord> statisticsInspectFlowRecord) {
        Set<String> set = new HashSet<>();
        ArrayList arrayList = new ArrayList();
        if (CollectionUtils.isEmpty(statisticsInspectFlowRecord)) {
            return 0;
        }
        LocalDate statisticsDate = statisticsInspectFlowRecord.get(0).getStatisticsDate();
        List<StatisticsInspectFlowRecord> oldList = this.statisticsInspectFlowRecordMapper.selectByStatisticsDate(statisticsDate);
        if (CollectionUtils.isEmpty(oldList)) {
            arrayList.addAll(statisticsInspectFlowRecord);
        } else {
            StringBuilder buf = new StringBuilder();
            for (StatisticsInspectFlowRecord danger : oldList) {
                buf.append(danger.getStatisticsDate());
                buf.append(danger.getRecordType());
                set.add(buf.toString());
                buf.setLength(0);
            }
            for (StatisticsInspectFlowRecord record : statisticsInspectFlowRecord) {
                buf.append(record.getStatisticsDate());
                buf.append(record.getRecordType());
                if (set.contains(buf.toString())) {
                    LambdaUpdateWrapper<StatisticsInspectFlowRecord> wrapper = new LambdaUpdateWrapper<>();
                    wrapper.eq((v0) -> {
                        return v0.getRecordType();
                    }, record.getRecordType());
                    wrapper.eq((v0) -> {
                        return v0.getStatisticsDate();
                    }, record.getStatisticsDate());
                    this.statisticsInspectFlowRecordMapper.update(record, wrapper);
                } else {
                    arrayList.add(record);
                }
                buf.setLength(0);
            }
        }
        if (!CollectionUtils.isEmpty(arrayList)) {
            this.statisticsInspectFlowRecordMapper.insertBatch(arrayList, 1000);
        }
        return arrayList.size();
    }
}
