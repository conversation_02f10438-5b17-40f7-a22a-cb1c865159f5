package com.xrkc.job.module.vehicle.service;
import com.xrkc.core.domain.vehicle.VehiclePositionCalc;
import com.xrkc.core.domain.vehicle.VehiclePositionCurrent;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/module/vehicle/service/IVehiclePositionCalcService.class */
public interface IVehiclePositionCalcService {
    List<VehiclePositionCalc> selectListOfHistory(int i);
    void batchInsert(List<VehiclePositionCalc> list);
    void delData();
    List<VehiclePositionCalc> selectCurrentListByCurrent(VehiclePositionCurrent vehiclePositionCurrent);
    List<VehiclePositionCalc> selectCurrentListByTime(VehiclePositionCurrent vehiclePositionCurrent);
}
