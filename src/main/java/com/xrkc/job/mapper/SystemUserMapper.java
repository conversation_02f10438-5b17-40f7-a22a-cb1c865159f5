package com.xrkc.job.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xrkc.job.domain.SystemUser;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/SystemUserMapper.class */
public interface SystemUserMapper extends BaseMapper<SystemUser> {
    @Select({"SELECT user_name FROM `system_user`"})
    List<String> findAllUserName();
    @Select({"select person_id from system_user where user_id=#{userId}"})
    Long getPersonIdByUserId(@Param("userId") Long l);
    List<SystemUser> findByUserIds(@Param("list") Set<Long> set);
}
