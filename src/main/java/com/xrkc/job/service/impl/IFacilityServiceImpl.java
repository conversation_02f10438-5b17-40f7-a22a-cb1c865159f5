package com.xrkc.job.service.impl;
import com.xrkc.job.domain.Facility;
import com.xrkc.job.domain.FacilityRail;
import com.xrkc.job.domain.FacilityRailVO;
import com.xrkc.job.mapper.FacilityMapper;
import com.xrkc.job.service.IFacilityService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/IFacilityServiceImpl.class */
public class IFacilityServiceImpl implements IFacilityService {
    @Autowired
    private FacilityMapper facilityMapper;
    @Override // com.xrkc.job.service.IFacilityService
    public List<FacilityRailVO> selectFacilityRailVOList() {
        return this.facilityMapper.selectFacilityRailVOList();
    }
    @Override // com.xrkc.job.service.IFacilityService
    public List<FacilityRail> selectFacilityRailList() {
        return this.facilityMapper.selectFacilityRailList();
    }
    @Override // com.xrkc.job.service.IFacilityService
    public List<Facility> selectFacilityAndRailList() {
        return this.facilityMapper.selectFacilityAndRailList();
    }
    @Override // com.xrkc.job.service.IFacilityService
    public List<Facility> selectFacilityList() {
        return this.facilityMapper.selectFacilityList();
    }
    @Override // com.xrkc.job.service.IFacilityService
    public List<FacilityRailVO> getRailScopes(Long facilityId) {
        return this.facilityMapper.getRailScopes(facilityId);
    }
}
