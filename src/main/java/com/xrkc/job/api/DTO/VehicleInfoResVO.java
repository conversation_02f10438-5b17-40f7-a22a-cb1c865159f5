package com.xrkc.job.api.DTO;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/api/DTO/VehicleInfoResVO.class */
public class VehicleInfoResVO {
    private Long cardId;
    private Long vehicleCarId;
    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }
    public void setVehicleCarId(Long vehicleCarId) {
        this.vehicleCarId = vehicleCarId;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof VehicleInfoResVO)) {
            return false;
        }
        VehicleInfoResVO other = (VehicleInfoResVO) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$cardId = getCardId();
        Object other$cardId = other.getCardId();
        if (this$cardId == null) {
            if (other$cardId != null) {
                return false;
            }
        } else if (!this$cardId.equals(other$cardId)) {
            return false;
        }
        Object this$vehicleCarId = getVehicleCarId();
        Object other$vehicleCarId = other.getVehicleCarId();
        return this$vehicleCarId == null ? other$vehicleCarId == null : this$vehicleCarId.equals(other$vehicleCarId);
    }
    protected boolean canEqual(Object other) {
        return other instanceof VehicleInfoResVO;
    }
    public int hashCode() {
        Object $cardId = getCardId();
        int result = (1 * 59) + ($cardId == null ? 43 : $cardId.hashCode());
        Object $vehicleCarId = getVehicleCarId();
        return (result * 59) + ($vehicleCarId == null ? 43 : $vehicleCarId.hashCode());
    }
    public String toString() {
        return "VehicleInfoResVO(cardId=" + getCardId() + ", vehicleCarId=" + getVehicleCarId() + StringPool.RIGHT_BRACKET;
    }
    public Long getCardId() {
        return this.cardId;
    }
    public Long getVehicleCarId() {
        return this.vehicleCarId;
    }
}
