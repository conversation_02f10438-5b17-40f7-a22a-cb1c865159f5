package com.xrkc.job.domain;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.math.BigDecimal;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/InspectRecordItemParam.class */
public class InspectRecordItemParam implements Serializable {
    private static final long serialVersionUID = 1;
    private Long recordId;
    private Long locationId;
    private Long itemId;
    private String itemName;
    private String paramName;
    private BigDecimal paramMin;
    private BigDecimal paramMax;
    private BigDecimal paramVal;
    private String paramUnits;
    @JsonSerialize(using = ToStringSerializer.class)
    public Long getItemId() {
        return this.itemId;
    }
    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }
    public String getParamName() {
        return this.paramName;
    }
    public void setParamName(String paramName) {
        this.paramName = paramName;
    }
    public BigDecimal getParamMin() {
        return this.paramMin;
    }
    public void setParamMin(BigDecimal paramMin) {
        this.paramMin = paramMin;
    }
    public BigDecimal getParamMax() {
        return this.paramMax;
    }
    public void setParamMax(BigDecimal paramMax) {
        this.paramMax = paramMax;
    }
    public String getParamUnits() {
        return this.paramUnits;
    }
    public void setParamUnits(String paramUnits) {
        this.paramUnits = paramUnits;
    }
    public Long getRecordId() {
        return this.recordId;
    }
    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }
    public String getItemName() {
        return this.itemName;
    }
    public void setItemName(String itemName) {
        this.itemName = itemName;
    }
    public BigDecimal getParamVal() {
        return this.paramVal;
    }
    public void setParamVal(BigDecimal paramVal) {
        this.paramVal = paramVal;
    }
    public Long getLocationId() {
        return this.locationId;
    }
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }
}
