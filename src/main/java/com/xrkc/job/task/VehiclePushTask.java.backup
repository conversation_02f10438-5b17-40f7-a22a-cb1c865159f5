package com.xrkc.job.task;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.domain.system.SystemApi;
import com.xrkc.job.domain.DeviceCardSenderVehicleLog;
import com.xrkc.job.service.IDeviceCardSenderService;
import com.xrkc.job.service.IDeviceCardSenderVehicleLogService;
import com.xrkc.job.service.SystemApiService;
import com.xrkc.job.util.MQTTPublishClient;
import java.lang.invoke.SerializedLambda;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import net.jodah.expiringmap.ExpiringMap;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
@Component("vehiclePushTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/VehiclePushTask.class */
public class VehiclePushTask {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) VehiclePushTask.class);
    @Autowired
    private IDeviceCardSenderService cardSenderService;
    @Autowired
    private MQTTPublishClient mqttPublishClient;
    @Autowired
    IDeviceCardSenderVehicleLogService deviceCardSenderVehicleLogService;
    @Autowired
    private SystemApiService systemApiService;
    ExpiringMap<Long, LocalDateTime> cardSenderLogMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    public void pushMQTTSenderCardRecord() {
        LambdaQueryWrapper<DeviceCardSenderVehicleLog> wrapper = Wrappers.lambdaQuery();
        wrapper.ge((v0) -> {
            return v0.getCreateTime();
        }, LocalDateTime.now().minusDays(1L));
        wrapper.ne((v0) -> {
            return v0.getNotifyStatus();
        }, "1");
        List<DeviceCardSenderVehicleLog> list = this.deviceCardSenderVehicleLogService.list(wrapper);
        List<DeviceCardSenderVehicleLog> removeList = (List) list.stream().filter(f -> {
            return 1 == f.getCardSenderType().intValue();
        }).collect(Collectors.toList());
        List<DeviceCardSenderVehicleLog> returnList = (List) list.stream().filter(f2 -> {
            return 0 == f2.getCardSenderType().intValue();
        }).collect(Collectors.toList());
        Map<String, Long> deviceSnMap = new HashMap<>();
        this.cardSenderService.list().forEach(record -> {
            deviceSnMap.put(record.getDeviceSn(), record.getCardSenderId());
        });
        MqttClient mqttClient = this.mqttPublishClient.getClient();
        if (mqttClient != null && mqttClient.isConnected()) {
            String topic = "/vehicle/takeCard";
            removeList.forEach(senderLog -> {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("uniqueId", UUID.randomUUID().toString());
                jsonObject.put("vehicleId", senderLog.getVehicleId());
                jsonObject.put("licensePlateNumber", senderLog.getLicensePlateNumber());
                jsonObject.put("cardId", senderLog.getCardId());
                jsonObject.put("result", StrUtil.equalsIgnoreCase(senderLog.getResult(), "成功") ? "0" : "1");
                jsonObject.put("remark", senderLog.getRemark());
                jsonObject.put("cardSenderId", deviceSnMap.get(senderLog.getDeviceSn()));
                jsonObject.put("takeTime", senderLog.getCreateTime());
                if (this.mqttPublishClient.publish(mqttClient, topic, jsonObject.toString()).booleanValue()) {
                    senderLog.setNotifyStatus("1");
                } else {
                    senderLog.setNotifyStatus("2");
                }
                this.deviceCardSenderVehicleLogService.updateById(senderLog);
            });
            returnList.forEach(senderLog2 -> {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("uniqueId", UUID.randomUUID().toString());
                jsonObject.put("vehicleId", senderLog2.getVehicleId());
                jsonObject.put("licensePlateNumber", senderLog2.getLicensePlateNumber());
                jsonObject.put("cardId", senderLog2.getCardId());
                jsonObject.put("cardSenderId", deviceSnMap.get(senderLog2.getDeviceSn()));
                jsonObject.put("returnTime", senderLog2.getCreateTime());
                jsonObject.put("remark", senderLog2.getRemark());
                jsonObject.put("result", StrUtil.equalsIgnoreCase(senderLog2.getResult(), "成功") ? "0" : "1");
                if (this.mqttPublishClient.publish(mqttClient, "/vehicle/returnCard", jsonObject.toString()).booleanValue()) {
                    senderLog2.setNotifyStatus("1");
                } else {
                    senderLog2.setNotifyStatus("2");
                }
                this.deviceCardSenderVehicleLogService.updateById(senderLog2);
            });
        }
    }
    public void pushHttpSenderTakeCardRecord() {
        SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_HTTP_PUSH);
        if (systemApi == null) {
            return;
        }
        LambdaQueryWrapper<DeviceCardSenderVehicleLog> wrapper = Wrappers.lambdaQuery();
        wrapper.ge((v0) -> {
            return v0.getCreateTime();
        }, LocalDateTime.now().minusDays(1L));
        wrapper.ne((v0) -> {
            return v0.getNotifyStatus();
        }, "1");
        wrapper.eq((v0) -> {
            return v0.getCardSenderType();
        }, "1");
        List<DeviceCardSenderVehicleLog> list = this.deviceCardSenderVehicleLogService.list(wrapper);
        Map<String, Long> deviceSnMap = new HashMap<>();
        this.cardSenderService.list().forEach(record -> {
            deviceSnMap.put(record.getDeviceSn(), record.getCardSenderId());
        });
        list.forEach(senderLog -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", senderLog.getId());
            jsonObject.put("vehicleId", senderLog.getVehicleId());
            jsonObject.put("cardId", senderLog.getCardId());
            jsonObject.put("result", StrUtil.equalsIgnoreCase(senderLog.getResult(), "成功") ? "0" : "1");
            jsonObject.put("licensePlateNumber", senderLog.getLicensePlateNumber());
            jsonObject.put("remark", senderLog.getRemark());
            jsonObject.put("cardSenderId", deviceSnMap.get(senderLog.getDeviceSn()));
            jsonObject.put("takeTime", senderLog.getCreateTime());
            String result = HttpUtil.post(systemApi.getHost() + "/cardSender/vehicle/takeCard", jsonObject.toJSONString(new JSONWriter.Feature[0]), 3000);
            log.info("发卡结果通知响应：{},参数：{}", result, jsonObject.toJSONString(new JSONWriter.Feature[0]));
            if (JSONObject.parseObject(result).getIntValue("code") == 200) {
                senderLog.setNotifyStatus("1");
            } else {
                senderLog.setNotifyStatus("2");
            }
            this.deviceCardSenderVehicleLogService.updateById(senderLog);
        });
    }
    public void pushHttpSenderReturnCardRecord() {
        SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_HTTP_PUSH);
        if (systemApi == null) {
            return;
        }
        LambdaQueryWrapper<DeviceCardSenderVehicleLog> wrapper = Wrappers.lambdaQuery();
        wrapper.ge((v0) -> {
            return v0.getCreateTime();
        }, LocalDateTime.now().minusDays(1L));
        wrapper.ne((v0) -> {
            return v0.getNotifyStatus();
        }, "1");
        wrapper.eq((v0) -> {
            return v0.getCardSenderType();
        }, "0");
        List<DeviceCardSenderVehicleLog> list = this.deviceCardSenderVehicleLogService.list(wrapper);
        Map<String, Long> deviceSnMap = new HashMap<>();
        this.cardSenderService.list().forEach(record -> {
            deviceSnMap.put(record.getDeviceSn(), record.getCardSenderId());
        });
        list.forEach(senderLog -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", senderLog.getId());
            jsonObject.put("vehicleId", senderLog.getVehicleId());
            jsonObject.put("licensePlateNumber", senderLog.getLicensePlateNumber());
            jsonObject.put("cardId", senderLog.getCardId());
            jsonObject.put("cardSenderId", deviceSnMap.get(senderLog.getDeviceSn()));
            jsonObject.put("returnTime", senderLog.getCreateTime());
            String result = HttpUtil.post(systemApi.getHost() + "/cardSender/vehicle/returnCard", jsonObject.toJSONString(new JSONWriter.Feature[0]), 3000);
            log.info("还卡结果通知响应：{},参数：{}", result, jsonObject.toJSONString(new JSONWriter.Feature[0]));
            if (JSONObject.parseObject(result).getIntValue("code") == 200) {
                senderLog.setNotifyStatus("1");
            } else {
                senderLog.setNotifyStatus("2");
            }
            this.deviceCardSenderVehicleLogService.updateById(senderLog);
        });
    }
}
