package com.xrkc.job.service.impl;
import com.xrkc.core.exception.CustomException;
import com.xrkc.core.utils.BackupSqlUtils;
import com.xrkc.core.utils.CommonUtil;
import com.xrkc.job.config.SpringDataSourceProperties;
import com.xrkc.job.domain.SystemBackupSqlLog;
import com.xrkc.job.mapper.SystemBackupSqlLogMapper;
import com.xrkc.job.service.ISystemBackupSqlLogService;
import java.time.LocalDateTime;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/SystemBackupSqlLogServiceImpl.class */
public class SystemBackupSqlLogServiceImpl implements ISystemBackupSqlLogService {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) SystemBackupSqlLogServiceImpl.class);
    @Autowired
    private SystemBackupSqlLogMapper backupSqlLogMapper;
    @Autowired
    private SpringDataSourceProperties springDataSourceProperties;
    @Override // com.xrkc.job.service.ISystemBackupSqlLogService
    public Long createSqlLog(String backUpPath) {
        String url = this.springDataSourceProperties.getUrl();
        String username = this.springDataSourceProperties.getUsername();
        String password = this.springDataSourceProperties.getPassword();
        String sqlCommand = BackupSqlUtils.getBackupSqlCommand(url, username, password, backUpPath);
        if (StringUtils.isBlank(sqlCommand)) {
            throw new CustomException("获取备份指令失败，请联系管理员");
        }
        SystemBackupSqlLog backUpLog = new SystemBackupSqlLog();
        backUpLog.setResult("备份中");
        backUpLog.setBackPath(backUpPath);
        backUpLog.setRemark("定时任务备份");
        backUpLog.setCreateTime(LocalDateTime.now());
        this.backupSqlLogMapper.insert(backUpLog);
        backSQLData(backUpLog.getId(), sqlCommand);
        return backUpLog.getId();
    }
    public void backSQLData(Long backId, String sqlCommand) {
        SystemBackupSqlLog systemBackupSqlLog = new SystemBackupSqlLog();
        systemBackupSqlLog.setId(backId);
        systemBackupSqlLog.setResult("失败");
        systemBackupSqlLog.setUpdateTime(LocalDateTime.now());
        try {
            try {
                int code = CommonUtil.execSqlCommand(sqlCommand);
                systemBackupSqlLog.setResult(code == 0 ? "成功" : "失败");
                systemBackupSqlLog.setRemark(code == 0 ? null : "code:" + code);
                this.backupSqlLogMapper.updateById(systemBackupSqlLog);
            } catch (Exception e) {
                log.info("数据库备份出现异常：{}", e.getMessage());
                this.backupSqlLogMapper.updateById(systemBackupSqlLog);
            }
        } catch (Throwable th) {
            this.backupSqlLogMapper.updateById(systemBackupSqlLog);
            throw th;
        }
    }
}
