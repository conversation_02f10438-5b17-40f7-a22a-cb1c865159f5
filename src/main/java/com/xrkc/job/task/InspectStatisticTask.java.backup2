package com.xrkc.job.task;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xrkc.datascope.aspect.DataScopeAspect;
import com.xrkc.job.domain.DangerContrastBo;
import com.xrkc.job.domain.HiddenDanger;
import com.xrkc.job.domain.InspectRecord;
import com.xrkc.job.domain.InspectRecordLocation;
import com.xrkc.job.domain.LocationContrastBo;
import com.xrkc.job.domain.RoadContrastBo;
import com.xrkc.job.domain.StatisticsInspectFlow;
import com.xrkc.job.domain.StatisticsInspectFlowDanger;
import com.xrkc.job.domain.StatisticsInspectFlowRecord;
import com.xrkc.job.domain.SystemDict;
import com.xrkc.job.mapper.HiddenDangerAbarbeitungMapper;
import com.xrkc.job.mapper.StatisticsInspectTaskMapper;
import com.xrkc.job.service.IHiddenDangerService;
import com.xrkc.job.service.IInspectRecordLocationService;
import com.xrkc.job.service.IInspectRecordService;
import com.xrkc.job.service.IStatisticsInspectFlowDangerService;
import com.xrkc.job.service.IStatisticsInspectFlowRecordService;
import com.xrkc.job.service.IStatisticsInspectFlowService;
import com.xrkc.job.service.ISystemDictService;
import java.lang.invoke.SerializedLambda;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
@Component("inspectStatisticTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/InspectStatisticTask.class */
public class InspectStatisticTask {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) InspectStatisticTask.class);
    @Autowired
    private IInspectRecordLocationService recordLocationService;
    @Autowired
    private IHiddenDangerService hiddenDangerService;
    @Autowired
    private IInspectRecordService recordService;
    @Autowired
    private StatisticsInspectTaskMapper inspectTaskMapper;
    @Autowired
    private IStatisticsInspectFlowService inspectFlowService;
    @Autowired
    private IStatisticsInspectFlowRecordService inspectFlowRecordService;
    @Autowired
    private IStatisticsInspectFlowDangerService inspectFlowDangerService;
    @Autowired
    private ISystemDictService systemDictService;
    @Autowired
    private IHiddenDangerService dangerService;
    @Autowired
    private HiddenDangerAbarbeitungMapper abarbeitungMapper;
    private static /* synthetic */ Object $deserializeLambda$(SerializedLambda lambda) {
        switch (lambda.getImplMethodName()) {
            case "getAbarbeitungId":
                if (lambda.getImplMethodKind() == 5 && lambda.getFunctionalInterfaceClass().equals("com/baomidou/mybatisplus/core/toolkit/support/SFunction") && lambda.getFunctionalInterfaceMethodName().equals("apply") && lambda.getFunctionalInterfaceMethodSignature().equals("(Ljava/lang/Object;)Ljava/lang/Object;") && lambda.getImplClass().equals("com/xrkc/job/domain/HiddenDanger") && lambda.getImplMethodSignature().equals("()Ljava/lang/Long;")) {
                    return (v0) -> {
                        return v0.getAbarbeitungId();
                    };
                }
                break;
        }
        throw new IllegalArgumentException("Invalid lambda deserialization");
    }
    public void dangerStatus() {
        List<Long> expireHiddenAbtungIds = this.abarbeitungMapper.findExpireRectifyDeadline(LocalDateTime.now());
        expireHiddenAbtungIds.forEach(f -> {
            HiddenDanger hiddenDanger = new HiddenDanger();
            hiddenDanger.setAbarbeitungId(f).setHiddenDangerResult(5);
            LambdaUpdateWrapper<HiddenDanger> wrapper = (LambdaUpdateWrapper) Wrappers.lambdaUpdate().eq((v0) -> {
                return v0.getAbarbeitungId();
            }, f);
            this.dangerService.update(hiddenDanger, wrapper);
        });
    }
    public void inspectStatistic() {
        LocalDateTime nowTime = LocalDateTime.now();
        LocalDate now = LocalDate.now();
        LocalDate yesterday = now.minus(1L, (TemporalUnit) ChronoUnit.DAYS);
        statisticsLocation(yesterday, nowTime);
        statisticsToday(now);
        statisticsHiddenDanger(yesterday, nowTime);
        statisticsRecord(yesterday, nowTime);
    }
    public void inspectStatisticTodayFlow() {
        LocalDateTime nowTime = LocalDateTime.now();
        LocalDate now = LocalDate.now();
        LocalDate yesterday = now.minus(1L, (TemporalUnit) ChronoUnit.DAYS);
        statisticsLocation(yesterday, nowTime);
        statisticsToday(now);
        statisticsHiddenDanger(yesterday, nowTime);
        statisticsRecord(yesterday, nowTime);
    }
    private void statisticsRecord(LocalDate now, LocalDateTime nowTime) {
        List<InspectRecord> records = this.recordService.selectYesterdayRecord(now, nowTime);
        Integer recordNotCount = Integer.valueOf(((List) records.stream().filter(f -> {
            return "1".equals(f.getInspectStatus());
        }).collect(Collectors.toList())).size());
        Integer recordDangerCount = Integer.valueOf(((List) records.stream().filter(f2 -> {
            return Objects.nonNull(f2.getAbnormalLocationCount()) && 0 != f2.getAbnormalLocationCount().intValue();
        }).collect(Collectors.toList())).size());
        Integer recordNormalCount = Integer.valueOf(((List) records.stream().filter(f3 -> {
            return "3".equals(f3.getInspectStatus()) && 0 == f3.getAbnormalLocationCount().intValue();
        }).collect(Collectors.toList())).size());
        Map<String, Integer> countMap = new HashMap<>();
        countMap.put("not_record", recordNotCount);
        countMap.put("danger_record", recordDangerCount);
        countMap.put("normal_record", recordNormalCount);
        List<StatisticsInspectFlowRecord> statisticsInspectFlowRecord = new ArrayList<>();
        List<SystemDict> inspectTypeList = this.systemDictService.findDictType("record_label");
        inspectTypeList.stream().forEach(dict -> {
            if (countMap.get(dict.getDictValue()) != null) {
                StatisticsInspectFlowRecord entity = new StatisticsInspectFlowRecord();
                entity.setStatisticsDate(now);
                entity.setRecordType(dict.getDictValue());
                entity.setRecordTypeName(dict.getDictLabel());
                entity.setCreateTime(nowTime);
                entity.setRecordCount((Integer) countMap.get(dict.getDictValue()));
                statisticsInspectFlowRecord.add(entity);
            }
        });
        if (CollectionUtils.isNotEmpty(statisticsInspectFlowRecord)) {
            this.inspectFlowRecordService.batchReplace(statisticsInspectFlowRecord);
        }
    }
    private void statisticsToday(LocalDate now) {
        RoadContrastBo statisticVo = this.inspectTaskMapper.selectTodayInspect(now);
        Set<Integer> personCount = this.inspectTaskMapper.selectTodayPerson(now);
        personCount.removeIf(f -> {
            return Objects.isNull(f);
        });
        LocationContrastBo locationStat = this.inspectTaskMapper.selectTodayLocation(now);
        DangerContrastBo dangerStat = this.inspectTaskMapper.selectTodayDanger(now);
        Map<String, Integer> countMap = new HashMap<>();
        countMap.put("finish_road", statisticVo.getRoadCount());
        countMap.put("all_road", statisticVo.getRoadAllCount());
        countMap.put("all_person", Integer.valueOf(personCount.size()));
        countMap.put("finish_location", locationStat.getLocationCount());
        countMap.put("all_location", locationStat.getLocationAllCount());
        countMap.put("finish_danger", dangerStat.getDangerCount());
        countMap.put("all_danger", dangerStat.getDangerAllCount());
        List<SystemDict> inspectTypeList = this.systemDictService.findDictType("inspect_label");
        List<StatisticsInspectFlow> statisticsInspectFlow = new ArrayList<>();
        inspectTypeList.stream().forEach(dict -> {
            StatisticsInspectFlow entity = new StatisticsInspectFlow();
            entity.setStatisticsDate(now);
            entity.setInspectType(dict.getDictValue());
            entity.setInspectTypeName(dict.getDictLabel());
            entity.setInspectCount((Integer) countMap.get(dict.getDictValue()));
            statisticsInspectFlow.add(entity);
        });
        if (CollectionUtils.isNotEmpty(statisticsInspectFlow)) {
            this.inspectFlowService.batchReplace(statisticsInspectFlow);
        }
    }
    private void statisticsHiddenDanger(LocalDate now, LocalDateTime nowTime) {
        List<HiddenDanger> hiddenDangers = this.hiddenDangerService.selectDangerList(now, nowTime);
        Map<String, Integer> countMap = new HashMap<>();
        countMap.put("1", Integer.valueOf(((List) hiddenDangers.stream().filter(f -> {
            return 1 == f.getHiddenDangerResult().intValue();
        }).collect(Collectors.toList())).size()));
        countMap.put("2", Integer.valueOf(((List) hiddenDangers.stream().filter(f2 -> {
            return 2 == f2.getHiddenDangerResult().intValue();
        }).collect(Collectors.toList())).size()));
        countMap.put("3", Integer.valueOf(((List) hiddenDangers.stream().filter(f3 -> {
            return 3 == f3.getHiddenDangerResult().intValue();
        }).collect(Collectors.toList())).size()));
        countMap.put("4", Integer.valueOf(((List) hiddenDangers.stream().filter(f4 -> {
            return 4 == f4.getHiddenDangerResult().intValue();
        }).collect(Collectors.toList())).size()));
        countMap.put("5", Integer.valueOf(((List) hiddenDangers.stream().filter(f5 -> {
            return 5 == f5.getHiddenDangerResult().intValue();
        }).collect(Collectors.toList())).size()));
        countMap.put(DataScopeAspect.DATA_SCOPE_CUSTOM_AND_CHILD, Integer.valueOf(((List) hiddenDangers.stream().filter(f6 -> {
            return 6 == f6.getHiddenDangerResult().intValue();
        }).collect(Collectors.toList())).size()));
        countMap.put("7", Integer.valueOf(((List) hiddenDangers.stream().filter(f7 -> {
            return 7 == f7.getHiddenDangerResult().intValue();
        }).collect(Collectors.toList())).size()));
        countMap.put("8", Integer.valueOf(((List) hiddenDangers.stream().filter(f8 -> {
            return 8 == f8.getHiddenDangerResult().intValue();
        }).collect(Collectors.toList())).size()));
        List<SystemDict> inspectTypeList = this.systemDictService.findDictType("danger_type");
        List<StatisticsInspectFlowDanger> statisticsInspectFlowDangers = new ArrayList<>();
        inspectTypeList.stream().forEach(dict -> {
            StatisticsInspectFlowDanger entity = new StatisticsInspectFlowDanger();
            entity.setStatisticsDate(now);
            entity.setDangerType(dict.getDictValue());
            entity.setDangerTypeName(dict.getDictLabel());
            entity.setCreateTime(nowTime);
            entity.setDangerCount((Integer) countMap.get(dict.getDictValue()));
            statisticsInspectFlowDangers.add(entity);
        });
        if (CollectionUtils.isNotEmpty(statisticsInspectFlowDangers)) {
            this.inspectFlowDangerService.batchReplace(statisticsInspectFlowDangers);
        }
    }
    private void statisticsLocation(LocalDate now, LocalDateTime nowTime) {
        List<InspectRecordLocation> locations = this.recordLocationService.selectLocationList(now, nowTime);
        Map<String, Integer> countMap = new HashMap<>();
        countMap.put("not_location", Integer.valueOf(((List) locations.stream().filter(f -> {
            return "N".equals(f.getLocationStatus());
        }).collect(Collectors.toList())).size()));
        countMap.put("danger_location", Integer.valueOf(((List) locations.stream().filter(f2 -> {
            return "N".equals(f2.getAbnormalStatus());
        }).collect(Collectors.toList())).size()));
        countMap.put("normal_location", Integer.valueOf(((List) locations.stream().filter(f3 -> {
            return "Y".equals(f3.getAbnormalStatus()) && "Y".equals(f3.getLocationStatus());
        }).collect(Collectors.toList())).size()));
        List<StatisticsInspectFlowRecord> statisticsInspectFlowRecord = new ArrayList<>();
        List<SystemDict> inspectTypeList = this.systemDictService.findDictType("record_label");
        inspectTypeList.stream().forEach(dict -> {
            if (countMap.get(dict.getDictValue()) != null) {
                StatisticsInspectFlowRecord entity = new StatisticsInspectFlowRecord();
                entity.setStatisticsDate(now);
                entity.setRecordType(dict.getDictValue());
                entity.setRecordTypeName(dict.getDictLabel());
                entity.setCreateTime(nowTime);
                entity.setRecordCount((Integer) countMap.get(dict.getDictValue()));
                statisticsInspectFlowRecord.add(entity);
            }
        });
        if (CollectionUtils.isNotEmpty(statisticsInspectFlowRecord)) {
            this.inspectFlowRecordService.batchReplace(statisticsInspectFlowRecord);
        }
    }
}
