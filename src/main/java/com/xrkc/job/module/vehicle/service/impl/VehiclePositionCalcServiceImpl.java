package com.xrkc.job.module.vehicle.service.impl;
import com.xrkc.core.domain.vehicle.VehiclePositionCalc;
import com.xrkc.core.domain.vehicle.VehiclePositionCurrent;
import com.xrkc.job.module.vehicle.mapper.VehiclePositionCalcMapper;
import com.xrkc.job.module.vehicle.service.IVehiclePositionCalcService;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/module/vehicle/service/impl/VehiclePositionCalcServiceImpl.class */
public class VehiclePositionCalcServiceImpl implements IVehiclePositionCalcService {
    private final VehiclePositionCalcMapper vehiclePositionCalcMapper;
    @Autowired
    public VehiclePositionCalcServiceImpl(VehiclePositionCalcMapper vehiclePositionCalcMapper) {
        this.vehiclePositionCalcMapper = vehiclePositionCalcMapper;
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehiclePositionCalcService
    public List<VehiclePositionCalc> selectListOfHistory(int second) {
        return this.vehiclePositionCalcMapper.selectListOfHistory(second);
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehiclePositionCalcService
    public void batchInsert(List<VehiclePositionCalc> calcList) {
        if (CollectionUtils.isEmpty(calcList)) {
            return;
        }
        calcList.stream().forEach(t -> {
            t.setId(null);
            t.setCreateTime(LocalDateTime.now());
        });
        this.vehiclePositionCalcMapper.insertBatch(calcList);
    }
    /* JADX WARN: Incorrect condition in loop: B:28:0x0016 */
    @Override // com.xrkc.job.module.vehicle.service.IVehiclePositionCalcService
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void delData() {
        /*
            r3 = this;
            r0 = r3
            com.xrkc.job.module.vehicle.mapper.VehiclePositionCalcMapper r0 = r0.vehiclePositionCalcMapper
            r1 = 60
            java.lang.Long r0 = r0.findMaxId(r1)
            r4 = r0
            r0 = r4
            boolean r0 = java.util.Objects.nonNull(r0)
            if (r0 == 0) goto L27
            r0 = 1
            r5 = r0
        L15:
            r0 = r5
            if (r0 <= 0) goto L27
            r0 = r3
            com.xrkc.job.module.vehicle.mapper.VehiclePositionCalcMapper r0 = r0.vehiclePositionCalcMapper
            r1 = r4
            int r0 = r0.delByMaxId(r1)
            r5 = r0
            goto L15
        L27:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: com.xrkc.job.module.vehicle.service.impl.VehiclePositionCalcServiceImpl.delData():void");
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehiclePositionCalcService
    public List<VehiclePositionCalc> selectCurrentListByCurrent(VehiclePositionCurrent positionCurrent) {
        return this.vehiclePositionCalcMapper.selectCurrentListByCurrent(positionCurrent);
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehiclePositionCalcService
    public List<VehiclePositionCalc> selectCurrentListByTime(VehiclePositionCurrent positionCurrent) {
        return this.vehiclePositionCalcMapper.selectCurrentListByTime(positionCurrent);
    }
}
