package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.core.domain.record.GateRecord;
import com.xrkc.job.mapper.GateRecordMapper;
import com.xrkc.job.service.IGateRecordService;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/GateRecordServiceImpl.class */
public class GateRecordServiceImpl extends ServiceImpl<GateRecordMapper, GateRecord> implements IGateRecordService {
}
