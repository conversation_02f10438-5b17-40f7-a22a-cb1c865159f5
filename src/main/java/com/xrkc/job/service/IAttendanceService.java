package com.xrkc.job.service;
import com.xrkc.job.domain.Attendance;
import java.util.List;
import java.util.Map;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IAttendanceService.class */
public interface IAttendanceService {
    List<Attendance> queryNotAttendanceList(String str);
    List<Attendance> selectPositionList(Map<String, Object> map);
    int batchInsert(List<Attendance> list);
}
