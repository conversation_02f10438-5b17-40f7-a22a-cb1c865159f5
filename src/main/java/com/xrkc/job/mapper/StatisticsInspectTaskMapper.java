package com.xrkc.job.mapper;
import com.xrkc.job.domain.DangerContrastBo;
import com.xrkc.job.domain.LocationContrastBo;
import com.xrkc.job.domain.RoadContrastBo;
import java.time.LocalDate;
import java.util.Set;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.postgresql.jdbc.EscapedFunctions;
import org.springframework.stereotype.Repository;
@Mapper
@Repository
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/StatisticsInspectTaskMapper.class */
public interface StatisticsInspectTaskMapper {
    RoadContrastBo selectTodayInspect(@Param(EscapedFunctions.NOW_FUNC) LocalDate localDate);
    Set<Integer> selectTodayPerson(@Param(EscapedFunctions.NOW_FUNC) LocalDate localDate);
    DangerContrastBo selectTodayDanger(@Param(EscapedFunctions.NOW_FUNC) LocalDate localDate);
    LocationContrastBo selectTodayLocation(@Param(EscapedFunctions.NOW_FUNC) LocalDate localDate);
}
