package com.xrkc.job.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xrkc.core.domain.alarm.CoreAlarmPeople;
import com.xrkc.job.domain.PositionCurrent;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IAlarmPeopleService.class */
public interface IAlarmPeopleService extends IService<CoreAlarmPeople> {
    void saveAlarmPeople(Map<Long, List<PositionCurrent>> map, String str);
    void updatepeopleAlarm(Map<Long, Set<Long>> map, String str, LocalDateTime localDateTime);
    void update50AlarmpeopleAlarm(Map<Long, Set<Long>> map, String str, LocalDateTime localDateTime);
    List<CoreAlarmPeople> selectPersonLowPowerCardList(int i);
    int batchInsert(List<CoreAlarmPeople> list);
}
