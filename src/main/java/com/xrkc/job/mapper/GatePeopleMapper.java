package com.xrkc.job.mapper;
import com.xrkc.core.domain.record.GateRecord;
import com.xrkc.core.domain.vo.GateAreaVo;
import com.xrkc.core.mybatisplus.BaseMapperX;
import com.xrkc.job.domain.DeviceGateVO;
import com.xrkc.job.domain.GatePeople;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/GatePeopleMapper.class */
public interface GatePeopleMapper extends BaseMapperX<GateRecord> {
    List<GateAreaVo> getAreaStaffRecord(@Param("gateArea") String str, @Param("initializationTime") LocalDateTime localDateTime);
    List<String> getAccessRecord(@Param("startTime") LocalDateTime localDateTime, @Param("endTime") LocalDateTime localDateTime2);
    List<DeviceGateVO> getAllGate();
    List<GatePeople> getAllPeople();
}
