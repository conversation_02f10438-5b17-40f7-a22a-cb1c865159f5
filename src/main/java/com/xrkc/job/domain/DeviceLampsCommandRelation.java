package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
@TableName("device_lamps_command_relation")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/DeviceLampsCommandRelation.class */
public class DeviceLampsCommandRelation implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId("id")
    private Long id;
    @TableField("command_id")
    private Long commandId;
    @TableField("command_type")
    private Integer commandType;
    @TableField("relation_id")
    private Long relationId;
    @TableField("command_result")
    private String commandResult;
    @TableField("command_remark")
    private String commandRemark;
    public void setId(Long id) {
        this.id = id;
    }
    public void setCommandId(Long commandId) {
        this.commandId = commandId;
    }
    public void setCommandType(Integer commandType) {
        this.commandType = commandType;
    }
    public void setRelationId(Long relationId) {
        this.relationId = relationId;
    }
    public void setCommandResult(String commandResult) {
        this.commandResult = commandResult;
    }
    public void setCommandRemark(String commandRemark) {
        this.commandRemark = commandRemark;
    }
    public String toString() {
        return "DeviceLampsCommandRelation(id=" + getId() + ", commandId=" + getCommandId() + ", commandType=" + getCommandType() + ", relationId=" + getRelationId() + ", commandResult=" + getCommandResult() + ", commandRemark=" + getCommandRemark() + StringPool.RIGHT_BRACKET;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof DeviceLampsCommandRelation)) {
            return false;
        }
        DeviceLampsCommandRelation other = (DeviceLampsCommandRelation) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$id = getId();
        Object other$id = other.getId();
        if (this$id == null) {
            if (other$id != null) {
                return false;
            }
        } else if (!this$id.equals(other$id)) {
            return false;
        }
        Object this$commandId = getCommandId();
        Object other$commandId = other.getCommandId();
        if (this$commandId == null) {
            if (other$commandId != null) {
                return false;
            }
        } else if (!this$commandId.equals(other$commandId)) {
            return false;
        }
        Object this$commandType = getCommandType();
        Object other$commandType = other.getCommandType();
        if (this$commandType == null) {
            if (other$commandType != null) {
                return false;
            }
        } else if (!this$commandType.equals(other$commandType)) {
            return false;
        }
        Object this$relationId = getRelationId();
        Object other$relationId = other.getRelationId();
        if (this$relationId == null) {
            if (other$relationId != null) {
                return false;
            }
        } else if (!this$relationId.equals(other$relationId)) {
            return false;
        }
        Object this$commandResult = getCommandResult();
        Object other$commandResult = other.getCommandResult();
        if (this$commandResult == null) {
            if (other$commandResult != null) {
                return false;
            }
        } else if (!this$commandResult.equals(other$commandResult)) {
            return false;
        }
        Object this$commandRemark = getCommandRemark();
        Object other$commandRemark = other.getCommandRemark();
        return this$commandRemark == null ? other$commandRemark == null : this$commandRemark.equals(other$commandRemark);
    }
    protected boolean canEqual(Object other) {
        return other instanceof DeviceLampsCommandRelation;
    }
    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $commandId = getCommandId();
        int result2 = (result * 59) + ($commandId == null ? 43 : $commandId.hashCode());
        Object $commandType = getCommandType();
        int result3 = (result2 * 59) + ($commandType == null ? 43 : $commandType.hashCode());
        Object $relationId = getRelationId();
        int result4 = (result3 * 59) + ($relationId == null ? 43 : $relationId.hashCode());
        Object $commandResult = getCommandResult();
        int result5 = (result4 * 59) + ($commandResult == null ? 43 : $commandResult.hashCode());
        Object $commandRemark = getCommandRemark();
        return (result5 * 59) + ($commandRemark == null ? 43 : $commandRemark.hashCode());
    }
    public Long getId() {
        return this.id;
    }
    public Long getCommandId() {
        return this.commandId;
    }
    public Integer getCommandType() {
        return this.commandType;
    }
    public Long getRelationId() {
        return this.relationId;
    }
    public String getCommandResult() {
        return this.commandResult;
    }
    public String getCommandRemark() {
        return this.commandRemark;
    }
}
