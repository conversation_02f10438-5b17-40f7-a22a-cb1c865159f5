package com.xrkc.job.mapper;
import com.xrkc.core.domain.rssi.RssiData;
import com.xrkc.core.mybatisplus.BaseMapperX;
import com.xrkc.job.domain.PositionVO;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.postgresql.jdbc.EscapedFunctions;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/RssiMapper.class */
public interface RssiMapper extends BaseMapperX<RssiData> {
    List<PositionVO> selectRssiList(Map<String, Object> map);
    Long findDelDataMaxId(@Param("minusSeconds") LocalDateTime localDateTime);
    Long findDelTempMaxId(@Param("minusSeconds") LocalDateTime localDateTime);
    Long findDelBakMaxId(@Param("minusHours") LocalDateTime localDateTime);
    int delByDataMaxId(@Param("maxId") Long l, @Param("delRow") int i);
    int delByTempMaxId(@Param("maxId") Long l, @Param("delRow") int i);
    int delByBakMaxId(@Param("maxId") Long l, @Param("delRow") int i);
    List<Long> selectCardIdListByMod(@Param(EscapedFunctions.MOD_FUNC) int i, @Param("minusSeconds") LocalDateTime localDateTime);
    @Delete({"delete from rssi_data where create_time=#{creatTime} ;"})
    void delByCreatTime(LocalDateTime localDateTime);
}
