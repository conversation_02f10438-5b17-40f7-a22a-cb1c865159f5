package com.xrkc.job.service.impl;
import com.xrkc.core.domain.system.SystemDept;
import com.xrkc.job.mapper.SystemDeptMapper;
import com.xrkc.job.service.ISystemDeptService;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/SystemDeptServiceImpl.class */
public class SystemDeptServiceImpl implements ISystemDeptService {
    @Autowired
    private SystemDeptMapper systemDeptMapper;
    @Override // com.xrkc.job.service.ISystemDeptService
    public List<SystemDept> selectList() {
        return this.systemDeptMapper.selectAllList();
    }
    @Override // com.xrkc.job.service.ISystemDeptService
    public Long insertEntity(SystemDept entity) {
        if (Objects.isNull(entity.getParentId())) {
            entity.setParentId(0L);
        }
        entity.setCreateTime(LocalDateTime.now());
        this.systemDeptMapper.insert((SystemDeptMapper) entity);
        return entity.getDeptId();
    }
    @Override // com.xrkc.job.service.ISystemDeptService
    public int updateEntity(SystemDept systemDept) {
        systemDept.setUpdateTime(LocalDateTime.now());
        return this.systemDeptMapper.updateById((SystemDeptMapper) systemDept);
    }
    @Override // com.xrkc.job.service.ISystemDeptService
    public int updatePrimaryId(Long primaryId, List<Long> deptIdList) {
        return this.systemDeptMapper.updatePrimaryId(primaryId, deptIdList);
    }
    @Override // com.xrkc.job.service.ISystemDeptService
    public int deleteEntity(Long deptId) {
        return this.systemDeptMapper.deleteById((Serializable) deptId);
    }
    @Override // com.xrkc.job.service.ISystemDeptService
    public Map<Long, SystemDept> findDeptInfoMap() {
        return (Map) this.systemDeptMapper.selectList().stream().collect(Collectors.toMap((v0) -> {
            return v0.getDeptId();
        }, Function.identity()));
    }
}
