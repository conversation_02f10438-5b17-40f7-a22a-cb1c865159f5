package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xrkc.job.domain.AlarmNoticeRecord;
import com.xrkc.job.mapper.AlarmNoticeRecordMapper;
import com.xrkc.job.service.IAlarmNoticeRecordService;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/AlarmNoticeRecordServiceImpl.class */
public class AlarmNoticeRecordServiceImpl implements IAlarmNoticeRecordService {
    @Autowired
    private AlarmNoticeRecordMapper alarmNoticeSosRecordMapper;
    @Override // com.xrkc.job.service.IAlarmNoticeRecordService
    public void saveEntity(AlarmNoticeRecord thirdRecord) {
        thirdRecord.setCreateTime(LocalDateTime.now());
        this.alarmNoticeSosRecordMapper.insert(thirdRecord);
    }
    @Override // com.xrkc.job.service.IAlarmNoticeRecordService
    public void updateById(AlarmNoticeRecord alarmNoticeRecord) {
        this.alarmNoticeSosRecordMapper.updateById( alarmNoticeRecord);
    }
    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.xrkc.job.service.IAlarmNoticeRecordService
    public List<AlarmNoticeRecord> selectListByParams(String noticeStatic, String source, LocalDateTime beginTime, LocalDateTime endTime) {
        return this.alarmNoticeSosRecordMapper.selectList(((new LambdaQueryWrapper<AlarmNoticeRecord>().eq((v0) -> {
            return v0.getNoticeStatic();
        }, noticeStatic)).eq((v0) -> {
            return v0.getSource();
        }, source)).between((v0) -> {
            return v0.getNoticeTime();
        }, beginTime, endTime));
    }
}
