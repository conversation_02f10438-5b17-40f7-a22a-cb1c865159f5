package com.xrkc.job.domain;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/DeviceGateVO.class */
public class DeviceGateVO {
    private Long id;
    private String readerIndexCode;
    private String gateAccess;
    private String gateName;
    private String gateArea;
    public void setId(Long id) {
        this.id = id;
    }
    public void setReaderIndexCode(String readerIndexCode) {
        this.readerIndexCode = readerIndexCode;
    }
    public void setGateAccess(String gateAccess) {
        this.gateAccess = gateAccess;
    }
    public void setGateName(String gateName) {
        this.gateName = gateName;
    }
    public void setGateArea(String gateArea) {
        this.gateArea = gateArea;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof DeviceGateVO)) {
            return false;
        }
        DeviceGateVO other = (DeviceGateVO) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$id = getId();
        Object other$id = other.getId();
        if (this$id == null) {
            if (other$id != null) {
                return false;
            }
        } else if (!this$id.equals(other$id)) {
            return false;
        }
        Object this$readerIndexCode = getReaderIndexCode();
        Object other$readerIndexCode = other.getReaderIndexCode();
        if (this$readerIndexCode == null) {
            if (other$readerIndexCode != null) {
                return false;
            }
        } else if (!this$readerIndexCode.equals(other$readerIndexCode)) {
            return false;
        }
        Object this$gateAccess = getGateAccess();
        Object other$gateAccess = other.getGateAccess();
        if (this$gateAccess == null) {
            if (other$gateAccess != null) {
                return false;
            }
        } else if (!this$gateAccess.equals(other$gateAccess)) {
            return false;
        }
        Object this$gateName = getGateName();
        Object other$gateName = other.getGateName();
        if (this$gateName == null) {
            if (other$gateName != null) {
                return false;
            }
        } else if (!this$gateName.equals(other$gateName)) {
            return false;
        }
        Object this$gateArea = getGateArea();
        Object other$gateArea = other.getGateArea();
        return this$gateArea == null ? other$gateArea == null : this$gateArea.equals(other$gateArea);
    }
    protected boolean canEqual(Object other) {
        return other instanceof DeviceGateVO;
    }
    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $readerIndexCode = getReaderIndexCode();
        int result2 = (result * 59) + ($readerIndexCode == null ? 43 : $readerIndexCode.hashCode());
        Object $gateAccess = getGateAccess();
        int result3 = (result2 * 59) + ($gateAccess == null ? 43 : $gateAccess.hashCode());
        Object $gateName = getGateName();
        int result4 = (result3 * 59) + ($gateName == null ? 43 : $gateName.hashCode());
        Object $gateArea = getGateArea();
        return (result4 * 59) + ($gateArea == null ? 43 : $gateArea.hashCode());
    }
    public String toString() {
        return "DeviceGateVO(id=" + getId() + ", readerIndexCode=" + getReaderIndexCode() + ", gateAccess=" + getGateAccess() + ", gateName=" + getGateName() + ", gateArea=" + getGateArea() + StringPool.RIGHT_BRACKET;
    }
    public Long getId() {
        return this.id;
    }
    public String getReaderIndexCode() {
        return this.readerIndexCode;
    }
    public String getGateAccess() {
        return this.gateAccess;
    }
    public String getGateName() {
        return this.gateName;
    }
    public String getGateArea() {
        return this.gateArea;
    }
}
