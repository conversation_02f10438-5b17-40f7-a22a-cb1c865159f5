package com.xrkc.job.service;
import com.xrkc.core.domain.inspect.InspectRecord;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IInspectRecordHistoryService.class */
public interface IInspectRecordHistoryService {
    List<InspectRecord> selectCopyHistory();
    int batchSave(List<InspectRecord> list);
    int batchDelRecord(List<Long> list);
    int batchDelRecordLocation(List<Long> list);
    int batchDelRecordItem(List<Long> list);
    int batchDelRecordParam(List<Long> list);
    int delHistory(List<Long> list);
    List<Long> findDelRecordIds();
}
