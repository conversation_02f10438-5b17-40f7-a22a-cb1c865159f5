package com.xrkc.job.util.direct;
import cn.hutool.core.collection.CollUtil;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/direct/DirectUtil.class */
public class DirectUtil {
    public final double earthRadius = 6371.393d;
    private double calculateLineDistance(Point source, Point target) {
        double sourceLongitude = source.getLongitude();
        double sourceLatitude = source.getLatitude();
        double targetLongitude = target.getLongitude();
        double sourceLatitude2 = (sourceLatitude * 3.141592653589793d) / 180.0d;
        double targetLatitude = (target.getLatitude() * 3.141592653589793d) / 180.0d;
        double a = sourceLatitude2 - targetLatitude;
        double b = ((sourceLongitude - targetLongitude) * 3.141592653589793d) / 180.0d;
        double sa2 = Math.sin(a / 2.0d);
        double sb2 = Math.sin(b / 2.0d);
        return 2.0d * 6378137.0d * Math.asin(Math.sqrt((sa2 * sa2) + (Math.cos(sourceLatitude2) * Math.cos(targetLatitude) * sb2 * sb2)));
    }
    public Point getCenterPoint(List<Point> pointList) {
        int total = pointList.size();
        double X = 0.0d;
        double Y = 0.0d;
        double Z = 0.0d;
        for (Point point : pointList) {
            double lat = (point.getLatitude() * 3.141592653589793d) / 180.0d;
            double lng = (point.getLongitude() * 3.141592653589793d) / 180.0d;
            double x = Math.cos(lat) * Math.cos(lng);
            double y = Math.cos(lat) * Math.sin(lng);
            double z = Math.sin(lat);
            X += x;
            Y += y;
            Z += z;
        }
        double X2 = X / total;
        double Y2 = Y / total;
        double Z2 = Z / total;
        double Lon = Math.atan2(Y2, X2);
        double Hyp = Math.sqrt((X2 * X2) + (Y2 * Y2));
        double Lat = Math.atan2(Z2, Hyp);
        double longitude = (Lon * 180.0d) / 3.141592653589793d;
        double latitude = (Lat * 180.0d) / 3.141592653589793d;
        return Point.builder().longitude(longitude).latitude(latitude).build();
    }
    public Double km2Degree(Double l) {
        return Double.valueOf(0.008992661340005603d * l.doubleValue());
    }
    public Double degree2Km(Double degree) {
        return Double.valueOf(111.20178578851908d * degree.doubleValue());
    }
    private boolean directDistance(double r, int vinCount, List<Point> points) {
        List<Cluster> clusters = new ArrayList<>();
        for (Point point : points) {
            Cluster isCluster = null;
            for (Cluster cluster : clusters) {
                if (cluster.getLatLngBounds().contains(point)) {
                    double distance = calculateLineDistance(point, cluster.getCenterPoint());
                    if (distance < r && (isCluster == null || calculateLineDistance(point, cluster.getCenterPoint()) < calculateLineDistance(point, isCluster.getCenterPoint()))) {
                        isCluster = cluster;
                    }
                }
            }
            if (isCluster != null) {
                isCluster.addCount();
                isCluster.getPoints().add(point);
                isCluster.setCenterPoint(getCenterPoint(isCluster.getPoints()));
                isCluster.setLatLngBounds(LatLngBounds.builder().northeast(Point.builder().latitude(point.getLatitude() + km2Degree(Double.valueOf(r / 1000.0d)).doubleValue()).longitude(point.getLongitude() + km2Degree(Double.valueOf(r / 1000.0d)).doubleValue()).build()).southwest(Point.builder().latitude(point.getLatitude() - km2Degree(Double.valueOf(r / 1000.0d)).doubleValue()).longitude(point.getLongitude() - km2Degree(Double.valueOf(r / 1000.0d)).doubleValue()).build()).build());
            } else {
                clusters.add(Cluster.builder().centerPoint(point).points(CollUtil.newLinkedList(point)).count(1).latLngBounds(LatLngBounds.builder().northeast(Point.builder().latitude(point.getLatitude() + km2Degree(Double.valueOf(r / 1000.0d)).doubleValue()).longitude(point.getLongitude() + km2Degree(Double.valueOf(r / 1000.0d)).doubleValue()).build()).southwest(Point.builder().latitude(point.getLatitude() - km2Degree(Double.valueOf(r / 1000.0d)).doubleValue()).longitude(point.getLongitude() - km2Degree(Double.valueOf(r / 1000.0d)).doubleValue()).build()).build()).build());
            }
        }
        Iterator<Cluster> it = clusters.iterator();
        while (it.hasNext()) {
            if (it.next().getPoints().size() > vinCount) {
                return true;
            }
        }
        return false;
    }
}
