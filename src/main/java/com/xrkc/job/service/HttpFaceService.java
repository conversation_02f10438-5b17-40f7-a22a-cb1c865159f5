package com.xrkc.job.service;
import com.alibaba.fastjson2.JSONObject;
import com.xrkc.job.domain.DeviceCardSender;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/HttpFaceService.class */
public interface HttpFaceService {
    String deviceLogin(DeviceCardSender deviceCardSender);
    JSONObject getDeviceParameter(DeviceCardSender deviceCardSender);
    JSONObject setDeviceParameter(String str, JSONObject jSONObject);
    JSONObject checkFacePicture2(String str, String str2, String str3);
    String syncPersonToFace(DeviceCardSender deviceCardSender, int i, int i2, JSONObject jSONObject, String str, String str2, String str3, String str4, String str5);
    List<String> getAllDeviceIdWhiteList(DeviceCardSender deviceCardSender);
    void deleteDeviceWhiteList(DeviceCardSender deviceCardSender, List<String> list);
    void deleteDeviceAllWhiteList(DeviceCardSender deviceCardSender);
    void setDeviceTime(String str, String str2);
    void setErrImageRedis(String str, String str2);
    String getCheckFaceResult(Long l, String str, String str2);
}
