package com.xrkc.job.task;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.domain.person.Person;
import com.xrkc.core.domain.system.SystemApi;
import com.xrkc.core.domain.vehicle.VehicleAlarm;
import com.xrkc.core.enums.NoticeType;
import com.xrkc.job.domain.AlarmNotice;
import com.xrkc.job.domain.AlarmNoticeRecord;
import com.xrkc.job.domain.AlarmNoticeRecordPerson;
import com.xrkc.job.mapper.AlarmNoticeSosMapper;
import com.xrkc.job.module.vehicle.service.IVehicleAlarmService;
import com.xrkc.job.service.IAlarmNoticeRecordPersonService;
import com.xrkc.job.service.IAlarmNoticeRecordService;
import com.xrkc.job.service.IPersonService;
import com.xrkc.job.service.SystemApiService;
import com.xrkc.job.util.CollStrConver;
import com.xrkc.job.util.CommonUtil;
import com.xrkc.job.util.EmailUtilApi;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import net.jodah.expiringmap.ExpiringMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
@Component("vehicleMessageTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/VehicleMessageTask.class */
public class VehicleMessageTask {
    private static final Logger log = LogManager.getLogger((Class<?>) VehicleMessageTask.class);
    @Autowired
    private IPersonService personService;
    @Autowired
    private AlarmNoticeSosMapper alarmNoticeSosMapper;
    @Autowired
    private EmailUtilApi emailUtilApi;
    @Autowired
    private IVehicleAlarmService vehicleAlarmService;
    @Autowired
    private IAlarmNoticeRecordService alarmNoticeRecordService;
    @Autowired
    private IAlarmNoticeRecordPersonService alarmNoticeRecordPersonService;
    @Autowired
    private SystemApiService systemApiService;
    public static final String source = "车辆";
    ExpiringMap<String, LocalDateTime> vehicleNoticeAlarmMap = ExpiringMap.builder().expiration(5, TimeUnit.DAYS).build();
    ExpiringMap<Long, LocalDateTime> vehicleAlarmSmsMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    ExpiringMap<Long, LocalDateTime> vehicleRecordSmsMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    @Transactional(rollbackFor = {Exception.class})
    public void insertVehicleAlarmRecord() {
        SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_SMS_PERSON);
        SystemApi emailApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_SEND_EMAIL);
        if (Objects.nonNull(systemApi) || Objects.nonNull(emailApi)) {
            List<VehicleAlarm> vehicleList = this.vehicleAlarmService.selectUnDisposedVehicleAlarmRail();
            if (!CollectionUtils.isEmpty(vehicleList)) {
                Map<Long, AlarmNotice> noticeMap = getNoticeMap(vehicleList);
                insertAlarmRecord(noticeMap, vehicleList);
            }
        }
    }
    private Map<Long, AlarmNotice> getNoticeMap(List<VehicleAlarm> alarmRailList) {
        Map<Long, AlarmNotice> map = new HashMap<>();
        Set<Long> alarmNoticeIdList = (Set) alarmRailList.stream().map((v0) -> {
            return v0.getSettingId();
        }).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty((Collection<?>) alarmNoticeIdList)) {
            List<AlarmNotice> noticeList = this.alarmNoticeSosMapper.findByAlarmNoticeIds(alarmNoticeIdList);
            map = (Map) noticeList.stream().collect(Collectors.toMap((v0) -> {
                return v0.getVehicleSeetingId();
            }, t -> {
                return t;
            }));
        }
        return map;
    }
    private void insertAlarmRecord(Map<Long, AlarmNotice> map, List<VehicleAlarm> alarmList) {
        alarmList.stream().forEach(personAlarm -> {
            AlarmNotice alarmNotice = (AlarmNotice) map.get(personAlarm.getSettingId());
            if (Objects.isNull(alarmNotice)) {
                log.error("车辆 alarmNotice is null，alarmList={}", JSONObject.toJSONString(alarmList, new JSONWriter.Feature[0]));
                return;
            }
            String noticeKey = StrUtil.concat(true, personAlarm.getId().toString(), ",", alarmNotice.getAlarmNoticeId().toString());
            if (this.vehicleNoticeAlarmMap.containsKey(noticeKey)) {
                return;
            }
            Long alarmNoticeId = alarmNotice.getAlarmNoticeId();
            List<AlarmNoticeRecord> allSosNoticeRecord = new ArrayList<>();
            ArrayList arrayList = new ArrayList();
            List<Long> firstPersons = CollStrConver.str2longList(alarmNotice.getFirstPersonId());
            List<Long> secondPersons = CollStrConver.str2longList(alarmNotice.getSecondPersonId());
            List<Long> thirdPersons = CollStrConver.str2longList(alarmNotice.getThirdPersonId());
            LocalDateTime now = LocalDateTime.now();
            String noticeType = alarmNotice.getNoticeType();
            if (!CollectionUtils.isEmpty(firstPersons)) {
                ArrayList arrayList2 = new ArrayList();
                AlarmNoticeRecord firstRecord = new AlarmNoticeRecord();
                firstRecord.setNoticeId(alarmNoticeId);
                firstRecord.setAlarmId(personAlarm.getId());
                firstRecord.setNoticeInterval(0L);
                firstRecord.setNoticeTime(now);
                firstRecord.setAlarmTime(personAlarm.getAcceptTime());
                firstRecord.setAlarmType(personAlarm.getAlarmType());
                firstRecord.setAlarmTypeName(personAlarm.getAlarmTypeName());
                firstRecord.setAreaName(personAlarm.getSettingName());
                firstRecord.setLayerId(personAlarm.getLayerId());
                firstRecord.setRealName(personAlarm.getDriverName());
                firstRecord.setNoticeStatic("20");
                firstRecord.setNoticeLevel(1);
                firstRecord.setSource(source);
                List<Person> personList = this.personService.getPersonsByIds(firstPersons);
                firstRecord.setNoticeType(noticeType);
                firstRecord.setVehicleName(personAlarm.getVehicleName());
                firstRecord.setLicensePlateNumber(personAlarm.getLicensePlateNumber());
                if (CollectionUtils.isEmpty(personList)) {
                    firstRecord.setNoticeStatic("10");
                    firstRecord.setRemark("无有效接收人");
                    allSosNoticeRecord.add(firstRecord);
                    this.alarmNoticeRecordService.saveEntity(firstRecord);
                } else {
                    this.alarmNoticeRecordService.saveEntity(firstRecord);
                    personList.forEach(person -> {
                        AlarmNoticeRecordPerson recordPerson = new AlarmNoticeRecordPerson();
                        recordPerson.setRecordId(firstRecord.getRecordId());
                        recordPerson.setNoticeTime(firstRecord.getNoticeTime());
                        recordPerson.setPersonId(person.getPersonId());
                        if (CommonUtil.isValidNumberOrMail(person, noticeType)) {
                            recordPerson.setNoticeStatic("20");
                            recordPerson.setNoticeResult("已处理待验证");
                        } else {
                            recordPerson.setNoticeStatic("10");
                            recordPerson.setNoticeResult("无效接收人");
                        }
                        recordPerson.setPhone(CommonUtil.setPersonPhone(person, noticeType));
                        recordPerson.setRealName(person.getRealName());
                        arrayList2.add(recordPerson);
                    });
                    allSosNoticeRecord.add(firstRecord);
                }
                arrayList.addAll(arrayList2);
            }
            if (!CollectionUtils.isEmpty(secondPersons)) {
                ArrayList arrayList3 = new ArrayList();
                AlarmNoticeRecord secondRecord = new AlarmNoticeRecord();
                secondRecord.setNoticeId(alarmNoticeId);
                secondRecord.setNoticeInterval(alarmNotice.getSecondIntervalSecond());
                secondRecord.setNoticeTime(now.plusMinutes(alarmNotice.getSecondIntervalSecond().longValue()));
                secondRecord.setAlarmTime(personAlarm.getAcceptTime());
                secondRecord.setAlarmId(personAlarm.getId());
                secondRecord.setAlarmType(personAlarm.getAlarmType());
                secondRecord.setAlarmTypeName(personAlarm.getAlarmTypeName());
                secondRecord.setAreaName(personAlarm.getSettingName());
                secondRecord.setLayerId(personAlarm.getLayerId());
                secondRecord.setRealName(personAlarm.getDriverName());
                secondRecord.setNoticeStatic("20");
                secondRecord.setNoticeLevel(2);
                allSosNoticeRecord.add(secondRecord);
                secondRecord.setNoticeType(noticeType);
                secondRecord.setSource(source);
                secondRecord.setVehicleName(personAlarm.getVehicleName());
                secondRecord.setLicensePlateNumber(personAlarm.getLicensePlateNumber());
                List<Person> personList2 = this.personService.getPersonsByIds(secondPersons);
                if (CollectionUtils.isEmpty(personList2)) {
                    secondRecord.setNoticeStatic("10");
                    secondRecord.setRemark("无有效接收人");
                    this.alarmNoticeRecordService.saveEntity(secondRecord);
                    allSosNoticeRecord.add(secondRecord);
                } else {
                    this.alarmNoticeRecordService.saveEntity(secondRecord);
                    personList2.forEach(person2 -> {
                        AlarmNoticeRecordPerson recordPerson = new AlarmNoticeRecordPerson();
                        recordPerson.setRecordId(secondRecord.getRecordId());
                        recordPerson.setNoticeTime(secondRecord.getNoticeTime());
                        recordPerson.setPersonId(person2.getPersonId());
                        if (CommonUtil.isValidNumberOrMail(person2, noticeType)) {
                            recordPerson.setNoticeStatic("20");
                            recordPerson.setNoticeResult("已处理待验证");
                        } else {
                            recordPerson.setNoticeStatic("10");
                            recordPerson.setNoticeResult("无效接收人");
                        }
                        recordPerson.setPhone(CommonUtil.setPersonPhone(person2, noticeType));
                        recordPerson.setRealName(person2.getRealName());
                        arrayList3.add(recordPerson);
                    });
                }
                arrayList.addAll(arrayList3);
            }
            if (!CollectionUtils.isEmpty(thirdPersons)) {
                ArrayList arrayList4 = new ArrayList();
                AlarmNoticeRecord thirdRecord = new AlarmNoticeRecord();
                thirdRecord.setNoticeInterval(alarmNotice.getThirdIntervalSecond());
                thirdRecord.setNoticeId(alarmNoticeId);
                thirdRecord.setNoticeTime(now.plusMinutes(alarmNotice.getThirdIntervalSecond().longValue()));
                thirdRecord.setAlarmTime(personAlarm.getAcceptTime());
                thirdRecord.setAlarmId(personAlarm.getId());
                thirdRecord.setAlarmType(personAlarm.getAlarmType());
                thirdRecord.setAlarmTypeName(personAlarm.getAlarmTypeName());
                thirdRecord.setAreaName(personAlarm.getSettingName());
                thirdRecord.setLayerId(personAlarm.getLayerId());
                thirdRecord.setRealName(personAlarm.getDriverName());
                thirdRecord.setNoticeStatic("20");
                thirdRecord.setNoticeLevel(3);
                thirdRecord.setNoticeType(noticeType);
                thirdRecord.setSource(source);
                thirdRecord.setVehicleName(personAlarm.getVehicleName());
                thirdRecord.setLicensePlateNumber(personAlarm.getLicensePlateNumber());
                List<Person> personList3 = this.personService.getPersonsByIds(thirdPersons);
                if (CollectionUtils.isEmpty(personList3)) {
                    thirdRecord.setNoticeStatic("10");
                    thirdRecord.setRemark("无有效接收人");
                    this.alarmNoticeRecordService.saveEntity(thirdRecord);
                    allSosNoticeRecord.add(thirdRecord);
                } else {
                    this.alarmNoticeRecordService.saveEntity(thirdRecord);
                    personList3.forEach(person3 -> {
                        AlarmNoticeRecordPerson recordPerson = new AlarmNoticeRecordPerson();
                        recordPerson.setRecordId(thirdRecord.getRecordId());
                        recordPerson.setNoticeTime(thirdRecord.getNoticeTime());
                        recordPerson.setPersonId(person3.getPersonId());
                        if (CommonUtil.isValidNumberOrMail(person3, noticeType)) {
                            recordPerson.setNoticeStatic("20");
                            recordPerson.setNoticeResult("已处理待验证");
                        } else {
                            recordPerson.setNoticeStatic("10");
                            recordPerson.setNoticeResult("无效接收人");
                        }
                        recordPerson.setPhone(CommonUtil.setPersonPhone(person3, noticeType));
                        recordPerson.setRealName(person3.getRealName());
                        arrayList4.add(recordPerson);
                    });
                }
                arrayList.addAll(arrayList4);
            }
            if (!CollectionUtils.isEmpty(arrayList)) {
                this.alarmNoticeRecordPersonService.saveBatch(arrayList);
            }
            this.vehicleNoticeAlarmMap.put(noticeKey, personAlarm.getAcceptTime());
        });
    }
    @Transactional(rollbackFor = {Exception.class})
    public void verifyVehicleAlarmNotice() {
        LocalDateTime now = LocalDateTime.now();
        List<AlarmNoticeRecord> alarmRecords = this.alarmNoticeRecordService.selectListByParams("20", source, now.minusSeconds(60L), now);
        if (CollectionUtils.isEmpty(alarmRecords)) {
            return;
        }
        List<Long> disposedVehicleAlarmIds = this.vehicleAlarmService.selectDisposedVehicleAlarmId((List) alarmRecords.stream().map((v0) -> {
            return v0.getAlarmId();
        }).collect(Collectors.toList()));
        alarmRecords.forEach(alarmRecord -> {
            if (this.vehicleAlarmSmsMap.containsKey(alarmRecord.getRecordId())) {
                return;
            }
            if (disposedVehicleAlarmIds.contains(alarmRecord.getAlarmId())) {
                alarmRecord.setNoticeStatic("30");
                alarmRecord.setRemark("报警已处理，无需通知");
                this.alarmNoticeRecordService.updateById(alarmRecord);
                AlarmNoticeRecordPerson recordPerson = new AlarmNoticeRecordPerson();
                recordPerson.setRecordId(alarmRecord.getRecordId());
                recordPerson.setNoticeStatic("30");
                recordPerson.setNoticeResult("报警已处理，无需通知");
                this.alarmNoticeRecordPersonService.updateByRecordId(recordPerson);
            } else {
                List<AlarmNoticeRecordPerson> recordPeoples = this.alarmNoticeRecordPersonService.getListByRecordId(alarmRecord.getRecordId());
                if (CollectionUtils.isEmpty(recordPeoples)) {
                    alarmRecord.setNoticeStatic("10");
                    alarmRecord.setRemark("无有效通知人");
                    this.alarmNoticeRecordService.updateById(alarmRecord);
                    return;
                } else {
                    StringBuilder phoneRealNames = new StringBuilder();
                    recordPeoples.forEach(recordPeople -> {
                        recordPeople.setNoticeStatic("40");
                        recordPeople.setNoticeResult("已验证待通知");
                        LambdaQueryWrapper<AlarmNoticeRecordPerson> wrapper3 = Wrappers.lambdaQuery();
                        ((LambdaQueryWrapper) wrapper3.eq((v0) -> {
                            return v0.getRecordId();
                        }, recordPeople.getRecordId())).eq((v0) -> {
                            return v0.getPersonId();
                        }, recordPeople.getPersonId());
                        this.alarmNoticeRecordPersonService.update(recordPeople, wrapper3);
                        phoneRealNames.append(recordPeople.getRealName()).append("：").append(recordPeople.getPhone()).append("；");
                    });
                    alarmRecord.setNoticeStatic("40");
                    alarmRecord.setRemark("已验证待通知");
                    alarmRecord.setPhoneNumbers((String) recordPeoples.stream().map((v0) -> {
                        return v0.getPhone();
                    }).collect(Collectors.joining(",")));
                    alarmRecord.setPhoneRealNames(phoneRealNames.toString());
                    this.alarmNoticeRecordService.updateById(alarmRecord);
                }
            }
            this.vehicleAlarmSmsMap.put(alarmRecord.getRecordId(), alarmRecord.getCreateTime());
        });
    }
    public void sendVehicleAlarmSms() {
        LocalDateTime now = LocalDateTime.now();
        List<AlarmNoticeRecord> alarmRecords = this.alarmNoticeRecordService.selectListByParams("40", source, now.minusSeconds(30L), now);
        alarmRecords.removeIf(r -> {
            return StringUtils.isBlank(r.getPhoneNumbers());
        });
        if (CollectionUtils.isEmpty(alarmRecords)) {
            return;
        }
        this.systemApiService.getByApiKey(ConfigValue.API_KEY_SMS_PERSON);
        SystemApi mailApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_SEND_EMAIL);
        alarmRecords.stream().forEach(alarmRecord -> {
            if (this.vehicleRecordSmsMap.containsKey(alarmRecord.getRecordId())) {
                return;
            }
            this.vehicleRecordSmsMap.put(alarmRecord.getRecordId(), now);
            try {
                if (NoticeType.sms.getKey().equals(alarmRecord.getNoticeType())) {
                    log.info("车辆短信功能暂未开启");
                } else if (NoticeType.email.getKey().equals(alarmRecord.getNoticeType())) {
                    this.emailUtilApi.sendMail(mailApi, alarmRecord, source);
                } else {
                    log.error("{}:短信发送通知类型异常:{}", source, alarmRecord.getRecordId());
                }
            } catch (Exception e) {
                log.error(e.getMessage(), (Throwable) e);
            }
        });
    }
}
