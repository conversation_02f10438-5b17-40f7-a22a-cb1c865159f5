package com.xrkc.job.util;
import com.xrkc.job.domain.SystemJob;
import org.quartz.CronScheduleBuilder;
import org.quartz.CronTrigger;
import org.quartz.Job;
import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.TriggerBuilder;
import org.quartz.TriggerKey;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/ScheduleUtils.class */
public class ScheduleUtils {
    private static Class<? extends Job> getQuartzJobClass(SystemJob systemJob) {
        boolean isConcurrent = "Y".equals(systemJob.getJobConcurrent());
        return isConcurrent ? QuartzJobExecution.class : QuartzDisallowConcurrentExecution.class;
    }
    public static TriggerKey getTriggerKey(Long jobId, String jobGroup) {
        return TriggerKey.triggerKey("TASK_CLASS_NAME" + jobId, jobGroup);
    }
    public static JobKey getJobKey(Long jobId, String jobGroup) {
        return JobKey.jobKey("TASK_CLASS_NAME" + jobId, jobGroup);
    }
    public static void createScheduleJob(Scheduler scheduler, SystemJob job) throws SchedulerException, TaskException {
        Class<? extends Job> jobClass = getQuartzJobClass(job);
        Long jobId = job.getJobId();
        String jobGroup = job.getJobGroup();
        JobDetail jobDetail = JobBuilder.newJob(jobClass).withIdentity(getJobKey(jobId, jobGroup)).build();
        CronScheduleBuilder cronScheduleBuilder = CronScheduleBuilder.cronSchedule(job.getJobCron());
        CronTrigger trigger = (CronTrigger) TriggerBuilder.newTrigger().withIdentity(getTriggerKey(jobId, jobGroup)).withSchedule(handleCronScheduleMisfirePolicy(job, cronScheduleBuilder)).build();
        jobDetail.getJobDataMap().put("TASK_PROPERTIES", (Object) job);
        if (scheduler.checkExists(getJobKey(jobId, jobGroup))) {
            scheduler.deleteJob(getJobKey(jobId, jobGroup));
        }
        scheduler.scheduleJob(jobDetail, trigger);
        if ("N".equals(job.getJobEnable())) {
            scheduler.pauseJob(getJobKey(jobId, jobGroup));
        }
    }
    public static CronScheduleBuilder handleCronScheduleMisfirePolicy(SystemJob job, CronScheduleBuilder cb) throws TaskException {
        switch (job.getJobPlan()) {
            case "0":
                return cb;
            case "1":
                return cb.withMisfireHandlingInstructionIgnoreMisfires();
            case "2":
                return cb.withMisfireHandlingInstructionFireAndProceed();
            case "3":
                return cb.withMisfireHandlingInstructionDoNothing();
            default:
                throw new TaskException("The task misfire policy '" + job.getJobPlan() + "' cannot be used in cron schedule tasks");
        }
    }
}
