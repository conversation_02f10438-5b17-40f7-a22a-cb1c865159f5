package com.xrkc.job.api.DTO;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/api/DTO/VehicleInfoRes.class */
public class VehicleInfoRes {
    private String imei;
    private Long carId;
    public void setImei(String imei) {
        this.imei = imei;
    }
    public void setCarId(Long carId) {
        this.carId = carId;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof VehicleInfoRes)) {
            return false;
        }
        VehicleInfoRes other = (VehicleInfoRes) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$carId = getCarId();
        Object other$carId = other.getCarId();
        if (this$carId == null) {
            if (other$carId != null) {
                return false;
            }
        } else if (!this$carId.equals(other$carId)) {
            return false;
        }
        Object this$imei = getImei();
        Object other$imei = other.getImei();
        return this$imei == null ? other$imei == null : this$imei.equals(other$imei);
    }
    protected boolean canEqual(Object other) {
        return other instanceof VehicleInfoRes;
    }
    public int hashCode() {
        Object $carId = getCarId();
        int result = (1 * 59) + ($carId == null ? 43 : $carId.hashCode());
        Object $imei = getImei();
        return (result * 59) + ($imei == null ? 43 : $imei.hashCode());
    }
    public String toString() {
        return "VehicleInfoRes(imei=" + getImei() + ", carId=" + getCarId() + StringPool.RIGHT_BRACKET;
    }
    public String getImei() {
        return this.imei;
    }
    public Long getCarId() {
        return this.carId;
    }
}
