package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
@TableName("statistics_inspect_flow_record")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/StatisticsInspectFlowRecord.class */
public class StatisticsInspectFlowRecord implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId("statistics_id")
    private Long statisticsId;
    @TableField("statistics_date")
    private LocalDate statisticsDate;
    @TableField("record_type")
    private String recordType;
    @TableField("record_type_name")
    private String recordTypeName;
    @TableField("record_count")
    private Integer recordCount;
    @TableField("create_time")
    private LocalDateTime createTime;
    public void setStatisticsId(Long statisticsId) {
        this.statisticsId = statisticsId;
    }
    public void setStatisticsDate(LocalDate statisticsDate) {
        this.statisticsDate = statisticsDate;
    }
    public void setRecordType(String recordType) {
        this.recordType = recordType;
    }
    public void setRecordTypeName(String recordTypeName) {
        this.recordTypeName = recordTypeName;
    }
    public void setRecordCount(Integer recordCount) {
        this.recordCount = recordCount;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof StatisticsInspectFlowRecord)) {
            return false;
        }
        StatisticsInspectFlowRecord other = (StatisticsInspectFlowRecord) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$statisticsId = getStatisticsId();
        Object other$statisticsId = other.getStatisticsId();
        if (this$statisticsId == null) {
            if (other$statisticsId != null) {
                return false;
            }
        } else if (!this$statisticsId.equals(other$statisticsId)) {
            return false;
        }
        Object this$recordCount = getRecordCount();
        Object other$recordCount = other.getRecordCount();
        if (this$recordCount == null) {
            if (other$recordCount != null) {
                return false;
            }
        } else if (!this$recordCount.equals(other$recordCount)) {
            return false;
        }
        Object this$statisticsDate = getStatisticsDate();
        Object other$statisticsDate = other.getStatisticsDate();
        if (this$statisticsDate == null) {
            if (other$statisticsDate != null) {
                return false;
            }
        } else if (!this$statisticsDate.equals(other$statisticsDate)) {
            return false;
        }
        Object this$recordType = getRecordType();
        Object other$recordType = other.getRecordType();
        if (this$recordType == null) {
            if (other$recordType != null) {
                return false;
            }
        } else if (!this$recordType.equals(other$recordType)) {
            return false;
        }
        Object this$recordTypeName = getRecordTypeName();
        Object other$recordTypeName = other.getRecordTypeName();
        if (this$recordTypeName == null) {
            if (other$recordTypeName != null) {
                return false;
            }
        } else if (!this$recordTypeName.equals(other$recordTypeName)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        return this$createTime == null ? other$createTime == null : this$createTime.equals(other$createTime);
    }
    protected boolean canEqual(Object other) {
        return other instanceof StatisticsInspectFlowRecord;
    }
    public int hashCode() {
        Object $statisticsId = getStatisticsId();
        int result = (1 * 59) + ($statisticsId == null ? 43 : $statisticsId.hashCode());
        Object $recordCount = getRecordCount();
        int result2 = (result * 59) + ($recordCount == null ? 43 : $recordCount.hashCode());
        Object $statisticsDate = getStatisticsDate();
        int result3 = (result2 * 59) + ($statisticsDate == null ? 43 : $statisticsDate.hashCode());
        Object $recordType = getRecordType();
        int result4 = (result3 * 59) + ($recordType == null ? 43 : $recordType.hashCode());
        Object $recordTypeName = getRecordTypeName();
        int result5 = (result4 * 59) + ($recordTypeName == null ? 43 : $recordTypeName.hashCode());
        Object $createTime = getCreateTime();
        return (result5 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
    }
    public String toString() {
        return "StatisticsInspectFlowRecord(statisticsId=" + getStatisticsId() + ", statisticsDate=" + getStatisticsDate() + ", recordType=" + getRecordType() + ", recordTypeName=" + getRecordTypeName() + ", recordCount=" + getRecordCount() + ", createTime=" + getCreateTime() + StringPool.RIGHT_BRACKET;
    }
    public Long getStatisticsId() {
        return this.statisticsId;
    }
    public LocalDate getStatisticsDate() {
        return this.statisticsDate;
    }
    public String getRecordType() {
        return this.recordType;
    }
    public String getRecordTypeName() {
        return this.recordTypeName;
    }
    public Integer getRecordCount() {
        return this.recordCount;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
}
