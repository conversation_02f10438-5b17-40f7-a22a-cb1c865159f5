package com.xrkc.job.task;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.constant.Constants;
import com.xrkc.core.domain.rail.RailDrawType;
import com.xrkc.core.domain.vehicle.VehicleAlarm;
import com.xrkc.core.domain.vehicle.VehicleInfo;
import com.xrkc.core.domain.vehicle.VehiclePositionCalc;
import com.xrkc.core.domain.vehicle.VehiclePositionCurrent;
import com.xrkc.core.domain.vehicle.dto.VehicleAlarmRailVO;
import com.xrkc.core.domain.vehicle.dto.VehicleAlarmSettingReq;
import com.xrkc.core.utils.CommonUtil;
import com.xrkc.job.domain.DeviceCard;
import com.xrkc.job.domain.DeviceLayer;
import com.xrkc.job.module.vehicle.service.IVehicleAlarmService;
import com.xrkc.job.module.vehicle.service.IVehicleAlarmSettingService;
import com.xrkc.job.module.vehicle.service.IVehicleInfoService;
import com.xrkc.job.module.vehicle.service.IVehiclePositionCalcService;
import com.xrkc.job.module.vehicle.service.IVehiclePositionCurrentService;
import com.xrkc.job.service.IDeviceCardService;
import com.xrkc.job.service.IDeviceLayerService;
import com.xrkc.job.service.ISystemInfoService;
import dm.jdbc.util.StringUtil;
import java.lang.invoke.SerializedLambda;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.chrono.ChronoLocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import net.jodah.expiringmap.ExpiringMap;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
@Component("vehicleAlarmTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/VehicleAlarmTask.class */
public class VehicleAlarmTask {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) VehicleAlarmTask.class);
    @Autowired
    private IVehicleAlarmSettingService vehicleAlarmSettingService;
    @Autowired
    private IVehiclePositionCurrentService vehiclePositionCurrentService;
    @Autowired
    private IVehicleAlarmService vehicleAlarmService;
    @Autowired
    private IVehicleInfoService vehicleInfoService;
    @Autowired
    private IDeviceCardService deviceCardService;
    @Autowired
    private IDeviceLayerService deviceLayerService;
    @Autowired
    private ISystemInfoService systemInfoService;
    Map<String, Map<Long, LocalDateTime>> vehicleAreaRetentionMap = ExpiringMap.builder().expiration(20, TimeUnit.DAYS).build();
    Map<String, Map<Long, LocalDateTime>> vehicleAreaIllegalParkingMap = ExpiringMap.builder().expiration(20, TimeUnit.DAYS).build();
    Map<Long, VehiclePositionCurrent> vehicleLastPositionMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    @Autowired
    private IVehiclePositionCalcService vehiclePositionCalcService;
    private static boolean removeInvalidLocalDate(LocalDate validBeginDate, LocalDate validEndDate, LocalDate nowDate) {
        if (Objects.isNull(validBeginDate) && Objects.isNull(validEndDate)) {
            return false;
        }
        if (Objects.nonNull(validBeginDate) && Objects.nonNull(validEndDate) && (nowDate.compareTo((ChronoLocalDate) validBeginDate) < 0 || nowDate.compareTo((ChronoLocalDate) validEndDate) > 0)) {
            return true;
        }
        if (Objects.nonNull(validBeginDate) && Objects.isNull(validEndDate) && nowDate.compareTo((ChronoLocalDate) validBeginDate) < 0) {
            return true;
        }
        if (Objects.isNull(validBeginDate) && Objects.nonNull(validEndDate) && nowDate.compareTo((ChronoLocalDate) validEndDate) > 0) {
            return true;
        }
        return false;
    }
    private static boolean removeInvalidLocalTime(LocalTime validBeginTime, LocalTime validEndTime, LocalTime nowTime) {
        if (Objects.nonNull(validBeginTime) && Objects.nonNull(validBeginTime) && nowTime.isAfter(validBeginTime) && nowTime.isBefore(validEndTime)) {
            return false;
        }
        return true;
    }
    private static boolean isValidRule(List<VehicleAlarmSettingReq> list) {
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        LocalDate nowDate = LocalDate.now();
        LocalTime nowTime = LocalTime.now();
        list.removeIf(r -> {
            return removeInvalidLocalDate(r.getValidBeginDate(), r.getValidEndDate(), nowDate) || removeInvalidLocalTime(r.getValidBeginTime(), r.getValidEndTime(), nowTime);
        });
        return !CollectionUtils.isEmpty(list);
    }
    public void calcOverSpeedAlarm() {
        List<VehicleAlarmSettingReq> list = this.vehicleAlarmSettingService.selectEnableListByType("5");
        if (!isValidRule(list)) {
            return;
        }
        List<VehiclePositionCurrent> positionCurrentList = this.vehiclePositionCurrentService.selectCurrentList();
        List<VehicleAlarm> vehicleAlarmList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        list.stream().forEach(setting -> {
            List<VehicleInfo> vehicleInfoList = setting.getVehicleInfoList();
            List<VehicleAlarmRailVO> railList = setting.getRailList();
            Long rule = setting.getRule();
            if (Objects.isNull(rule) || CollectionUtils.isEmpty(vehicleInfoList) || CollectionUtils.isEmpty(positionCurrentList)) {
                return;
            }
            Map<Long, VehicleInfo> vehicleInfoMap = (Map) vehicleInfoList.stream().collect(Collectors.toMap((v0) -> {
                return v0.getId();
            }, Function.identity()));
            List<VehiclePositionCurrent> vehicleOverSpeedPositionCurrentList = (List) positionCurrentList.stream().filter(f -> {
                return Objects.nonNull(f.getSpeed()) && ((long) f.getSpeed().intValue()) > rule.longValue() && vehicleInfoMap.containsKey(f.getVehicleId()) && f.getAcceptTime().isAfter(LocalDateTime.now().minusSeconds(5L));
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(vehicleOverSpeedPositionCurrentList)) {
                return;
            }
            if (CollectionUtils.isEmpty(railList)) {
                vehicleOverSpeedPositionCurrentList.stream().forEach(positionCurrent -> {
                    VehicleInfo info = (VehicleInfo) vehicleInfoMap.get(positionCurrent.getVehicleId());
                    VehicleAlarm vehicleAlarm = (VehicleAlarm) BeanUtil.copyProperties((Object) positionCurrent, VehicleAlarm.class, new String[0]);
                    BeanUtil.copyProperties((Object) info, (Object) vehicleAlarm, true);
                    vehicleAlarm.setVehicleId(info.getId());
                    vehicleAlarm.setRailId(null);
                    vehicleAlarm.setRailName(null);
                    vehicleAlarm.setRailScope(null);
                    vehicleAlarm.setAlarmTime(now);
                    vehicleAlarm.setAlarmType("5");
                    vehicleAlarm.setAlarmTypeName(ConfigValue.getVehicleAlarmTypeName("5"));
                    vehicleAlarm.setAlarmStatus("15");
                    vehicleAlarm.setCreateBy("task");
                    vehicleAlarm.setCreateTime(now);
                    vehicleAlarm.setUpdateBy(null);
                    vehicleAlarm.setUpdateTime(null);
                    vehicleAlarm.setRemark(null);
                    vehicleAlarm.setId(null);
                    vehicleAlarm.setSettingId(setting.getId());
                    vehicleAlarm.setSettingName(setting.getName());
                    vehicleAlarmList.add(vehicleAlarm);
                });
            } else {
                vehicleOverSpeedPositionCurrentList.stream().forEach(positionCurrent2 -> {
                    railList.stream().anyMatch(rail -> {
                        boolean inLayer = true;
                        if (StringUtils.isNotBlank(positionCurrent2.getLayerId())) {
                            inLayer = positionCurrent2.getLayerId().equals(rail.getLayerId());
                        }
                        if (inLayer) {
                            boolean inRail = CommonUtil.containsRailScope(positionCurrent2.getLongitude(), positionCurrent2.getLatitude(), rail.getRailScope(), rail.getDrawType());
                            if (inRail) {
                                VehicleInfo info = (VehicleInfo) vehicleInfoMap.get(positionCurrent2.getVehicleId());
                                VehicleAlarm vehicleAlarm = (VehicleAlarm) BeanUtil.copyProperties((Object) positionCurrent2, VehicleAlarm.class, new String[0]);
                                BeanUtil.copyProperties((Object) info, (Object) vehicleAlarm, true);
                                vehicleAlarm.setVehicleId(info.getId());
                                vehicleAlarm.setRailId(rail.getRailId());
                                vehicleAlarm.setRailName(rail.getRailName());
                                vehicleAlarm.setRailScope(rail.getRailScope());
                                vehicleAlarm.setAlarmTime(now);
                                vehicleAlarm.setAlarmType("5");
                                vehicleAlarm.setAlarmTypeName(ConfigValue.getVehicleAlarmTypeName("5"));
                                vehicleAlarm.setAlarmStatus("15");
                                vehicleAlarm.setCreateBy("task");
                                vehicleAlarm.setCreateTime(now);
                                vehicleAlarm.setUpdateBy(null);
                                vehicleAlarm.setUpdateTime(null);
                                vehicleAlarm.setRemark(null);
                                vehicleAlarm.setId(null);
                                vehicleAlarm.setSettingId(setting.getId());
                                vehicleAlarm.setSettingName(setting.getName());
                                vehicleAlarmList.add(vehicleAlarm);
                                return true;
                            }
                            return false;
                        }
                        return false;
                    });
                });
            }
        });
        if (!CollectionUtils.isEmpty(vehicleAlarmList)) {
            this.vehicleAlarmService.batchCalcInsert(vehicleAlarmList);
        }
    }
    private static VehicleAlarm buildVehicleAlarm(VehicleInfo info, VehiclePositionCurrent positionCurrent) {
        return null;
    }
    public void calcElectricityAlarm() {
        List<VehicleInfo> vehicleInfoList = this.vehicleInfoService.selectLowBatteryList();
        if (CollectionUtils.isEmpty(vehicleInfoList)) {
            return;
        }
        VehicleAlarm vehicleAlarm = VehicleAlarm.builder().build();
        LocalDateTime now = LocalDateTime.now();
        vehicleAlarm.setAlarmTime(now);
        vehicleAlarm.setElectricity(null);
        vehicleAlarm.setAlarmType("3");
        vehicleAlarm.setAlarmTypeName(ConfigValue.getVehicleAlarmTypeName("3"));
        vehicleAlarm.setAlarmName("车辆定位卡低电提醒，请及时充电");
        StringJoiner sj = new StringJoiner(";");
        vehicleInfoList.stream().forEach(t -> {
            sj.add(t.getCardId() + "-" + t.getVehicleName());
        });
        StringBuilder descBuilder = new StringBuilder("低电卡片");
        descBuilder.append(vehicleInfoList.size()).append("张，车辆：").append(sj.toString());
        String alarmDesc = descBuilder.toString();
        if (alarmDesc.length() > 498) {
            alarmDesc = alarmDesc.substring(0, 498) + StringUtil.INVISIBLE_USERNAME;
        }
        vehicleAlarm.setAlarmDesc(alarmDesc);
        vehicleAlarm.setAlarmStatus("15");
        vehicleAlarm.setCreateBy("task");
        vehicleAlarm.setCreateTime(now);
        vehicleAlarm.setUpdateBy(null);
        vehicleAlarm.setUpdateTime(null);
        vehicleAlarm.setRemark(null);
        vehicleAlarm.setId(null);
        this.vehicleAlarmService.insert(vehicleAlarm);
    }
    public void calcInOrOutVehicleAlarm() {
        List<String> vehicleAlarmTypeList = Arrays.asList(ConfigValue.VEHICLE_ALARM_TYPE_26, ConfigValue.VEHICLE_ALARM_TYPE_27);
        List<VehicleAlarmSettingReq> list = this.vehicleAlarmSettingService.selectEnableList(vehicleAlarmTypeList);
        if (!isValidRule(list)) {
            return;
        }
        List<VehiclePositionCurrent> positionCurrentList = this.vehiclePositionCurrentService.selectCurrentList();
        calcEntryAlarm((List) list.stream().filter(f -> {
            return ConfigValue.VEHICLE_ALARM_TYPE_26.equals(f.getVehicleAlarmType());
        }).collect(Collectors.toList()), positionCurrentList);
        calcOutAlarm((List) list.stream().filter(f2 -> {
            return ConfigValue.VEHICLE_ALARM_TYPE_27.equals(f2.getVehicleAlarmType());
        }).collect(Collectors.toList()), positionCurrentList);
    }
    private void calcEntryAlarm(List<VehicleAlarmSettingReq> list, List<VehiclePositionCurrent> positionCurrentList) {
        if (CollectionUtils.isEmpty(positionCurrentList) || CollectionUtils.isEmpty(list)) {
            return;
        }
        list.stream().forEach(setting -> {
            List<VehicleAlarm> vehicleAlarmList = triggerAlarmInAndOr(ConfigValue.VEHICLE_ALARM_TYPE_26, setting.getId(), setting.getName(), positionCurrentList, setting.getVehicleInfoList(), setting.getRailList());
            if (!CollectionUtils.isEmpty(vehicleAlarmList)) {
                this.vehicleAlarmService.batchCalcInsert(vehicleAlarmList);
            }
        });
    }
    private void calcOutAlarm(List<VehicleAlarmSettingReq> list, List<VehiclePositionCurrent> positionCurrentList) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.stream().forEach(setting -> {
            List<VehicleAlarm> vehicleAlarmList = triggerAlarmNotInAndOr(ConfigValue.VEHICLE_ALARM_TYPE_27, setting.getId(), setting.getName(), positionCurrentList, setting.getVehicleInfoList(), setting.getRailList());
            if (!CollectionUtils.isEmpty(vehicleAlarmList)) {
                this.vehicleAlarmService.batchCalcInsert(vehicleAlarmList);
            }
        });
    }
    private static List<VehicleAlarm> triggerAlarmInAndOr(String alarmType, Long settingId, String settingName, List<VehiclePositionCurrent> positionCurrentList, List<VehicleInfo> vehicleInfoList, List<VehicleAlarmRailVO> railList) {
        if (CollectionUtils.isEmpty(vehicleInfoList) || CollectionUtils.isEmpty(positionCurrentList) || CollectionUtils.isEmpty(railList)) {
            return null;
        }
        Map<Long, VehicleInfo> vehicleInfoMap = (Map) vehicleInfoList.stream().collect(Collectors.toMap((v0) -> {
            return v0.getId();
        }, Function.identity()));
        List<VehiclePositionCurrent> vehiclePositionCurrentList = (List) positionCurrentList.stream().filter(f -> {
            return vehicleInfoMap.containsKey(f.getVehicleId());
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vehiclePositionCurrentList)) {
            return null;
        }
        LocalDateTime now = LocalDateTime.now();
        List<VehicleAlarm> list = new ArrayList<>();
        vehiclePositionCurrentList.stream().forEach(positionCurrent -> {
            railList.stream().anyMatch(rail -> {
                boolean inLayer = true;
                if (StringUtils.isNotBlank(positionCurrent.getLayerId())) {
                    inLayer = positionCurrent.getLayerId().equals(rail.getLayerId());
                }
                if (inLayer) {
                    boolean inRail = CommonUtil.containsRailScope(positionCurrent.getLongitude(), positionCurrent.getLatitude(), rail.getRailScope(), rail.getDrawType());
                    if (inRail) {
                        VehicleInfo info = (VehicleInfo) vehicleInfoMap.get(positionCurrent.getVehicleId());
                        VehicleAlarm vehicleAlarm = (VehicleAlarm) BeanUtil.copyProperties((Object) positionCurrent, VehicleAlarm.class, new String[0]);
                        BeanUtil.copyProperties((Object) info, (Object) vehicleAlarm, true);
                        vehicleAlarm.setVehicleId(info.getId());
                        vehicleAlarm.setSettingId(settingId);
                        vehicleAlarm.setSettingName(settingName);
                        vehicleAlarm.setRailId(rail.getRailId());
                        vehicleAlarm.setRailName(rail.getRailName());
                        vehicleAlarm.setRailScope(rail.getRailScope());
                        vehicleAlarm.setAlarmTime(now);
                        vehicleAlarm.setElectricity(positionCurrent.getElectricity());
                        vehicleAlarm.setAlarmType(alarmType);
                        vehicleAlarm.setAlarmTypeName(ConfigValue.getVehicleAlarmTypeName(alarmType));
                        vehicleAlarm.setAlarmName(info.getVehicleName() + " 进入 " + settingName + " 请及时处理");
                        vehicleAlarm.setAlarmDesc(info.getVehicleName() + " 进入 " + settingName + " 请及时处理");
                        vehicleAlarm.setAlarmStatus("15");
                        vehicleAlarm.setCreateBy("task");
                        vehicleAlarm.setCreateTime(now);
                        vehicleAlarm.setUpdateBy(null);
                        vehicleAlarm.setUpdateTime(null);
                        vehicleAlarm.setRemark(null);
                        vehicleAlarm.setId(null);
                        list.add(vehicleAlarm);
                        return true;
                    }
                    return false;
                }
                return false;
            });
        });
        return list;
    }
    private List<VehicleAlarm> triggerAlarmNotInAndOr(String alarmType, Long settingId, String settingName, List<VehiclePositionCurrent> positionCurrentList, List<VehicleInfo> vehicleInfoList, List<VehicleAlarmRailVO> railList) {
        if (CollectionUtils.isEmpty(vehicleInfoList) || CollectionUtils.isEmpty(railList)) {
            return null;
        }
        LocalDateTime now = LocalDateTime.now();
        if (CollectionUtils.isEmpty(positionCurrentList)) {
            List<VehicleAlarm> list = new ArrayList<>();
            vehicleInfoList.stream().forEach(info -> {
                if (!this.vehicleLastPositionMap.containsKey(info.getId()) || !this.vehicleLastPositionMap.get(info.getId()).getCardId().equals(info.getCardId())) {
                    this.vehicleLastPositionMap.remove(info.getId());
                    return;
                }
                VehicleAlarm vehicleAlarm = (VehicleAlarm) BeanUtil.copyProperties((Object) info, VehicleAlarm.class, new String[0]);
                vehicleAlarm.setVehicleId(info.getId());
                list.add(vehicleAlarm);
            });
            railList.stream().findFirst().ifPresent(rail -> {
                list.stream().forEach(vehicleAlarm -> {
                    vehicleAlarm.setSettingId(settingId);
                    vehicleAlarm.setSettingName(settingName);
                    vehicleAlarm.setRailId(rail.getRailId());
                    vehicleAlarm.setRailName(rail.getRailName());
                    vehicleAlarm.setRailScope(rail.getRailScope());
                    vehicleAlarm.setAlarmTime(now);
                    vehicleAlarm.setElectricity(null);
                    vehicleAlarm.setAlarmType(alarmType);
                    vehicleAlarm.setAlarmTypeName(ConfigValue.getVehicleAlarmTypeName(alarmType));
                    vehicleAlarm.setAlarmName(vehicleAlarm.getVehicleName() + " 离开 " + settingName + " 请及时处理");
                    vehicleAlarm.setAlarmDesc(vehicleAlarm.getVehicleName() + " 离开 " + settingName + " 请及时处理");
                    vehicleAlarm.setAlarmStatus("15");
                    vehicleAlarm.setCreateBy("task");
                    vehicleAlarm.setCreateTime(now);
                    vehicleAlarm.setUpdateBy(null);
                    vehicleAlarm.setUpdateTime(null);
                    vehicleAlarm.setRemark(null);
                    vehicleAlarm.setId(null);
                });
            });
            return list;
        }
        List<VehicleAlarm> list2 = new ArrayList<>();
        Map<Long, VehicleInfo> vehicleInfoMap = (Map) vehicleInfoList.stream().collect(Collectors.toMap((v0) -> {
            return v0.getId();
        }, Function.identity()));
        List<Long> positionVehicleIdList = (List) positionCurrentList.stream().map((v0) -> {
            return v0.getVehicleId();
        }).collect(Collectors.toList());
        vehicleInfoMap.forEach((vehicleId, info2) -> {
            if (!positionVehicleIdList.contains(vehicleId)) {
                if (!this.vehicleLastPositionMap.containsKey(info2.getId()) || !this.vehicleLastPositionMap.get(info2.getId()).getCardId().equals(info2.getCardId())) {
                    this.vehicleLastPositionMap.remove(info2.getId());
                } else {
                    VehicleAlarm vehicleAlarm = (VehicleAlarm) BeanUtil.copyProperties((Object) info2, VehicleAlarm.class, new String[0]);
                    railList.stream().findFirst().ifPresent(rail2 -> {
                        vehicleAlarm.setVehicleId(info2.getId());
                        vehicleAlarm.setSettingId(settingId);
                        vehicleAlarm.setSettingName(settingName);
                        vehicleAlarm.setRailId(rail2.getRailId());
                        vehicleAlarm.setRailName(rail2.getRailName());
                        vehicleAlarm.setRailScope(rail2.getRailScope());
                        vehicleAlarm.setAlarmTime(now);
                        vehicleAlarm.setElectricity(null);
                        vehicleAlarm.setAlarmType(alarmType);
                        vehicleAlarm.setAlarmTypeName(ConfigValue.getVehicleAlarmTypeName(alarmType));
                        vehicleAlarm.setAlarmName(info2.getVehicleName() + " 离开 " + settingName + " 请及时处理");
                        vehicleAlarm.setAlarmDesc(info2.getVehicleName() + " 离开 " + settingName + " 请及时处理");
                        vehicleAlarm.setAlarmStatus("15");
                        vehicleAlarm.setCreateBy("task");
                        vehicleAlarm.setCreateTime(now);
                        vehicleAlarm.setUpdateBy(null);
                        vehicleAlarm.setUpdateTime(null);
                        vehicleAlarm.setRemark(null);
                        vehicleAlarm.setId(null);
                        list2.add(vehicleAlarm);
                    });
                }
            }
        });
        positionCurrentList.stream().filter(f -> {
            return vehicleInfoMap.containsKey(f.getVehicleId());
        }).forEach(positionCurrent -> {
            this.vehicleLastPositionMap.put(positionCurrent.getVehicleId(), positionCurrent);
            railList.stream().anyMatch(rail2 -> {
                boolean inLayer = false;
                if (StringUtils.isNotBlank(positionCurrent.getLayerId())) {
                    inLayer = positionCurrent.getLayerId().equals(rail2.getLayerId());
                }
                if (!inLayer) {
                    boolean inRail = CommonUtil.containsRailScope(positionCurrent.getLongitude(), positionCurrent.getLatitude(), rail2.getRailScope(), rail2.getDrawType());
                    if (!inRail) {
                        VehicleInfo info3 = (VehicleInfo) vehicleInfoMap.get(positionCurrent.getVehicleId());
                        VehicleAlarm vehicleAlarm = (VehicleAlarm) BeanUtil.copyProperties((Object) positionCurrent, VehicleAlarm.class, new String[0]);
                        BeanUtil.copyProperties((Object) info3, (Object) vehicleAlarm, true);
                        vehicleAlarm.setSettingId(settingId);
                        vehicleAlarm.setSettingName(settingName);
                        vehicleAlarm.setRailId(rail2.getRailId());
                        vehicleAlarm.setRailName(rail2.getRailName());
                        vehicleAlarm.setRailScope(rail2.getRailScope());
                        vehicleAlarm.setAlarmTime(now);
                        vehicleAlarm.setElectricity(positionCurrent.getElectricity());
                        vehicleAlarm.setAlarmType(alarmType);
                        vehicleAlarm.setAlarmTypeName(ConfigValue.getVehicleAlarmTypeName(alarmType));
                        vehicleAlarm.setAlarmName(info3.getVehicleName() + " 离开 " + settingName + " 请及时处理");
                        vehicleAlarm.setAlarmDesc(info3.getVehicleName() + " 离开 " + settingName + " 请及时处理");
                        vehicleAlarm.setAlarmStatus("15");
                        vehicleAlarm.setCreateBy("task");
                        vehicleAlarm.setCreateTime(now);
                        vehicleAlarm.setUpdateBy(null);
                        vehicleAlarm.setUpdateTime(null);
                        vehicleAlarm.setRemark(null);
                        vehicleAlarm.setId(null);
                        list2.add(vehicleAlarm);
                        return true;
                    }
                    return false;
                }
                return false;
            });
        });
        return list2;
    }
    /* JADX WARN: Multi-variable type inference failed */
    public void calcVehicleDeviceOutAlarm() {
        List<VehicleAlarm> vehicleAlarmList;
        List<Long> cardIdList = (List) this.deviceCardService.list(new LambdaQueryWrapper<DeviceCard>().eq((v0) -> {
            return v0.getCardType();
        }, "vehicle")).stream().map((v0) -> {
            return v0.getCardId();
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cardIdList)) {
            return;
        }
        Map<Long, VehicleInfo> cardIdEntityMap = this.vehicleInfoService.findCardIdEntityMap();
        List<VehicleInfo> vehicleInfoList = new ArrayList<>();
        cardIdList.stream().forEach(cardId -> {
            VehicleInfo vehicleInfo = (VehicleInfo) cardIdEntityMap.get(cardId);
            if (Objects.isNull(vehicleInfo)) {
                vehicleInfo = VehicleInfo.builder().cardId(cardId).build();
            }
            vehicleInfoList.add(vehicleInfo);
        });
        LocalDateTime now = LocalDateTime.now();
        Map<Long, VehicleInfo> cardIdVehicleMap = (Map) vehicleInfoList.stream().collect(Collectors.toMap((v0) -> {
            return v0.getCardId();
        }, Function.identity()));
        List<VehiclePositionCurrent> positionCurrentList = this.vehiclePositionCurrentService.selectCurrentList();
        if (CollectionUtils.isEmpty(positionCurrentList)) {
            vehicleAlarmList = BeanUtil.copyToList(vehicleInfoList, VehicleAlarm.class);
        } else {
            List<Long> positionCardId = (List) positionCurrentList.stream().map((v0) -> {
                return v0.getCardId();
            }).collect(Collectors.toList());
            List<VehicleAlarm> finalVehicleAlarmList = new ArrayList<>();
            RailDrawType mapRailScope = this.systemInfoService.findMapRailScope(Constants.SYSTEM_ID_MONITOR);
            DeviceLayer deviceLayer = this.deviceLayerService.findOneLayer();
            positionCurrentList.stream().filter(f -> {
                return cardIdVehicleMap.containsKey(f.getCardId());
            }).forEach(positionCurrent -> {
                if (Objects.nonNull(mapRailScope) && StringUtils.isNotEmpty(mapRailScope.getRailScope())) {
                    if (!CommonUtil.containsRailScope(positionCurrent.getLongitude(), positionCurrent.getLatitude(), mapRailScope.getRailScope(), mapRailScope.getDrawType())) {
                        VehicleInfo info = (VehicleInfo) cardIdVehicleMap.get(positionCurrent.getCardId());
                        VehicleAlarm vehicleAlarm = (VehicleAlarm) BeanUtil.copyProperties((Object) positionCurrent, VehicleAlarm.class, new String[0]);
                        BeanUtil.copyProperties((Object) info, (Object) vehicleAlarm, true);
                        vehicleAlarm.setElectricity(positionCurrent.getElectricity());
                        finalVehicleAlarmList.add(vehicleAlarm);
                        return;
                    }
                    return;
                }
                if (Objects.nonNull(deviceLayer) && !CommonUtil.inOneLayer(positionCurrent.getLongitude(), positionCurrent.getLatitude(), deviceLayer.getMinLongitude(), deviceLayer.getMaxLongitude(), deviceLayer.getMinLatitude(), deviceLayer.getMaxLatitude()).booleanValue()) {
                    VehicleInfo info2 = (VehicleInfo) cardIdVehicleMap.get(positionCurrent.getCardId());
                    VehicleAlarm vehicleAlarm2 = (VehicleAlarm) BeanUtil.copyProperties((Object) positionCurrent, VehicleAlarm.class, new String[0]);
                    BeanUtil.copyProperties((Object) info2, (Object) vehicleAlarm2, true);
                    vehicleAlarm2.setElectricity(positionCurrent.getElectricity());
                    finalVehicleAlarmList.add(vehicleAlarm2);
                }
            });
            cardIdVehicleMap.forEach((cardId2, info) -> {
                if (!positionCardId.contains(cardId2)) {
                    VehicleAlarm vehicleAlarm = (VehicleAlarm) BeanUtil.copyProperties((Object) info, VehicleAlarm.class, new String[0]);
                    finalVehicleAlarmList.add(vehicleAlarm);
                }
            });
            vehicleAlarmList = finalVehicleAlarmList;
        }
        if (!CollectionUtils.isEmpty(vehicleAlarmList)) {
            vehicleAlarmList.stream().forEach(vehicleAlarm -> {
                vehicleAlarm.setSettingId(null);
                vehicleAlarm.setSettingName(null);
                vehicleAlarm.setRailId(null);
                vehicleAlarm.setRailName(null);
                vehicleAlarm.setRailScope(null);
                vehicleAlarm.setAlarmTime(now);
                vehicleAlarm.setAlarmType(ConfigValue.VEHICLE_ALARM_TYPE_27);
                vehicleAlarm.setAlarmTypeName(ConfigValue.getVehicleAlarmTypeName(ConfigValue.VEHICLE_ALARM_TYPE_27));
                vehicleAlarm.setAlarmName(vehicleAlarm.getVehicleName() + " 离开厂区请及时处理");
                vehicleAlarm.setAlarmDesc(vehicleAlarm.getVehicleName() + " 离开厂区请及时处理");
                vehicleAlarm.setAlarmStatus("15");
                vehicleAlarm.setCreateBy("task");
                vehicleAlarm.setCreateTime(now);
                vehicleAlarm.setUpdateBy(null);
                vehicleAlarm.setUpdateTime(null);
                vehicleAlarm.setRemark("设备离厂告警");
                vehicleAlarm.setId(null);
            });
            this.vehicleAlarmService.batchCalcInsert(vehicleAlarmList);
        }
    }
    public void calcRetentionOrIllegalParkingAlarm() {
        List<String> vehicleAlarmTypeList = Arrays.asList("30", "70");
        List<VehicleAlarmSettingReq> list = this.vehicleAlarmSettingService.selectEnableList(vehicleAlarmTypeList);
        if (!isValidRule(list)) {
            return;
        }
        List<VehiclePositionCurrent> positionCurrentList = this.vehiclePositionCurrentService.selectCurrentList();
        calcRetentionAlarm((List) list.stream().filter(f -> {
            return "30".equals(f.getVehicleAlarmType());
        }).collect(Collectors.toList()), positionCurrentList);
        calcIllegalParkingAlarm((List) list.stream().filter(f2 -> {
            return "70".equals(f2.getVehicleAlarmType());
        }).collect(Collectors.toList()), positionCurrentList);
    }
    private void calcIllegalParkingAlarm(List<VehicleAlarmSettingReq> list, List<VehiclePositionCurrent> positionCurrentList) {
        if (CollectionUtils.isEmpty(positionCurrentList) || CollectionUtils.isEmpty(list)) {
            return;
        }
        List<VehiclePositionCurrent> currentList = (List) positionCurrentList.stream().filter(f -> {
            return f.getStillStatus().intValue() == 1;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(currentList)) {
            return;
        }
        list.forEach(setting -> {
            List<VehicleAlarm> vehicleAlarmList = triggerAlarmIllegalParkingAndOr("70", setting.getId(), setting.getName(), setting.getRule(), currentList, setting.getVehicleInfoList(), setting.getRailList());
            if (!CollectionUtils.isEmpty(vehicleAlarmList)) {
                this.vehicleAlarmService.batchCalcInsert(vehicleAlarmList);
            }
        });
    }
    private List<VehicleAlarm> triggerAlarmIllegalParkingAndOr(String alarmType, Long settingId, String settingName, Long rule, List<VehiclePositionCurrent> positionCurrentList, List<VehicleInfo> vehicleInfoList, List<VehicleAlarmRailVO> railList) {
        if (CollectionUtils.isEmpty(vehicleInfoList) || CollectionUtils.isEmpty(positionCurrentList) || CollectionUtils.isEmpty(railList)) {
            return null;
        }
        Map<Long, VehicleInfo> vehicleInfoMap = (Map) vehicleInfoList.stream().collect(Collectors.toMap((v0) -> {
            return v0.getId();
        }, Function.identity()));
        List<VehiclePositionCurrent> vehiclePositionCurrentList = (List) positionCurrentList.stream().filter(f -> {
            return vehicleInfoMap.containsKey(f.getVehicleId());
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vehiclePositionCurrentList)) {
            return null;
        }
        String areaKey = settingId + "";
        if (!this.vehicleAreaIllegalParkingMap.containsKey(areaKey)) {
            this.vehicleAreaIllegalParkingMap.put(areaKey, ExpiringMap.builder().expiration(20L, TimeUnit.DAYS).build());
        }
        Map<Long, LocalDateTime> vehicleIllegalParkingMap = this.vehicleAreaIllegalParkingMap.get(areaKey);
        List<VehicleAlarm> list = new ArrayList<>();
        vehiclePositionCurrentList.forEach(positionCurrent -> {
            railList.stream().anyMatch(rail -> {
                Boolean inLayer = true;
                if (StringUtils.isNotBlank(positionCurrent.getLayerId())) {
                    inLayer = Boolean.valueOf(positionCurrent.getLayerId().equals(rail.getLayerId()));
                }
                if (inLayer.booleanValue()) {
                    Boolean inRail = Boolean.valueOf(CommonUtil.containsRailScope(positionCurrent.getLongitude(), positionCurrent.getLatitude(), rail.getRailScope(), rail.getDrawType()));
                    if (inRail.booleanValue()) {
                        VehicleInfo info = (VehicleInfo) vehicleInfoMap.get(positionCurrent.getVehicleId());
                        if (!vehicleIllegalParkingMap.containsKey(positionCurrent.getVehicleId())) {
                            vehicleIllegalParkingMap.put(positionCurrent.getVehicleId(), positionCurrent.getAcceptTime());
                            return false;
                        }
                        LocalDateTime intoRailTime = (LocalDateTime) vehicleIllegalParkingMap.get(positionCurrent.getVehicleId());
                        LocalDateTime now = LocalDateTime.now();
                        Long waitSecond = Long.valueOf(Duration.between(intoRailTime, now).getSeconds());
                        if (waitSecond.longValue() >= rule.longValue() * 60) {
                            VehicleAlarm vehicleAlarm = (VehicleAlarm) BeanUtil.copyProperties((Object) positionCurrent, VehicleAlarm.class, new String[0]);
                            BeanUtil.copyProperties((Object) info, (Object) vehicleAlarm, true);
                            vehicleAlarm.setSettingId(settingId);
                            vehicleAlarm.setSettingName(settingName);
                            vehicleAlarm.setRailId(rail.getRailId());
                            vehicleAlarm.setRailName(rail.getRailName());
                            vehicleAlarm.setRailScope(rail.getRailScope());
                            vehicleAlarm.setAlarmTime(now);
                            vehicleAlarm.setElectricity(positionCurrent.getElectricity());
                            vehicleAlarm.setAlarmType(alarmType);
                            vehicleAlarm.setAlarmTypeName(ConfigValue.getVehicleAlarmTypeName(alarmType));
                            vehicleAlarm.setAlarmName(info.getVehicleName() + " 禁止 " + settingName + " 请及时处理");
                            vehicleAlarm.setAlarmDesc(info.getVehicleName() + " 禁止 " + settingName + " 请及时处理");
                            vehicleAlarm.setAlarmStatus("15");
                            vehicleAlarm.setCreateBy("task");
                            vehicleAlarm.setCreateTime(now);
                            vehicleAlarm.setUpdateBy(null);
                            vehicleAlarm.setUpdateTime(null);
                            vehicleAlarm.setRemark(null);
                            vehicleAlarm.setId(null);
                            list.add(vehicleAlarm);
                            return true;
                        }
                        return false;
                    }
                    vehicleIllegalParkingMap.remove(positionCurrent.getVehicleId());
                    return false;
                }
                vehicleIllegalParkingMap.remove(positionCurrent.getVehicleId());
                return false;
            });
        });
        return list;
    }
    private LocalDateTime getVehicleIntoRailTime(VehiclePositionCurrent positionCurrent, VehicleAlarmRailVO rail, Long rule) {
        boolean isRail = CommonUtil.containsRailScope(positionCurrent.getLongitude(), positionCurrent.getLatitude(), rail.getRailScope(), rail.getDrawType());
        LocalDateTime acceptTime = null;
        if (isRail) {
            List<VehiclePositionCalc> list = this.vehiclePositionCalcService.selectCurrentListByCurrent(positionCurrent);
            if (CollectionUtils.isEmpty(list)) {
                return null;
            }
            LocalDateTime minutes = positionCurrent.getAcceptTime().minusMinutes(rule.longValue() + 3);
            List<VehiclePositionCalc> positionCalcs = (List) list.stream().filter(f -> {
                return f.getAcceptTime().isAfter(minutes);
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(positionCalcs)) {
                return null;
            }
            acceptTime = positionCalcs.get(0).getAcceptTime();
        }
        return acceptTime;
    }
    private void calcRetentionAlarm(List<VehicleAlarmSettingReq> list, List<VehiclePositionCurrent> positionCurrentList) {
        if (CollectionUtils.isEmpty(positionCurrentList) || CollectionUtils.isEmpty(list)) {
            return;
        }
        List<VehiclePositionCurrent> currentList = (List) positionCurrentList.stream().filter(f -> {
            return f.getStillStatus().intValue() == 0;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(currentList)) {
            return;
        }
        list.forEach(setting -> {
            List<VehicleAlarm> vehicleAlarmList = triggerAlarmRetentionAndOr("30", setting.getId(), setting.getName(), setting.getRule(), currentList, setting.getVehicleInfoList(), setting.getRailList());
            if (!CollectionUtils.isEmpty(vehicleAlarmList)) {
                this.vehicleAlarmService.batchCalcInsert(vehicleAlarmList);
            }
        });
    }
    private List<VehicleAlarm> triggerAlarmRetentionAndOr(String alarmType, Long settingId, String settingName, Long rule, List<VehiclePositionCurrent> positionCurrentList, List<VehicleInfo> vehicleInfoList, List<VehicleAlarmRailVO> railList) {
        if (CollectionUtils.isEmpty(vehicleInfoList) || CollectionUtils.isEmpty(positionCurrentList) || CollectionUtils.isEmpty(railList)) {
            return null;
        }
        Map<Long, VehicleInfo> vehicleInfoMap = (Map) vehicleInfoList.stream().collect(Collectors.toMap((v0) -> {
            return v0.getId();
        }, Function.identity()));
        List<VehiclePositionCurrent> vehiclePositionCurrentList = (List) positionCurrentList.stream().filter(f -> {
            return vehicleInfoMap.containsKey(f.getVehicleId());
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vehiclePositionCurrentList)) {
            return null;
        }
        LocalDateTime now = LocalDateTime.now();
        List<VehicleAlarm> list = new ArrayList<>();
        String areaKey = settingId + "";
        if (!this.vehicleAreaRetentionMap.containsKey(areaKey)) {
            this.vehicleAreaRetentionMap.put(areaKey, ExpiringMap.builder().expiration(20L, TimeUnit.DAYS).build());
        }
        Map<Long, LocalDateTime> vehicleRetentMap = this.vehicleAreaRetentionMap.get(areaKey);
        vehiclePositionCurrentList.forEach(positionCurrent -> {
            railList.stream().anyMatch(rail -> {
                Boolean inLayer = true;
                if (StringUtils.isNotBlank(positionCurrent.getLayerId())) {
                    inLayer = Boolean.valueOf(positionCurrent.getLayerId().equals(rail.getLayerId()));
                }
                if (inLayer.booleanValue()) {
                    Boolean inRail = Boolean.valueOf(CommonUtil.containsRailScope(positionCurrent.getLongitude(), positionCurrent.getLatitude(), rail.getRailScope(), rail.getDrawType()));
                    if (inRail.booleanValue()) {
                        if (!vehicleRetentMap.containsKey(positionCurrent.getVehicleId())) {
                            vehicleRetentMap.put(positionCurrent.getVehicleId(), positionCurrent.getAcceptTime());
                            return false;
                        }
                        LocalDateTime intoRailTime = (LocalDateTime) vehicleRetentMap.get(positionCurrent.getVehicleId());
                        Long waitSecond = Long.valueOf(Duration.between(intoRailTime, now).getSeconds());
                        VehicleInfo info = (VehicleInfo) vehicleInfoMap.get(positionCurrent.getVehicleId());
                        if (waitSecond.longValue() >= rule.longValue() * 60) {
                            VehicleAlarm vehicleAlarm = (VehicleAlarm) BeanUtil.copyProperties((Object) positionCurrent, VehicleAlarm.class, new String[0]);
                            BeanUtil.copyProperties((Object) info, (Object) vehicleAlarm, true);
                            vehicleAlarm.setSettingId(settingId);
                            vehicleAlarm.setSettingName(settingName);
                            vehicleAlarm.setRailId(rail.getRailId());
                            vehicleAlarm.setRailName(rail.getRailName());
                            vehicleAlarm.setRailScope(rail.getRailScope());
                            vehicleAlarm.setAlarmTime(now);
                            vehicleAlarm.setElectricity(positionCurrent.getElectricity());
                            vehicleAlarm.setAlarmType(alarmType);
                            vehicleAlarm.setAlarmTypeName(ConfigValue.getVehicleAlarmTypeName(alarmType));
                            vehicleAlarm.setAlarmName(info.getVehicleName() + " 滞留 " + settingName + " 请及时处理");
                            vehicleAlarm.setAlarmDesc(info.getVehicleName() + " 滞留 " + settingName + " 请及时处理");
                            vehicleAlarm.setAlarmStatus("15");
                            vehicleAlarm.setCreateBy("task");
                            vehicleAlarm.setCreateTime(now);
                            vehicleAlarm.setUpdateBy(null);
                            vehicleAlarm.setUpdateTime(null);
                            vehicleAlarm.setRemark(null);
                            vehicleAlarm.setId(null);
                            list.add(vehicleAlarm);
                            return true;
                        }
                        return false;
                    }
                    vehicleRetentMap.remove(positionCurrent.getVehicleId());
                    return false;
                }
                vehicleRetentMap.remove(positionCurrent.getVehicleId());
                return false;
            });
        });
        return list;
    }
    private LocalDateTime getVehicleIntoRailTimeByRetention(VehiclePositionCurrent positionCurrent, VehicleAlarmRailVO rail, Long rule) {
        LocalDateTime acceptTime = null;
        List<VehiclePositionCalc> list = this.vehiclePositionCalcService.selectCurrentListByTime(positionCurrent);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<VehiclePositionCalc> calcList = (List) list.stream().filter(f -> {
            return f.getAcceptTime().isAfter(positionCurrent.getAcceptTime().minusMinutes(rule.longValue() + 3));
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(calcList)) {
            return null;
        }
        int i = 0;
        while (i < calcList.size()) {
            VehiclePositionCalc calc = calcList.get(i);
            boolean isRails = CommonUtil.containsRailScope(calc.getLongitude(), calc.getLatitude(), rail.getRailScope(), rail.getDrawType());
            if (!isRails) {
                while (i >= 0) {
                    calcList.remove(calcList.get(i));
                    i--;
                }
            }
            i++;
        }
        if (!CollectionUtils.isEmpty(calcList)) {
            acceptTime = calcList.get(0).getAcceptTime();
        }
        return acceptTime;
    }
}
