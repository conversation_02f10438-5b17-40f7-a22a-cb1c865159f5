package com.xrkc.job.util;
import java.awt.geom.GeneralPath;
import java.awt.geom.Point2D;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Objects;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/AreaUtils.class */
public class AreaUtils {
    public static boolean containsRailScope(double longitude, double latitude, String railScope) {
        String railScope2;
        if (Objects.nonNull(Double.valueOf(longitude)) && Objects.nonNull(Double.valueOf(latitude)) && org.springframework.util.StringUtils.hasLength(railScope)) {
            if (railScope.contains("POLYGON ((")) {
                railScope2 = railScope.replaceAll("POLYGON \\(\\(", "").replaceAll("\\)\\)", "");
            } else {
                railScope2 = railScope.replaceAll("POLYGON\\(\\(", "").replaceAll("\\)\\)", "");
            }
            String[] scopeArray = railScope2.split(",");
            ArrayList<Point2D.Double> list = new ArrayList<>();
            for (String str : scopeArray) {
                String[] i = str.split(" ");
                Point2D.Double point = new Point2D.Double(Double.valueOf(i[0]).doubleValue(), Double.valueOf(i[1]).doubleValue());
                list.add(point);
            }
            GeneralPath path = genGeneralPath(list);
            return path.contains(longitude, latitude);
        }
        return false;
    }
    private static GeneralPath genGeneralPath(ArrayList<Point2D.Double> points) {
        GeneralPath path = new GeneralPath();
        if (points.size() < 3) {
            return null;
        }
        path.moveTo((float) points.get(0).getX(), (float) points.get(0).getY());
        Iterator<Point2D.Double> it = points.iterator();
        while (it.hasNext()) {
            Point2D.Double point = it.next();
            path.lineTo((float) point.getX(), (float) point.getY());
        }
        path.closePath();
        return path;
    }
}
