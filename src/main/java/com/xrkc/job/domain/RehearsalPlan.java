package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/RehearsalPlan.class */
public class RehearsalPlan {
    @TableId
    private Long rehearsalId;
    private String rehearsalName;
    private String rehearsalLocation;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate rehearsalDate;
    private String leader;
    @TableField(exist = false)
    private List<Long> deptIds;
    @TableField(exist = false)
    private String deptNames;
    private String rehearsalStatus;
    private String layerId;
    private Integer layerHeight;
    private String railScope;
    private LocalTime validBeginTime;
    private LocalTime validEndTime;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String updateBy;
    public void setRehearsalId(Long rehearsalId) {
        this.rehearsalId = rehearsalId;
    }
    public void setRehearsalName(String rehearsalName) {
        this.rehearsalName = rehearsalName;
    }
    public void setRehearsalLocation(String rehearsalLocation) {
        this.rehearsalLocation = rehearsalLocation;
    }
    @JsonFormat(pattern = "yyyy-MM-dd")
    public void setRehearsalDate(LocalDate rehearsalDate) {
        this.rehearsalDate = rehearsalDate;
    }
    public void setLeader(String leader) {
        this.leader = leader;
    }
    public void setDeptIds(List<Long> deptIds) {
        this.deptIds = deptIds;
    }
    public void setDeptNames(String deptNames) {
        this.deptNames = deptNames;
    }
    public void setRehearsalStatus(String rehearsalStatus) {
        this.rehearsalStatus = rehearsalStatus;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
    public void setLayerHeight(Integer layerHeight) {
        this.layerHeight = layerHeight;
    }
    public void setRailScope(String railScope) {
        this.railScope = railScope;
    }
    public void setValidBeginTime(LocalTime validBeginTime) {
        this.validBeginTime = validBeginTime;
    }
    public void setValidEndTime(LocalTime validEndTime) {
        this.validEndTime = validEndTime;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof RehearsalPlan)) {
            return false;
        }
        RehearsalPlan other = (RehearsalPlan) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$rehearsalId = getRehearsalId();
        Object other$rehearsalId = other.getRehearsalId();
        if (this$rehearsalId == null) {
            if (other$rehearsalId != null) {
                return false;
            }
        } else if (!this$rehearsalId.equals(other$rehearsalId)) {
            return false;
        }
        Object this$layerHeight = getLayerHeight();
        Object other$layerHeight = other.getLayerHeight();
        if (this$layerHeight == null) {
            if (other$layerHeight != null) {
                return false;
            }
        } else if (!this$layerHeight.equals(other$layerHeight)) {
            return false;
        }
        Object this$rehearsalName = getRehearsalName();
        Object other$rehearsalName = other.getRehearsalName();
        if (this$rehearsalName == null) {
            if (other$rehearsalName != null) {
                return false;
            }
        } else if (!this$rehearsalName.equals(other$rehearsalName)) {
            return false;
        }
        Object this$rehearsalLocation = getRehearsalLocation();
        Object other$rehearsalLocation = other.getRehearsalLocation();
        if (this$rehearsalLocation == null) {
            if (other$rehearsalLocation != null) {
                return false;
            }
        } else if (!this$rehearsalLocation.equals(other$rehearsalLocation)) {
            return false;
        }
        Object this$rehearsalDate = getRehearsalDate();
        Object other$rehearsalDate = other.getRehearsalDate();
        if (this$rehearsalDate == null) {
            if (other$rehearsalDate != null) {
                return false;
            }
        } else if (!this$rehearsalDate.equals(other$rehearsalDate)) {
            return false;
        }
        Object this$leader = getLeader();
        Object other$leader = other.getLeader();
        if (this$leader == null) {
            if (other$leader != null) {
                return false;
            }
        } else if (!this$leader.equals(other$leader)) {
            return false;
        }
        Object this$deptIds = getDeptIds();
        Object other$deptIds = other.getDeptIds();
        if (this$deptIds == null) {
            if (other$deptIds != null) {
                return false;
            }
        } else if (!this$deptIds.equals(other$deptIds)) {
            return false;
        }
        Object this$deptNames = getDeptNames();
        Object other$deptNames = other.getDeptNames();
        if (this$deptNames == null) {
            if (other$deptNames != null) {
                return false;
            }
        } else if (!this$deptNames.equals(other$deptNames)) {
            return false;
        }
        Object this$rehearsalStatus = getRehearsalStatus();
        Object other$rehearsalStatus = other.getRehearsalStatus();
        if (this$rehearsalStatus == null) {
            if (other$rehearsalStatus != null) {
                return false;
            }
        } else if (!this$rehearsalStatus.equals(other$rehearsalStatus)) {
            return false;
        }
        Object this$layerId = getLayerId();
        Object other$layerId = other.getLayerId();
        if (this$layerId == null) {
            if (other$layerId != null) {
                return false;
            }
        } else if (!this$layerId.equals(other$layerId)) {
            return false;
        }
        Object this$railScope = getRailScope();
        Object other$railScope = other.getRailScope();
        if (this$railScope == null) {
            if (other$railScope != null) {
                return false;
            }
        } else if (!this$railScope.equals(other$railScope)) {
            return false;
        }
        Object this$validBeginTime = getValidBeginTime();
        Object other$validBeginTime = other.getValidBeginTime();
        if (this$validBeginTime == null) {
            if (other$validBeginTime != null) {
                return false;
            }
        } else if (!this$validBeginTime.equals(other$validBeginTime)) {
            return false;
        }
        Object this$validEndTime = getValidEndTime();
        Object other$validEndTime = other.getValidEndTime();
        if (this$validEndTime == null) {
            if (other$validEndTime != null) {
                return false;
            }
        } else if (!this$validEndTime.equals(other$validEndTime)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        if (this$createTime == null) {
            if (other$createTime != null) {
                return false;
            }
        } else if (!this$createTime.equals(other$createTime)) {
            return false;
        }
        Object this$updateTime = getUpdateTime();
        Object other$updateTime = other.getUpdateTime();
        if (this$updateTime == null) {
            if (other$updateTime != null) {
                return false;
            }
        } else if (!this$updateTime.equals(other$updateTime)) {
            return false;
        }
        Object this$updateBy = getUpdateBy();
        Object other$updateBy = other.getUpdateBy();
        return this$updateBy == null ? other$updateBy == null : this$updateBy.equals(other$updateBy);
    }
    protected boolean canEqual(Object other) {
        return other instanceof RehearsalPlan;
    }
    public int hashCode() {
        Object $rehearsalId = getRehearsalId();
        int result = (1 * 59) + ($rehearsalId == null ? 43 : $rehearsalId.hashCode());
        Object $layerHeight = getLayerHeight();
        int result2 = (result * 59) + ($layerHeight == null ? 43 : $layerHeight.hashCode());
        Object $rehearsalName = getRehearsalName();
        int result3 = (result2 * 59) + ($rehearsalName == null ? 43 : $rehearsalName.hashCode());
        Object $rehearsalLocation = getRehearsalLocation();
        int result4 = (result3 * 59) + ($rehearsalLocation == null ? 43 : $rehearsalLocation.hashCode());
        Object $rehearsalDate = getRehearsalDate();
        int result5 = (result4 * 59) + ($rehearsalDate == null ? 43 : $rehearsalDate.hashCode());
        Object $leader = getLeader();
        int result6 = (result5 * 59) + ($leader == null ? 43 : $leader.hashCode());
        Object $deptIds = getDeptIds();
        int result7 = (result6 * 59) + ($deptIds == null ? 43 : $deptIds.hashCode());
        Object $deptNames = getDeptNames();
        int result8 = (result7 * 59) + ($deptNames == null ? 43 : $deptNames.hashCode());
        Object $rehearsalStatus = getRehearsalStatus();
        int result9 = (result8 * 59) + ($rehearsalStatus == null ? 43 : $rehearsalStatus.hashCode());
        Object $layerId = getLayerId();
        int result10 = (result9 * 59) + ($layerId == null ? 43 : $layerId.hashCode());
        Object $railScope = getRailScope();
        int result11 = (result10 * 59) + ($railScope == null ? 43 : $railScope.hashCode());
        Object $validBeginTime = getValidBeginTime();
        int result12 = (result11 * 59) + ($validBeginTime == null ? 43 : $validBeginTime.hashCode());
        Object $validEndTime = getValidEndTime();
        int result13 = (result12 * 59) + ($validEndTime == null ? 43 : $validEndTime.hashCode());
        Object $createTime = getCreateTime();
        int result14 = (result13 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $updateTime = getUpdateTime();
        int result15 = (result14 * 59) + ($updateTime == null ? 43 : $updateTime.hashCode());
        Object $updateBy = getUpdateBy();
        return (result15 * 59) + ($updateBy == null ? 43 : $updateBy.hashCode());
    }
    public String toString() {
        return "RehearsalPlan(rehearsalId=" + getRehearsalId() + ", rehearsalName=" + getRehearsalName() + ", rehearsalLocation=" + getRehearsalLocation() + ", rehearsalDate=" + getRehearsalDate() + ", leader=" + getLeader() + ", deptIds=" + getDeptIds() + ", deptNames=" + getDeptNames() + ", rehearsalStatus=" + getRehearsalStatus() + ", layerId=" + getLayerId() + ", layerHeight=" + getLayerHeight() + ", railScope=" + getRailScope() + ", validBeginTime=" + getValidBeginTime() + ", validEndTime=" + getValidEndTime() + ", createTime=" + getCreateTime() + ", updateTime=" + getUpdateTime() + ", updateBy=" + getUpdateBy() + StringPool.RIGHT_BRACKET;
    }
    public Long getRehearsalId() {
        return this.rehearsalId;
    }
    public String getRehearsalName() {
        return this.rehearsalName;
    }
    public String getRehearsalLocation() {
        return this.rehearsalLocation;
    }
    public LocalDate getRehearsalDate() {
        return this.rehearsalDate;
    }
    public String getLeader() {
        return this.leader;
    }
    public List<Long> getDeptIds() {
        return this.deptIds;
    }
    public String getDeptNames() {
        return this.deptNames;
    }
    public String getRehearsalStatus() {
        return this.rehearsalStatus;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public Integer getLayerHeight() {
        return this.layerHeight;
    }
    public String getRailScope() {
        return this.railScope;
    }
    public LocalTime getValidBeginTime() {
        return this.validBeginTime;
    }
    public LocalTime getValidEndTime() {
        return this.validEndTime;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public LocalDateTime getUpdateTime() {
        return this.updateTime;
    }
    public String getUpdateBy() {
        return this.updateBy;
    }
}
