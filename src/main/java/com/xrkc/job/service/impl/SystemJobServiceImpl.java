package com.xrkc.job.service.impl;
import com.xrkc.job.domain.SystemJob;
import com.xrkc.job.mapper.SystemJobMapper;
import com.xrkc.job.service.ISystemJobService;
import com.xrkc.job.util.ScheduleUtils;
import com.xrkc.job.util.TaskException;
import java.util.List;
import javax.annotation.PostConstruct;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/SystemJobServiceImpl.class */
public class SystemJobServiceImpl implements ISystemJobService {
    @Autowired
    private Scheduler scheduler;
    @Autowired
    private SystemJobMapper systemJobMapper;
    @PostConstruct
    public void init() throws SchedulerException, TaskException {
        this.scheduler.clear();
        List<SystemJob> jobList = this.systemJobMapper.listPage(null);
        for (SystemJob job : jobList) {
            ScheduleUtils.createScheduleJob(this.scheduler, job);
        }
    }
}
