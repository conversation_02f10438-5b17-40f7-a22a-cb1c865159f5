package com.xrkc.job.domain;
import java.nio.charset.Charset;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
//import org.apache.commons.compress.compressors.bzip2.BZip2Constants;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/Constants.class */
public class Constants {
    public static final String HTTP_CODE = "code";
    public static final String HTTP_MSG = "msg";
    public static final String HTTP_MSG_OK = "操作成功";
    public static final String HTTP_MSG_UNCHANGED = "当前操作未改变数据，请刷新后重试";
    public static final String HTTP_MSG_NOT_FOUND = "不存在相关数据，请刷新后重试";
    public static final String HTTP_MSG_LACK_PARAM = "缺少必要参数";
    public static final String HTTP_MSG_EXIST_NAME = "名称已存在，换其它试试";
    public static final String HTTP_MSG_EXCEL_IMPORT_ERROR = "导入失败，请检查EXCEL模板中各项数据是否正确且合规";
    public static final String HTTP_MSG_EXCEL_CONVERT_ERROR = "EXCEL转换数据失败，请检查格式、数据是否合规";
    public static final String HTTP_MSG_EXCEL_IMPORT_EMPTY = "数据验证、去重等处理后，已没有可导入数据";
    public static final String HTTP_MSG_EXCEL_NULL = "请选择EXCEL导入文件";
    public static final String HTTP_MSG_AUTH_ERROR = "抱歉，为保证系统正常运行，该操作仅允许技术支持执行";
    public static final String HTTP_SUCCESS = "success";
    public static final String CODE_PREFIX_JSA = "JSA-";
    public static final String CODE_PREFIX_YY = "YY-";
    public static final String USER_NAME_SUPER_ADMIN = "xrkc_admin";
    public static final String USER_NAME_ADMIN = "admin";
    public static final String PERSON_TYPE_STAFF = "staff";
    public static final String PERSON_TYPE_CONTRACTOR = "contractor";
    public static final String PERSON_TYPE_VISITOR = "visitor";
    public static final String ALARM_TYPE_10 = "10";
    public static final String ALARM_TYPE_20 = "20";
    public static final String ALARM_TYPE_30 = "30";
    public static final String ALARM_TYPE_40 = "40";
    public static final String ALARM_TYPE_50 = "50";
    public static final String ALARM_TYPE_70 = "70";
    public static final String CONFIG_KEY_KEEP_LINE_DISTANCE = "keep_line_distance";
    public static final String CONFIG_KEY_KEEP_LINE_SECOND = "keep_line_second";
    public static final String FLAG_BEGIN_TO_END = "beginToEnd";
    public static final String FLAG_END_TO_BEGIN = "endToBegin";
    public static final String CONFIG_KEY_VEHICLE_KEEP_LINE_DISTANCE = "vehicle_keep_line_distance";
    public static final String CONFIG_KEY_VEHICLE_KEEP_LINE_SECOND = "vehicle_keep_line_second";
    public static final String CONIFG_KEY_POSITION_HISTORY_OPTIMIZATION = "position_history_optimization";
    public static final String CONIFG_KEY_AREA_INOUT_DURATION_TIME = "area_inout_judge_duration_time";
    public static final String MENU_TYPE_DIR = "D";
    public static final String MENU_TYPE_MENU = "M";
    public static final String MENU_TYPE_BUTTON = "B";
    public static final String FREQUENCY_TYPE_DAY = "日检";
    public static final String FREQUENCY_WEEK_DAY = "周检";
    public static final String FREQUENCY_MONTH_DAY = "月检";
    public static final String FREQUENCY_YEAR_DAY = "年检";
    public static final String RAIL_TYPE_ALARM = "alarm";
    public static final String RAIL_TYPE_RISK = "risk";
    public static final String RAIL_TYPE_REGION = "region";
    public static final String UTF8 = "UTF-8";
    public static final String PAGE_NUM_KEY = "pageNum";
    public static final String PAGE_SIZE_KEY = "pageSize";
    public static final String ORDER_BY_KEY = "orderBy";
    public static final String SORT_BY_KEY = "sortBy";
    public static final String ORDER_SQL_KEY = "orderSql";
    public static final String DEFAULT_ENABLE_Y = "Y";
    public static final String DEFAULT_ENABLE_N = "N";
    public static final String DEFAULT_STATUS_SUCCESS = "success";
    public static final String DEFAULT_STATUS_ERROR = "error";
    public static final String DEFAULT_STATUS_LOGOUT = "logout";
    public static final String GBK = "GBK";
    public static final String HTTP = "http://";
    public static final String HTTPS = "https://";
    public static final String LOGIN_SUCCESS = "Success";
    public static final String LOGOUT = "Logout";
    public static final String REGISTER = "Register";
    public static final String LOGIN_FAIL = "Error";
    public static final String PAGE_NUM = "pageNum";
    public static final String PAGE_SIZE = "pageSize";
    public static final String ORDER_BY_COLUMN = "orderByColumn";
    public static final String IS_ASC = "isAsc";
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";
    public static final long CAPTCHA_EXPIRATION = 3;
    public static final long TOKEN_EXPIRE = 86400;
    public static final long AUTH_TOKEN_EXPIRE = 315360000;
    public static final String SYS_CONFIG_KEY = "sys_config:";
    public static final String SYS_DICT_KEY = "sys_dict:";
    public static final String RESOURCE_PREFIX = "/profile";
    public static final String COORDINATE_SYSTEM_WGS84 = "wgs84";
    public static final String COORDINATE_SYSTEM_3857 = "3857";
    public static final Integer HTTP_OK = 200;
    public static final Integer HTTP_ERROR = 201;
    public static final Long SYSTEM_ID_MONITOR = 17660486646308910L;
    public static final Long USER_ID_SUPER_ADMIN = 88L;
    public static final Long USER_ID_ADMIN = 66L;
    public static final Integer DEFAULT_KEEP_LINE_DISTANCE = 30;
    public static final Long DEFAULT_KEY_KEEP_LINE_SECOND = 300L;
    public static final Integer DEFAULT_VEHICLE_KEEP_LINE_DISTANCE = 100;
    public static final Long DEFAULT_KEY_VEHICLE_KEEP_LINE_SECOND = 600L;
    public static final Long MENU_BASE_ID = 0L;
    public static final List<String> FREQUENCY_YEAR_LIST = Arrays.asList("日检", "周检", "月检", "年检");
    public static final Charset CHARSET_UTF_8 = Charset.forName("UTF-8");
    public static final Integer PAGE_NUM_DEFAULT_VALUE = 1;
    public static final Integer PAGE_SIZE_DEFAULT_VALUE = 20;
   // public static final Integer PAGE_NUM_MAX_VALUE = Integer.valueOf(BZip2Constants.BASEBLOCKSIZE);
    public static final Integer PAGE_SIZE_MAX_VALUE = 201;
    public static final Integer PAGE_NUM_MIN_VALUE = 1;
    public static final Integer PAGE_SIZE_MIN_VALUE = 1;
    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final Integer SUCCESS = 200;
    public static final Integer FAIL = 500;
    public static final String generateCode(String codePrefix) {
        return codePrefix + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSSS"));
    }
}
