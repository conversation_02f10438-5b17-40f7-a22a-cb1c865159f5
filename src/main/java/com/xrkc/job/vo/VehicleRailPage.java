package com.xrkc.job.vo;
import java.io.Serializable;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/vo/VehicleRailPage.class */
public class VehicleRailPage implements Serializable {
    private Integer pageNum;
    private Integer pageSize;
    private Long carId;
    private Integer vehicleRailId;
    private Boolean binding;
    private List<Long> carIds;
    public Integer getPageNum() {
        return this.pageNum;
    }
    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }
    public Integer getPageSize() {
        return this.pageSize;
    }
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
    public Long getCarId() {
        return this.carId;
    }
    public void setCarId(Long carId) {
        this.carId = carId;
    }
    public Integer getVehicleRailId() {
        return this.vehicleRailId;
    }
    public void setVehicleRailId(Integer vehicleRailId) {
        this.vehicleRailId = vehicleRailId;
    }
    public Boolean getBinding() {
        return this.binding;
    }
    public void setBinding(Boolean binding) {
        this.binding = binding;
    }
    public List<Long> getCarIds() {
        return this.carIds;
    }
    public void setCarIds(List<Long> carIds) {
        this.carIds = carIds;
    }
}
