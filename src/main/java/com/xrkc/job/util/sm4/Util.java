package com.xrkc.job.util.sm4;
import java.math.BigInteger;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/sm4/Util.class */
public class Util {
    private static final char[] DIGITS_LOWER = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
    private static final char[] DIGITS_UPPER = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
    public static byte[] intToBytes(int num) {
        byte[] bytes = {(byte) (255 & (num >> 0)), (byte) (255 & (num >> 8)), (byte) (255 & (num >> 16)), (byte) (255 & (num >> 24))};
        return bytes;
    }
    public static int byteToInt(byte[] bytes) {
        int temp = (255 & bytes[0]) << 0;
        int num = 0 | temp;
        int temp2 = (255 & bytes[1]) << 8;
        int num2 = num | temp2;
        int temp3 = (255 & bytes[2]) << 16;
        int num3 = num2 | temp3;
        int temp4 = (255 & bytes[3]) << 24;
        return num3 | temp4;
    }
    public static byte[] longToBytes(long num) {
        byte[] bytes = new byte[8];
        for (int i = 0; i < 8; i++) {
            bytes[i] = (byte) (255 & (num >> (i * 8)));
        }
        return bytes;
    }
    public static byte[] byteConvert32Bytes(BigInteger n) {
        byte[] tmpd;
        if (n == null) {
            return null;
        }
        if (n.toByteArray().length == 33) {
            tmpd = new byte[32];
            System.arraycopy(n.toByteArray(), 1, tmpd, 0, 32);
        } else if (n.toByteArray().length == 32) {
            tmpd = n.toByteArray();
        } else {
            tmpd = new byte[32];
            for (int i = 0; i < 32 - n.toByteArray().length; i++) {
                tmpd[i] = 0;
            }
            System.arraycopy(n.toByteArray(), 0, tmpd, 32 - n.toByteArray().length, n.toByteArray().length);
        }
        return tmpd;
    }
    public static BigInteger byteConvertInteger(byte[] b) {
        if (b[0] < 0) {
            byte[] temp = new byte[b.length + 1];
            temp[0] = 0;
            System.arraycopy(b, 0, temp, 1, b.length);
            return new BigInteger(temp);
        }
        return new BigInteger(b);
    }
    public static String getHexString(byte[] bytes) {
        return getHexString(bytes, true);
    }
    public static String getHexString(byte[] bytes, boolean upperCase) {
        String ret = "";
        for (byte b : bytes) {
            ret = ret + Integer.toString((b & 255) + 256, 16).substring(1);
        }
        return upperCase ? ret.toUpperCase() : ret;
    }
    public static void printHexString(byte[] bytes) {
        for (byte b : bytes) {
            String hex = Integer.toHexString(b & 255);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            System.out.print("0x" + hex.toUpperCase() + ",");
        }
        System.out.println("");
    }
    public static byte[] hexStringToBytes(String hexString) {
        if (hexString == null || hexString.equals("")) {
            return null;
        }
        String hexString2 = hexString.toUpperCase();
        int length = hexString2.length() / 2;
        char[] hexChars = hexString2.toCharArray();
        byte[] d = new byte[length];
        for (int i = 0; i < length; i++) {
            int pos = i * 2;
            d[i] = (byte) ((charToByte(hexChars[pos]) << 4) | charToByte(hexChars[pos + 1]));
        }
        return d;
    }
    public static byte charToByte(char c) {
        return (byte) "0123456789ABCDEF".indexOf(c);
    }
    public static char[] encodeHex(byte[] data) {
        return encodeHex(data, true);
    }
    public static char[] encodeHex(byte[] data, boolean toLowerCase) {
        return encodeHex(data, toLowerCase ? DIGITS_LOWER : DIGITS_UPPER);
    }
    protected static char[] encodeHex(byte[] data, char[] toDigits) {
        int l = data.length;
        char[] out = new char[l << 1];
        int j = 0;
        for (int i = 0; i < l; i++) {
            int i2 = j;
            int j2 = j + 1;
            out[i2] = toDigits[(240 & data[i]) >>> 4];
            j = j2 + 1;
            out[j2] = toDigits[15 & data[i]];
        }
        return out;
    }
    public static String encodeHexString(byte[] data) {
        return encodeHexString(data, true);
    }
    public static String encodeHexString(byte[] data, boolean toLowerCase) {
        return encodeHexString(data, toLowerCase ? DIGITS_LOWER : DIGITS_UPPER);
    }
    protected static String encodeHexString(byte[] data, char[] toDigits) {
        return new String(encodeHex(data, toDigits));
    }
    public static byte[] decodeHex(char[] data) {
        int len = data.length;
        if ((len & 1) != 0) {
            throw new RuntimeException("Odd number of characters.");
        }
        byte[] out = new byte[len >> 1];
        int i = 0;
        int j = 0;
        while (j < len) {
            int f = toDigit(data[j], j) << 4;
            int j2 = j + 1;
            int f2 = f | toDigit(data[j2], j2);
            j = j2 + 1;
            out[i] = (byte) (f2 & 255);
            i++;
        }
        return out;
    }
    protected static int toDigit(char ch2, int index) {
        int digit = Character.digit(ch2, 16);
        if (digit == -1) {
            throw new RuntimeException("Illegal hexadecimal character " + ch2 + " at index " + index);
        }
        return digit;
    }
    public static String StringToAsciiString(String content) {
        String result = "";
        int max = content.length();
        for (int i = 0; i < max; i++) {
            char c = content.charAt(i);
            String b = Integer.toHexString(c);
            result = result + b;
        }
        return result;
    }
    public static String hexStringToString(String hexString, int encodeType) {
        String result = "";
        int max = hexString.length() / encodeType;
        for (int i = 0; i < max; i++) {
            char c = (char) hexStringToAlgorism(hexString.substring(i * encodeType, (i + 1) * encodeType));
            result = result + c;
        }
        return result;
    }
    public static int hexStringToAlgorism(String hex) {
        char c;
        char c2;
        String hex2 = hex.toUpperCase();
        int max = hex2.length();
        int result = 0;
        for (int i = max; i > 0; i--) {
            char c3 = hex2.charAt(i - 1);
            if (c3 >= '0' && c3 <= '9') {
                c = c3;
                c2 = '0';
            } else {
                c = c3;
                c2 = '7';
            }
            int algorism = c - c2;
            result = (int) (result + (Math.pow(16.0d, max - i) * algorism));
        }
        return result;
    }
    public static String hexStringToBinary(String hex) {
        String hex2 = hex.toUpperCase();
        String result = "";
        int max = hex2.length();
        for (int i = 0; i < max; i++) {
            char c = hex2.charAt(i);
            switch (c) {
                case '0':
                    result = result + "0000";
                    break;
                case '1':
                    result = result + "0001";
                    break;
                case '2':
                    result = result + "0010";
                    break;
                case '3':
                    result = result + "0011";
                    break;
                case '4':
                    result = result + "0100";
                    break;
                case '5':
                    result = result + "0101";
                    break;
                case '6':
                    result = result + "0110";
                    break;
                case '7':
                    result = result + "0111";
                    break;
                case '8':
                    result = result + "1000";
                    break;
                case '9':
                    result = result + "1001";
                    break;
                case 'A':
                    result = result + "1010";
                    break;
                case 'B':
                    result = result + "1011";
                    break;
                case 'C':
                    result = result + "1100";
                    break;
                case 'D':
                    result = result + "1101";
                    break;
                case 'E':
                    result = result + "1110";
                    break;
                case 'F':
                    result = result + "1111";
                    break;
            }
        }
        return result;
    }
    public static String AsciiStringToString(String content) {
        String result = "";
        int length = content.length() / 2;
        for (int i = 0; i < length; i++) {
            String c = content.substring(i * 2, (i * 2) + 2);
            int a = hexStringToAlgorism(c);
            char b = (char) a;
            String d = String.valueOf(b);
            result = result + d;
        }
        return result;
    }
    public static String algorismToHexString(int algorism, int maxLength) {
        String result = Integer.toHexString(algorism);
        if (result.length() % 2 == 1) {
            result = "0" + result;
        }
        return patchHexString(result.toUpperCase(), maxLength);
    }
    public static String byteToString(byte[] bytearray) {
        String result = "";
        for (byte b : bytearray) {
            char temp = (char) b;
            result = result + temp;
        }
        return result;
    }
    public static int binaryToAlgorism(String binary) {
        int max = binary.length();
        int result = 0;
        for (int i = max; i > 0; i--) {
            char c = binary.charAt(i - 1);
            int algorism = c - '0';
            result = (int) (result + (Math.pow(2.0d, max - i) * algorism));
        }
        return result;
    }
    public static String algorismToHEXString(int algorism) {
        String result = Integer.toHexString(algorism);
        if (result.length() % 2 == 1) {
            result = "0" + result;
        }
        return result.toUpperCase();
    }
    public static String patchHexString(String str, int maxLength) {
        String temp = "";
        for (int i = 0; i < maxLength - str.length(); i++) {
            temp = "0" + temp;
        }
        return (temp + str).substring(0, maxLength);
    }
    public static int parseToInt(String s, int defaultInt, int radix) throws NumberFormatException {
        int i;
        try {
            i = Integer.parseInt(s, radix);
        } catch (NumberFormatException e) {
            i = defaultInt;
        }
        return i;
    }
    public static int parseToInt(String s, int defaultInt) throws NumberFormatException {
        int i;
        try {
            i = Integer.parseInt(s);
        } catch (NumberFormatException e) {
            i = defaultInt;
        }
        return i;
    }
    public static byte[] hexToByte(String hex) throws IllegalArgumentException {
        if (hex.length() % 2 != 0) {
            throw new IllegalArgumentException();
        }
        char[] arr = hex.toCharArray();
        byte[] b = new byte[hex.length() / 2];
        int i = 0;
        int j = 0;
        int l = hex.length();
        while (i < l) {
            int i2 = i;
            int i3 = i + 1;
            String swap = "" + arr[i2] + arr[i3];
            int byteint = Integer.parseInt(swap, 16) & 255;
            b[j] = new Integer(byteint).byteValue();
            i = i3 + 1;
            j++;
        }
        return b;
    }
    public static String byteToHex(byte[] b) {
        String str;
        if (b == null) {
            throw new IllegalArgumentException("Argument b ( byte array ) is null! ");
        }
        String hs = "";
        for (byte b2 : b) {
            String stmp = Integer.toHexString(b2 & 255);
            if (stmp.length() == 1) {
                str = hs + "0" + stmp;
            } else {
                str = hs + stmp;
            }
            hs = str;
        }
        return hs.toUpperCase();
    }
    public static byte[] subByte(byte[] input, int startIndex, int length) {
        byte[] bt = new byte[length];
        for (int i = 0; i < length; i++) {
            bt[i] = input[i + startIndex];
        }
        return bt;
    }
}
