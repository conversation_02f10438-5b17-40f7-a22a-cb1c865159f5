package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
@TableName(value = "core_area_alarm_post", autoResultMap = true)
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/AreaAlarmPost.class */
public class AreaAlarmPost implements Serializable {
    private static final long serialVersionUID = 1;
    private Long areaId;
    private Long postId;
    @TableField(exist = false)
    private String postName;
    public static final String AREA_ID = "area_id";
    public static final String POST_ID = "post_id";
    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }
    public void setPostId(Long postId) {
        this.postId = postId;
    }
    public void setPostName(String postName) {
        this.postName = postName;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof AreaAlarmPost)) {
            return false;
        }
        AreaAlarmPost other = (AreaAlarmPost) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$areaId = getAreaId();
        Object other$areaId = other.getAreaId();
        if (this$areaId == null) {
            if (other$areaId != null) {
                return false;
            }
        } else if (!this$areaId.equals(other$areaId)) {
            return false;
        }
        Object this$postId = getPostId();
        Object other$postId = other.getPostId();
        if (this$postId == null) {
            if (other$postId != null) {
                return false;
            }
        } else if (!this$postId.equals(other$postId)) {
            return false;
        }
        Object this$postName = getPostName();
        Object other$postName = other.getPostName();
        return this$postName == null ? other$postName == null : this$postName.equals(other$postName);
    }
    protected boolean canEqual(Object other) {
        return other instanceof AreaAlarmPost;
    }
    public int hashCode() {
        Object $areaId = getAreaId();
        int result = (1 * 59) + ($areaId == null ? 43 : $areaId.hashCode());
        Object $postId = getPostId();
        int result2 = (result * 59) + ($postId == null ? 43 : $postId.hashCode());
        Object $postName = getPostName();
        return (result2 * 59) + ($postName == null ? 43 : $postName.hashCode());
    }
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/AreaAlarmPost$AreaAlarmPostBuilder.class */
    public static class AreaAlarmPostBuilder {
        private Long areaId;
        private Long postId;
        private String postName;
        AreaAlarmPostBuilder() {
        }
        public AreaAlarmPostBuilder areaId(Long areaId) {
            this.areaId = areaId;
            return this;
        }
        public AreaAlarmPostBuilder postId(Long postId) {
            this.postId = postId;
            return this;
        }
        public AreaAlarmPostBuilder postName(String postName) {
            this.postName = postName;
            return this;
        }
        public AreaAlarmPost build() {
            return new AreaAlarmPost(this.areaId, this.postId, this.postName);
        }
        public String toString() {
            return "AreaAlarmPost.AreaAlarmPostBuilder(areaId=" + this.areaId + ", postId=" + this.postId + ", postName=" + this.postName + StringPool.RIGHT_BRACKET;
        }
    }
    public String toString() {
        return "AreaAlarmPost(super=" + super.toString() + ", areaId=" + getAreaId() + ", postId=" + getPostId() + ", postName=" + getPostName() + StringPool.RIGHT_BRACKET;
    }
    public static AreaAlarmPostBuilder builder() {
        return new AreaAlarmPostBuilder();
    }
    public AreaAlarmPost(Long areaId, Long postId, String postName) {
        this.areaId = areaId;
        this.postId = postId;
        this.postName = postName;
    }
    public AreaAlarmPost() {
    }
    public Long getAreaId() {
        return this.areaId;
    }
    public Long getPostId() {
        return this.postId;
    }
    public String getPostName() {
        return this.postName;
    }
    public AreaAlarmPost(Long areaId, Long postId) {
        this.areaId = areaId;
        this.postId = postId;
    }
}
