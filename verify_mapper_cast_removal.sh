#!/bin/bash

# 验证项目中所有Mapper的强制转换是否已被删除

echo "验证Mapper强制转换删除结果..."
echo "=========================================="

# 检查updateById方法中的强制转换
echo "1. 检查updateById方法中的强制转换："
updateById_files=$(find src/main/java -name "*.java" -exec grep -l "updateById.*Mapper.*)" {} \; 2>/dev/null)
if [ -z "$updateById_files" ]; then
    echo "✓ 未发现updateById方法中的强制转换"
else
    echo "✗ 仍有以下文件包含updateById强制转换："
    echo "$updateById_files"
    echo ""
    echo "详细信息："
    grep -n "updateById.*Mapper.*)" $updateById_files
fi

echo ""

# 检查insert方法中的强制转换
echo "2. 检查insert方法中的强制转换："
insert_files=$(find src/main/java -name "*.java" -exec grep -l "insert.*Mapper.*)" {} \; 2>/dev/null)
if [ -z "$insert_files" ]; then
    echo "✓ 未发现insert方法中的强制转换"
else
    echo "✗ 仍有以下文件包含insert强制转换："
    echo "$insert_files"
    echo ""
    echo "详细信息："
    grep -n "insert.*Mapper.*)" $insert_files
fi

echo ""

# 检查备份文件
echo "3. 检查备份文件："
backup_files=$(find src/main/java -name "*.backup" 2>/dev/null)
if [ -z "$backup_files" ]; then
    echo "未找到备份文件"
else
    echo "找到以下备份文件："
    echo "$backup_files"
fi

echo ""
echo "=========================================="

# 总结
if [ -z "$updateById_files" ] && [ -z "$insert_files" ]; then
    echo "✓ 验证完成：所有Mapper强制转换已成功删除！"
    echo ""
    echo "修复前后对比示例："
    echo "修复前: this.mapper.updateById((SomeMapper) entity)"
    echo "修复后: this.mapper.updateById(entity)"
    echo ""
    echo "修复前: this.mapper.insert((SomeMapper) entity)"
    echo "修复后: this.mapper.insert(entity)"
else
    echo "✗ 验证失败：仍有强制转换未被删除"
    echo "请检查上述文件并手动修复"
fi

echo "=========================================="
