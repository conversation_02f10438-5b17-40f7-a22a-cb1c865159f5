package com.xrkc.job.service;
import com.xrkc.job.domain.AreaAlarmTempVO;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IAreaService.class */
public interface IAreaService {
    List<AreaAlarmTempVO> selectList();
    List<Long> findDeptIdsByAreaId(Long l);
    List<Long> findPersonIdsByAreaId(Long l);
    List<Long> findPostIdsByAreaId(Long l);
    List<Long> findAlarmPersonIdsByAreaId(Long l);
    List<AreaAlarmTempVO> selectAreaAlarmByAlarmType(String str);
}
