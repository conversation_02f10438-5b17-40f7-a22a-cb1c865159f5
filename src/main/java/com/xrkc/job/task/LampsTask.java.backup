package com.xrkc.job.task;
import com.xrkc.job.domain.DeviceLampsCommand;
import com.xrkc.job.domain.DeviceLampsCommandRelation;
import com.xrkc.job.domain.LampsTimeControl;
import com.xrkc.job.mapper.DeviceLampsCommandMapper;
import com.xrkc.job.mapper.DeviceLampsCommandRelationMapper;
import com.xrkc.job.service.ILampsTimeControlService;
import com.xrkc.job.service.IStatisticsLampsService;
import com.xrkc.job.util.TimeSectionUtils;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
@Component("lampsTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/LampsTask.class */
public class LampsTask {
    @Autowired
    private DeviceLampsCommandRelationMapper lampsCommandRelationMapper;
    @Autowired
    private DeviceLampsCommandMapper lampsCommandMapper;
    @Autowired
    private ILampsTimeControlService lampsTimeControlService;
    @Autowired
    private IStatisticsLampsService statisticsLampsService;
    private static final String ONLINE_LAMPS = "00";
    private static final String DEVICE_LAMPS = "02";
    private static final Logger log = LoggerFactory.getLogger((Class<?>) LampsTask.class);
    private static final Integer LAMPS_TWO_COMMAND = 2;
    public void lampsTimeControl() {
        List<LampsTimeControl> list = this.lampsTimeControlService.list();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(lampsTimeControl -> {
            List<Long> lampsGroupIds = this.lampsTimeControlService.findGroupIdByTimeId(lampsTimeControl.getId());
            if (!CollectionUtils.isEmpty(lampsGroupIds)) {
                if ((lampsTimeControl.getTaskEnable().intValue() == 0 || lampsTimeControl.getTaskEnable().intValue() == 2) && TimeSectionUtils.currentTimeBetween(lampsTimeControl.getStartTime(), lampsTimeControl.getEndTime())) {
                    DeviceLampsCommand lampsCommand = new DeviceLampsCommand();
                    lampsCommand.setCommandType(LAMPS_TWO_COMMAND);
                    if (Objects.nonNull(lampsTimeControl.getLampsStatus()) && (lampsTimeControl.getLampsStatus().intValue() == 2 || lampsTimeControl.getLampsStatus().intValue() == 3)) {
                        log.info("自动感应下发1：{}", Integer.valueOf(lampsTimeControl.getLampsStatus().intValue() == 2 ? 0 : 1));
                        lampsCommand.setPushInductiveEnable(Integer.valueOf(lampsTimeControl.getLampsStatus().intValue() == 2 ? 0 : 1));
                        lampsCommand.setPushInductiveOffLuminance(Integer.valueOf(Objects.nonNull(lampsTimeControl.getPushInductiveOffLuminance()) ? lampsTimeControl.getPushInductiveOffLuminance().intValue() : 0));
                        lampsCommand.setPushInductiveDistance(Integer.valueOf(Objects.nonNull(lampsTimeControl.getPushInductiveDistance()) ? lampsTimeControl.getPushInductiveDistance().intValue() : 0));
                        lampsCommand.setPushInductiveLuminance(Integer.valueOf(Objects.nonNull(lampsTimeControl.getPushInductiveLuminance()) ? lampsTimeControl.getPushInductiveLuminance().intValue() : 5));
                        lampsCommand.setPushInductiveOffDelayTime(Integer.valueOf(Objects.nonNull(lampsTimeControl.getPushInductiveOffDelayTime()) ? lampsTimeControl.getPushInductiveOffDelayTime().intValue() : 1));
                        lampsCommand.setOpcode(DEVICE_LAMPS);
                    } else {
                        lampsCommand.setLampsStatus(lampsTimeControl.getLampsStatus());
                        lampsCommand.setLampsLuminance(lampsTimeControl.getLampsLuminance());
                        log.info("下发1：{}", lampsTimeControl.getLampsStatus());
                        lampsCommand.setOpcode("00");
                    }
                    lampsCommand.setCreateBy(lampsTimeControl.getCreateBy());
                    lampsCommand.setCreateTime(LocalDateTime.now());
                    lampsCommand.setCommandStatus("N");
                    lampsCommand.setCommandSource("时控任务");
                    this.lampsCommandMapper.insert((DeviceLampsCommandMapper) lampsCommand);
                    lampsGroupIds.forEach(lampsGroupId -> {
                        DeviceLampsCommandRelation relationEntity = new DeviceLampsCommandRelation();
                        relationEntity.setCommandId(lampsCommand.getCommandId());
                        relationEntity.setCommandType(LAMPS_TWO_COMMAND);
                        relationEntity.setRelationId(lampsGroupId);
                        relationEntity.setCommandResult("X");
                        this.lampsCommandRelationMapper.insert((DeviceLampsCommandRelationMapper) relationEntity);
                    });
                    lampsTimeControl.setTaskEnable(1);
                    this.lampsTimeControlService.updateById(lampsTimeControl);
                    return;
                }
                if (lampsTimeControl.getTaskEnable().intValue() == 1 && !TimeSectionUtils.currentTimeBetween(lampsTimeControl.getStartTime(), lampsTimeControl.getEndTime())) {
                    DeviceLampsCommand lampsCommand2 = new DeviceLampsCommand();
                    if (Objects.nonNull(lampsTimeControl.getLampsStatus()) && (lampsTimeControl.getLampsStatus().intValue() == 2 || lampsTimeControl.getLampsStatus().intValue() == 3)) {
                        lampsCommand2.setPushInductiveEnable(Integer.valueOf(lampsTimeControl.getLampsStatus().intValue() == 2 ? 0 : 1));
                        log.info("自动感应下发2：{}", Integer.valueOf(lampsTimeControl.getLampsStatus().intValue() == 2 ? 1 : 0));
                        lampsCommand2.setPushInductiveOffLuminance(Integer.valueOf(Objects.nonNull(lampsTimeControl.getPushInductiveOffLuminance()) ? lampsTimeControl.getPushInductiveOffLuminance().intValue() : 0));
                        lampsCommand2.setPushInductiveDistance(Integer.valueOf(Objects.nonNull(lampsTimeControl.getPushInductiveDistance()) ? lampsTimeControl.getPushInductiveDistance().intValue() : 0));
                        lampsCommand2.setPushInductiveLuminance(Integer.valueOf(Objects.nonNull(lampsTimeControl.getPushInductiveLuminance()) ? lampsTimeControl.getPushInductiveLuminance().intValue() : 5));
                        lampsCommand2.setPushInductiveOffDelayTime(Integer.valueOf(Objects.nonNull(lampsTimeControl.getPushInductiveOffDelayTime()) ? lampsTimeControl.getPushInductiveOffDelayTime().intValue() : 1));
                        lampsCommand2.setOpcode(DEVICE_LAMPS);
                    } else {
                        lampsCommand2.setOpcode("00");
                        lampsCommand2.setLampsStatus(Integer.valueOf(lampsTimeControl.getLampsStatus().intValue() == 0 ? 1 : 0));
                        lampsCommand2.setLampsLuminance(lampsTimeControl.getLampsLuminance());
                        log.info("下发2：{}", Integer.valueOf(lampsTimeControl.getLampsStatus().intValue() == 0 ? 1 : 0));
                    }
                    lampsCommand2.setCommandType(LAMPS_TWO_COMMAND);
                    lampsCommand2.setCreateBy(lampsTimeControl.getCreateBy());
                    lampsCommand2.setCreateTime(LocalDateTime.now());
                    lampsCommand2.setCommandStatus("N");
                    lampsCommand2.setCommandSource("时控任务");
                    this.lampsCommandMapper.insert((DeviceLampsCommandMapper) lampsCommand2);
                    lampsGroupIds.forEach(lampsGroupId2 -> {
                        DeviceLampsCommandRelation relationEntity = new DeviceLampsCommandRelation();
                        relationEntity.setCommandId(lampsCommand2.getCommandId());
                        relationEntity.setCommandType(LAMPS_TWO_COMMAND);
                        relationEntity.setRelationId(lampsGroupId2);
                        relationEntity.setCommandResult("X");
                        this.lampsCommandRelationMapper.insert((DeviceLampsCommandRelationMapper) relationEntity);
                    });
                    lampsTimeControl.setTaskEnable(2);
                    this.lampsTimeControlService.updateById(lampsTimeControl);
                }
            }
        });
    }
    public void statisticLamps() {
        LocalDateTime now = LocalDateTime.now();
        this.statisticsLampsService.addStatistLamps(now);
        this.statisticsLampsService.addStatistLamps(now.minusHours(1L));
    }
}
