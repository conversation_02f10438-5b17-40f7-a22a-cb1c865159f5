package com.xrkc.job.module.vehicle.service.impl;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.domain.vehicle.VehicleAlarm;
import com.xrkc.datascope.aspect.DataScopeAspect;
import com.xrkc.job.module.vehicle.mapper.VehicleAlarmMapper;
import com.xrkc.job.module.vehicle.service.IVehicleAlarmService;
import com.xrkc.job.module.vehicle.service.IVehicleAlarmSettingService;
import com.xrkc.redis.utils.RedisCacheUtils;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/module/vehicle/service/impl/VehicleAlarmServiceImpl.class */
public class VehicleAlarmServiceImpl implements IVehicleAlarmService {
    private final VehicleAlarmMapper mapper;
    @Autowired
    private IVehicleAlarmSettingService vehicleAlarmSettingService;
    @Autowired
    public VehicleAlarmServiceImpl(VehicleAlarmMapper mapper) {
        this.mapper = mapper;
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehicleAlarmService
    public void batchInsert(List<VehicleAlarm> vehicleAlarmList) {
        this.mapper.insertBatch(vehicleAlarmList);
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehicleAlarmService
    public void batchCalcInsert(List<VehicleAlarm> vehicleAlarmList) throws NumberFormatException {
        Map<String, String> configMap = RedisCacheUtils.getConfigRedisCache();
        int alarm_interval_5 = Integer.parseInt(configMap.getOrDefault(ConfigValue.CONFIG_KEY_VEHICLE_ALARM_INTERVAL_5, DataScopeAspect.DATA_SCOPE_CUSTOM_AND_CHILD));
        int alarm_interval_26 = Integer.parseInt(configMap.getOrDefault(ConfigValue.CONFIG_KEY_VEHICLE_ALARM_INTERVAL_26, DataScopeAspect.DATA_SCOPE_CUSTOM_AND_CHILD));
        int alarm_interval_27 = Integer.parseInt(configMap.getOrDefault(ConfigValue.CONFIG_KEY_VEHICLE_ALARM_INTERVAL_27, DataScopeAspect.DATA_SCOPE_CUSTOM_AND_CHILD));
        int alarm_interval_30 = Integer.parseInt(configMap.getOrDefault(ConfigValue.CONFIG_KEY_VEHICLE_ALARM_INTERVAL_30, DataScopeAspect.DATA_SCOPE_CUSTOM_AND_CHILD));
        int alarm_interval_70 = Integer.parseInt(configMap.getOrDefault(ConfigValue.CONFIG_KEY_VEHICLE_ALARM_INTERVAL_70, DataScopeAspect.DATA_SCOPE_CUSTOM_AND_CHILD));
        List<Integer> numbers = Arrays.asList(Integer.valueOf(alarm_interval_5), Integer.valueOf(alarm_interval_26), Integer.valueOf(alarm_interval_27), Integer.valueOf(alarm_interval_30), Integer.valueOf(alarm_interval_70));
        int max = 0;
        Iterator<Integer> it = numbers.iterator();
        while (it.hasNext()) {
            int number = it.next().intValue();
            max = Math.max(max, number);
        }
        int maxInterval = max + 1;
        List<String> alarmTypeList = (List) vehicleAlarmList.stream().map((v0) -> {
            return v0.getAlarmType();
        }).distinct().collect(Collectors.toList());
        List<VehicleAlarm> existList = this.mapper.selectExistList(alarmTypeList, maxInterval);
        if (CollectionUtils.isEmpty(existList)) {
            this.mapper.insertBatch(vehicleAlarmList);
        } else {
            vehicleAlarmList.stream().forEach(t -> {
                int alarmInterval;
                boolean exist;
                String vehicleAlarmType = t.getAlarmType();
                alarmInterval = 6;
                switch (vehicleAlarmType) {
                    case "5":
                        alarmInterval = alarm_interval_5;
                        break;
                    case "26":
                        alarmInterval = alarm_interval_26;
                        break;
                    case "27":
                        alarmInterval = alarm_interval_27;
                        break;
                    case "30":
                        alarmInterval = alarm_interval_30;
                        break;
                    case "70":
                        alarmInterval = alarm_interval_70;
                        break;
                }
                LocalDateTime alarmIntervalTime = LocalDateTime.now().minusMinutes(alarmInterval);
                if (Objects.nonNull(t.getSettingId())) {
                    if (Objects.nonNull(t.getVehicleId())) {
                        exist = existList.stream().filter(f -> {
                            return t.getSettingId().compareTo(f.getSettingId()) == 0 && vehicleAlarmType.equals(f.getAlarmType()) && f.getAlarmTime().isAfter(alarmIntervalTime) && Objects.nonNull(f.getVehicleId()) && f.getVehicleId().compareTo(t.getVehicleId()) == 0;
                        }).count() > 0;
                    } else {
                        exist = existList.stream().filter(f2 -> {
                            return t.getSettingId().compareTo(f2.getSettingId()) == 0 && vehicleAlarmType.equals(f2.getAlarmType()) && f2.getAlarmTime().isAfter(alarmIntervalTime) && Objects.nonNull(f2.getCardId()) && f2.getCardId().compareTo(t.getCardId()) == 0;
                        }).count() > 0;
                    }
                } else if (Objects.nonNull(t.getVehicleId())) {
                    exist = existList.stream().filter(f3 -> {
                        return vehicleAlarmType.equals(f3.getAlarmType()) && f3.getAlarmTime().isAfter(alarmIntervalTime) && Objects.nonNull(f3.getVehicleId()) && f3.getVehicleId().compareTo(t.getVehicleId()) == 0;
                    }).count() > 0;
                } else {
                    exist = existList.stream().filter(f4 -> {
                        return vehicleAlarmType.equals(f4.getAlarmType()) && f4.getAlarmTime().isAfter(alarmIntervalTime) && Objects.nonNull(f4.getCardId()) && f4.getCardId().compareTo(t.getCardId()) == 0;
                    }).count() > 0;
                }
                if (!exist) {
                    this.mapper.insert((VehicleAlarmMapper) t);
                    existList.add(t);
                }
            });
        }
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehicleAlarmService
    public List<VehicleAlarm> selectCurrentUnDisposeList() {
        return this.mapper.selectCurrentUnDisposeList();
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehicleAlarmService
    public Long countUnDisposed() {
        return this.mapper.selectCount((v0) -> {
            return v0.getAlarmStatus();
        }, "15");
    }
    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.xrkc.job.module.vehicle.service.IVehicleAlarmService
    public Map<Long, List<String>> selectUnDisposeAlarmTypeMap(List<Long> vehicleIdList) {
        Map<Long, List<String>> result = new HashMap<>();
        Map<Long, List<VehicleAlarm>> map = (Map) this.mapper.selectList((Wrapper) ((LambdaQueryWrapper) new LambdaQueryWrapper<VehicleAlarm>().eq((v0) -> {
            return v0.getAlarmStatus();
        }, "15")).in((LambdaQueryWrapper) (v0) -> {
            return v0.getVehicleId();
        }, (Collection<?>) vehicleIdList)).stream().collect(Collectors.groupingBy((v0) -> {
            return v0.getVehicleId();
        }));
        map.forEach((vehicleId, l) -> {
        });
        return result;
    }
    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.xrkc.job.module.vehicle.service.IVehicleAlarmService
    public Map<String, Long> statisticsAlarmTypeMap(LocalDate statisticsDate) {
        LocalDateTime min = LocalDateTime.of(statisticsDate, LocalTime.MIN);
        LocalDateTime max = LocalDateTime.of(statisticsDate, LocalTime.MAX);
        return (Map) this.mapper.selectList((Wrapper) new LambdaQueryWrapper<VehicleAlarm>().between((v0) -> {
            return v0.getAlarmTime();
        }, min, max)).stream().collect(Collectors.groupingBy((v0) -> {
            return v0.getAlarmType();
        }, Collectors.counting()));
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehicleAlarmService
    public Long insert(VehicleAlarm vehicleAlarm) {
        this.mapper.insert((VehicleAlarmMapper) vehicleAlarm);
        return vehicleAlarm.getId();
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehicleAlarmService
    public LambdaQueryChainWrapper<VehicleAlarm> lambdaSelectList() {
        return ChainWrappers.lambdaQueryChain((BaseMapper) this.mapper, VehicleAlarm.class);
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehicleAlarmService
    public Map<Long, Long> findUndisposedMap() {
        return (Map) this.mapper.selectList((v0) -> {
            return v0.getAlarmStatus();
        }, "15").stream().filter(f -> {
            return Objects.nonNull(f.getVehicleId());
        }).collect(Collectors.groupingBy((v0) -> {
            return v0.getVehicleId();
        }, Collectors.counting()));
    }
    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.xrkc.job.module.vehicle.service.IVehicleAlarmService
    public List<Long> selectDisposedVehicleAlarmId(List<Long> list) {
        return (List) this.mapper.selectList((Wrapper) ((LambdaQueryWrapper) new LambdaQueryWrapper<VehicleAlarm>().in((LambdaQueryWrapper) (v0) -> {
            return v0.getId();
        }, (Collection<?>) list)).eq((v0) -> {
            return v0.getAlarmStatus();
        }, "20")).stream().map((v0) -> {
            return v0.getId();
        }).collect(Collectors.toList());
    }
    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.xrkc.job.module.vehicle.service.IVehicleAlarmService
    public List<VehicleAlarm> selectUnDisposedVehicleAlarmRail() {
        List<Long> settingIdList = this.vehicleAlarmSettingService.selectEnableSettingId();
        List<VehicleAlarm> list = (List) this.mapper.selectList((Wrapper) ((LambdaQueryWrapper) ((LambdaQueryWrapper) new LambdaQueryWrapper<VehicleAlarm>().eq((v0) -> {
            return v0.getAlarmStatus();
        }, "15")).in((LambdaQueryWrapper) (v0) -> {
            return v0.getSettingId();
        }, (Collection<?>) settingIdList)).gt((v0) -> {
            return v0.getCreateTime();
        }, LocalDateTime.now().minusSeconds(60L))).stream().collect(Collectors.toList());
        return list;
    }
}
