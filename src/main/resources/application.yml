# Spring
spring:
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  mail:
    host: smtp.exmail.qq.com
    port: 465
    username: peng<PERSON>@xinruikc.cn
    password: FFELEVco92xbdLcg
    protocol: smtp
    properties:
      mail:
        smtp:
          auth: true
          ssl:
            enable: true
            socketFactory:
              class: com.sun.mail.util.MailSSLSocketFactory
              fallback: false

mybatis-plus:
  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    #驼峰转换
    map-underscore-to-camel-case: true
    cache-enabled: true
  #  mapper-locations: classpath*:mybatis/*.xml
  type-aliases-package: com.xrkc.job.domain
  global-config:
    # mybaitsplus 动态刷新xml
    #    refresh: true
    #
    banner: false
    #刷新mapper 调试神器 未部署
    db-config:
      #主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
      id-type: assign_id
      #字段策略 0:"忽略判断",1:"非 NULL 判断"),2:"非空判断" 未部署
      #      field-strategy: not_empty
      #驼峰下划线转换 未部署      db-column-underline: true
      #数据库大写下划线转换
      capital-mode: true
      column-format: "`%s`"
      #序列接口实现类配置
      #key-generator: com.baomidou.springboot.xxx
      #逻辑删除配置
      #      logic-delete-value: 0
      #      logic-not-delete-value: 1
      #数据库类型 未部署
    #      logic-delete-field: bool_del
    #      db-type: mysql
    #自定义SQL注入器
    #sql-injector: com.baomidou.mybatisplus.mapper.LogicSqlInjector
    #自定义填充策略接口实现
    #meta-object-handler: com.baomidou.springboot.xxx
#
management:
  server:
    port: -1
logging:
  level:
    com.alibaba.nacos: warn