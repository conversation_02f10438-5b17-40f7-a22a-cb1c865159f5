package com.xrkc.job.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xrkc.job.domain.RehearsalPlan;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IRehearsalPlanService.class */
public interface IRehearsalPlanService extends IService<RehearsalPlan> {
    List<RehearsalPlan> selectRehearsalPlanList();
    int batchUpdateStatus(String str, List<Long> list);
}
