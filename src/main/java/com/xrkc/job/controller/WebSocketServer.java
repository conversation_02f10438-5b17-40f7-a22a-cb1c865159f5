package com.xrkc.job.controller;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.xrkc.job.config.SocketValue;
import java.io.IOException;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
@ServerEndpoint("/ws/{socketKey}")
@Component
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/controller/WebSocketServer.class */
public class WebSocketServer {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) WebSocketServer.class);
    private static AtomicInteger onlineNum = new AtomicInteger();
    public static ConcurrentHashMap<String, Session> systemSessionMap = new ConcurrentHashMap<>();
    public static ConcurrentHashMap<String, Session> apiSessionMap = new ConcurrentHashMap<>();
    public void sendMessageForXR(Session session, String message) {
        if (session != null) {
            if (session.isOpen()) {
                synchronized (session) {
                    try {
                        session.getBasicRemote().sendText(message);
                    } catch (IOException e) {
                        log.error("【WS推送】失败: {}，message：{}", e.getMessage(), message);
                    }
                }
                return;
            }
            Set<String> rmKey = new HashSet<>();
            systemSessionMap.forEach((k, s) -> {
                if (Objects.equals(s, session)) {
                    rmKey.add(k);
                }
            });
            rmKey.stream().forEach(k2 -> {
                systemSessionMap.remove(k2);
            });
            return;
        }
        if (!CollectionUtils.isEmpty(systemSessionMap)) {
            for (Session s2 : systemSessionMap.values()) {
                sendMessageForXR(s2, message);
            }
        }
    }
    public void sendMessageForAPI(Session session, String message) {
        if (session != null) {
            if (session.isOpen()) {
                synchronized (session) {
                    try {
                        session.getBasicRemote().sendText(message);
                    } catch (IOException e) {
                        log.error("【WS推送】失败: {}，message：{}", e.getMessage(), message);
                    }
                }
                return;
            }
            Set<String> rmKey = new HashSet<>();
            apiSessionMap.forEach((k, s) -> {
                if (Objects.equals(s, session)) {
                    rmKey.add(k);
                }
            });
            rmKey.stream().forEach(k2 -> {
                apiSessionMap.remove(k2);
            });
            return;
        }
        if (!CollectionUtils.isEmpty(apiSessionMap)) {
            for (Session s2 : apiSessionMap.values()) {
                sendMessageForAPI(s2, message);
            }
        }
    }
    public void sendMessageForSystemUser(Long userId, String message) {
        if (!CollectionUtils.isEmpty(systemSessionMap)) {
            systemSessionMap.forEach((socketKey, session) -> {
                String wsUserId = socketKey.split("_")[2];
                if (NumberUtil.isLong(wsUserId) && wsUserId.equals(userId.toString())) {
                    sendMessageForXR(session, message);
                }
            });
        }
    }
    public void sendMessageForAPIUser(Long userId, String message) {
        if (!CollectionUtils.isEmpty(systemSessionMap)) {
            systemSessionMap.forEach((socketKey, session) -> {
                String wsUserId = socketKey.split("_")[2];
                if (NumberUtil.isLong(wsUserId) && wsUserId.equals(userId.toString())) {
                    sendMessageForAPI(session, message);
                }
            });
        }
    }
    @OnOpen
    public void onOpen(Session session, @PathParam("socketKey") String socketKey) {
        if (StringUtils.isNotBlank(socketKey) && socketKey.split("_").length == 3) {
            String prefix = socketKey.split("_")[0];
            if (!NumberUtil.isLong(socketKey.split("_")[2])) {
                log.error("【WS连接】非法用户：{}", socketKey);
                return;
            }
            if (SocketValue.KEY_PREFIX_XR.equals(prefix)) {
                systemSessionMap.put(socketKey, session);
                addOnlineCount();
                log.info("【WS连接】{}加入{}", socketKey, prefix);
            } else {
                if (prefix.startsWith(SocketValue.KEY_PREFIX_TAIJI) || prefix.startsWith(SocketValue.KEY_PREFIX_API)) {
                    apiSessionMap.put(socketKey, session);
                    addOnlineCount();
                    log.info("【WS连接】{}加入{}", socketKey, prefix);
                    return;
                }
                log.info("【WS连接】不支持的连接前缀：{}，断开{}", socketKey, prefix);
            }
        }
    }
    @OnClose
    public void onClose(@PathParam("socketKey") String socketKey) {
        try {
            if (StringUtils.isNotBlank(socketKey) && socketKey.split("_").length == 3) {
                String prefix = socketKey.split("_")[0];
                if (SocketValue.KEY_PREFIX_XR.equals(prefix)) {
                    systemSessionMap.remove(socketKey);
                    subOnlineCount();
                    log.info("【WS关闭】{}断开{}", socketKey, prefix);
                } else if (prefix.startsWith(SocketValue.KEY_PREFIX_TAIJI)) {
                    apiSessionMap.remove(socketKey);
                    subOnlineCount();
                    log.info("【WS关闭】{}断开{}", socketKey, prefix);
                } else {
                    log.info("【WS关闭】不支持的连接前缀：{}，断开{}", socketKey, prefix);
                }
            }
        } catch (Exception e) {
            log.error("【WS关闭】处理失败", e.getMessage());
        }
    }
    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("【WS接收】{}:{}", session.getId(), message);
        try {
            JSONObject json = new JSONObject();
            json.put("msgType", "answer");
            json.put("data", "ok");
            session.getBasicRemote().sendText(json.toJSONString(new JSONWriter.Feature[0]));
        } catch (Exception e) {
            log.error("【WS消息应答】失败：{}", e.getMessage());
        }
    }
    @OnError
    public void onError(Session session, Throwable throwable) {
        log.info("【WS错误】{}", throwable.getMessage());
    }
    public static void addOnlineCount() {
        onlineNum.incrementAndGet();
    }
    public static void subOnlineCount() {
        onlineNum.decrementAndGet();
    }
}
