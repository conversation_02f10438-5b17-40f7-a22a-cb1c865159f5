package com.xrkc.job.domain;
import com.aliyun.auth.credentials.utils.AuthConstant;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
@TableName("core_aliyun_sms")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/CoreAliyunSms.class */
public class CoreAliyunSms {
    @TableId("id")
    private Long id;
    @TableField("sms_person_id")
    private Long SmsPersonId;
    @TableField(AuthConstant.INI_ACCESS_KEY_ID)
    private String accessKeyId;
    @TableField(AuthConstant.INI_ACCESS_KEY_IDSECRET)
    private String accessKeySecret;
    @TableField("sign_name")
    private String signName;
    @TableField("template_code")
    private String templateCode;
    @TableField("endpoint")
    private String endpoint;
    @TableField("number_sms")
    private Long numberSms;
    @TableField("salt")
    private String salt;
    public void setId(Long id) {
        this.id = id;
    }
    public void setSmsPersonId(Long SmsPersonId) {
        this.SmsPersonId = SmsPersonId;
    }
    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }
    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }
    public void setSignName(String signName) {
        this.signName = signName;
    }
    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }
    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }
    public void setNumberSms(Long numberSms) {
        this.numberSms = numberSms;
    }
    public void setSalt(String salt) {
        this.salt = salt;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof CoreAliyunSms)) {
            return false;
        }
        CoreAliyunSms other = (CoreAliyunSms) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$id = getId();
        Object other$id = other.getId();
        if (this$id == null) {
            if (other$id != null) {
                return false;
            }
        } else if (!this$id.equals(other$id)) {
            return false;
        }
        Object this$SmsPersonId = getSmsPersonId();
        Object other$SmsPersonId = other.getSmsPersonId();
        if (this$SmsPersonId == null) {
            if (other$SmsPersonId != null) {
                return false;
            }
        } else if (!this$SmsPersonId.equals(other$SmsPersonId)) {
            return false;
        }
        Object this$numberSms = getNumberSms();
        Object other$numberSms = other.getNumberSms();
        if (this$numberSms == null) {
            if (other$numberSms != null) {
                return false;
            }
        } else if (!this$numberSms.equals(other$numberSms)) {
            return false;
        }
        Object this$accessKeyId = getAccessKeyId();
        Object other$accessKeyId = other.getAccessKeyId();
        if (this$accessKeyId == null) {
            if (other$accessKeyId != null) {
                return false;
            }
        } else if (!this$accessKeyId.equals(other$accessKeyId)) {
            return false;
        }
        Object this$accessKeySecret = getAccessKeySecret();
        Object other$accessKeySecret = other.getAccessKeySecret();
        if (this$accessKeySecret == null) {
            if (other$accessKeySecret != null) {
                return false;
            }
        } else if (!this$accessKeySecret.equals(other$accessKeySecret)) {
            return false;
        }
        Object this$signName = getSignName();
        Object other$signName = other.getSignName();
        if (this$signName == null) {
            if (other$signName != null) {
                return false;
            }
        } else if (!this$signName.equals(other$signName)) {
            return false;
        }
        Object this$templateCode = getTemplateCode();
        Object other$templateCode = other.getTemplateCode();
        if (this$templateCode == null) {
            if (other$templateCode != null) {
                return false;
            }
        } else if (!this$templateCode.equals(other$templateCode)) {
            return false;
        }
        Object this$endpoint = getEndpoint();
        Object other$endpoint = other.getEndpoint();
        if (this$endpoint == null) {
            if (other$endpoint != null) {
                return false;
            }
        } else if (!this$endpoint.equals(other$endpoint)) {
            return false;
        }
        Object this$salt = getSalt();
        Object other$salt = other.getSalt();
        return this$salt == null ? other$salt == null : this$salt.equals(other$salt);
    }
    protected boolean canEqual(Object other) {
        return other instanceof CoreAliyunSms;
    }
    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $SmsPersonId = getSmsPersonId();
        int result2 = (result * 59) + ($SmsPersonId == null ? 43 : $SmsPersonId.hashCode());
        Object $numberSms = getNumberSms();
        int result3 = (result2 * 59) + ($numberSms == null ? 43 : $numberSms.hashCode());
        Object $accessKeyId = getAccessKeyId();
        int result4 = (result3 * 59) + ($accessKeyId == null ? 43 : $accessKeyId.hashCode());
        Object $accessKeySecret = getAccessKeySecret();
        int result5 = (result4 * 59) + ($accessKeySecret == null ? 43 : $accessKeySecret.hashCode());
        Object $signName = getSignName();
        int result6 = (result5 * 59) + ($signName == null ? 43 : $signName.hashCode());
        Object $templateCode = getTemplateCode();
        int result7 = (result6 * 59) + ($templateCode == null ? 43 : $templateCode.hashCode());
        Object $endpoint = getEndpoint();
        int result8 = (result7 * 59) + ($endpoint == null ? 43 : $endpoint.hashCode());
        Object $salt = getSalt();
        return (result8 * 59) + ($salt == null ? 43 : $salt.hashCode());
    }
    public String toString() {
        return "CoreAliyunSms(id=" + getId() + ", SmsPersonId=" + getSmsPersonId() + ", accessKeyId=" + getAccessKeyId() + ", accessKeySecret=" + getAccessKeySecret() + ", signName=" + getSignName() + ", templateCode=" + getTemplateCode() + ", endpoint=" + getEndpoint() + ", numberSms=" + getNumberSms() + ", salt=" + getSalt() + StringPool.RIGHT_BRACKET;
    }
    public Long getId() {
        return this.id;
    }
    public Long getSmsPersonId() {
        return this.SmsPersonId;
    }
    public String getAccessKeyId() {
        return this.accessKeyId;
    }
    public String getAccessKeySecret() {
        return this.accessKeySecret;
    }
    public String getSignName() {
        return this.signName;
    }
    public String getTemplateCode() {
        return this.templateCode;
    }
    public String getEndpoint() {
        return this.endpoint;
    }
    public Long getNumberSms() {
        return this.numberSms;
    }
    public String getSalt() {
        return this.salt;
    }
}
