package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import java.util.List;
@TableName("core_alarm_notice_record")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/AlarmNoticeRecord.class */
public class AlarmNoticeRecord {
    @TableId("record_id")
    private Long recordId;
    private Long noticeId;
    private Long alarmId;
    private String alarmType;
    private String alarmTypeName;
    private String areaName;
    private String layerId;
    private String realName;
    private String deptName;
    private String phoneNumbers;
    private String phoneRealNames;
    private LocalDateTime alarmTime;
    private Integer noticeLevel;
    private Long noticeInterval;
    private LocalDateTime noticeTime;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String noticeStatic;
    private String remark;
    private String noticeType;
    private String source;
    private String vehicleName;
    private String licensePlateNumber;
    @TableField(exist = false)
    private List<AlarmNoticeRecordPerson> alarmNoticeSosRecordPersonList;
    public Long getRecordId() {
        return this.recordId;
    }
    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }
    public Long getAlarmId() {
        return this.alarmId;
    }
    public void setAlarmId(Long alarmId) {
        this.alarmId = alarmId;
    }
    public LocalDateTime getAlarmTime() {
        return this.alarmTime;
    }
    public void setAlarmTime(LocalDateTime alarmTime) {
        this.alarmTime = alarmTime;
    }
    public Integer getNoticeLevel() {
        return this.noticeLevel;
    }
    public void setNoticeLevel(Integer noticeLevel) {
        this.noticeLevel = noticeLevel;
    }
    public Long getNoticeInterval() {
        return this.noticeInterval;
    }
    public void setNoticeInterval(Long noticeInterval) {
        this.noticeInterval = noticeInterval;
    }
    public LocalDateTime getNoticeTime() {
        return this.noticeTime;
    }
    public void setNoticeTime(LocalDateTime noticeTime) {
        this.noticeTime = noticeTime;
    }
    public String getNoticeStatic() {
        return this.noticeStatic;
    }
    public void setNoticeStatic(String noticeStatic) {
        this.noticeStatic = noticeStatic;
    }
    public List<AlarmNoticeRecordPerson> getAlarmNoticeSosRecordPersonList() {
        return this.alarmNoticeSosRecordPersonList;
    }
    public void setAlarmNoticeSosRecordPersonList(List<AlarmNoticeRecordPerson> alarmNoticeSosRecordPersonList) {
        this.alarmNoticeSosRecordPersonList = alarmNoticeSosRecordPersonList;
    }
    public Long getNoticeId() {
        return this.noticeId;
    }
    public void setNoticeId(Long noticeId) {
        this.noticeId = noticeId;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getUpdateTime() {
        return this.updateTime;
    }
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    public String getRemark() {
        return this.remark;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }
    public String getAlarmType() {
        return this.alarmType;
    }
    public void setAlarmType(String alarmType) {
        this.alarmType = alarmType;
    }
    public String getAlarmTypeName() {
        return this.alarmTypeName;
    }
    public void setAlarmTypeName(String alarmTypeName) {
        this.alarmTypeName = alarmTypeName;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
    public String getPhoneNumbers() {
        return this.phoneNumbers;
    }
    public void setPhoneNumbers(String phoneNumbers) {
        this.phoneNumbers = phoneNumbers;
    }
    public String getAreaName() {
        return this.areaName;
    }
    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }
    public String getRealName() {
        return this.realName;
    }
    public void setRealName(String realName) {
        this.realName = realName;
    }
    public String getDeptName() {
        return this.deptName;
    }
    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    public String getPhoneRealNames() {
        return this.phoneRealNames;
    }
    public void setPhoneRealNames(String phoneRealNames) {
        this.phoneRealNames = phoneRealNames;
    }
    public String getNoticeType() {
        return this.noticeType;
    }
    public void setNoticeType(String noticeType) {
        this.noticeType = noticeType;
    }
    public String getSource() {
        return this.source;
    }
    public void setSource(String source) {
        this.source = source;
    }
    public String getVehicleName() {
        return this.vehicleName;
    }
    public void setVehicleName(String vehicleName) {
        this.vehicleName = vehicleName;
    }
    public String getLicensePlateNumber() {
        return this.licensePlateNumber;
    }
    public void setLicensePlateNumber(String licensePlateNumber) {
        this.licensePlateNumber = licensePlateNumber;
    }
}
