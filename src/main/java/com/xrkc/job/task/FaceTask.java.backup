package com.xrkc.job.task;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xrkc.core.constant.CacheConstants;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.domain.cardDispenser.CardDispenserPerson;
import com.xrkc.core.domain.cardDispenser.DeviceCardSenderVehicle;
import com.xrkc.core.domain.vehicle.VehicleInfo;
import com.xrkc.core.utils.PersonUtils;
import com.xrkc.job.domain.DeviceCardSender;
import com.xrkc.job.domain.DeviceCardSenderLog;
import com.xrkc.job.domain.DeviceCardSenderVehicleLog;
import com.xrkc.job.domain.FaceDispenserPersonVO;
import com.xrkc.job.mapper.PersonMapper;
import com.xrkc.job.module.vehicle.service.IVehicleInfoService;
import com.xrkc.job.service.HttpFaceService;
import com.xrkc.job.service.IDeviceCardSenderLogService;
import com.xrkc.job.service.IDeviceCardSenderPersonService;
import com.xrkc.job.service.IDeviceCardSenderService;
import com.xrkc.job.service.IDeviceCardSenderVehicleLogService;
import com.xrkc.job.service.IDeviceCardSenderVehicleService;
import com.xrkc.job.util.ImageCompressUtils;
import com.xrkc.redis.service.RedisService;
import com.xrkc.redis.utils.RedisCacheUtils;
import java.io.IOException;
import java.lang.invoke.SerializedLambda;
import java.time.LocalDateTime;
import java.time.chrono.ChronoLocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
@Component("faceTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/FaceTask.class */
public class FaceTask {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) FaceTask.class);
    @Autowired
    private PersonMapper personMapper;
    @Autowired
    private HttpFaceService httpFaceService;
    @Autowired
    private IDeviceCardSenderService deviceCardSenderService;
    @Autowired
    private IDeviceCardSenderLogService deviceCardSenderLogService;
    @Autowired
    private IDeviceCardSenderPersonService deviceCardSenderPersonService;
    @Autowired
    private IDeviceCardSenderVehicleService deviceCardSenderVehicleService;
    @Autowired
    private IDeviceCardSenderVehicleLogService deviceCardSenderVehicleLogService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private IVehicleInfoService vehicleInfoService;
    private static final String jsonKey_identify_mode = "identify_mode";
    private static final String jsonKey_face_comparison_url = "face_comparison_url";
    private static final String jsonKey_face_callback_url = "face_callback_url";
    private static final String jsonKey_face_heart_url = "face_heart_url";
    private static final int batchSyncSize = 5;
    private static /* synthetic */ Object $deserializeLambda$(SerializedLambda lambda) {
        switch (lambda.getImplMethodName()) {
            case "getDeviceBtnRent":
                if (lambda.getImplMethodKind() == 5 && lambda.getFunctionalInterfaceClass().equals("com/baomidou/mybatisplus/core/toolkit/support/SFunction") && lambda.getFunctionalInterfaceMethodName().equals("apply") && lambda.getFunctionalInterfaceMethodSignature().equals("(Ljava/lang/Object;)Ljava/lang/Object;") && lambda.getImplClass().equals("com/xrkc/job/domain/DeviceCardSender") && lambda.getImplMethodSignature().equals("()Ljava/lang/Integer;")) {
                    return (v0) -> {
                        return v0.getDeviceBtnRent();
                    };
                }
                break;
        }
        throw new IllegalArgumentException("Invalid lambda deserialization");
    }
    public void syncFaceDispenser() {
        List<DeviceCardSender> deviceList = this.deviceCardSenderService.selectValidFaceDevice();
        if (CollectionUtils.isEmpty(deviceList)) {
            return;
        }
        Set<String> keyList = this.redisService.keys("personPhoto*");
        if (!CollectionUtils.isEmpty(keyList)) {
            log.info("删除弃用的base64缓存，{}", Integer.valueOf(keyList.size()));
            RedisService redisService = this.redisService;
            redisService.getClass();
            keyList.forEach(redisService::deleteObject);
        }
        Set<String> errImageKeyList = this.redisService.keys("errImage_*");
        if (!CollectionUtils.isEmpty(errImageKeyList)) {
            log.info("删除弃用的异常图片缓存，{}", Integer.valueOf(errImageKeyList.size()));
            RedisService redisService2 = this.redisService;
            redisService2.getClass();
            errImageKeyList.forEach(redisService2::deleteObject);
        }
        deviceList.forEach(device -> {
            this.httpFaceService.setDeviceTime(device.getFaceUrl(), device.getPassword());
        });
        List<FaceDispenserPersonVO> waitingPushPersonList = this.personMapper.selectToFacePersonList();
        List<VehicleInfo> waitingPushVehicleList = this.vehicleInfoService.selectToFaceVehicleList();
        List<String> allEmployeeNumberList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(waitingPushPersonList)) {
            List<String> personEmployeeNumberList = (List) waitingPushPersonList.stream().map(person -> {
                return PersonUtils.getPersonEmployeeNumber(person.getPersonId());
            }).collect(Collectors.toList());
            allEmployeeNumberList.addAll(personEmployeeNumberList);
        }
        if (!CollectionUtils.isEmpty(waitingPushVehicleList)) {
            List<String> vehicleEmployeeNumberList = (List) waitingPushVehicleList.stream().map(vehicle -> {
                return PersonUtils.getVehicleEmployeeNumber(vehicle.getId());
            }).collect(Collectors.toList());
            allEmployeeNumberList.addAll(vehicleEmployeeNumberList);
        }
        if (!CollectionUtils.isEmpty(allEmployeeNumberList)) {
            deviceList.parallelStream().forEach(device2 -> {
                List<String> allDeviceIdWhiteList = this.httpFaceService.getAllDeviceIdWhiteList(device2);
                if (!CollectionUtils.isEmpty(allDeviceIdWhiteList)) {
                    allEmployeeNumberList.stream().filter(employeeNumber -> {
                        return !allDeviceIdWhiteList.contains(employeeNumber);
                    }).forEach(employeeNumber2 -> {
                        String key = PersonUtils.getWhitePersonKey(device2.getFaceSn(), employeeNumber2);
                        if (this.redisService.hasKey(key)) {
                            this.redisService.deleteObject(key);
                        }
                    });
                }
            });
        }
    }
    public void delFaceInvalidPerson() {
        List<DeviceCardSender> deviceList = this.deviceCardSenderService.selectValidFaceDevice();
        if (CollectionUtils.isEmpty(deviceList)) {
            return;
        }
        List<FaceDispenserPersonVO> waitingPushPersonList = this.personMapper.selectToFacePersonList();
        List<VehicleInfo> waitingPushVehicleList = this.vehicleInfoService.selectToFaceVehicleList();
        if (CollectionUtils.isEmpty(waitingPushPersonList) && CollectionUtils.isEmpty(waitingPushVehicleList)) {
            deviceList.forEach(deviceCardSender -> {
                this.httpFaceService.deleteDeviceAllWhiteList(deviceCardSender);
            });
            return;
        }
        Map<String, String> allEmployeePhotoMap = new HashMap<>();
        List<String> allEmployeeNumberList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(waitingPushPersonList)) {
            List<String> personEmployeeNumberList = (List) waitingPushPersonList.stream().map(person -> {
                return PersonUtils.getPersonEmployeeNumber(person.getPersonId());
            }).collect(Collectors.toList());
            allEmployeeNumberList.addAll(personEmployeeNumberList);
            waitingPushPersonList.forEach(person2 -> {
            });
        }
        if (!CollectionUtils.isEmpty(waitingPushVehicleList)) {
            List<String> vehicleEmployeeNumberList = (List) waitingPushVehicleList.stream().map(vehicle -> {
                return PersonUtils.getVehicleEmployeeNumber(vehicle.getId());
            }).collect(Collectors.toList());
            allEmployeeNumberList.addAll(vehicleEmployeeNumberList);
            waitingPushVehicleList.forEach(vehicle2 -> {
            });
        }
        deviceList.forEach(device -> {
            List<String> allDeviceIdWhiteList = this.httpFaceService.getAllDeviceIdWhiteList(device);
            if (!CollectionUtils.isEmpty(allDeviceIdWhiteList)) {
                List<String> removeList = (List) allDeviceIdWhiteList.stream().filter(f -> {
                    return !allEmployeeNumberList.contains(f);
                }).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(removeList)) {
                    log.info("人脸机删除无效人员,url:{},sn:{},数量：{}，其它参考> db：{}，设备：{}", device.getFaceUrl(), device.getFaceSn(), Integer.valueOf(removeList.size()), Integer.valueOf(allEmployeeNumberList.size()), Integer.valueOf(allDeviceIdWhiteList.size()));
                    this.httpFaceService.deleteDeviceWhiteList(device, removeList);
                    Set<String> keys = new HashSet<>();
                    removeList.forEach(employeeNumber -> {
                        keys.add(PersonUtils.getWhitePersonKey(device.getFaceSn(), employeeNumber));
                    });
                    this.redisService.deleteObject(keys);
                }
                Stream<String> stream = allDeviceIdWhiteList.stream();
                allDeviceIdWhiteList.getClass();
                List<String> successPersonIdList = (List) stream.filter((v1) -> {
                    return r1.contains(v1);
                }).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(successPersonIdList)) {
                    successPersonIdList.parallelStream().forEach(employeeNumber2 -> {
                        String key = PersonUtils.getWhitePersonKey(device.getFaceSn(), employeeNumber2);
                        if (!this.redisService.hasKey(key)) {
                            String personPhoto = (String) allEmployeePhotoMap.getOrDefault(employeeNumber2, "temp");
                            log.info("[人脸机]白名单分担下发压力，反向写入1天缓存key:{}", key);
                            this.redisService.set(key, personPhoto, PersonUtils.getRedisTimeSecond(1));
                        }
                    });
                }
            }
        });
    }
    public void deviceManage() {
        List<DeviceCardSender> list = this.deviceCardSenderService.selectValidFaceDevice();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        deviceLogin(list);
        Map<String, String> configMap = this.redisService.getCacheMap(CacheConstants.SYSTEM_CONFIG_KEY);
        String identify_mode = configMap.getOrDefault(jsonKey_identify_mode, "1");
        String face_comparison_url = configMap.getOrDefault(jsonKey_face_comparison_url, "http://127.0.0.1:8090/gateway-service/monitor-service/faceDispenser");
        String face_callback_url = configMap.getOrDefault(jsonKey_face_callback_url, "http://127.0.0.1:8090/gateway-service/monitor-service/cardSender/recordCallback");
        String face_heart_url = configMap.getOrDefault(jsonKey_face_heart_url, "http://127.0.0.1:8090/gateway-service/monitor-service/cardSender/heartCallback");
        list.forEach(device -> {
            String key = PersonUtils.getFaceConfigKey(device.getFaceSn());
            Object obj = this.redisService.get(key);
            if (Objects.nonNull(obj)) {
                JSONObject configJson = JSONObject.parseObject(obj.toString());
                String redis_identify_mode = configJson.getString(jsonKey_identify_mode);
                String redis_face_comparison_url = configJson.getString(jsonKey_face_comparison_url);
                String redis_face_callback_url = configJson.getString(jsonKey_face_callback_url);
                String redis_face_heart_url = configJson.getString(jsonKey_face_heart_url);
                if (!identify_mode.equals(redis_identify_mode) || !face_comparison_url.equals(redis_face_comparison_url) || !face_callback_url.equals(redis_face_callback_url) || !face_heart_url.equals(redis_face_heart_url)) {
                    pushUpdateConfig(key, device.getFaceUrl(), device.getPassword(), face_heart_url, face_callback_url, identify_mode, face_comparison_url);
                }
            } else {
                pushUpdateConfig(key, device.getFaceUrl(), device.getPassword(), face_heart_url, face_callback_url, identify_mode, face_comparison_url);
            }
            try {
                JSONObject jsonObject = this.httpFaceService.getDeviceParameter(device);
                if (jsonObject.getIntValue("code") == 200) {
                    JSONObject data = jsonObject.getJSONObject("data");
                    String faceMacAddr = data.getString("faceMacAddr");
                    device.setFaceMacAddr(faceMacAddr);
                    device.setFaceSn(data.getString("faceSn"));
                    this.deviceCardSenderService.updateById(device);
                }
            } catch (Exception e) {
                log.info("获取配置信息失败：{}，url:{}", e.getMessage(), device.getFaceUrl());
            }
        });
    }
    private void pushUpdateConfig(String key, String faceUrl, String password, String face_heart_url, String face_callback_url, String identify_mode, String face_comparison_url) {
        try {
            JSONObject params = new JSONObject();
            params.put("password", password);
            JSONObject paramsData = new JSONObject();
            paramsData.put("heartBeatEnable", 0);
            paramsData.put("heartBeatIp", face_heart_url);
            paramsData.put("platformEnable", 0);
            paramsData.put("platformIp", face_callback_url);
            paramsData.put("Comparison", identify_mode);
            paramsData.put("comparisonAddress", face_comparison_url);
            paramsData.put("qrcodeMode", 0);
            params.put("data", paramsData);
            JSONObject json = this.httpFaceService.setDeviceParameter(faceUrl, params);
            int resultCode = json.getIntValue("result");
            if (resultCode == 0) {
                log.info("【人脸机】下发配置成功:{}，传参：{}", faceUrl, params.toJSONString(new JSONWriter.Feature[0]));
                JSONObject configJson = new JSONObject();
                configJson.put(jsonKey_identify_mode, identify_mode);
                configJson.put(jsonKey_face_comparison_url, face_comparison_url);
                configJson.put(jsonKey_face_callback_url, face_callback_url);
                configJson.put(jsonKey_face_heart_url, face_heart_url);
                this.redisService.set(key, configJson.toJSONString(new JSONWriter.Feature[0]), PersonUtils.getRedisTimeSecond(3));
            } else {
                log.info("【人脸机】下发配置失败:{}，传参：{}，结果：{}", faceUrl, params.toJSONString(new JSONWriter.Feature[0]), json.toJSONString(new JSONWriter.Feature[0]));
            }
        } catch (Exception err) {
            log.info("设置配置信息失败：{}，url:{}", err.getMessage(), faceUrl);
        }
    }
    private void deviceLogin(List<DeviceCardSender> list) {
        LocalDateTime time = LocalDateTime.now().minusMinutes(3L);
        list.stream().filter(f -> {
            return Objects.isNull(f.getFaceHeartTime()) || f.getFaceHeartTime().compareTo((ChronoLocalDateTime<?>) time) < 0;
        }).forEach(device -> {
            this.httpFaceService.deviceLogin(device);
        });
    }
    public void syncWhiteList() {
        List<DeviceCardSender> deviceList = this.deviceCardSenderService.selectValidFaceDevice();
        if (CollectionUtils.isEmpty(deviceList)) {
            return;
        }
        List<FaceDispenserPersonVO> waitingPushList = new ArrayList<>();
        List<FaceDispenserPersonVO> waitingPushPersonList = this.personMapper.selectToFacePersonList();
        waitingPushPersonList.forEach(t -> {
            t.setEmployeeNumber(PersonUtils.WHITELIST_PREFIX_PERSON + t.getPersonId());
            t.setNation("person");
            waitingPushList.add(t);
        });
        List<VehicleInfo> waitingPushVehicleList = this.vehicleInfoService.selectToFaceVehicleList();
        waitingPushVehicleList.forEach(v -> {
            FaceDispenserPersonVO t2 = new FaceDispenserPersonVO();
            t2.setPersonId(v.getId());
            t2.setRealName(StringUtils.isNotBlank(v.getDriverName()) ? v.getDriverName() : "未知");
            t2.setPersonPhoto(v.getDriverPhoto());
            t2.setEmployeeNumber(PersonUtils.WHITELIST_PREFIX_VEHICLE + t2.getPersonId());
            t2.setNation("vehicle");
            waitingPushList.add(t2);
        });
        if (CollectionUtils.isEmpty(waitingPushList)) {
            return;
        }
        String staticPath = RedisCacheUtils.getConfigRedisCache().get(ConfigValue.CONFIG_KEY_STATIC_PATH);
        Object last_redis_static_path = this.redisService.get(PersonUtils.LAST_REDIS_STATIC_PATH);
        if (Objects.isNull(last_redis_static_path) || !last_redis_static_path.toString().equals(staticPath)) {
            Set<String> keyList = this.redisService.keys(PersonUtils.getErrImageKeyPrefixMatching());
            log.info("静态资源配置地址变更，清理原异常图片的缓存:{}", keyList);
            RedisService redisService = this.redisService;
            redisService.getClass();
            keyList.forEach(redisService::deleteObject);
        }
        this.redisService.set(PersonUtils.LAST_REDIS_STATIC_PATH, staticPath, PersonUtils.getRedisTimeSecond(10));
        waitingPushList.removeIf(person -> {
            return person.getPersonPhoto().equals(this.redisService.get(PersonUtils.getErrImageKey(person.getEmployeeNumber())));
        });
        if (CollectionUtils.isEmpty(waitingPushList)) {
            return;
        }
        log.info("移除不符合图片后，当前人员数：{}", Integer.valueOf(waitingPushList.size()));
        AtomicInteger currentBatch = new AtomicInteger();
        int totalBatch = (int) Math.ceil(waitingPushList.size() / 5.0d);
        while (!waitingPushList.isEmpty()) {
            currentBatch.getAndIncrement();
            int totalSize = waitingPushList.size();
            List<FaceDispenserPersonVO> batchList = waitingPushList.subList(0, Math.min(5, totalSize));
            processBatchPush(deviceList, batchList, currentBatch.get(), totalBatch);
            waitingPushList.subList(0, Math.min(5, totalSize)).clear();
        }
    }
    private void processBatchPush(List<DeviceCardSender> deviceList, List<FaceDispenserPersonVO> batchPushList, int batch, int totalBatch) {
        deviceList.parallelStream().forEach(device -> {
            List<FaceDispenserPersonVO> currentPersonList = (List) batchPushList.stream().filter(f -> {
                return filterPushPerson(device.getFaceSn(), f);
            }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(currentPersonList)) {
                int currentTotal = currentPersonList.size();
                AtomicInteger currentCount = new AtomicInteger();
                currentPersonList.parallelStream().forEach(person -> {
                    String key = PersonUtils.getWhitePersonKey(device.getFaceSn(), person.getEmployeeNumber());
                    JSONObject data = new JSONObject();
                    data.put("usertype", "white");
                    data.put("passAlgo", false);
                    data.put("TimeGroupId", 0);
                    data.put("employee_number", person.getEmployeeNumber());
                    data.put("name", person.getRealName());
                    data.put("register_base64", person.getBase64());
                    data.put("nation", person.getNation());
                    if (StringUtils.isBlank(person.getBase64())) {
                        log.error("base64为null:{}", key);
                    }
                    currentCount.getAndIncrement();
                    String info = String.format("Key:%s，进度：%d/%d，批次：%d/%d", key, Integer.valueOf(currentCount.intValue()), Integer.valueOf(currentTotal), Integer.valueOf(batch), Integer.valueOf(totalBatch));
                    log.info("[人脸机]准备下发白名单,{},url:{}", info, device.getFaceUrl());
                    this.httpFaceService.syncPersonToFace(device, currentTotal, currentCount.intValue(), data, person.getEmployeeNumber(), person.getPersonPhoto(), person.getRealName(), key, info);
                });
            }
        });
    }
    private boolean filterPushPerson(String faceSn, FaceDispenserPersonVO person) throws IOException {
        String key = PersonUtils.getWhitePersonKey(faceSn, person.getEmployeeNumber());
        String facePhoto = StrUtil.toStringOrNull(this.redisService.get(key));
        if (StrUtil.equalsIgnoreCase(facePhoto, person.getPersonPhoto())) {
            return false;
        }
        String base64 = ImageCompressUtils.faceDispenserImgCompressToBase64(person.getPersonPhoto());
        if (StringUtils.isNotBlank(base64)) {
            person.setBase64(base64);
            return true;
        }
        this.httpFaceService.setErrImageRedis(person.getEmployeeNumber(), person.getPersonPhoto());
        return false;
    }
    /* JADX WARN: Multi-variable type inference failed */
    @Transactional(rollbackFor = {Exception.class})
    public void devicePatrol() throws NumberFormatException {
        List<DeviceCardSender> deviceList = this.deviceCardSenderService.list((Wrapper) new LambdaQueryWrapper<FaceDispenserPersonVO>().eq((v0) -> {
            return v0.getDeviceBtnRent();
        }, 1));
        Map<String, String> configMap = this.redisService.getCacheMap(CacheConstants.SYSTEM_CONFIG_KEY);
        int card_rent_timeout_second = Integer.parseInt(configMap.getOrDefault("card_rent_timeout_second", "60"));
        if (card_rent_timeout_second < 30) {
            card_rent_timeout_second = 60;
        }
        int card_btn_rent_timeout_second = Integer.parseInt(configMap.getOrDefault("card_btn_rent_timeout_second", "600"));
        if (card_btn_rent_timeout_second < 30) {
            card_btn_rent_timeout_second = 60;
        }
        LocalDateTime now = LocalDateTime.now();
        List<CardDispenserPerson> list = new ArrayList<>();
        List<DeviceCardSenderVehicle> vehicleList = new ArrayList<>();
        if (CollectionUtils.isEmpty(deviceList)) {
            list.addAll(this.deviceCardSenderPersonService.selectTimeoutList(null, null, card_rent_timeout_second));
            vehicleList.addAll(this.deviceCardSenderVehicleService.selectTimeoutList(null, null, card_rent_timeout_second));
        } else {
            list.addAll(this.deviceCardSenderPersonService.selectTimeoutList((List) deviceList.stream().map((v0) -> {
                return v0.getDeviceSn();
            }).collect(Collectors.toList()), null, card_rent_timeout_second));
            list.addAll(this.deviceCardSenderPersonService.selectTimeoutList(null, (List) deviceList.stream().map((v0) -> {
                return v0.getDeviceSn();
            }).collect(Collectors.toList()), card_btn_rent_timeout_second));
            vehicleList.addAll(this.deviceCardSenderVehicleService.selectTimeoutList((List) deviceList.stream().map((v0) -> {
                return v0.getDeviceSn();
            }).collect(Collectors.toList()), null, card_rent_timeout_second));
            vehicleList.addAll(this.deviceCardSenderVehicleService.selectTimeoutList(null, (List) deviceList.stream().map((v0) -> {
                return v0.getDeviceSn();
            }).collect(Collectors.toList()), card_btn_rent_timeout_second));
        }
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(t -> {
                t.setCardSenderType(1);
                t.setResult("失败");
                t.setClosedTime(now);
                t.setUpdateTime(now);
                t.setRemark("处理超时");
            });
            log.info("[发卡机]人员发卡超时处理,数量：{}，列表：{}", Integer.valueOf(list.size()), JSONObject.toJSONString(list, new JSONWriter.Feature[0]));
            this.deviceCardSenderPersonService.batchUpdate(list);
            List<DeviceCardSenderLog> logList = BeanUtil.copyToList(list, DeviceCardSenderLog.class);
            this.deviceCardSenderLogService.batchInsert(logList);
        }
        if (!CollectionUtils.isEmpty(vehicleList)) {
            vehicleList.forEach(t2 -> {
                t2.setCardSenderType(1);
                t2.setResult("失败");
                t2.setClosedTime(now);
                t2.setUpdateTime(now);
                t2.setRemark("处理超时");
            });
            log.info("[发卡机]车辆发卡超时处理,数量：{}，列表：{}", Integer.valueOf(vehicleList.size()), JSONObject.toJSONString(vehicleList, new JSONWriter.Feature[0]));
            this.deviceCardSenderVehicleService.batchUpdate(vehicleList);
            List<DeviceCardSenderVehicleLog> logList2 = BeanUtil.copyToList(vehicleList, DeviceCardSenderVehicleLog.class);
            this.deviceCardSenderVehicleLogService.batchInsert(logList2);
        }
    }
}
