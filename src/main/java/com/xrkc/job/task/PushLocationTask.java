package com.xrkc.job.task;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.domain.basic.JsonResult;
import com.xrkc.core.domain.person.Person;
import com.xrkc.core.domain.system.SystemDept;
import com.xrkc.datascope.aspect.DataScopeAspect;
import com.xrkc.job.controller.WebSocketServer;
import com.xrkc.job.domain.PositionCurrent;
import com.xrkc.job.domain.PositionCurrentPushWsVO;
import com.xrkc.job.domain.SystemRoleFacilityVO;
import com.xrkc.job.domain.SystemUser;
import com.xrkc.job.domain.SystemUserDataScopeVO;
import com.xrkc.job.service.IPersonService;
import com.xrkc.job.service.IPositionCurrentService;
import com.xrkc.job.service.ISystemDeptService;
import com.xrkc.job.service.ISystemRoleFacilityService;
import com.xrkc.job.service.ISystemUserRoleService;
import com.xrkc.job.service.ISystemUserService;
import com.xrkc.job.util.CommonUtil;
import com.xrkc.redis.utils.RedisCacheUtils;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.TreeSet;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import net.jodah.expiringmap.ExpiringMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
@Component("pushLocationTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/PushLocationTask.class */
public class PushLocationTask {
    @Autowired
    private WebSocketServer webSocketServer;
    @Autowired
    private IPositionCurrentService positionCurrentService;
    @Autowired
    private ISystemUserRoleService systemUserRoleService;
    @Autowired
    private ISystemUserService systemUserService;
    @Autowired
    private ISystemDeptService systemDeptService;
    @Autowired
    private ISystemRoleFacilityService systemRoleFacilityService;
    @Autowired
    private IPersonService personService;
    private Map<String, LocalDateTime> pushWsPersonCurrentLocationCache = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    private static final Logger log = LoggerFactory.getLogger((Class<?>) PushLocationTask.class);
    private static final DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public void pushCurrentPersonLocation() {
        pushCurrentPersonLocationFull();
    }
    public void pushPersonCount() {
        if (CollectionUtils.isEmpty(WebSocketServer.systemSessionMap)) {
            return;
        }
        int count = this.personService.countOnJob();
        JSONObject pushJson = new JSONObject();
        pushJson.put("msgType", "personCount");
        pushJson.put("data", Integer.valueOf(count));
        this.webSocketServer.sendMessageForXR(null, JSONObject.toJSONString(pushJson, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible));
    }
    private static String getPushWsPersonCurrentLocationKey(Long userId, Long personId) {
        StringJoiner sj = new StringJoiner("_");
        sj.add(userId.toString()).add(personId.toString());
        return sj.toString();
    }
    private boolean filterData(Long userId, Long personId, LocalDateTime acceptTime) {
        String key = getPushWsPersonCurrentLocationKey(userId, personId);
        if (!this.pushWsPersonCurrentLocationCache.containsKey(key)) {
            return true;
        }
        LocalDateTime lastAcceptTime = this.pushWsPersonCurrentLocationCache.get(key);
        return acceptTime.isAfter(lastAcceptTime);
    }
    public void pushCurrentPersonLocationFull() {
        if (CollectionUtils.isEmpty(WebSocketServer.systemSessionMap)) {
            return;
        }
        Set<Long> wsUserIds = new HashSet<>();
        WebSocketServer.systemSessionMap.forEach((socketKey, session) -> {
            String userId = socketKey.split("_")[2];
            if (NumberUtil.isLong(userId)) {
                wsUserIds.add(Long.valueOf(userId));
            }
        });
        List<SystemUser> systemUsers = this.systemUserService.findByUserIds(wsUserIds);
        systemUsers.removeIf(r -> {
            return !wsUserIds.contains(r.getUserId());
        });
        Set<Long> pushUserIds = (Set) systemUsers.stream().map((v0) -> {
            return v0.getUserId();
        }).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(pushUserIds)) {
            return;
        }
        List<PositionCurrent> list = this.positionCurrentService.selectList();
        JSONObject pushJson = new JSONObject();
        pushJson.put("msgType", "currentPersonLocation");
        if (CollectionUtils.isEmpty(list)) {
            pushJson.put(JsonResult.TOTAL, Integer.valueOf(list.size()));
            pushJson.put("data", list);
            this.webSocketServer.sendMessageForXR(null, JSONObject.toJSONString(pushJson, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible));
        } else {
            List<SystemDept> allDeptList = this.systemDeptService.selectList();
            Map<Long, List<SystemUserDataScopeVO>> dataScopeMap = (Map) this.systemUserRoleService.findDataScopeByUserIds(pushUserIds).stream().collect(Collectors.groupingBy((v0) -> {
                return v0.getUserId();
            }));
            systemUsers.stream().forEach(user -> {
                pushJson.put(JsonResult.TOTAL, Integer.valueOf(list.size()));
                pushJson.put("data", list);
                Long userId = user.getUserId();
                if ("xrkc_admin".equals(user.getUserName())) {
                    this.webSocketServer.sendMessageForSystemUser(userId, JSONObject.toJSONString(pushJson, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible));
                    return;
                }
                List<SystemUserDataScopeVO> dataScopeVOList = (List) dataScopeMap.get(userId);
                if (CollectionUtils.isEmpty(dataScopeVOList)) {
                    pushJson.put(JsonResult.TOTAL, 0);
                    pushJson.put("data", new ArrayList());
                    this.webSocketServer.sendMessageForSystemUser(userId, JSONObject.toJSONString(pushJson, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible));
                    return;
                }
                Set<String> userDataScopeSet = (Set) dataScopeVOList.stream().map((v0) -> {
                    return v0.getDataScope();
                }).collect(Collectors.toSet());
                if (userDataScopeSet.contains("1")) {
                    this.webSocketServer.sendMessageForSystemUser(userId, JSONObject.toJSONString(pushJson, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible));
                    return;
                }
                Long userDeptId = user.getDeptId();
                Long userPersonId = user.getPersonId();
                Set<Long> filterDeptIds = new HashSet<>();
                Set<Long> customDeptIds = (Set) dataScopeVOList.stream().filter(f -> {
                    return "2".equals(f.getDataScope()) && Objects.nonNull(f.getDeptId());
                }).map((v0) -> {
                    return v0.getDeptId();
                }).collect(Collectors.toSet());
                filterDeptIds.addAll(customDeptIds);
                if (userDataScopeSet.contains(DataScopeAspect.DATA_SCOPE_CUSTOM_AND_CHILD)) {
                    Set<Long> cDeptIds = (Set) dataScopeVOList.stream().filter(f2 -> {
                        return DataScopeAspect.DATA_SCOPE_CUSTOM_AND_CHILD.equals(f2.getDataScope()) && Objects.nonNull(f2.getDeptId());
                    }).map((v0) -> {
                        return v0.getDeptId();
                    }).collect(Collectors.toSet());
                    filterDeptIds.addAll(cDeptIds);
                    cDeptIds.forEach(cId -> {
                        filterDeptIds.addAll(CommonUtil.getChildDeptIds(cId, allDeptList));
                    });
                }
                if (userDataScopeSet.contains("3") && Objects.nonNull(userDeptId)) {
                    filterDeptIds.add(userDeptId);
                }
                if (userDataScopeSet.contains("4")) {
                    filterDeptIds.addAll(CommonUtil.getChildDeptIds(userDeptId, allDeptList));
                }
                List<PositionCurrent> deptDataLocation = (List) list.stream().filter(f3 -> {
                    return Objects.nonNull(f3.getDeptId()) && filterDeptIds.contains(f3.getDeptId());
                }).collect(Collectors.toList());
                List<PositionCurrent> personDataLocation = new ArrayList<>();
                if (userDataScopeSet.contains("5") && Objects.nonNull(userPersonId)) {
                    personDataLocation = (List) list.stream().filter(f4 -> {
                        return Objects.nonNull(f4.getPersonId()) && userPersonId.compareTo(f4.getPersonId()) == 0;
                    }).collect(Collectors.toList());
                }
                List<PositionCurrent> facilityDataLocation = new ArrayList<>();
                List<SystemRoleFacilityVO> systemRoleFacilityVOList = this.systemRoleFacilityService.findByRoleIds((List) dataScopeVOList.stream().map((v0) -> {
                    return v0.getRoleId();
                }).collect(Collectors.toList()));
                if (!CollectionUtils.isEmpty(systemRoleFacilityVOList)) {
                    facilityDataLocation = (List) list.stream().filter(f5 -> {
                        return CommonUtil.containsFacilityRailScope(f5.getLayerId(), f5.getLongitude(), f5.getLatitude(), systemRoleFacilityVOList);
                    }).collect(Collectors.toList());
                }
                deptDataLocation.addAll(personDataLocation);
                deptDataLocation.addAll(facilityDataLocation);
                List<PositionCurrent> deptDataLocation2 = (List) deptDataLocation.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> {
                    return new TreeSet<>(Comparator.comparing((v0) -> v0.getPersonId()));
                }), (v1) -> {
                    return new ArrayList(v1);
                }));
                pushJson.put(JsonResult.TOTAL, Integer.valueOf(deptDataLocation2.size()));
                pushJson.put("data", deptDataLocation2);
                this.webSocketServer.sendMessageForSystemUser(userId, JSONObject.toJSONString(pushJson, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible));
            });
        }
    }
    public void pushCurrentPersonLocationIncrement() {
        if (CollectionUtils.isEmpty(WebSocketServer.systemSessionMap)) {
            return;
        }
        Set<Long> wsUserIds = new HashSet<>();
        WebSocketServer.systemSessionMap.forEach((socketKey, session) -> {
            String userId = socketKey.split("_")[2];
            if (NumberUtil.isLong(userId)) {
                wsUserIds.add(Long.valueOf(userId));
            }
        });
        List<SystemUser> systemUsers = this.systemUserService.findByUserIds(wsUserIds);
        systemUsers.removeIf(r -> {
            return !wsUserIds.contains(r.getUserId());
        });
        Set<Long> pushUserIds = (Set) systemUsers.stream().map((v0) -> {
            return v0.getUserId();
        }).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(pushUserIds)) {
            return;
        }
        List<PositionCurrent> urrentList = this.positionCurrentService.selectList();
        List<PositionCurrentPushWsVO> allCurrentList = BeanUtil.copyToList(urrentList, PositionCurrentPushWsVO.class);
        Map<String, String> configMap = RedisCacheUtils.getConfigRedisCache();
        JSONObject offlineRule = new JSONObject();
        String onlineMinute = configMap.getOrDefault(ConfigValue.CONFIG_KEY_PERSON_ONLINE_MINUTE, "");
        offlineRule.put("onlineMinute", Long.valueOf(NumberUtil.isLong(onlineMinute) ? Long.parseLong(onlineMinute) : 10L));
        offlineRule.put("offlineBeacons", configMap.getOrDefault(ConfigValue.CONFIG_KEY_OFFLINE_BEACONS, ""));
        JSONObject pushJson = new JSONObject();
        pushJson.put("msgType", "currentPersonLocation");
        pushJson.put("clear", Boolean.valueOf(CollectionUtils.isEmpty(allCurrentList)));
        pushJson.put("onlineCount", Integer.valueOf(allCurrentList.size()));
        pushJson.put("offlineRule", offlineRule);
        Set<Long> offlinePersonIdList = new HashSet<>();
        pushJson.put("offlinePersonIdList", offlinePersonIdList);
        if (CollectionUtils.isEmpty(allCurrentList)) {
            this.pushWsPersonCurrentLocationCache.clear();
            pushJson.put(JsonResult.TOTAL, 0);
            pushJson.put("data", allCurrentList);
            this.webSocketServer.sendMessageForXR(null, JSONObject.toJSONString(pushJson, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible));
            this.pushWsPersonCurrentLocationCache.clear();
            return;
        }
        List<SystemDept> allDeptList = this.systemDeptService.selectList();
        Map<Long, List<SystemUserDataScopeVO>> dataScopeMap = (Map) this.systemUserRoleService.findDataScopeByUserIds(pushUserIds).stream().collect(Collectors.groupingBy((v0) -> {
            return v0.getUserId();
        }));
        systemUsers.forEach(user -> {
            Long userId = user.getUserId();
            if ("xrkc_admin".equals(user.getUserName())) {
                List<PositionCurrentPushWsVO> pushList = (List) allCurrentList.stream().filter(f -> {
                    return filterData(userId, f.getPersonId(), f.getAcceptTime());
                }).collect(Collectors.toList());
                pushJson.put(JsonResult.TOTAL, Integer.valueOf(pushList.size()));
                pushJson.put("data", pushList);
                this.webSocketServer.sendMessageForSystemUser(userId, JSONObject.toJSONString(pushJson, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible));
                pushList.forEach(f2 -> {
                    this.pushWsPersonCurrentLocationCache.put(getPushWsPersonCurrentLocationKey(userId, f2.getPersonId()), f2.getAcceptTime());
                });
                return;
            }
            List<SystemUserDataScopeVO> dataScopeVOList = (List) dataScopeMap.get(userId);
            if (CollectionUtils.isEmpty(dataScopeVOList)) {
                pushJson.put("clear", true);
                pushJson.put("onlineCount", 0);
                pushJson.put(JsonResult.TOTAL, 0);
                pushJson.put("data", new ArrayList());
                this.webSocketServer.sendMessageForSystemUser(userId, JSONObject.toJSONString(pushJson, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible));
                allCurrentList.forEach(f3 -> {
                    this.pushWsPersonCurrentLocationCache.remove(getPushWsPersonCurrentLocationKey(userId, f3.getPersonId()));
                });
                return;
            }
            Set<String> userDataScopeSet = (Set) dataScopeVOList.stream().map((v0) -> {
                return v0.getDataScope();
            }).collect(Collectors.toSet());
            if (userDataScopeSet.contains("1")) {
                List<PositionCurrentPushWsVO> pushList2 = (List) allCurrentList.stream().filter(f4 -> {
                    return filterData(userId, f4.getPersonId(), f4.getAcceptTime());
                }).collect(Collectors.toList());
                pushJson.put(JsonResult.TOTAL, Integer.valueOf(pushList2.size()));
                pushJson.put("data", pushList2);
                this.webSocketServer.sendMessageForSystemUser(userId, JSONObject.toJSONString(pushJson, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible));
                pushList2.forEach(f5 -> {
                    this.pushWsPersonCurrentLocationCache.put(getPushWsPersonCurrentLocationKey(userId, f5.getPersonId()), f5.getAcceptTime());
                });
                return;
            }
            Long userDeptId = user.getDeptId();
            Long userPersonId = user.getPersonId();
            Set<Long> filterDeptIds = new HashSet<>();
            Set<Long> customDeptIds = (Set) dataScopeVOList.stream().filter(f6 -> {
                return "2".equals(f6.getDataScope()) && Objects.nonNull(f6.getDeptId());
            }).map((v0) -> {
                return v0.getDeptId();
            }).collect(Collectors.toSet());
            filterDeptIds.addAll(customDeptIds);
            if (userDataScopeSet.contains(DataScopeAspect.DATA_SCOPE_CUSTOM_AND_CHILD)) {
                Set<Long> cDeptIds = (Set) dataScopeVOList.stream().filter(f7 -> {
                    return DataScopeAspect.DATA_SCOPE_CUSTOM_AND_CHILD.equals(f7.getDataScope()) && Objects.nonNull(f7.getDeptId());
                }).map((v0) -> {
                    return v0.getDeptId();
                }).collect(Collectors.toSet());
                filterDeptIds.addAll(cDeptIds);
                cDeptIds.forEach(cId -> {
                    filterDeptIds.addAll(CommonUtil.getChildDeptIds(cId, allDeptList));
                });
            }
            if (userDataScopeSet.contains("3") && Objects.nonNull(userDeptId)) {
                filterDeptIds.add(userDeptId);
            }
            if (userDataScopeSet.contains("4")) {
                filterDeptIds.addAll(CommonUtil.getChildDeptIds(userDeptId, allDeptList));
            }
            List<PositionCurrentPushWsVO> deptDataLocation = (List) allCurrentList.stream().filter(f8 -> {
                return Objects.nonNull(f8.getDeptId()) && filterDeptIds.contains(f8.getDeptId());
            }).collect(Collectors.toList());
            List<PositionCurrentPushWsVO> personDataLocation = new ArrayList<>();
            if (userDataScopeSet.contains("5") && Objects.nonNull(userPersonId)) {
                personDataLocation = (List) allCurrentList.stream().filter(f9 -> {
                    return Objects.nonNull(f9.getPersonId()) && userPersonId.compareTo(f9.getPersonId()) == 0;
                }).collect(Collectors.toList());
            }
            List<PositionCurrentPushWsVO> facilityDataLocation = new ArrayList<>();
            List<SystemRoleFacilityVO> systemRoleFacilityVOList = this.systemRoleFacilityService.findByRoleIds((List) dataScopeVOList.stream().map((v0) -> {
                return v0.getRoleId();
            }).collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(systemRoleFacilityVOList)) {
                facilityDataLocation = (List) allCurrentList.stream().filter(f10 -> {
                    return CommonUtil.containsFacilityRailScope(f10.getLayerId(), f10.getLongitude(), f10.getLatitude(), systemRoleFacilityVOList);
                }).collect(Collectors.toList());
            }
            deptDataLocation.addAll(personDataLocation);
            deptDataLocation.addAll(facilityDataLocation);
            List<PositionCurrentPushWsVO> deptDataLocation2 = deptDataLocation.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> {
                return new TreeSet<>(Comparator.comparing((v0) -> {
                    return v0.getPersonId();
                }));
            }), (v1) -> {
                return new ArrayList(v1);
            }));
            pushJson.put("onlineCount", Integer.valueOf(deptDataLocation2.size()));
            List<Long> onlinePersonIds = deptDataLocation2.stream().map((v0) -> {
                return v0.getPersonId();
            }).collect(Collectors.toList());
            Set offlinePersonIdList2 = (Set) allCurrentList.stream().map((v0) -> {
                return v0.getPersonId();
            }).filter(personId -> {
                return !onlinePersonIds.contains(personId);
            }).collect(Collectors.toSet());
            pushJson.put("offlinePersonIdList", offlinePersonIdList2);
            List<PositionCurrentPushWsVO> pushList3 = deptDataLocation2.stream().filter(f11 -> {
                return filterData(userId, f11.getPersonId(), f11.getAcceptTime());
            }).collect(Collectors.toList());
            pushJson.put(JsonResult.TOTAL, Integer.valueOf(pushList3.size()));
            pushJson.put("data", pushList3);
            this.webSocketServer.sendMessageForSystemUser(userId, JSONObject.toJSONString(pushJson, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible));
            pushList3.forEach(f12 -> {
                this.pushWsPersonCurrentLocationCache.put(getPushWsPersonCurrentLocationKey(userId, f12.getPersonId()), f12.getAcceptTime());
            });
        });
    }
}
