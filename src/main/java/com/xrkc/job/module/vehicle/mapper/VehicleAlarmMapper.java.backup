package com.xrkc.job.module.vehicle.mapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xrkc.core.domain.vehicle.VehicleAlarm;
import com.xrkc.core.mybatisplus.BaseMapperX;
import java.lang.invoke.SerializedLambda;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/module/vehicle/mapper/VehicleAlarmMapper.class */
public interface VehicleAlarmMapper extends BaseMapperX<VehicleAlarm> {
    /* JADX WARN: Multi-variable type inference failed */
    default List<VehicleAlarm> selectExistList(List<String> alarmTypeList, int maxInterval) {
        return selectList((Wrapper) ((LambdaQueryWrapper) new LambdaQueryWrapper<VehicleAlarm>().in((LambdaQueryWrapper) (v0) -> {
            return v0.getAlarmType();
        }, (Collection<?>) alarmTypeList)).gt((v0) -> {
            return v0.getAlarmTime();
        }, LocalDateTime.now().minusMinutes(maxInterval)));
    }
    /* JADX WARN: Multi-variable type inference failed */
    default List<VehicleAlarm> selectCurrentUnDisposeList() {
        return selectList((Wrapper) ((LambdaQueryWrapper) new LambdaQueryWrapper<VehicleAlarm>().eq((v0) -> {
            return v0.getAlarmStatus();
        }, "15")).gt((v0) -> {
            return v0.getAlarmTime();
        }, LocalDateTime.now().minusSeconds(30L)));
    }
}
