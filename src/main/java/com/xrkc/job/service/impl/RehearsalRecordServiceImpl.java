package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.job.domain.PositionHistory;
import com.xrkc.job.domain.RehearsalRecord;
import com.xrkc.job.mapper.RehearsalRecordMapper;
import com.xrkc.job.service.IPositionHistoryService;
import com.xrkc.job.service.IRehearsalRecordService;
import com.xrkc.job.util.CommonUtil;
import java.awt.geom.GeneralPath;
import java.awt.geom.Point2D;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/RehearsalRecordServiceImpl.class */
public class RehearsalRecordServiceImpl extends ServiceImpl<RehearsalRecordMapper, RehearsalRecord> implements IRehearsalRecordService {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) RehearsalRecordServiceImpl.class);
    @Autowired
    private RehearsalRecordMapper rehearsalRecordMapper;
    @Autowired
    private IPositionHistoryService positionHistoryService;
    @Value("${spring.datasource.driver-class-name}")
    private String driverClassName;
    @Override // com.xrkc.job.service.IRehearsalRecordService
    public List<RehearsalRecord> selectUnCompletePlan() {
        return this.rehearsalRecordMapper.selectUnCompletePlan();
    }
    @Override // com.xrkc.job.service.IRehearsalRecordService
    public List<Long> findRehearsalCard(RehearsalRecord entity) {
        if (Objects.nonNull(entity.getRehearsalDate())) {
            String tableName = this.driverClassName.contains("dm") ? "POSITION_HISTORY_" + entity.getRehearsalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")) : "position_history_" + entity.getRehearsalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if (this.positionHistoryService.existTable(tableName)) {
                try {
                    entity.setTableName(tableName);
                    List<Long> longList = new ArrayList<>();
                    List<PositionHistory> positionHistoryList = this.rehearsalRecordMapper.selectByLayerId(entity);
                    for (PositionHistory history : positionHistoryList) {
                        if (CommonUtil.containsRailScope(history.getLongitude(), history.getLatitude(), entity.getRailScope(), entity.getDrawType())) {
                            longList.add(history.getCardId());
                        }
                    }
                    return (List) longList.stream().distinct().collect(Collectors.toList());
                } catch (Exception e) {
                    log.info(entity.getRehearsalDate() + "当前暂无演练集合人数");
                }
            }
        }
        return new ArrayList();
    }
    private static GeneralPath genGeneralPath(ArrayList<Point2D.Double> points) {
        GeneralPath path = new GeneralPath();
        if (points.size() < 3) {
            return null;
        }
        path.moveTo((float) points.get(0).getX(), (float) points.get(0).getY());
        Iterator<Point2D.Double> it = points.iterator();
        while (it.hasNext()) {
            Point2D.Double point = it.next();
            path.lineTo((float) point.getX(), (float) point.getY());
        }
        path.closePath();
        return path;
    }
    @Override // com.xrkc.job.service.IRehearsalRecordService
    public int batchInsert(List<RehearsalRecord> list) {
        this.rehearsalRecordMapper.insertBatch(list, 1000);
        return list.size();
    }
    @Override // com.xrkc.job.service.IRehearsalRecordService
    public List<RehearsalRecord> selectList(Map<String, Object> queryParams) {
        return this.rehearsalRecordMapper.selectList(queryParams);
    }
    @Override // com.xrkc.job.service.IRehearsalRecordService
    public Integer batchUpdate(List<RehearsalRecord> list) {
        list.forEach(f -> {
            this.rehearsalRecordMapper.batchUpdate(f);
        });
        return 1;
    }
}
