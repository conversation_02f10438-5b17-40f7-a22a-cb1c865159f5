package com.xrkc.job.service.impl;
import cn.hutool.core.collection.CollUtil;
import com.xrkc.core.domain.device.DeviceCamera;
import com.xrkc.job.mapper.DeviceCameraMapper;
import com.xrkc.job.service.IDeviceCameraService;
import com.xrkc.job.service.ISystemConfigService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.gavaghan.geodesy.Ellipsoid;
import org.gavaghan.geodesy.GeodeticCalculator;
import org.gavaghan.geodesy.GeodeticCurve;
import org.gavaghan.geodesy.GlobalCoordinates;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/DeviceCameraServiceImpl.class */
public class DeviceCameraServiceImpl implements IDeviceCameraService {
    @Autowired
    private DeviceCameraMapper deviceCameraMapper;
    @Autowired
    private ISystemConfigService systemConfigService;
    private static final String CONFIG_KEY_CAMERA_VALID_DISTANCE = "camera_valid_distance";
    @Override // com.xrkc.job.service.IDeviceCameraService
    public String findRtspByParam(String layerId, BigDecimal longitude, BigDecimal latitude) {
        if (StringUtils.isNotBlank(layerId) && Objects.nonNull(longitude) && Objects.nonNull(latitude)) {
            Map<String, String> config = this.systemConfigService.getConfig();
            String camera_valid_distance = config.get(CONFIG_KEY_CAMERA_VALID_DISTANCE);
            int valid_distance = 50;
            if (StringUtils.isNotBlank(camera_valid_distance)) {
                valid_distance = Integer.valueOf(camera_valid_distance).intValue();
            }
            GlobalCoordinates newGlobalCoordinates = new GlobalCoordinates(longitude.doubleValue(), latitude.doubleValue());
            List<DeviceCamera> list = this.deviceCameraMapper.selectAll(layerId);
            Map<Double, String> map = new HashMap<>();
            List<Double> resultList = new ArrayList<>();
            for (DeviceCamera camera : list) {
                BigDecimal cameraLongitude = camera.getLongitude();
                BigDecimal cameraLatitude = camera.getLatitude();
                GlobalCoordinates oldGlobalCoordinates = new GlobalCoordinates(cameraLongitude.doubleValue(), cameraLatitude.doubleValue());
                GeodeticCurve geodeticCurve = new GeodeticCalculator().calculateGeodeticCurve(Ellipsoid.Sphere, newGlobalCoordinates, oldGlobalCoordinates);
                double result = geodeticCurve.getEllipsoidalDistance();
                if (result <= valid_distance) {
                    resultList.add(Double.valueOf(result));
                    String rtsp = camera.getRstp();
                    map.put(Double.valueOf(result), rtsp);
                }
            }
            if (CollUtil.isEmpty((Collection<?>) resultList)) {
                return null;
            }
            Double min = (Double) Collections.min(resultList);
            return map.get(min);
        }
        return null;
    }
}
