package com.xrkc.job.service.impl;
import com.xrkc.job.domain.StatisticsRailCurrentPerson;
import com.xrkc.job.mapper.StatisticsRailCurrentPersonMapper;
import com.xrkc.job.service.IStatisticsRailCurrentPersonService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/StatisticsRailCurrentPersonServiceImpl.class */
public class StatisticsRailCurrentPersonServiceImpl implements IStatisticsRailCurrentPersonService {
    @Autowired
    private StatisticsRailCurrentPersonMapper statisticsRailCurrentPersonMapper;
    @Override // com.xrkc.job.service.IStatisticsRailCurrentPersonService
    @Transactional
    public int batchReplace(String railType, List<StatisticsRailCurrentPerson> statisticsRailCurrentPersonList) {
        this.statisticsRailCurrentPersonMapper.batchDelete(railType);
        if (!CollectionUtils.isEmpty(statisticsRailCurrentPersonList)) {
            this.statisticsRailCurrentPersonMapper.batchInsert(statisticsRailCurrentPersonList);
            return 1;
        }
        return 1;
    }
}
