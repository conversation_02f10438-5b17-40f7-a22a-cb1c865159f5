package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/RehearsalPerson.class */
public class RehearsalPerson {
    @TableId("id")
    private Long id;
    private Long rehearsalId;
    private Long personId;
    private String personType;
    private String personTypeName;
    private String realName;
    private Long cardId;
    private String jobNumber;
    private Long deptId;
    private Long postId;
    private String deptName;
    private String postName;
    private String positionStatus;
    public void setId(Long id) {
        this.id = id;
    }
    public void setRehearsalId(Long rehearsalId) {
        this.rehearsalId = rehearsalId;
    }
    public void setPersonId(Long personId) {
        this.personId = personId;
    }
    public void setPersonType(String personType) {
        this.personType = personType;
    }
    public void setPersonTypeName(String personTypeName) {
        this.personTypeName = personTypeName;
    }
    public void setRealName(String realName) {
        this.realName = realName;
    }
    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }
    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }
    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
    public void setPostId(Long postId) {
        this.postId = postId;
    }
    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    public void setPostName(String postName) {
        this.postName = postName;
    }
    public void setPositionStatus(String positionStatus) {
        this.positionStatus = positionStatus;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof RehearsalPerson)) {
            return false;
        }
        RehearsalPerson other = (RehearsalPerson) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$id = getId();
        Object other$id = other.getId();
        if (this$id == null) {
            if (other$id != null) {
                return false;
            }
        } else if (!this$id.equals(other$id)) {
            return false;
        }
        Object this$rehearsalId = getRehearsalId();
        Object other$rehearsalId = other.getRehearsalId();
        if (this$rehearsalId == null) {
            if (other$rehearsalId != null) {
                return false;
            }
        } else if (!this$rehearsalId.equals(other$rehearsalId)) {
            return false;
        }
        Object this$personId = getPersonId();
        Object other$personId = other.getPersonId();
        if (this$personId == null) {
            if (other$personId != null) {
                return false;
            }
        } else if (!this$personId.equals(other$personId)) {
            return false;
        }
        Object this$cardId = getCardId();
        Object other$cardId = other.getCardId();
        if (this$cardId == null) {
            if (other$cardId != null) {
                return false;
            }
        } else if (!this$cardId.equals(other$cardId)) {
            return false;
        }
        Object this$deptId = getDeptId();
        Object other$deptId = other.getDeptId();
        if (this$deptId == null) {
            if (other$deptId != null) {
                return false;
            }
        } else if (!this$deptId.equals(other$deptId)) {
            return false;
        }
        Object this$postId = getPostId();
        Object other$postId = other.getPostId();
        if (this$postId == null) {
            if (other$postId != null) {
                return false;
            }
        } else if (!this$postId.equals(other$postId)) {
            return false;
        }
        Object this$personType = getPersonType();
        Object other$personType = other.getPersonType();
        if (this$personType == null) {
            if (other$personType != null) {
                return false;
            }
        } else if (!this$personType.equals(other$personType)) {
            return false;
        }
        Object this$personTypeName = getPersonTypeName();
        Object other$personTypeName = other.getPersonTypeName();
        if (this$personTypeName == null) {
            if (other$personTypeName != null) {
                return false;
            }
        } else if (!this$personTypeName.equals(other$personTypeName)) {
            return false;
        }
        Object this$realName = getRealName();
        Object other$realName = other.getRealName();
        if (this$realName == null) {
            if (other$realName != null) {
                return false;
            }
        } else if (!this$realName.equals(other$realName)) {
            return false;
        }
        Object this$jobNumber = getJobNumber();
        Object other$jobNumber = other.getJobNumber();
        if (this$jobNumber == null) {
            if (other$jobNumber != null) {
                return false;
            }
        } else if (!this$jobNumber.equals(other$jobNumber)) {
            return false;
        }
        Object this$deptName = getDeptName();
        Object other$deptName = other.getDeptName();
        if (this$deptName == null) {
            if (other$deptName != null) {
                return false;
            }
        } else if (!this$deptName.equals(other$deptName)) {
            return false;
        }
        Object this$postName = getPostName();
        Object other$postName = other.getPostName();
        if (this$postName == null) {
            if (other$postName != null) {
                return false;
            }
        } else if (!this$postName.equals(other$postName)) {
            return false;
        }
        Object this$positionStatus = getPositionStatus();
        Object other$positionStatus = other.getPositionStatus();
        return this$positionStatus == null ? other$positionStatus == null : this$positionStatus.equals(other$positionStatus);
    }
    protected boolean canEqual(Object other) {
        return other instanceof RehearsalPerson;
    }
    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $rehearsalId = getRehearsalId();
        int result2 = (result * 59) + ($rehearsalId == null ? 43 : $rehearsalId.hashCode());
        Object $personId = getPersonId();
        int result3 = (result2 * 59) + ($personId == null ? 43 : $personId.hashCode());
        Object $cardId = getCardId();
        int result4 = (result3 * 59) + ($cardId == null ? 43 : $cardId.hashCode());
        Object $deptId = getDeptId();
        int result5 = (result4 * 59) + ($deptId == null ? 43 : $deptId.hashCode());
        Object $postId = getPostId();
        int result6 = (result5 * 59) + ($postId == null ? 43 : $postId.hashCode());
        Object $personType = getPersonType();
        int result7 = (result6 * 59) + ($personType == null ? 43 : $personType.hashCode());
        Object $personTypeName = getPersonTypeName();
        int result8 = (result7 * 59) + ($personTypeName == null ? 43 : $personTypeName.hashCode());
        Object $realName = getRealName();
        int result9 = (result8 * 59) + ($realName == null ? 43 : $realName.hashCode());
        Object $jobNumber = getJobNumber();
        int result10 = (result9 * 59) + ($jobNumber == null ? 43 : $jobNumber.hashCode());
        Object $deptName = getDeptName();
        int result11 = (result10 * 59) + ($deptName == null ? 43 : $deptName.hashCode());
        Object $postName = getPostName();
        int result12 = (result11 * 59) + ($postName == null ? 43 : $postName.hashCode());
        Object $positionStatus = getPositionStatus();
        return (result12 * 59) + ($positionStatus == null ? 43 : $positionStatus.hashCode());
    }
    public String toString() {
        return "RehearsalPerson(id=" + getId() + ", rehearsalId=" + getRehearsalId() + ", personId=" + getPersonId() + ", personType=" + getPersonType() + ", personTypeName=" + getPersonTypeName() + ", realName=" + getRealName() + ", cardId=" + getCardId() + ", jobNumber=" + getJobNumber() + ", deptId=" + getDeptId() + ", postId=" + getPostId() + ", deptName=" + getDeptName() + ", postName=" + getPostName() + ", positionStatus=" + getPositionStatus() + StringPool.RIGHT_BRACKET;
    }
    public Long getId() {
        return this.id;
    }
    public Long getRehearsalId() {
        return this.rehearsalId;
    }
    public Long getPersonId() {
        return this.personId;
    }
    public String getPersonType() {
        return this.personType;
    }
    public String getPersonTypeName() {
        return this.personTypeName;
    }
    public String getRealName() {
        return this.realName;
    }
    public Long getCardId() {
        return this.cardId;
    }
    public String getJobNumber() {
        return this.jobNumber;
    }
    public Long getDeptId() {
        return this.deptId;
    }
    public Long getPostId() {
        return this.postId;
    }
    public String getDeptName() {
        return this.deptName;
    }
    public String getPostName() {
        return this.postName;
    }
    public String getPositionStatus() {
        return this.positionStatus;
    }
}
