package com.xrkc.job.service;
import com.xrkc.job.domain.AlarmNoticeRecord;
import java.time.LocalDateTime;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IAlarmNoticeRecordService.class */
public interface IAlarmNoticeRecordService {
    void saveEntity(AlarmNoticeRecord alarmNoticeRecord);
    void updateById(AlarmNoticeRecord alarmNoticeRecord);
    List<AlarmNoticeRecord> selectListByParams(String str, String str2, LocalDateTime localDateTime, LocalDateTime localDateTime2);
}
