package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.core.domain.device.DeviceCardSenderDetail;
import com.xrkc.job.mapper.DeviceCardSenderDetailMapper;
import com.xrkc.job.service.IDeviceCardSenderDetailService;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/DeviceCardSenderDetailServiceImpl.class */
public class DeviceCardSenderDetailServiceImpl extends ServiceImpl<DeviceCardSenderDetailMapper, DeviceCardSenderDetail> implements IDeviceCardSenderDetailService {
}
