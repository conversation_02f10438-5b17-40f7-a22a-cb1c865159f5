package com.xrkc.job.mapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xrkc.core.mybatisplus.BaseMapperX;
import com.xrkc.job.domain.RehearsalPlan;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/RehearsalPlanMapper.class */
public interface RehearsalPlanMapper extends BaseMapperX<RehearsalPlan> {
    /* JADX WARN: Multi-variable type inference failed */
    default List<RehearsalPlan> selectRehearsalPlanList() {
        return selectList((Wrapper) ((LambdaQueryWrapper) ((LambdaQueryWrapper) ((LambdaQueryWrapper) new LambdaQueryWrapper<RehearsalPlan>().in((LambdaQueryWrapper) (v0) -> {
            return v0.getRehearsalStatus();
        }, (Collection<?>) Arrays.asList("1", "2"))).isNotNull((v0) -> {
            return v0.getValidBeginTime();
        })).isNotNull((v0) -> {
            return v0.getValidEndTime();
        })).le((v0) -> {
            return v0.getRehearsalDate();
        }, LocalDate.now()));
    }
    default int batchUpdateStatus(String rehearsalStatus, List<Long> rehearsalIds) {
        LambdaUpdateWrapper<RehearsalPlan> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set((v0) -> {
            return v0.getRehearsalStatus();
        }, rehearsalStatus);
        wrapper.set((v0) -> {
            return v0.getUpdateTime();
        }, LocalDateTime.now());
        wrapper.set((v0) -> {
            return v0.getUpdateBy();
        }, "task");
        wrapper.in((LambdaUpdateWrapper<RehearsalPlan>) (v0) -> {
            return v0.getRehearsalId();
        }, rehearsalIds);
        return update(null, wrapper);
    }
}
