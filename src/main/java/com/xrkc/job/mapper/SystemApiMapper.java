package com.xrkc.job.mapper;
import com.xrkc.core.domain.system.SystemApi;
import java.time.LocalDateTime;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;
@Mapper
@Repository
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/SystemApiMapper.class */
public interface SystemApiMapper {
    @Select({"SELECT * FROM system_api WHERE api_enable='Y' AND api_key = #{apiKey}"})
    SystemApi selectOne(@Param("apiKey") String str);
    @Update({"update system_api set create_time = #{createTime} , update_time = #{updateTime}, username = #{orgIndexCode}   WHERE   api_id = #{apiId} "})
    void updateByApiKey(@Param("createTime") LocalDateTime localDateTime, @Param("updateTime") LocalDateTime localDateTime2, @Param("orgIndexCode") String str, @Param("apiId") Long l);
    @Update({"update system_api set password = #{password}  WHERE   api_id = #{apiId} "})
    void updateEquipmentNumber(@Param("password") String str, @Param("apiId") Long l);
    @Update({"update system_api set  update_time = #{updateTime}  WHERE   api_id = #{apiId} "})
    void updateTimeByApiKey(@Param("updateTime") LocalDateTime localDateTime, @Param("apiId") Long l);
}
