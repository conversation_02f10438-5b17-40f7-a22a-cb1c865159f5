package com.xrkc.job.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xrkc.job.domain.SystemUserDataScopeVO;
import com.xrkc.job.domain.SystemUserRole;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/SystemUserRoleMapper.class */
public interface SystemUserRoleMapper extends BaseMapper<SystemUserRole> {
    List<SystemUserRole> getUserRoleList();
    List<SystemUserDataScopeVO> findDataScopeByUserIds(@Param("list") Set<Long> set);
}
