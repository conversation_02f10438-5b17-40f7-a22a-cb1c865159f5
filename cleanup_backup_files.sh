#!/bin/bash

# 清理备份文件脚本
# 删除所有由remove_mapper_cast.sh创建的.backup文件

echo "清理备份文件..."
echo "=========================================="

# 查找所有备份文件
backup_files=$(find src/main/java -name "*.backup" 2>/dev/null)

if [ -z "$backup_files" ]; then
    echo "未找到备份文件"
    exit 0
fi

echo "找到以下备份文件："
echo "$backup_files"
echo ""

# 询问用户是否确认删除
read -p "是否确认删除这些备份文件？(y/N): " confirm

if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
    echo ""
    echo "正在删除备份文件..."
    
    count=0
    for file in $backup_files; do
        if [ -f "$file" ]; then
            rm "$file"
            echo "已删除: $file"
            count=$((count + 1))
        fi
    done
    
    echo ""
    echo "=========================================="
    echo "清理完成！共删除 $count 个备份文件"
else
    echo ""
    echo "取消删除操作"
fi

echo "=========================================="
