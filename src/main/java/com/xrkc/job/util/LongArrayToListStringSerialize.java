package com.xrkc.job.util;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/LongArrayToListStringSerialize.class */
public class LongArrayToListStringSerialize extends JsonSerializer<List<Long>> {
    @Override // com.fasterxml.jackson.databind.JsonSerializer
    public void serialize(List<Long> value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        List<String> strList = (List) value.stream().map(x -> {
            return x + "";
        }).collect(Collectors.toList());
        String[] array = (String[]) strList.toArray(new String[strList.size()]);
        gen.writeArray(array, 0, array.length);
    }
}
