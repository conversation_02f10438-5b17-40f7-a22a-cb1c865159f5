package com.xrkc.job.domain;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/InspectRecordVo.class */
public class InspectRecordVo implements Serializable {
    private static final long serialVersionUID = 1;
    private Long recordId;
    private Long roadId;
    private String roadName;
    private String roadEnable;
    private String leader;
    private String frequencyType;
    private String frequencyVal;
    private LocalDateTime inspectBeginTime;
    private LocalDateTime inspectEndTime;
    private String inspectStatus;
    private String recordStatus;
    private Integer roadLocationCount;
    private Integer abnormalLocationCount;
    private Long typeId;
    private String typeName;
    private String way;
    private String content;
    private Long locationId;
    private String locationCode;
    private String locationName;
    private String locationEnable;
    private Long deptId;
    private String deptName;
    private String layerId;
    private BigDecimal layerHeight;
    private BigDecimal longitude;
    private BigDecimal latitude;
    private String locationDesc;
    private Integer locationSort;
    private Long personId;
    private String realName;
    private Long teamId;
    private String teamName;
    private Long teamLeaderPersonId;
    private String teamLeader;
    private String memberNames;
    private Long inspectPersonId;
    private String inspectPersonName;
    private String inspectPersonFace;
    private String inspectPersonSignature;
    private LocalDateTime finishTime;
    @JsonSerialize(using = ToStringSerializer.class)
    public Long getRoadId() {
        return this.roadId;
    }
    public void setRoadId(Long roadId) {
        this.roadId = roadId;
    }
    public String getRoadName() {
        return this.roadName;
    }
    public void setRoadName(String roadName) {
        this.roadName = roadName;
    }
    public String getRoadEnable() {
        return this.roadEnable;
    }
    public void setRoadEnable(String roadEnable) {
        this.roadEnable = roadEnable;
    }
    public String getLeader() {
        return this.leader;
    }
    public void setLeader(String leader) {
        this.leader = leader;
    }
    public String getFrequencyType() {
        return this.frequencyType;
    }
    public void setFrequencyType(String frequencyType) {
        this.frequencyType = frequencyType;
    }
    public String getFrequencyVal() {
        return this.frequencyVal;
    }
    public void setFrequencyVal(String frequencyVal) {
        this.frequencyVal = frequencyVal;
    }
    public Integer getLocationSort() {
        return this.locationSort;
    }
    public void setLocationSort(Integer locationSort) {
        this.locationSort = locationSort;
    }
    public Long getTypeId() {
        return this.typeId;
    }
    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }
    public String getTypeName() {
        return this.typeName;
    }
    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }
    public Long getRecordId() {
        return this.recordId;
    }
    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }
    public LocalDateTime getInspectBeginTime() {
        return this.inspectBeginTime;
    }
    public void setInspectBeginTime(LocalDateTime inspectBeginTime) {
        this.inspectBeginTime = inspectBeginTime;
    }
    public LocalDateTime getInspectEndTime() {
        return this.inspectEndTime;
    }
    public void setInspectEndTime(LocalDateTime inspectEndTime) {
        this.inspectEndTime = inspectEndTime;
    }
    public String getInspectStatus() {
        return this.inspectStatus;
    }
    public void setInspectStatus(String inspectStatus) {
        this.inspectStatus = inspectStatus;
    }
    public String getRecordStatus() {
        return this.recordStatus;
    }
    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
    }
    public Integer getRoadLocationCount() {
        return this.roadLocationCount;
    }
    public void setRoadLocationCount(Integer roadLocationCount) {
        this.roadLocationCount = roadLocationCount;
    }
    public Integer getAbnormalLocationCount() {
        return this.abnormalLocationCount;
    }
    public void setAbnormalLocationCount(Integer abnormalLocationCount) {
        this.abnormalLocationCount = abnormalLocationCount;
    }
    public String getWay() {
        return this.way;
    }
    public void setWay(String way) {
        this.way = way;
    }
    public String getContent() {
        return this.content;
    }
    public void setContent(String content) {
        this.content = content;
    }
    public Long getTeamId() {
        return this.teamId;
    }
    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }
    public String getTeamName() {
        return this.teamName;
    }
    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }
    public Long getTeamLeaderPersonId() {
        return this.teamLeaderPersonId;
    }
    public void setTeamLeaderPersonId(Long teamLeaderPersonId) {
        this.teamLeaderPersonId = teamLeaderPersonId;
    }
    public String getTeamLeader() {
        return this.teamLeader;
    }
    public void setTeamLeader(String teamLeader) {
        this.teamLeader = teamLeader;
    }
    public String getMemberNames() {
        return this.memberNames;
    }
    public void setMemberNames(String memberNames) {
        this.memberNames = memberNames;
    }
    public Long getInspectPersonId() {
        return this.inspectPersonId;
    }
    public void setInspectPersonId(Long inspectPersonId) {
        this.inspectPersonId = inspectPersonId;
    }
    public String getInspectPersonName() {
        return this.inspectPersonName;
    }
    public void setInspectPersonName(String inspectPersonName) {
        this.inspectPersonName = inspectPersonName;
    }
    public String getInspectPersonFace() {
        return this.inspectPersonFace;
    }
    public void setInspectPersonFace(String inspectPersonFace) {
        this.inspectPersonFace = inspectPersonFace;
    }
    public String getInspectPersonSignature() {
        return this.inspectPersonSignature;
    }
    public void setInspectPersonSignature(String inspectPersonSignature) {
        this.inspectPersonSignature = inspectPersonSignature;
    }
    public Long getLocationId() {
        return this.locationId;
    }
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }
    public String getLocationCode() {
        return this.locationCode;
    }
    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }
    public String getLocationName() {
        return this.locationName;
    }
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }
    public String getLocationEnable() {
        return this.locationEnable;
    }
    public void setLocationEnable(String locationEnable) {
        this.locationEnable = locationEnable;
    }
    public Long getDeptId() {
        return this.deptId;
    }
    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
    public String getDeptName() {
        return this.deptName;
    }
    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
    public BigDecimal getLayerHeight() {
        return this.layerHeight;
    }
    public void setLayerHeight(BigDecimal layerHeight) {
        this.layerHeight = layerHeight;
    }
    public BigDecimal getLongitude() {
        return this.longitude;
    }
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    public BigDecimal getLatitude() {
        return this.latitude;
    }
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    public String getLocationDesc() {
        return this.locationDesc;
    }
    public void setLocationDesc(String locationDesc) {
        this.locationDesc = locationDesc;
    }
    public Long getPersonId() {
        return this.personId;
    }
    public void setPersonId(Long personId) {
        this.personId = personId;
    }
    public String getRealName() {
        return this.realName;
    }
    public void setRealName(String realName) {
        this.realName = realName;
    }
    public LocalDateTime getFinishTime() {
        return this.finishTime;
    }
    public void setFinishTime(LocalDateTime finishTime) {
        this.finishTime = finishTime;
    }
}
