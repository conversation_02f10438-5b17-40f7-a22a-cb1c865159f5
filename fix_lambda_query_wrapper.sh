#!/bin/bash

# 批量修复 new LambdaQueryWrapperX() 的使用，添加具体的泛型类型

echo "开始批量修复 new LambdaQueryWrapperX() 的使用..."

# 查找所有包含 new LambdaQueryWrapperX() 的 Java 文件
files=$(find src/main/java -name "*.java" -exec grep -l "new LambdaQueryWrapperX()" {} \;)

if [ -z "$files" ]; then
    echo "没有找到包含 new LambdaQueryWrapperX() 的文件"
    exit 0
fi

echo "找到以下文件需要修复："
echo "$files"
echo ""

# 统计修复的文件数量
count=0

# 遍历每个文件进行修复
for file in $files; do
    echo "正在修复文件: $file"
    
    # 备份原文件
    cp "$file" "$file.backup"
    
    # 获取文件中的实体类名称
    entity_class=$(grep -o "extends BaseMapperX<[^>]*>" "$file" | head -1 | sed 's/.*<\([^>]*\)>.*/\1/')
    
    if [ -z "$entity_class" ]; then
        # 尝试从导入语句中获取实体类
        entity_class=$(grep "^import.*\." "$file" | grep -v "import.*\*" | grep -E "(Entity|Domain|Model|VO|DTO)" | head -1 | sed 's/.*\.//')
    fi
    
    if [ -z "$entity_class" ]; then
        # 尝试从文件名推断
        filename=$(basename "$file" .java)
        if [[ "$filename" == *"Mapper" ]]; then
            entity_class=$(echo "$filename" | sed 's/Mapper$//')
        fi
    fi
    
    echo "  - 推断的实体类: $entity_class"
    
    # 替换 new LambdaQueryWrapperX() 为 new LambdaQueryWrapperX<EntityClass>()
    if [ ! -z "$entity_class" ]; then
        sed -i '' "s/new LambdaQueryWrapperX()/new LambdaQueryWrapperX<$entity_class>()/g" "$file"
        echo "  - 已添加泛型类型: $entity_class"
    else
        echo "  - 警告: 无法推断实体类，跳过此文件"
        continue
    fi
    
    count=$((count + 1))
    echo "  - 修复完成"
done

echo ""
echo "批量修复完成！"
echo "总共修复了 $count 个文件"
echo ""
echo "修复内容："
echo "1. 将 new LambdaQueryWrapperX() 替换为 new LambdaQueryWrapperX<EntityClass>()"
echo "2. 根据 Mapper 接口的泛型参数推断实体类名称"
echo ""
echo "注意：原文件已备份为 .backup 文件，如需恢复请手动操作" 