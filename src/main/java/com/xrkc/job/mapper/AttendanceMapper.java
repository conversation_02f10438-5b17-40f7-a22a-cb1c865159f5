package com.xrkc.job.mapper;
import com.xrkc.core.mybatisplus.BaseMapperX;
import com.xrkc.job.domain.Attendance;
import java.util.List;
import java.util.Map;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/AttendanceMapper.class */
public interface AttendanceMapper extends BaseMapperX<Attendance> {
    List<Attendance> queryNotAttendanceList(String str);
    List<Attendance> selectPositionList(Map<String, Object> map);
}
