package com.xrkc.job.task;
import cn.hutool.core.collection.CollUtil;
import com.xrkc.job.util.direct.Cluster;
import com.xrkc.job.util.direct.LatLngBounds;
import com.xrkc.job.util.direct.Point;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
@Component("calcTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/CalcTask.class */
public class CalcTask {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) CalcTask.class);
    private double r = 5.0d;
    public final double earthRadius = 6371.393d;
    public static void main(String[] args) {
        CalcTask a = new CalcTask();
        a.directDistance(mockPoint("POLYGON((114.392817405783 30.459949546955, 114.396930073014 30.4586371860134, 114.395471278224 30.4564988302653, 114.392027711791 30.4581458803928, 114.392817405783 30.459949546955))"));
        a.directDistance(mockPoint("POLYGON((114.392847023444 30.4599347889286, 114.396800654155 30.4586665193617, 114.396783627619 30.458637025035, 114.395494266416 30.4565177742016, 114.392031374569 30.458128286421, 114.392847023444 30.4599347889286))"));
        a.directDistance(mockPoint("POLYGON((114.392839371577 30.4599161626377, 114.396810198878 30.4586371551426, 114.396800548118 30.4585744589243, 114.394277196278 30.4585243113002, 114.394262722866 30.4584908731271, 114.395483350447 30.456547458634, 114.392034738034 30.4581435882701, 114.392839371577 30.4599161626377))"));
        a.directDistance(mockPoint("POLYGON((114.395046937498 30.459063778433, 114.39574467488 30.4588479020296, 114.395775823831 30.458804726659, 114.396074853712 30.4587183749016, 114.396168300005 30.4586805956316, 114.395775820328 30.4578440723301, 114.395115468329 30.458043759086, 114.394691844665 30.4582164592113, 114.394691844066 30.4582650316484, 114.395046937498 30.459063778433))"));
        a.directDistance(mockPoint("POLYGON((114.392858592208 30.459921117668, 114.393851568842 30.4596238001003, 114.39385490091 30.4595978208686, 114.394178114958 30.4594823567521, 114.394537980849 30.4593697790387, 114.396950420045 30.4586278914767, 114.396570543069 30.4580852130026, 114.396553883315 30.4580650064576, 114.39652994197 30.4580452678371, 114.396489971385 30.4580279551636, 114.395866992865 30.4575402444429, 114.395474098189 30.4564875217248, 114.39542749412 30.4565048259273, 114.395364246619 30.4565365492917, 114.393773064849 30.4573757623026, 114.39291077938 30.4577535008502, 114.392168138932 30.4582785687433, 114.392151485831 30.4583304991496, 114.392138164925 30.4583824292175, 114.392527270649 30.4593102894754, 114.39285786779 30.459935870176, 114.392858592208 30.459921117668))"));
    }
    private static List<Map<String, Object>> mockPoint(String str) {
        String[] arr = str.replace("POLYGON((", "").replace("))", "").replace(", ", ",").split(",");
        System.out.println("点位数：" + arr.length);
        List<Map<String, Object>> list = new ArrayList<>();
        for (int i = 0; i < arr.length; i++) {
            Map<String, Object> map = new HashMap<>();
            map.put("lng", arr[i].split(" ")[0]);
            map.put("lat", arr[i].split(" ")[1]);
            list.add(map);
        }
        return list;
    }
    public void directDistance(List<Map<String, Object>> list) {
        List<Point> points = new ArrayList<>();
        list.forEach(data -> {
            points.add(Point.builder().latitude(Double.parseDouble(data.get("lat").toString())).longitude(Double.parseDouble(data.get("lng").toString())).build());
        });
        List<Cluster> clusters = new ArrayList<>();
        int count = 0;
        for (Point point : points) {
            Cluster isCluster = null;
            for (Cluster cluster : clusters) {
                if (cluster.getLatLngBounds().contains(point)) {
                    double distance = calculateLineDistance(point, cluster.getCenterPoint());
                    if (distance < this.r && (isCluster == null || calculateLineDistance(point, cluster.getCenterPoint()) < calculateLineDistance(point, isCluster.getCenterPoint()))) {
                        isCluster = cluster;
                        count++;
                    }
                }
            }
            if (isCluster != null) {
                isCluster.addCount();
                isCluster.getPoints().add(point);
                isCluster.setCenterPoint(getCenterPoint(isCluster.getPoints()));
                isCluster.setLatLngBounds(LatLngBounds.builder().northeast(Point.builder().latitude(point.getLatitude() + km2Degree(Double.valueOf(this.r / 1000.0d)).doubleValue()).longitude(point.getLongitude() + km2Degree(Double.valueOf(this.r / 1000.0d)).doubleValue()).build()).southwest(Point.builder().latitude(point.getLatitude() - km2Degree(Double.valueOf(this.r / 1000.0d)).doubleValue()).longitude(point.getLongitude() - km2Degree(Double.valueOf(this.r / 1000.0d)).doubleValue()).build()).build());
            } else {
                clusters.add(Cluster.builder().centerPoint(point).points(CollUtil.newLinkedList(point)).count(1).latLngBounds(LatLngBounds.builder().northeast(Point.builder().latitude(point.getLatitude() + km2Degree(Double.valueOf(this.r / 1000.0d)).doubleValue()).longitude(point.getLongitude() + km2Degree(Double.valueOf(this.r / 1000.0d)).doubleValue()).build()).southwest(Point.builder().latitude(point.getLatitude() - km2Degree(Double.valueOf(this.r / 1000.0d)).doubleValue()).longitude(point.getLongitude() - km2Degree(Double.valueOf(this.r / 1000.0d)).doubleValue()).build()).build()).build());
            }
        }
        int vinCount = 0;
        Iterator<Cluster> it = clusters.iterator();
        while (it.hasNext()) {
            if (it.next().getPoints().size() > 100) {
                vinCount++;
            }
        }
        System.out.println(count);
    }
    private double calculateLineDistance(Point source, Point target) {
        double sourceLongitude = source.getLongitude();
        double sourceLatitude = source.getLatitude();
        double targetLongitude = target.getLongitude();
        double sourceLatitude2 = (sourceLatitude * 3.141592653589793d) / 180.0d;
        double targetLatitude = (target.getLatitude() * 3.141592653589793d) / 180.0d;
        double a = sourceLatitude2 - targetLatitude;
        double b = ((sourceLongitude - targetLongitude) * 3.141592653589793d) / 180.0d;
        double sa2 = Math.sin(a / 2.0d);
        double sb2 = Math.sin(b / 2.0d);
        return 2.0d * 6378137.0d * Math.asin(Math.sqrt((sa2 * sa2) + (Math.cos(sourceLatitude2) * Math.cos(targetLatitude) * sb2 * sb2)));
    }
    public Point getCenterPoint(List<Point> pointList) {
        int total = pointList.size();
        double X = 0.0d;
        double Y = 0.0d;
        double Z = 0.0d;
        for (Point point : pointList) {
            double lat = (point.getLatitude() * 3.141592653589793d) / 180.0d;
            double lng = (point.getLongitude() * 3.141592653589793d) / 180.0d;
            double x = Math.cos(lat) * Math.cos(lng);
            double y = Math.cos(lat) * Math.sin(lng);
            double z = Math.sin(lat);
            X += x;
            Y += y;
            Z += z;
        }
        double X2 = X / total;
        double Y2 = Y / total;
        double Z2 = Z / total;
        double Lon = Math.atan2(Y2, X2);
        double Hyp = Math.sqrt((X2 * X2) + (Y2 * Y2));
        double Lat = Math.atan2(Z2, Hyp);
        double longitude = (Lon * 180.0d) / 3.141592653589793d;
        double latitude = (Lat * 180.0d) / 3.141592653589793d;
        return Point.builder().longitude(longitude).latitude(latitude).build();
    }
    public Double km2Degree(Double l) {
        return Double.valueOf(0.008992661340005603d * l.doubleValue());
    }
    public Double degree2Km(Double degree) {
        return Double.valueOf(111.20178578851908d * degree.doubleValue());
    }
}
