package com.xrkc.job.domain;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/Face.class */
public class Face implements Serializable {
    private static final long serialVersionUID = 1;
    private String faceData;
    public void setFaceData(String faceData) {
        this.faceData = faceData;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof Face)) {
            return false;
        }
        Face other = (Face) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$faceData = getFaceData();
        Object other$faceData = other.getFaceData();
        return this$faceData == null ? other$faceData == null : this$faceData.equals(other$faceData);
    }
    protected boolean canEqual(Object other) {
        return other instanceof Face;
    }
    public int hashCode() {
        Object $faceData = getFaceData();
        int result = (1 * 59) + ($faceData == null ? 43 : $faceData.hashCode());
        return result;
    }
    public String toString() {
        return "Face(faceData=" + getFaceData() + StringPool.RIGHT_BRACKET;
    }
    public String getFaceData() {
        return this.faceData;
    }
}
