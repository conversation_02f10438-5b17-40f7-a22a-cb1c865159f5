package com.xrkc.job.service;
import com.xrkc.core.domain.cardDispenser.DeviceCardSenderVehicle;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IDeviceCardSenderVehicleService.class */
public interface IDeviceCardSenderVehicleService {
    List<DeviceCardSenderVehicle> selectTimeoutList(List<String> list, List<String> list2, int i);
    void batchUpdate(List<DeviceCardSenderVehicle> list);
}
