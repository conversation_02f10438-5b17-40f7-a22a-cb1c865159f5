package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
@TableName("device_lamps_status")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/DeviceLampsStatus.class */
public class DeviceLampsStatus implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId("id")
    private Long id;
    @TableField("lamps_id")
    private Long lampsId;
    @TableField("lamps_status")
    private Integer lampsStatus;
    @TableField("lamps_luminance")
    private Integer lampsLuminance;
    @TableField("lamps_version")
    private Integer lampsVersion;
    @TableField("lamps_voltage")
    private BigDecimal lampsVoltage;
    @TableField("lamps_electricity")
    private BigDecimal lampsElectricity;
    @TableField("lamps_kw")
    private BigDecimal lampsKw;
    @TableField("lamps_group")
    private Integer lampsGroup;
    @TableField("heart_time")
    private LocalDateTime heartTime;
    public void setId(Long id) {
        this.id = id;
    }
    public void setLampsId(Long lampsId) {
        this.lampsId = lampsId;
    }
    public void setLampsStatus(Integer lampsStatus) {
        this.lampsStatus = lampsStatus;
    }
    public void setLampsLuminance(Integer lampsLuminance) {
        this.lampsLuminance = lampsLuminance;
    }
    public void setLampsVersion(Integer lampsVersion) {
        this.lampsVersion = lampsVersion;
    }
    public void setLampsVoltage(BigDecimal lampsVoltage) {
        this.lampsVoltage = lampsVoltage;
    }
    public void setLampsElectricity(BigDecimal lampsElectricity) {
        this.lampsElectricity = lampsElectricity;
    }
    public void setLampsKw(BigDecimal lampsKw) {
        this.lampsKw = lampsKw;
    }
    public void setLampsGroup(Integer lampsGroup) {
        this.lampsGroup = lampsGroup;
    }
    public void setHeartTime(LocalDateTime heartTime) {
        this.heartTime = heartTime;
    }
    public String toString() {
        return "DeviceLampsStatus(id=" + getId() + ", lampsId=" + getLampsId() + ", lampsStatus=" + getLampsStatus() + ", lampsLuminance=" + getLampsLuminance() + ", lampsVersion=" + getLampsVersion() + ", lampsVoltage=" + getLampsVoltage() + ", lampsElectricity=" + getLampsElectricity() + ", lampsKw=" + getLampsKw() + ", lampsGroup=" + getLampsGroup() + ", heartTime=" + getHeartTime() + StringPool.RIGHT_BRACKET;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof DeviceLampsStatus)) {
            return false;
        }
        DeviceLampsStatus other = (DeviceLampsStatus) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$id = getId();
        Object other$id = other.getId();
        if (this$id == null) {
            if (other$id != null) {
                return false;
            }
        } else if (!this$id.equals(other$id)) {
            return false;
        }
        Object this$lampsId = getLampsId();
        Object other$lampsId = other.getLampsId();
        if (this$lampsId == null) {
            if (other$lampsId != null) {
                return false;
            }
        } else if (!this$lampsId.equals(other$lampsId)) {
            return false;
        }
        Object this$lampsStatus = getLampsStatus();
        Object other$lampsStatus = other.getLampsStatus();
        if (this$lampsStatus == null) {
            if (other$lampsStatus != null) {
                return false;
            }
        } else if (!this$lampsStatus.equals(other$lampsStatus)) {
            return false;
        }
        Object this$lampsLuminance = getLampsLuminance();
        Object other$lampsLuminance = other.getLampsLuminance();
        if (this$lampsLuminance == null) {
            if (other$lampsLuminance != null) {
                return false;
            }
        } else if (!this$lampsLuminance.equals(other$lampsLuminance)) {
            return false;
        }
        Object this$lampsVersion = getLampsVersion();
        Object other$lampsVersion = other.getLampsVersion();
        if (this$lampsVersion == null) {
            if (other$lampsVersion != null) {
                return false;
            }
        } else if (!this$lampsVersion.equals(other$lampsVersion)) {
            return false;
        }
        Object this$lampsGroup = getLampsGroup();
        Object other$lampsGroup = other.getLampsGroup();
        if (this$lampsGroup == null) {
            if (other$lampsGroup != null) {
                return false;
            }
        } else if (!this$lampsGroup.equals(other$lampsGroup)) {
            return false;
        }
        Object this$lampsVoltage = getLampsVoltage();
        Object other$lampsVoltage = other.getLampsVoltage();
        if (this$lampsVoltage == null) {
            if (other$lampsVoltage != null) {
                return false;
            }
        } else if (!this$lampsVoltage.equals(other$lampsVoltage)) {
            return false;
        }
        Object this$lampsElectricity = getLampsElectricity();
        Object other$lampsElectricity = other.getLampsElectricity();
        if (this$lampsElectricity == null) {
            if (other$lampsElectricity != null) {
                return false;
            }
        } else if (!this$lampsElectricity.equals(other$lampsElectricity)) {
            return false;
        }
        Object this$lampsKw = getLampsKw();
        Object other$lampsKw = other.getLampsKw();
        if (this$lampsKw == null) {
            if (other$lampsKw != null) {
                return false;
            }
        } else if (!this$lampsKw.equals(other$lampsKw)) {
            return false;
        }
        Object this$heartTime = getHeartTime();
        Object other$heartTime = other.getHeartTime();
        return this$heartTime == null ? other$heartTime == null : this$heartTime.equals(other$heartTime);
    }
    protected boolean canEqual(Object other) {
        return other instanceof DeviceLampsStatus;
    }
    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $lampsId = getLampsId();
        int result2 = (result * 59) + ($lampsId == null ? 43 : $lampsId.hashCode());
        Object $lampsStatus = getLampsStatus();
        int result3 = (result2 * 59) + ($lampsStatus == null ? 43 : $lampsStatus.hashCode());
        Object $lampsLuminance = getLampsLuminance();
        int result4 = (result3 * 59) + ($lampsLuminance == null ? 43 : $lampsLuminance.hashCode());
        Object $lampsVersion = getLampsVersion();
        int result5 = (result4 * 59) + ($lampsVersion == null ? 43 : $lampsVersion.hashCode());
        Object $lampsGroup = getLampsGroup();
        int result6 = (result5 * 59) + ($lampsGroup == null ? 43 : $lampsGroup.hashCode());
        Object $lampsVoltage = getLampsVoltage();
        int result7 = (result6 * 59) + ($lampsVoltage == null ? 43 : $lampsVoltage.hashCode());
        Object $lampsElectricity = getLampsElectricity();
        int result8 = (result7 * 59) + ($lampsElectricity == null ? 43 : $lampsElectricity.hashCode());
        Object $lampsKw = getLampsKw();
        int result9 = (result8 * 59) + ($lampsKw == null ? 43 : $lampsKw.hashCode());
        Object $heartTime = getHeartTime();
        return (result9 * 59) + ($heartTime == null ? 43 : $heartTime.hashCode());
    }
    public Long getId() {
        return this.id;
    }
    public Long getLampsId() {
        return this.lampsId;
    }
    public Integer getLampsStatus() {
        return this.lampsStatus;
    }
    public Integer getLampsLuminance() {
        return this.lampsLuminance;
    }
    public Integer getLampsVersion() {
        return this.lampsVersion;
    }
    public BigDecimal getLampsVoltage() {
        return this.lampsVoltage;
    }
    public BigDecimal getLampsElectricity() {
        return this.lampsElectricity;
    }
    public BigDecimal getLampsKw() {
        return this.lampsKw;
    }
    public Integer getLampsGroup() {
        return this.lampsGroup;
    }
    public LocalDateTime getHeartTime() {
        return this.heartTime;
    }
}
