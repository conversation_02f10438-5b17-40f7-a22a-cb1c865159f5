package com.xrkc.job.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.JSONWriter;
import com.xrkc.core.utils.PersonUtils;
import com.xrkc.job.domain.DeviceCardSender;
import com.xrkc.job.service.HttpFaceService;
import com.xrkc.redis.service.RedisService;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/HttpFaceServiceImpl.class */
public class HttpFaceServiceImpl implements HttpFaceService {

    @Autowired
    private HttpClientService httpClientService;

    @Autowired
    private RedisService redisService;
    private static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    private static final Logger log = LogManager.getLogger((Class<?>) HttpFaceServiceImpl.class);
    public static JSONObject lastSuccessFaceDevice = new JSONObject();

    @Override // com.xrkc.job.service.HttpFaceService
    public String deviceLogin(DeviceCardSender entity) {
        JSONObject params = new JSONObject();
        params.put("userName", entity.getUserName());
        params.put("password", entity.getPassword());
        JSONObject json = sendLink(entity.getFaceUrl() + "/deviceLogin", params);
        if (Objects.isNull(json)) {
            return "设备连接失败，请重试或检查配置";
        }
        int resultCode = json.getIntValue("result");
        if (resultCode == 0) {
            log.info("登录人脸机成功:{}", entity.getFaceUrl());
            return "success";
        }
        log.info("登录人脸机失败:{}", entity.getFaceUrl());
        return "登录失败，" + json.getString("message");
    }

    @Override // com.xrkc.job.service.HttpFaceService
    public JSONObject getDeviceParameter(DeviceCardSender entity) {
        JSONObject result = new JSONObject();
        result.put("code", 201);
        JSONObject params = new JSONObject();
        params.put("password", entity.getPassword());
        JSONObject json = sendLink(entity.getFaceUrl() + "/getDeviceParameter", params);
        if (Objects.isNull(json)) {
            result.put("msg", "设备连接失败，请重试或检查配置");
            return result;
        }
        //todo
      //  int resultCode = json.getIntValue(CacheOperationExpressionEvaluator.RESULT_VARIABLE);
        int resultCode =0;
        if (resultCode == 0) {
            log.info("人脸机设备参数详情,url:{},详情：{}", entity.getFaceUrl(), json.toString());
            result.put("code", 200);
            String data = json.getString("data");
            data.replaceAll("\n", "");
            data.replaceAll("\t", "");
            data.replaceAll("\\\\", "");
            JSONObject dataJson = JSONObject.parseObject(data);
            JSONObject resultData = new JSONObject();
            resultData.put("platformEnable", dataJson.getString("platformEnable"));
            resultData.put("platformIp", dataJson.getString("platformIp"));
            resultData.put("heartBeatEnable", dataJson.getString("heartBeatEnable"));
            resultData.put("heartBeatIp", dataJson.getString("heartBeatIp"));
            resultData.put("faceMacAddr", dataJson.getString("Mac_addr"));
            resultData.put("faceSn", dataJson.getString("SN"));
            result.put("data", resultData);
            return result;
        }
        result.put("msg", json.getString("message"));
        return result;
    }

    @Override // com.xrkc.job.service.HttpFaceService
    public JSONObject setDeviceParameter(String faceUrl, JSONObject params) {
        return sendLink(faceUrl + "/setDeviceParameter", params);
    }

    @Override // com.xrkc.job.service.HttpFaceService
    public List<String> getAllDeviceIdWhiteList(DeviceCardSender entity) {
        List<String> result = new ArrayList<>();
        JSONObject params = new JSONObject();
        params.put("password", entity.getPassword());
        JSONObject json = sendLink(entity.getFaceUrl() + "/getAllDeviceIdWhiteList", params);
        if (Objects.isNull(json)) {
            log.error("msg:{},url:{}", "设备连接失败，请重试或检查配置", entity.getFaceUrl());
            return result;
        }
        int resultCode = json.getIntValue("result");
        if (resultCode == 0) {
            String data = json.getString("data");
            data.replaceAll("\n", "");
            data.replaceAll("\t", "");
            data.replaceAll("\\\\", "");
            try {
                JSONObject dataJson = JSONObject.parseObject(data);
                JSONArray array = dataJson.getJSONArray("idList");
                if (Objects.nonNull(array)) {
                    log.info("【人脸机】白名单数：{}，url：{}，所有白名单idList:{}", Integer.valueOf(array.size()), entity.getFaceUrl(), array);
                    return array.toJavaList(String.class, new JSONReader.Feature[0]);
                }
            } catch (Exception e) {
                log.info("解析获取白名单列表失败：{}", e.getMessage());
            }
        }
        return result;
    }

    @Override // com.xrkc.job.service.HttpFaceService
    public void deleteDeviceWhiteList(DeviceCardSender entity, List<String> delWhiteList) {
        if (CollectionUtils.isEmpty(delWhiteList)) {
            return;
        }
        delWhiteList.forEach(employeeNumber -> {
            log.info("【人脸机】准备删除白名单：{}", employeeNumber);
            JSONObject params = new JSONObject();
            params.put("password", entity.getPassword());
            JSONObject data = new JSONObject();
            data.put("employee_number", employeeNumber);
            data.put("usertype", "white");
            params.put("data", data);
            JSONObject json = sendLink(entity.getFaceUrl() + "/deleteDeviceWhiteList", params);
            if (Objects.isNull(json)) {
                return;
            }
            int resultCode = json.getIntValue(CacheOperationExpressionEvaluator.RESULT_VARIABLE);
            if (resultCode == 0) {
                log.info("删除人脸机白名单成功：{}", params.toJSONString(new JSONWriter.Feature[0]));
            } else {
                log.info("删除人脸机白名单失败：{},json:{}", params.toJSONString(new JSONWriter.Feature[0]), json.toJSONString(new JSONWriter.Feature[0]));
            }
        });
    }

    @Override // com.xrkc.job.service.HttpFaceService
    public void deleteDeviceAllWhiteList(DeviceCardSender entity) {
        log.info("【人脸机】准备删除所有白名单：{}", JSONObject.toJSONString(entity, new JSONWriter.Feature[0]));
        JSONObject params = new JSONObject();
        params.put("password", entity.getPassword());
        JSONObject json = sendLink(entity.getFaceUrl() + "/deleteDeviceAllWhiteList", params);
        if (Objects.isNull(json)) {
            return;
        }
        int resultCode = json.getIntValue(CacheOperationExpressionEvaluator.RESULT_VARIABLE);
        if (resultCode == 0) {
            log.info("删除人脸机所有白名单成功:{}", entity.getFaceUrl());
            String key = PersonUtils.getWhitePersonKey(entity.getFaceSn());
            Set<String> keyList = this.redisService.keys(key + "*");
            log.info("{}需要清理redis人员白名单列表为:{}", key, keyList);
            RedisService redisService = this.redisService;
            redisService.getClass();
            keyList.forEach(redisService::deleteObject);
            this.redisService.deleteObject(key);
            return;
        }
        log.error("删除人脸机所有白名单失败:{},json:{},url:{}", params.toJSONString(new JSONWriter.Feature[0]), json.toJSONString(new JSONWriter.Feature[0]), entity.getFaceUrl());
    }

    @Override // com.xrkc.job.service.HttpFaceService
    public void setDeviceTime(String faceUrl, String password) {
        try {
            JSONObject params = new JSONObject();
            params.put("password", password);
            JSONObject data = new JSONObject();
            data.put("time", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            params.put("data", data);
            JSONObject json = sendLink(faceUrl + "/setDeviceTime", params);
            log.error("[人脸机]同步时间结果：{}", json);
        } catch (Exception e) {
            log.error("[人脸机]同步时间失败");
        }
    }

    @Override // com.xrkc.job.service.HttpFaceService
    public void setErrImageRedis(String employeeNumber, String personPhoto) {
        String errImageKey = PersonUtils.getErrImageKey(employeeNumber);
        log.info("[人脸机]图片异常，无法被下发到设备，key:{},value:{}", errImageKey, personPhoto);
        this.redisService.set(errImageKey, personPhoto, PersonUtils.getRedisTimeSecond(3));
    }

    @Override // com.xrkc.job.service.HttpFaceService
    @Deprecated
    public String getCheckFaceResult(Long personId, String realName, String base64) {
        String checkFace;
        JSONObject lastSuccessFaceDevice2 = lastSuccessFaceDevice;
        if (StringUtils.isBlank(lastSuccessFaceDevice2.getString("url"))) {
            return "X";
        }
        JSONObject checkFaceJson = checkFacePicture2(lastSuccessFaceDevice2.getString("url"), lastSuccessFaceDevice2.getString("password"), base64);
        int code = checkFaceJson.getIntValue("code");
        String msg = checkFaceJson.getString("msg");
        if (code == 200) {
            checkFace = "Y";
        } else if (code == 204) {
            log.info("本次暂不验证，personId:{},realName:{},msg：{}", personId, realName, msg);
            checkFace = "X";
        } else {
            checkFace = "N";
            log.info("【人脸机】校验人脸图片失败：personId:{},name:{}，msg:{}", personId, realName, msg);
        }
        return checkFace;
    }

    @Override // com.xrkc.job.service.HttpFaceService
    public JSONObject checkFacePicture2(String faceUrl, String password, String imgBase64) {
        JSONObject result = new JSONObject();
        JSONObject params = new JSONObject();
        params.put("password", password);
        JSONObject data = new JSONObject();
        data.put("register_base64", imgBase64);
        params.put("data", data);
        JSONObject json = sendLink(faceUrl + "/checkFacePicture", params);
        if (Objects.isNull(json)) {
            result.put("code", 204);
            result.put("msg", "连接人脸机网络失败：" + faceUrl);
            return result;
        }
        int resultCode = json.getIntValue(CacheOperationExpressionEvaluator.RESULT_VARIABLE);
        if (resultCode == 0) {
            result.put("code", 200);
            result.put("msg", "ok");
            return result;
        }
        result.put("code", Integer.valueOf(resultCode));
        result.put("msg", json.getString("message") + "：" + faceUrl);
        log.info("图片质量校验不通过：url:{},result:{}", faceUrl, json.toString());
        return result;
    }

    @Override // com.xrkc.job.service.HttpFaceService
    public String syncPersonToFace(DeviceCardSender entity, int total, int current, JSONObject data, String employeeNumber, String personPhoto, String realName, String key, String info) {
        JSONObject params = new JSONObject();
        params.put("password", entity.getPassword());
        params.put("totalnum", Integer.valueOf(total));
        params.put("currentnum", Integer.valueOf(current));
        params.put("data", data);
        JSONObject json = sendLink(entity.getFaceUrl() + "/addDeviceWhiteList", params);
        if (Objects.isNull(json)) {
            log.info("[人脸机]添加白名单失败，网络错误：url:{},key:{}", entity.getFaceUrl(), key);
            return "设备连接失败，请重试或检查配置";
        }
        int resultCode = json.getIntValue(CacheOperationExpressionEvaluator.RESULT_VARIABLE);
        if (resultCode == 0) {
            this.redisService.set(key, personPhoto, PersonUtils.getRedisTimeSecond(30));
            log.info("[人脸机]添加白名单成功,{},url:{}", info, entity.getFaceUrl());
            return "success";
        }
        if (resultCode == 1 && "算法录入失败".equals(json.getString("message"))) {
            setErrImageRedis(employeeNumber, personPhoto);
        }
        log.info("[人脸机]添加白名单失败,{},result：{},url:{},params:{}", info, json.toJSONString(new JSONWriter.Feature[0]), entity.getFaceUrl(), params.toString());
        return "添加名单失败，" + json.getString("message");
    }

    public static void main(String[] args) {
        String url = substrUrl("http://192.168.0.141:8091/checkFacePicture");
        System.out.println(url);
    }

    private static String substrUrl(String url) {
        String result = null;
        int count = 0;
        int iIndexOf = url.indexOf(47);
        while (true) {
            int index = iIndexOf;
            if (index == -1) {
                break;
            }
            count++;
            if (count == 3) {
                result = url.substring(0, index);
                break;
            }
            iIndexOf = url.indexOf(47, index + 1);
        }
        return result;
    }

    private JSONObject sendLink(String url, JSONObject params) {
        try {
            String result = this.httpClientService.doPostWithCharset(url, params.toJSONString(new JSONWriter.Feature[0]), "UTF-8");
            JSONObject json = JSONObject.parseObject(result);
            if (StringUtils.isNotBlank(params.getString("password"))) {
                lastSuccessFaceDevice.put("url", substrUrl(url));
                lastSuccessFaceDevice.put("password", params.getString("password"));
            }
            return json;
        } catch (Exception e) {
            log.info("【人脸机】请求失败,url:{}，msg:{}", url, e.getMessage());
            return null;
        }
    }
}
