package com.xrkc.job.vo;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/vo/PushCardSenderAimsVO.class */
public class PushCardSenderAimsVO {
    private Integer deviceAims;
    private List<PushCardSenderNumVO> numVOList;
    public void setDeviceAims(Integer deviceAims) {
        this.deviceAims = deviceAims;
    }
    public void setNumVOList(List<PushCardSenderNumVO> numVOList) {
        this.numVOList = numVOList;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof PushCardSenderAimsVO)) {
            return false;
        }
        PushCardSenderAimsVO other = (PushCardSenderAimsVO) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$deviceAims = getDeviceAims();
        Object other$deviceAims = other.getDeviceAims();
        if (this$deviceAims == null) {
            if (other$deviceAims != null) {
                return false;
            }
        } else if (!this$deviceAims.equals(other$deviceAims)) {
            return false;
        }
        Object this$numVOList = getNumVOList();
        Object other$numVOList = other.getNumVOList();
        return this$numVOList == null ? other$numVOList == null : this$numVOList.equals(other$numVOList);
    }
    protected boolean canEqual(Object other) {
        return other instanceof PushCardSenderAimsVO;
    }
    public int hashCode() {
        Object $deviceAims = getDeviceAims();
        int result = (1 * 59) + ($deviceAims == null ? 43 : $deviceAims.hashCode());
        Object $numVOList = getNumVOList();
        return (result * 59) + ($numVOList == null ? 43 : $numVOList.hashCode());
    }
    public String toString() {
        return "PushCardSenderAimsVO(deviceAims=" + getDeviceAims() + ", numVOList=" + getNumVOList() + StringPool.RIGHT_BRACKET;
    }
    public Integer getDeviceAims() {
        return this.deviceAims;
    }
    public List<PushCardSenderNumVO> getNumVOList() {
        return this.numVOList;
    }
}
