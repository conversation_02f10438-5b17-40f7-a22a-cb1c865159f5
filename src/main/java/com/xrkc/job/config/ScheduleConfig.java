package com.xrkc.job.config;
import java.util.Properties;
import javax.sql.DataSource;
import org.quartz.impl.StdSchedulerFactory;
import org.quartz.impl.jdbcjobstore.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
@Configuration
@ConditionalOnProperty(value = {"cluster.job.enable"}, havingValue = "true")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/config/ScheduleConfig.class */
public class ScheduleConfig {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) ScheduleConfig.class);
    @Bean
    public SchedulerFactoryBean schedulerFactoryBean(DataSource dataSource) {
        log.info("开启了job集群，请注意数据库是否有qrtz_相关表");
        SchedulerFactoryBean factory = new SchedulerFactoryBean();
        factory.setDataSource(dataSource);
        Properties prop = new Properties();
        prop.put(StdSchedulerFactory.PROP_SCHED_INSTANCE_NAME, "XrkcScheduler");
        prop.put("org.quartz.scheduler.instanceId", StdSchedulerFactory.AUTO_GENERATE_INSTANCE_ID);
        prop.put(StdSchedulerFactory.PROP_THREAD_POOL_CLASS, "org.quartz.simpl.SimpleThreadPool");
        prop.put(SchedulerFactoryBean.PROP_THREAD_COUNT, "20");
        prop.put("org.quartz.threadPool.threadPriority", "5");
        prop.put(StdSchedulerFactory.PROP_JOB_STORE_CLASS, "org.springframework.scheduling.quartz.LocalDataSourceJobStore");
        prop.put("org.quartz.jobStore.isClustered", "true");
        prop.put("org.quartz.jobStore.clusterCheckinInterval", "15000");
        prop.put("org.quartz.jobStore.maxMisfiresToHandleAtATime", "10");
        prop.put("org.quartz.jobStore.txIsolationLevelSerializable", "true");
        prop.put("org.quartz.jobStore.misfireThreshold", "12000");
        prop.put("org.quartz.jobStore.tablePrefix", Constants.DEFAULT_TABLE_PREFIX);
        factory.setQuartzProperties(prop);
        factory.setSchedulerName("XrkcScheduler");
        factory.setStartupDelay(1);
        factory.setApplicationContextSchedulerContextKey("applicationContextKey");
        factory.setOverwriteExistingJobs(true);
        factory.setAutoStartup(true);
        return factory;
    }
}
