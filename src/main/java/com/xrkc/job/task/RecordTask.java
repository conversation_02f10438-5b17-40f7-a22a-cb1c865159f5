package com.xrkc.job.task;
import com.xrkc.job.domain.Attendance;
import com.xrkc.job.service.IAttendanceService;
import com.xrkc.job.service.ISystemConfigService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
@Component("recordTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/RecordTask.class */
public class RecordTask {
    @Autowired
    private ISystemConfigService systemConfigService;
    @Autowired
    private IAttendanceService attendanceService;
    private static final String CONFIG_KEY_ON_DUTY_BEACONS = "on_duty_beacons";
    private static final String CONFIG_KEY_OFF_DUTY_BEACONS = "off_duty_beacons";
    private static final String CONFIG_KEY_ATTENDANCE_VALID_DISTANCE = "attendance_valid_distance";
    public void insertAttendance() {
        Map<String, String> config = this.systemConfigService.getConfig();
        String on_duty_beacons = config.get(CONFIG_KEY_ON_DUTY_BEACONS);
        String off_duty_beacons = config.get(CONFIG_KEY_OFF_DUTY_BEACONS);
        String attendance_valid_distance = config.get(CONFIG_KEY_ATTENDANCE_VALID_DISTANCE);
        int valid_distance = 20;
        if (StringUtils.isNotBlank(attendance_valid_distance)) {
            valid_distance = Integer.valueOf(attendance_valid_distance).intValue();
        }
        if (StringUtils.isNotBlank(on_duty_beacons)) {
            generate("1", (List) Arrays.stream(on_duty_beacons.split(",")).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList()), Integer.valueOf(valid_distance));
        }
        if (StringUtils.isNotBlank(off_duty_beacons)) {
            generate("2", (List) Arrays.stream(off_duty_beacons.split(",")).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList()), Integer.valueOf(valid_distance));
        }
    }
    private void generate(String attendanceType, List<Integer> beaconIds, Integer validDistance) {
        List<Attendance> attendanceList = this.attendanceService.queryNotAttendanceList(attendanceType);
        if (!CollectionUtils.isEmpty(attendanceList)) {
            Object cardIds = (List) attendanceList.stream().map((v0) -> {
                return v0.getCardId();
            }).collect(Collectors.toList());
            Map<String, Object> map = new HashMap<>();
            map.put("cardIds", cardIds);
            map.put("beaconIds", beaconIds);
            map.put("validDistance", validDistance);
            List<Attendance> positionDataList = this.attendanceService.selectPositionList(map);
            List<Attendance> insertList = new ArrayList<>();
            attendanceList.stream().forEach(attendance -> {
                positionDataList.stream().filter(f -> {
                    return attendance.getCardId().compareTo(f.getCardId()) == 0;
                }).findFirst().ifPresent(vo -> {
                    attendance.setBeaconId(vo.getBeaconId());
                    attendance.setAcceptTime(vo.getAcceptTime());
                    attendance.setAttendanceType(attendanceType);
                    insertList.add(attendance);
                });
            });
            this.attendanceService.batchInsert(insertList);
        }
    }
}
