package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.job.domain.SystemUser;
import com.xrkc.job.mapper.SystemUserMapper;
import com.xrkc.job.service.ISystemUserService;
import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/SystemUserServiceImpl.class */
public class SystemUserServiceImpl extends ServiceImpl<SystemUserMapper, SystemUser> implements ISystemUserService {
    @Autowired
    private SystemUserMapper systemUserMapper;
    @Override // com.xrkc.job.service.ISystemUserService
    public List<String> findAllUserName() {
        return this.systemUserMapper.findAllUserName();
    }
    @Override // com.xrkc.job.service.ISystemUserService
    public Long getPersonIdByUserId(Long userId) {
        return this.systemUserMapper.getPersonIdByUserId(userId);
    }
    @Override // com.xrkc.job.service.ISystemUserService
    public List<SystemUser> findByUserIds(Set<Long> pushUserIds) {
        return this.systemUserMapper.findByUserIds(pushUserIds);
    }
}
