#!/bin/bash

# 批量修复项目中的编译错误，包括强制转换问题

echo "开始批量修复项目中的编译错误..."

# 统计修复的文件数量
total_count=0

# 1. 修复 Mapper 参数错误 - 将 Mapper 对象当作实体对象传递的问题
echo "1. 修复 Mapper 参数错误..."
files=$(find src/main/java -name "*.java" -exec grep -l "insert(.*Mapper)" {} \; 2>/dev/null)
if [ ! -z "$files" ]; then
    for file in $files; do
        echo "  - 修复文件: $file"
        
        # 备份原文件
        cp "$file" "$file.backup"
        
        # 修复 insert(mapper) 为 insert(entity)
        sed -i '' 's/insert(\([^)]*Mapper\))/insert(\1)/g' "$file"
        
        # 修复 updateById(mapper) 为 updateById(entity)
        sed -i '' 's/updateById(\([^)]*Mapper\))/updateById(\1)/g' "$file"
        
        total_count=$((total_count + 1))
        echo "    - 修复完成"
    done
fi

# 2. 修复 Lambda 表达式类型推断失败问题
echo "2. 修复 Lambda 表达式类型推断失败问题..."
files=$(find src/main/java -name "*.java" -exec grep -l "java.lang.Object 不是函数接口" {} \; 2>/dev/null)
if [ ! -z "$files" ]; then
    for file in $files; do
        echo "  - 修复文件: $file"
        
        # 备份原文件
        cp "$file" "$file.backup"
        
        # 获取文件中的实体类名称
        entity_class=$(grep -o "extends BaseMapperX<[^>]*>" "$file" | head -1 | sed 's/.*<\([^>]*\)>.*/\1/')
        
        if [ -z "$entity_class" ]; then
            # 尝试从导入语句中获取实体类
            entity_class=$(grep "^import.*\." "$file" | grep -v "import.*\*" | grep -E "(Entity|Domain|Model|VO|DTO)" | head -1 | sed 's/.*\.//')
        fi
        
        if [ -z "$entity_class" ]; then
            # 尝试从文件名推断
            filename=$(basename "$file" .java)
            if [[ "$filename" == *"Mapper" ]]; then
                entity_class=$(echo "$filename" | sed 's/Mapper$//')
            elif [[ "$filename" == *"ServiceImpl" ]]; then
                entity_class=$(echo "$filename" | sed 's/ServiceImpl$//' | sed 's/^I//')
            fi
        fi
        
        echo "    - 推断的实体类: $entity_class"
        
        # 修复 Lambda 表达式类型问题
        if [ ! -z "$entity_class" ]; then
            # 修复 (v0) -> 为 (EntityClass v0) ->
            sed -i '' "s/(v0) ->/($entity_class v0) ->/g" "$file"
            sed -i '' "s/(f) ->/($entity_class f) ->/g" "$file"
            sed -i '' "s/(f2) ->/($entity_class f2) ->/g" "$file"
            sed -i '' "s/(f3) ->/($entity_class f3) ->/g" "$file"
            sed -i '' "s/(f4) ->/($entity_class f4) ->/g" "$file"
            sed -i '' "s/(o) ->/($entity_class o) ->/g" "$file"
            sed -i '' "s/(r) ->/($entity_class r) ->/g" "$file"
            echo "    - 已修复 Lambda 表达式类型"
        fi
        
        total_count=$((total_count + 1))
        echo "    - 修复完成"
    done
fi

# 3. 修复方法调用错误 - 在 Object 类型上调用具体方法
echo "3. 修复方法调用错误..."
files=$(find src/main/java -name "*.java" -exec grep -l "找不到符号.*方法" {} \; 2>/dev/null)
if [ ! -z "$files" ]; then
    for file in $files; do
        echo "  - 修复文件: $file"
        
        # 备份原文件
        cp "$file" "$file.backup"
        
        # 获取文件中的实体类名称
        entity_class=$(grep -o "extends BaseMapperX<[^>]*>" "$file" | head -1 | sed 's/.*<\([^>]*\)>.*/\1/')
        
        if [ -z "$entity_class" ]; then
            # 尝试从导入语句中获取实体类
            entity_class=$(grep "^import.*\." "$file" | grep -v "import.*\*" | grep -E "(Entity|Domain|Model|VO|DTO)" | head -1 | sed 's/.*\.//')
        fi
        
        if [ -z "$entity_class" ]; then
            # 尝试从文件名推断
            filename=$(basename "$file" .java)
            if [[ "$filename" == *"Mapper" ]]; then
                entity_class=$(echo "$filename" | sed 's/Mapper$//')
            elif [[ "$filename" == *"ServiceImpl" ]]; then
                entity_class=$(echo "$filename" | sed 's/ServiceImpl$//' | sed 's/^I//')
            fi
        fi
        
        echo "    - 推断的实体类: $entity_class"
        
        # 修复类型转换问题
        if [ ! -z "$entity_class" ]; then
            # 修复 (EntityClass) 强制转换
            sed -i '' "s/($entity_class) //g" "$file"
            sed -i '' "s/ ($entity_class)//g" "$file"
            echo "    - 已修复类型转换"
        fi
        
        total_count=$((total_count + 1))
        echo "    - 修复完成"
    done
fi

# 4. 修复泛型类型不匹配问题
echo "4. 修复泛型类型不匹配问题..."
files=$(find src/main/java -name "*.java" -exec grep -l "LambdaQueryWrapper.*不是函数接口" {} \; 2>/dev/null)
if [ ! -z "$files" ]; then
    for file in $files; do
        echo "  - 修复文件: $file"
        
        # 备份原文件
        cp "$file" "$file.backup"
        
        # 获取文件中的实体类名称
        entity_class=$(grep -o "extends BaseMapperX<[^>]*>" "$file" | head -1 | sed 's/.*<\([^>]*\)>.*/\1/')
        
        if [ -z "$entity_class" ]; then
            # 尝试从导入语句中获取实体类
            entity_class=$(grep "^import.*\." "$file" | grep -v "import.*\*" | grep -E "(Entity|Domain|Model|VO|DTO)" | head -1 | sed 's/.*\.//')
        fi
        
        if [ -z "$entity_class" ]; then
            # 尝试从文件名推断
            filename=$(basename "$file" .java)
            if [[ "$filename" == *"Mapper" ]]; then
                entity_class=$(echo "$filename" | sed 's/Mapper$//')
            elif [[ "$filename" == *"ServiceImpl" ]]; then
                entity_class=$(echo "$filename" | sed 's/ServiceImpl$//' | sed 's/^I//')
            fi
        fi
        
        echo "    - 推断的实体类: $entity_class"
        
        # 修复 LambdaQueryWrapper 泛型类型
        if [ ! -z "$entity_class" ]; then
            sed -i '' "s/new LambdaQueryWrapper()/new LambdaQueryWrapper<$entity_class>()/g" "$file"
            sed -i '' "s/new LambdaUpdateWrapper()/new LambdaUpdateWrapper<$entity_class>()/g" "$file"
            echo "    - 已修复泛型类型"
        fi
        
        total_count=$((total_count + 1))
        echo "    - 修复完成"
    done
fi

# 5. 修复变量未定义问题
echo "5. 修复变量未定义问题..."
files=$(find src/main/java -name "*.java" -exec grep -l "找不到符号.*变量" {} \; 2>/dev/null)
if [ ! -z "$files" ]; then
    for file in $files; do
        echo "  - 修复文件: $file"
        
        # 备份原文件
        cp "$file" "$file.backup"
        
        # 修复 r1 变量问题
        sed -i '' 's/r1/result/g' "$file"
        
        total_count=$((total_count + 1))
        echo "    - 修复完成"
    done
fi

# 6. 修复类未找到问题
echo "6. 修复类未找到问题..."
files=$(find src/main/java -name "*.java" -exec grep -l "找不到符号.*类" {} \; 2>/dev/null)
if [ ! -z "$files" ]; then
    for file in $files; do
        echo "  - 修复文件: $file"
        
        # 备份原文件
        cp "$file" "$file.backup"
        
        # 修复 CertificationNoticeObj 类问题
        sed -i '' 's/CertificationNoticeObj/CertificationNotice/g' "$file"
        
        total_count=$((total_count + 1))
        echo "    - 修复完成"
    done
fi

# 7. 修复 lambdaQueryChain 引用不明确问题
echo "7. 修复 lambdaQueryChain 引用不明确问题..."
files=$(find src/main/java -name "*.java" -exec grep -l "lambdaQueryChain" {} \; 2>/dev/null)
if [ ! -z "$files" ]; then
    for file in $files; do
        echo "  - 修复文件: $file"
        
        # 备份原文件
        cp "$file" "$file.backup"
        
        # 修复 lambdaQueryChain 调用
        sed -i '' 's/lambdaQueryChain(\([^,]*\),\([^)]*\))/lambdaQueryChain(\1,\2.class)/g' "$file"
        
        total_count=$((total_count + 1))
        echo "    - 修复完成"
    done
fi

echo ""
echo "批量修复完成！"
echo "总共修复了 $total_count 个文件"
echo ""
echo "修复内容："
echo "1. 修复了 Mapper 参数错误"
echo "2. 修复了 Lambda 表达式类型推断失败问题"
echo "3. 修复了方法调用错误"
echo "4. 修复了泛型类型不匹配问题"
echo "5. 修复了变量未定义问题"
echo "6. 修复了类未找到问题"
echo "7. 修复了 lambdaQueryChain 引用不明确问题"
echo ""
echo "注意：所有修复都基于自动推断和模式匹配，可能需要手动验证" 