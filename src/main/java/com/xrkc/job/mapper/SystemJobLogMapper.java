package com.xrkc.job.mapper;
import com.xrkc.core.mybatisplus.BaseMapperX;
import com.xrkc.job.domain.SystemJobLog;
import java.time.LocalDateTime;
import org.apache.ibatis.annotations.Param;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/SystemJobLogMapper.class */
public interface SystemJobLogMapper extends BaseMapperX<SystemJobLog> {
    Long findMaxId(@Param("minusMinutes") LocalDateTime localDateTime, @Param("jobStatus") String str);
    int delByMaxId(@Param("maxId") Long l, @Param("delRow") int i, @Param("jobStatus") String str);
    int truncateTable();
}
