package com.xrkc.job.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xrkc.core.domain.system.SystemRoleDept;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/SystemRoleDeptMapper.class */
public interface SystemRoleDeptMapper extends BaseMapper<SystemRoleDept> {
    List<Long> getDeptIdsByRoleId(@Param("roleId") Long l);
    List<Long> getDeptIdCadre(@Param("userId") Long l);
    List<Long> getDeptIdsSubdivision(@Param("parentIds") List<Long> list);
    List<String> getDeptNameByIds(@Param("deptIds") List<Long> list);
}
