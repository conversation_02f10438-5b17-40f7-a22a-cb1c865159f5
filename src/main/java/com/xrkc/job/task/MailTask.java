package com.xrkc.job.task;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.domain.system.SystemApi;
import com.xrkc.job.domain.DeviceStation;
import com.xrkc.job.service.IDeviceStationService;
import com.xrkc.job.service.ISystemLicenseService;
import com.xrkc.job.service.SystemApiService;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
@Component("mailTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/MailTask.class */
public class MailTask {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) MailTask.class);
    @Autowired
    private JavaMailSender javaMailSender;
    @Autowired
    private SystemApiService systemApiService;
    @Value("${spring.mail.username}")
    private String mailFrom;
    @Autowired
    private ISystemLicenseService systemLicenseService;
    @Autowired
    private IDeviceStationService deviceStationService;
    private static final String collectService = "collect-service";
    public void mailStationService() {
        SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_RECEIVE_EMAIL);
        if (Objects.isNull(systemApi) || StringUtils.isBlank(systemApi.getHost())) {
            log.warn("邮箱收件人未配置");
            return;
        }
        List<DeviceStation> deviceStationList = this.deviceStationService.selectList();
        if (CollectionUtils.isEmpty(deviceStationList)) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        List<DeviceStation> tipList = new ArrayList<>();
        long maxMinutes = 30;
        deviceStationList.stream().filter(f -> {
            return Objects.nonNull(f.getHeartTime());
        }).forEach(deviceStation -> {
            long minutes = Duration.between(deviceStation.getHeartTime(), now).toMinutes();
            if (minutes > maxMinutes) {
                tipList.add(deviceStation);
            }
        });
        if (CollectionUtils.isEmpty(tipList)) {
            return;
        }
        String project = StringUtils.isNotBlank(systemApi.getBasicTopic()) ? systemApi.getBasicTopic() : "定位系统";
        log.warn("【基站】可能离线监测");
        String subject = "【" + project + "】新锐科创V2基站监测";
        String text = "项目名：" + project + "；基站：" + ((String) tipList.stream().map((v0) -> {
            return v0.getHost();
        }).collect(Collectors.joining(","))) + ("；超30分钟没有人员经过了，可能离线，如果您收到此邮件，请您尽快完成相关核查工作");
        sendMail(subject, text, systemApi.getHost(), systemApi.getUserName(), systemApi.getPassword());
    }
    public void mailCollectService() {
        SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_RECEIVE_EMAIL);
        if (Objects.isNull(systemApi) || StringUtils.isBlank(systemApi.getHost())) {
            log.warn("邮箱收件人未配置");
            return;
        }
        Map<String, LocalDateTime> expireTimeMap = this.systemLicenseService.getConfig();
        if (CollectionUtils.isEmpty(expireTimeMap)) {
            log.warn("系统监测未配置");
            return;
        }
        LocalDateTime expireTime = expireTimeMap.get(collectService);
        if (Objects.isNull(expireTime)) {
            log.warn("到期时间未配置");
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        String project = StringUtils.isNotBlank(systemApi.getBasicTopic()) ? systemApi.getBasicTopic() : "定位系统";
        if (expireTime.isBefore(now)) {
            log.warn("【采集服务】授权到期提醒");
            String subject = "【" + project + "】新锐科创V2授权到期监测";
            String text = "【" + project + "】如果你收到此邮件，请尽快完成相关工作";
            sendMail(subject, text, systemApi.getHost(), systemApi.getUserName(), systemApi.getPassword());
            return;
        }
        long days = Duration.between(expireTime, now).toDays();
        if (days == 7) {
            log.warn("【采集服务】授权还有7天到期提醒");
            String subject2 = "【" + project + "】新锐科创V2授权7天到期监测";
            String text2 = "【" + project + "】如果您收到此邮件，请尽快完成相关工作";
            sendMail(subject2, text2, systemApi.getHost(), systemApi.getUserName(), systemApi.getPassword());
            return;
        }
        if (days == 3) {
            log.warn("【采集服务】授权还有3天到期提醒");
            String subject3 = "【" + project + "】新锐科创V2授权3天到期监测";
            String text3 = "【" + project + "】如果您收到此邮件，请尽快完成相关工作";
            sendMail(subject3, text3, systemApi.getHost(), systemApi.getUserName(), systemApi.getPassword());
        }
    }
    private void sendMail(String subject, String text, String to, String cc, String bcc) {
        SimpleMailMessage message = new SimpleMailMessage();
        message.setFrom(this.mailFrom);
        message.setTo(to);
        message.setSubject(subject);
        message.setText(text);
        if (StringUtils.isNotBlank(cc)) {
            message.setCc(cc);
        }
        if (StringUtils.isNotBlank(bcc)) {
            message.setBcc(bcc);
        }
        this.javaMailSender.send(message);
    }
}
