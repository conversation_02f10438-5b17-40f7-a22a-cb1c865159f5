package com.xrkc.job.service.impl;
import com.xrkc.job.domain.DeptStatisticsVo;
import com.xrkc.job.domain.SystemDict;
import com.xrkc.job.mapper.SystemDictMapper;
import com.xrkc.job.service.ISystemDictService;
import java.lang.invoke.SerializedLambda;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/SystemDictServiceImpl.class */
public class SystemDictServiceImpl implements ISystemDictService {
    @Autowired
    private SystemDictMapper systemDictMapper;
    @Override // com.xrkc.job.service.ISystemDictService
    public List<SystemDict> findDictType(String dictType) {
        return StringUtils.isNotBlank(dictType) ? this.systemDictMapper.findDictType(dictType) : new ArrayList();
    }
    @Override // com.xrkc.job.service.ISystemDictService
    public List<DeptStatisticsVo> selectCountDeptList() {
        return this.systemDictMapper.selectCountDeptList();
    }
    @Override // com.xrkc.job.service.ISystemDictService
    public Map<String, SystemDict> findValEntityMap(String dictType) {
        return (Map) this.systemDictMapper.selectList((v0) -> {
            return v0.getDictType();
        }, dictType).stream().filter(f -> {
            return StringUtils.isNotBlank(f.getDictValue());
        }).collect(Collectors.toMap((v0) -> {
            return v0.getDictValue();
        }, Function.identity()));
    }
}
