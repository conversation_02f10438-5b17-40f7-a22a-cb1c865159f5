package com.xrkc.job.mapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xrkc.core.mybatisplus.BaseMapperX;
import com.xrkc.job.domain.PositionDataCalc;
import com.xrkc.job.domain.PositionVO;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Param;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/PositionDataCalcMapper.class */
public interface PositionDataCalcMapper extends BaseMapperX<PositionDataCalc> {
    int updateByAcceptTimeAndCardId(PositionVO positionVO);
    Long findMaxId(@Param("minusMinutes") LocalDateTime localDateTime);
    int delByMaxId(@Param("maxId") Long l, @Param("delRow") int i);
    /* JADX WARN: Multi-variable type inference failed */
    default List<PositionDataCalc> selectListByBakTime(int bakSecond) {
        return selectList(( ( new LambdaQueryWrapper<PositionDataCalc>().eq((v0) -> {
            return v0.getPositionStatus();
        }, "1")).gt((v0) -> {
            return v0.getAcceptTime();
        }, LocalDateTime.now().minusSeconds(bakSecond))).orderByAsc((LambdaQueryWrapper) (v0) -> {
            return v0.getAcceptTime();
        }));
    }
    default PositionDataCalc selectByAcceptTimeAndCardId(LocalDateTime acceptTime, Long cardId) {
        return selectOne((v0) -> {
            return v0.getAcceptTime();
        }, acceptTime, (v0) -> {
            return v0.getCardId();
        }, cardId);
    }
}
