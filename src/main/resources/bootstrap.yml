# Spring
spring:
  application:
    # 应用名称
    name: monitor-job
  profiles:
    # 环境配置
    active: prod
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: localhost:8848
      config:
        # 配置中心地址
        server-addr: localhost:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs[0]:
          data-id: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          refresh: true
        max-retry: 10000
