package com.xrkc.job.service;
import com.xrkc.core.domain.system.SystemDept;
import java.util.List;
import java.util.Map;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/ISystemDeptService.class */
public interface ISystemDeptService {
    List<SystemDept> selectList();
    Long insertEntity(SystemDept systemDept);
    int updateEntity(SystemDept systemDept);
    int updatePrimaryId(Long l, List<Long> list);
    int deleteEntity(Long l);
    Map<Long, SystemDept> findDeptInfoMap();
}
