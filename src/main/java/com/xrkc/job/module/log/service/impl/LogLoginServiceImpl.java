package com.xrkc.job.module.log.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.core.domain.log.LogLogin;
import com.xrkc.job.module.log.mapper.LogLoginMapper;
import com.xrkc.job.module.log.service.LogLoginService;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/module/log/service/impl/LogLoginServiceImpl.class */
public class LogLoginServiceImpl extends ServiceImpl<LogLoginMapper, LogLogin> implements LogLoginService {
}
