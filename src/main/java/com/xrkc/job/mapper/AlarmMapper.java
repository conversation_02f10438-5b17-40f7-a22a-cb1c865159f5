package com.xrkc.job.mapper;
import com.xrkc.core.mybatisplus.BaseMapperX;
import com.xrkc.job.domain.Alarm;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.ibatis.annotations.Param;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/AlarmMapper.class */
public interface AlarmMapper extends BaseMapperX<Alarm> {
    List<Alarm> selectUnDisposedAlarm(@Param("intervalSecond") Integer num, @Param("seconds") LocalDateTime localDateTime);
    List<Alarm> statisticsByAlarm(Map<String, Object> map);
    List<Alarm> statisticsAlarmArea(Map<String, Object> map);
    List<Alarm> selectUnDisposedAlarmCard(List<Long> list);
    int existAlarmByParams(Map<String, Object> map);
    Alarm getAlarmIdByParams(Map<String, Object> map);
    List<Alarm> selectCustomList(Map<String, Object> map);
    List<Alarm> selectUnDisposedAlarmRail(LocalDateTime localDateTime);
    List<Alarm> selectUnDisposedAlarmSOS(LocalDateTime localDateTime);
    List<Long> selectDisposedAlarmId(List<Long> list);
    void closeAlarmEndTime(@Param("alarmType") String str, @Param("time") LocalDateTime localDateTime);
    void close10_20_AlarmEndTime(@Param("alarmType") String str, @Param("areaId") Long l, @Param("list") Set<Long> set, @Param("time") LocalDateTime localDateTime);
    void close10_20_AlarmEndTimeByAreaId(@Param("alarmType") String str, @Param("list") Set<Long> set, @Param("time") LocalDateTime localDateTime);
    Integer selectClose10_20_AlarmEndTimeByAreaId(@Param("alarmType") String str, @Param("list") Set<Long> set);
    void close50_AlarmEndTime(@Param("list") Set<Long> set, @Param("alarmType") String str, @Param("time") LocalDateTime localDateTime);
    Integer selectClose50_AlarmEndTime(@Param("list") Set<Long> set, @Param("alarmType") String str);
    void close50_KeepAlarmEndTime(@Param("list") Set<Long> set, @Param("alarmType") String str, @Param("time") LocalDateTime localDateTime);
    Integer selectClose50_KeepAlarmEndTime(@Param("list") Set<Long> set, @Param("alarmType") String str);
    void close40_AlarmEndTime(@Param("list") Set<Long> set, @Param("alarmType") String str, @Param("time") LocalDateTime localDateTime);
    void close3AlarmEndTimeByAreaId(@Param("alarmType") String str, @Param("list") Set<Long> set, @Param("time") LocalDateTime localDateTime);
    Integer select3AlarmEndTimeByAreaId(@Param("alarmType") String str, @Param("list") Set<Long> set);
    Set<Long> selectNoClose81AlarmEndTime();
    void close81AlarmEndTime(@Param("list") Set<Long> set, @Param("time") LocalDateTime localDateTime);
    void updateReturnCardStatus(@Param("list") List<Long> list);
    List<Long> selectClose80_AlarmEndTime(@Param("list") Set<Long> set, @Param("alarmType") String str);
    void close80AlarmEndTimeByPersonIds(@Param("alarmType") String str, @Param("list") List<Long> list, @Param("time") LocalDateTime localDateTime);
}
