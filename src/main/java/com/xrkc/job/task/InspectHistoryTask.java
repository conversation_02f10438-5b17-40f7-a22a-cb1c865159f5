package com.xrkc.job.task;
import com.xrkc.core.domain.inspect.InspectRecord;
import com.xrkc.job.service.IInspectRecordHistoryService;
import java.util.List;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
@Component("inspectHistoryTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/InspectHistoryTask.class */
public class InspectHistoryTask {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) InspectHistoryTask.class);
    @Autowired
    private IInspectRecordHistoryService inspectRecordHistoryService;
    @Transactional(rollbackFor = {Exception.class})
    public void saveInspectRecordHistory() {
        List<InspectRecord> list = this.inspectRecordHistoryService.selectCopyHistory();
        if (!CollectionUtils.isEmpty(list)) {
            int rows = this.inspectRecordHistoryService.batchSave(list);
            if (rows > 0) {
                List<Long> recordIds = (List) list.stream().map((v0) -> {
                    return v0.getRecordId();
                }).collect(Collectors.toList());
                this.inspectRecordHistoryService.batchDelRecord(recordIds);
            }
        }
    }
    public void delInspectRecordHistory() {
        List<Long> recordIds = this.inspectRecordHistoryService.findDelRecordIds();
        if (!CollectionUtils.isEmpty(recordIds)) {
            this.inspectRecordHistoryService.delHistory(recordIds);
            this.inspectRecordHistoryService.batchDelRecordLocation(recordIds);
            this.inspectRecordHistoryService.batchDelRecordItem(recordIds);
            this.inspectRecordHistoryService.batchDelRecordParam(recordIds);
        }
    }
}
