package com.xrkc.job.api.DTO;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.math.BigDecimal;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/api/DTO/VehicleCurrentRes.class */
public class VehicleCurrentRes {
    private Long carId;
    private Integer dir;
    private String exData;
    private Long heartTime;
    private BigDecimal lat;
    private BigDecimal lon;
    private Integer online;
    private Long pointTime;
    private String pointType;
    private Integer run;
    private Integer speed;
    public void setCarId(Long carId) {
        this.carId = carId;
    }
    public void setDir(Integer dir) {
        this.dir = dir;
    }
    public void setExData(String exData) {
        this.exData = exData;
    }
    public void setHeartTime(Long heartTime) {
        this.heartTime = heartTime;
    }
    public void setLat(BigDecimal lat) {
        this.lat = lat;
    }
    public void setLon(BigDecimal lon) {
        this.lon = lon;
    }
    public void setOnline(Integer online) {
        this.online = online;
    }
    public void setPointTime(Long pointTime) {
        this.pointTime = pointTime;
    }
    public void setPointType(String pointType) {
        this.pointType = pointType;
    }
    public void setRun(Integer run) {
        this.run = run;
    }
    public void setSpeed(Integer speed) {
        this.speed = speed;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof VehicleCurrentRes)) {
            return false;
        }
        VehicleCurrentRes other = (VehicleCurrentRes) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$carId = getCarId();
        Object other$carId = other.getCarId();
        if (this$carId == null) {
            if (other$carId != null) {
                return false;
            }
        } else if (!this$carId.equals(other$carId)) {
            return false;
        }
        Object this$dir = getDir();
        Object other$dir = other.getDir();
        if (this$dir == null) {
            if (other$dir != null) {
                return false;
            }
        } else if (!this$dir.equals(other$dir)) {
            return false;
        }
        Object this$heartTime = getHeartTime();
        Object other$heartTime = other.getHeartTime();
        if (this$heartTime == null) {
            if (other$heartTime != null) {
                return false;
            }
        } else if (!this$heartTime.equals(other$heartTime)) {
            return false;
        }
        Object this$online = getOnline();
        Object other$online = other.getOnline();
        if (this$online == null) {
            if (other$online != null) {
                return false;
            }
        } else if (!this$online.equals(other$online)) {
            return false;
        }
        Object this$pointTime = getPointTime();
        Object other$pointTime = other.getPointTime();
        if (this$pointTime == null) {
            if (other$pointTime != null) {
                return false;
            }
        } else if (!this$pointTime.equals(other$pointTime)) {
            return false;
        }
        Object this$run = getRun();
        Object other$run = other.getRun();
        if (this$run == null) {
            if (other$run != null) {
                return false;
            }
        } else if (!this$run.equals(other$run)) {
            return false;
        }
        Object this$speed = getSpeed();
        Object other$speed = other.getSpeed();
        if (this$speed == null) {
            if (other$speed != null) {
                return false;
            }
        } else if (!this$speed.equals(other$speed)) {
            return false;
        }
        Object this$exData = getExData();
        Object other$exData = other.getExData();
        if (this$exData == null) {
            if (other$exData != null) {
                return false;
            }
        } else if (!this$exData.equals(other$exData)) {
            return false;
        }
        Object this$lat = getLat();
        Object other$lat = other.getLat();
        if (this$lat == null) {
            if (other$lat != null) {
                return false;
            }
        } else if (!this$lat.equals(other$lat)) {
            return false;
        }
        Object this$lon = getLon();
        Object other$lon = other.getLon();
        if (this$lon == null) {
            if (other$lon != null) {
                return false;
            }
        } else if (!this$lon.equals(other$lon)) {
            return false;
        }
        Object this$pointType = getPointType();
        Object other$pointType = other.getPointType();
        return this$pointType == null ? other$pointType == null : this$pointType.equals(other$pointType);
    }
    protected boolean canEqual(Object other) {
        return other instanceof VehicleCurrentRes;
    }
    public int hashCode() {
        Object $carId = getCarId();
        int result = (1 * 59) + ($carId == null ? 43 : $carId.hashCode());
        Object $dir = getDir();
        int result2 = (result * 59) + ($dir == null ? 43 : $dir.hashCode());
        Object $heartTime = getHeartTime();
        int result3 = (result2 * 59) + ($heartTime == null ? 43 : $heartTime.hashCode());
        Object $online = getOnline();
        int result4 = (result3 * 59) + ($online == null ? 43 : $online.hashCode());
        Object $pointTime = getPointTime();
        int result5 = (result4 * 59) + ($pointTime == null ? 43 : $pointTime.hashCode());
        Object $run = getRun();
        int result6 = (result5 * 59) + ($run == null ? 43 : $run.hashCode());
        Object $speed = getSpeed();
        int result7 = (result6 * 59) + ($speed == null ? 43 : $speed.hashCode());
        Object $exData = getExData();
        int result8 = (result7 * 59) + ($exData == null ? 43 : $exData.hashCode());
        Object $lat = getLat();
        int result9 = (result8 * 59) + ($lat == null ? 43 : $lat.hashCode());
        Object $lon = getLon();
        int result10 = (result9 * 59) + ($lon == null ? 43 : $lon.hashCode());
        Object $pointType = getPointType();
        return (result10 * 59) + ($pointType == null ? 43 : $pointType.hashCode());
    }
    public String toString() {
        return "VehicleCurrentRes(carId=" + getCarId() + ", dir=" + getDir() + ", exData=" + getExData() + ", heartTime=" + getHeartTime() + ", lat=" + getLat() + ", lon=" + getLon() + ", online=" + getOnline() + ", pointTime=" + getPointTime() + ", pointType=" + getPointType() + ", run=" + getRun() + ", speed=" + getSpeed() + StringPool.RIGHT_BRACKET;
    }
    public Long getCarId() {
        return this.carId;
    }
    public Integer getDir() {
        return this.dir;
    }
    public String getExData() {
        return this.exData;
    }
    public Long getHeartTime() {
        return this.heartTime;
    }
    public BigDecimal getLat() {
        return this.lat;
    }
    public BigDecimal getLon() {
        return this.lon;
    }
    public Integer getOnline() {
        return this.online;
    }
    public Long getPointTime() {
        return this.pointTime;
    }
    public String getPointType() {
        return this.pointType;
    }
    public Integer getRun() {
        return this.run;
    }
    public Integer getSpeed() {
        return this.speed;
    }
}
