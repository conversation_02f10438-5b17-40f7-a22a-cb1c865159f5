package com.xrkc.job.domain;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/DangerContrastBo.class */
public class DangerContrastBo {
    private Integer dangerCount;
    private Integer dangerAllCount;
    public void setDangerCount(Integer dangerCount) {
        this.dangerCount = dangerCount;
    }
    public void setDangerAllCount(Integer dangerAllCount) {
        this.dangerAllCount = dangerAllCount;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof DangerContrastBo)) {
            return false;
        }
        DangerContrastBo other = (DangerContrastBo) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$dangerCount = getDangerCount();
        Object other$dangerCount = other.getDangerCount();
        if (this$dangerCount == null) {
            if (other$dangerCount != null) {
                return false;
            }
        } else if (!this$dangerCount.equals(other$dangerCount)) {
            return false;
        }
        Object this$dangerAllCount = getDangerAllCount();
        Object other$dangerAllCount = other.getDangerAllCount();
        return this$dangerAllCount == null ? other$dangerAllCount == null : this$dangerAllCount.equals(other$dangerAllCount);
    }
    protected boolean canEqual(Object other) {
        return other instanceof DangerContrastBo;
    }
    public int hashCode() {
        Object $dangerCount = getDangerCount();
        int result = (1 * 59) + ($dangerCount == null ? 43 : $dangerCount.hashCode());
        Object $dangerAllCount = getDangerAllCount();
        return (result * 59) + ($dangerAllCount == null ? 43 : $dangerAllCount.hashCode());
    }
    public String toString() {
        return "DangerContrastBo(dangerCount=" + getDangerCount() + ", dangerAllCount=" + getDangerAllCount() + StringPool.RIGHT_BRACKET;
    }
    public Integer getDangerCount() {
        return this.dangerCount;
    }
    public Integer getDangerAllCount() {
        return this.dangerAllCount;
    }
}
