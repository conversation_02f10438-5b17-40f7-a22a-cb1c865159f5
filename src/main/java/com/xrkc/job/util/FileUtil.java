package com.xrkc.job.util;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.exception.CustomException;
import com.xrkc.redis.utils.RedisCacheUtils;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/FileUtil.class */
public final class FileUtil {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) FileUtil.class);
    private static Set<String> errorUrl = new HashSet();
    public static String imgPathToBase64(String url, String source) throws IOException {
        if (!org.apache.commons.lang3.StringUtils.isNotBlank(url) || errorUrl.contains(url)) {
            return null;
        }
        String staticPath = RedisCacheUtils.getConfigRedisCache().get(ConfigValue.CONFIG_KEY_STATIC_PATH);
        if (org.apache.commons.lang3.StringUtils.isBlank(staticPath)) {
            log.error("请在系统中配置静态资源路径");
            return null;
        }
        String staticPath2 = staticPath + url;
        try {
            InputStream in = new FileInputStream(staticPath2);
            Throwable th = null;
            try {
                try {
                    byte[] data = new byte[in.available()];
                    in.read(data);
                    Base64.Encoder encoder = java.util.Base64.getEncoder();
                    String strEncodeToString = encoder.encodeToString(data);
                    if (in != null) {
                        if (0 != 0) {
                            try {
                                in.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            in.close();
                        }
                    }
                    return strEncodeToString;
                } finally {
                }
            } finally {
            }
        } catch (Exception e) {
            errorUrl.add(url);
            log.error("图转base64失败：url：{}，resourcePath:{},msg:{},来源：{}", url, staticPath2, e.getMessage(), source);
            return null;
        }
    }
    public static String saveBase64UpLoadFile(String base64, String dirPath, String filePrefix) throws IOException {
        String fileName = new StringBuffer().append(System.currentTimeMillis()).append(new SecureRandom().nextInt(10000)).append("_").append(filePrefix).append(".").append("jpg").toString();
        Map<String, String> configMap = RedisCacheUtils.getConfigRedisCache();
        String staticPath = configMap.get(ConfigValue.CONFIG_KEY_STATIC_PATH);
        File dir = new File(staticPath + dirPath);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        String fileAbsolutePath = staticPath + dirPath + fileName;
        log.info("上传文件的位置为：{}", fileAbsolutePath);
        File layerFile = new File(fileAbsolutePath);
        layerFile.createNewFile();
        try {
            FileOutputStream out = new FileOutputStream(layerFile);
            Throwable th = null;
            try {
                out.write(org.apache.commons.codec.binary.Base64.decodeBase64(base64));
                if (out != null) {
                    if (0 != 0) {
                        try {
                            out.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        out.close();
                    }
                }
                return dirPath + fileName;
            } finally {
            }
        } catch (Exception e) {
            log.error("处理异常：{}", e.getMessage());
            throw new CustomException("处理异常：" + e.getMessage());
        }
    }
    public static void main(String[] args) throws IOException {
        System.out.println(imgPathToBase64("D:/xinruikc_cloud/cloud/monitor/static/sender/sender16723633976013812.jpg", "test自测"));
    }
}
