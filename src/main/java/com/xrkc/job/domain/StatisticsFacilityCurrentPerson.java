package com.xrkc.job.domain;
import java.io.Serializable;
import java.time.LocalDateTime;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/StatisticsFacilityCurrentPerson.class */
public class StatisticsFacilityCurrentPerson implements Serializable {
    private static final long serialVersionUID = 1;
    private Long statisticsId;
    private String facilityName;
    private Long facilityId;
    private LocalDateTime statisticsTime;
    private Integer personCount;
    public Long getStatisticsId() {
        return this.statisticsId;
    }
    public void setStatisticsId(Long statisticsId) {
        this.statisticsId = statisticsId;
    }
    public String getFacilityName() {
        return this.facilityName;
    }
    public void setFacilityName(String facilityName) {
        this.facilityName = facilityName;
    }
    public LocalDateTime getStatisticsTime() {
        return this.statisticsTime;
    }
    public void setStatisticsTime(LocalDateTime statisticsTime) {
        this.statisticsTime = statisticsTime;
    }
    public Integer getPersonCount() {
        return this.personCount;
    }
    public void setPersonCount(Integer personCount) {
        this.personCount = personCount;
    }
    public Long getFacilityId() {
        return this.facilityId;
    }
    public void setFacilityId(Long facilityId) {
        this.facilityId = facilityId;
    }
}
