package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
import java.time.LocalDateTime;
@TableName(value = "record_attendance", autoResultMap = true)
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/Attendance.class */
public class Attendance implements Serializable {
    private static final long serialVersionUID = 1;
    private Long attendanceId;
    private String attendanceType;
    private LocalDateTime acceptTime;
    private Integer beaconId;
    private Long cardId;
    private String icCardId;
    private String realName;
    private String jobNumber;
    private String personType;
    private String personTypeName;
    private String staffType;
    private String staffTypeName;
    private Long deptId;
    private String deptName;
    private Long postId;
    private String postName;
    private Long contractorId;
    private String contractorName;
    private LocalDateTime createTime;
    public void setAttendanceId(Long attendanceId) {
        this.attendanceId = attendanceId;
    }
    public void setAttendanceType(String attendanceType) {
        this.attendanceType = attendanceType;
    }
    public void setAcceptTime(LocalDateTime acceptTime) {
        this.acceptTime = acceptTime;
    }
    public void setBeaconId(Integer beaconId) {
        this.beaconId = beaconId;
    }
    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }
    public void setIcCardId(String icCardId) {
        this.icCardId = icCardId;
    }
    public void setRealName(String realName) {
        this.realName = realName;
    }
    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }
    public void setPersonType(String personType) {
        this.personType = personType;
    }
    public void setPersonTypeName(String personTypeName) {
        this.personTypeName = personTypeName;
    }
    public void setStaffType(String staffType) {
        this.staffType = staffType;
    }
    public void setStaffTypeName(String staffTypeName) {
        this.staffTypeName = staffTypeName;
    }
    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    public void setPostId(Long postId) {
        this.postId = postId;
    }
    public void setPostName(String postName) {
        this.postName = postName;
    }
    public void setContractorId(Long contractorId) {
        this.contractorId = contractorId;
    }
    public void setContractorName(String contractorName) {
        this.contractorName = contractorName;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof Attendance)) {
            return false;
        }
        Attendance other = (Attendance) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$attendanceId = getAttendanceId();
        Object other$attendanceId = other.getAttendanceId();
        if (this$attendanceId == null) {
            if (other$attendanceId != null) {
                return false;
            }
        } else if (!this$attendanceId.equals(other$attendanceId)) {
            return false;
        }
        Object this$beaconId = getBeaconId();
        Object other$beaconId = other.getBeaconId();
        if (this$beaconId == null) {
            if (other$beaconId != null) {
                return false;
            }
        } else if (!this$beaconId.equals(other$beaconId)) {
            return false;
        }
        Object this$cardId = getCardId();
        Object other$cardId = other.getCardId();
        if (this$cardId == null) {
            if (other$cardId != null) {
                return false;
            }
        } else if (!this$cardId.equals(other$cardId)) {
            return false;
        }
        Object this$deptId = getDeptId();
        Object other$deptId = other.getDeptId();
        if (this$deptId == null) {
            if (other$deptId != null) {
                return false;
            }
        } else if (!this$deptId.equals(other$deptId)) {
            return false;
        }
        Object this$postId = getPostId();
        Object other$postId = other.getPostId();
        if (this$postId == null) {
            if (other$postId != null) {
                return false;
            }
        } else if (!this$postId.equals(other$postId)) {
            return false;
        }
        Object this$contractorId = getContractorId();
        Object other$contractorId = other.getContractorId();
        if (this$contractorId == null) {
            if (other$contractorId != null) {
                return false;
            }
        } else if (!this$contractorId.equals(other$contractorId)) {
            return false;
        }
        Object this$attendanceType = getAttendanceType();
        Object other$attendanceType = other.getAttendanceType();
        if (this$attendanceType == null) {
            if (other$attendanceType != null) {
                return false;
            }
        } else if (!this$attendanceType.equals(other$attendanceType)) {
            return false;
        }
        Object this$acceptTime = getAcceptTime();
        Object other$acceptTime = other.getAcceptTime();
        if (this$acceptTime == null) {
            if (other$acceptTime != null) {
                return false;
            }
        } else if (!this$acceptTime.equals(other$acceptTime)) {
            return false;
        }
        Object this$icCardId = getIcCardId();
        Object other$icCardId = other.getIcCardId();
        if (this$icCardId == null) {
            if (other$icCardId != null) {
                return false;
            }
        } else if (!this$icCardId.equals(other$icCardId)) {
            return false;
        }
        Object this$realName = getRealName();
        Object other$realName = other.getRealName();
        if (this$realName == null) {
            if (other$realName != null) {
                return false;
            }
        } else if (!this$realName.equals(other$realName)) {
            return false;
        }
        Object this$jobNumber = getJobNumber();
        Object other$jobNumber = other.getJobNumber();
        if (this$jobNumber == null) {
            if (other$jobNumber != null) {
                return false;
            }
        } else if (!this$jobNumber.equals(other$jobNumber)) {
            return false;
        }
        Object this$personType = getPersonType();
        Object other$personType = other.getPersonType();
        if (this$personType == null) {
            if (other$personType != null) {
                return false;
            }
        } else if (!this$personType.equals(other$personType)) {
            return false;
        }
        Object this$personTypeName = getPersonTypeName();
        Object other$personTypeName = other.getPersonTypeName();
        if (this$personTypeName == null) {
            if (other$personTypeName != null) {
                return false;
            }
        } else if (!this$personTypeName.equals(other$personTypeName)) {
            return false;
        }
        Object this$staffType = getStaffType();
        Object other$staffType = other.getStaffType();
        if (this$staffType == null) {
            if (other$staffType != null) {
                return false;
            }
        } else if (!this$staffType.equals(other$staffType)) {
            return false;
        }
        Object this$staffTypeName = getStaffTypeName();
        Object other$staffTypeName = other.getStaffTypeName();
        if (this$staffTypeName == null) {
            if (other$staffTypeName != null) {
                return false;
            }
        } else if (!this$staffTypeName.equals(other$staffTypeName)) {
            return false;
        }
        Object this$deptName = getDeptName();
        Object other$deptName = other.getDeptName();
        if (this$deptName == null) {
            if (other$deptName != null) {
                return false;
            }
        } else if (!this$deptName.equals(other$deptName)) {
            return false;
        }
        Object this$postName = getPostName();
        Object other$postName = other.getPostName();
        if (this$postName == null) {
            if (other$postName != null) {
                return false;
            }
        } else if (!this$postName.equals(other$postName)) {
            return false;
        }
        Object this$contractorName = getContractorName();
        Object other$contractorName = other.getContractorName();
        if (this$contractorName == null) {
            if (other$contractorName != null) {
                return false;
            }
        } else if (!this$contractorName.equals(other$contractorName)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        return this$createTime == null ? other$createTime == null : this$createTime.equals(other$createTime);
    }
    protected boolean canEqual(Object other) {
        return other instanceof Attendance;
    }
    public int hashCode() {
        Object $attendanceId = getAttendanceId();
        int result = (1 * 59) + ($attendanceId == null ? 43 : $attendanceId.hashCode());
        Object $beaconId = getBeaconId();
        int result2 = (result * 59) + ($beaconId == null ? 43 : $beaconId.hashCode());
        Object $cardId = getCardId();
        int result3 = (result2 * 59) + ($cardId == null ? 43 : $cardId.hashCode());
        Object $deptId = getDeptId();
        int result4 = (result3 * 59) + ($deptId == null ? 43 : $deptId.hashCode());
        Object $postId = getPostId();
        int result5 = (result4 * 59) + ($postId == null ? 43 : $postId.hashCode());
        Object $contractorId = getContractorId();
        int result6 = (result5 * 59) + ($contractorId == null ? 43 : $contractorId.hashCode());
        Object $attendanceType = getAttendanceType();
        int result7 = (result6 * 59) + ($attendanceType == null ? 43 : $attendanceType.hashCode());
        Object $acceptTime = getAcceptTime();
        int result8 = (result7 * 59) + ($acceptTime == null ? 43 : $acceptTime.hashCode());
        Object $icCardId = getIcCardId();
        int result9 = (result8 * 59) + ($icCardId == null ? 43 : $icCardId.hashCode());
        Object $realName = getRealName();
        int result10 = (result9 * 59) + ($realName == null ? 43 : $realName.hashCode());
        Object $jobNumber = getJobNumber();
        int result11 = (result10 * 59) + ($jobNumber == null ? 43 : $jobNumber.hashCode());
        Object $personType = getPersonType();
        int result12 = (result11 * 59) + ($personType == null ? 43 : $personType.hashCode());
        Object $personTypeName = getPersonTypeName();
        int result13 = (result12 * 59) + ($personTypeName == null ? 43 : $personTypeName.hashCode());
        Object $staffType = getStaffType();
        int result14 = (result13 * 59) + ($staffType == null ? 43 : $staffType.hashCode());
        Object $staffTypeName = getStaffTypeName();
        int result15 = (result14 * 59) + ($staffTypeName == null ? 43 : $staffTypeName.hashCode());
        Object $deptName = getDeptName();
        int result16 = (result15 * 59) + ($deptName == null ? 43 : $deptName.hashCode());
        Object $postName = getPostName();
        int result17 = (result16 * 59) + ($postName == null ? 43 : $postName.hashCode());
        Object $contractorName = getContractorName();
        int result18 = (result17 * 59) + ($contractorName == null ? 43 : $contractorName.hashCode());
        Object $createTime = getCreateTime();
        return (result18 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
    }
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/Attendance$AttendanceBuilder.class */
    public static class AttendanceBuilder {
        private Long attendanceId;
        private String attendanceType;
        private LocalDateTime acceptTime;
        private Integer beaconId;
        private Long cardId;
        private String icCardId;
        private String realName;
        private String jobNumber;
        private String personType;
        private String personTypeName;
        private String staffType;
        private String staffTypeName;
        private Long deptId;
        private String deptName;
        private Long postId;
        private String postName;
        private Long contractorId;
        private String contractorName;
        private LocalDateTime createTime;
        AttendanceBuilder() {
        }
        public AttendanceBuilder attendanceId(Long attendanceId) {
            this.attendanceId = attendanceId;
            return this;
        }
        public AttendanceBuilder attendanceType(String attendanceType) {
            this.attendanceType = attendanceType;
            return this;
        }
        public AttendanceBuilder acceptTime(LocalDateTime acceptTime) {
            this.acceptTime = acceptTime;
            return this;
        }
        public AttendanceBuilder beaconId(Integer beaconId) {
            this.beaconId = beaconId;
            return this;
        }
        public AttendanceBuilder cardId(Long cardId) {
            this.cardId = cardId;
            return this;
        }
        public AttendanceBuilder icCardId(String icCardId) {
            this.icCardId = icCardId;
            return this;
        }
        public AttendanceBuilder realName(String realName) {
            this.realName = realName;
            return this;
        }
        public AttendanceBuilder jobNumber(String jobNumber) {
            this.jobNumber = jobNumber;
            return this;
        }
        public AttendanceBuilder personType(String personType) {
            this.personType = personType;
            return this;
        }
        public AttendanceBuilder personTypeName(String personTypeName) {
            this.personTypeName = personTypeName;
            return this;
        }
        public AttendanceBuilder staffType(String staffType) {
            this.staffType = staffType;
            return this;
        }
        public AttendanceBuilder staffTypeName(String staffTypeName) {
            this.staffTypeName = staffTypeName;
            return this;
        }
        public AttendanceBuilder deptId(Long deptId) {
            this.deptId = deptId;
            return this;
        }
        public AttendanceBuilder deptName(String deptName) {
            this.deptName = deptName;
            return this;
        }
        public AttendanceBuilder postId(Long postId) {
            this.postId = postId;
            return this;
        }
        public AttendanceBuilder postName(String postName) {
            this.postName = postName;
            return this;
        }
        public AttendanceBuilder contractorId(Long contractorId) {
            this.contractorId = contractorId;
            return this;
        }
        public AttendanceBuilder contractorName(String contractorName) {
            this.contractorName = contractorName;
            return this;
        }
        public AttendanceBuilder createTime(LocalDateTime createTime) {
            this.createTime = createTime;
            return this;
        }
        public Attendance build() {
            return new Attendance(this.attendanceId, this.attendanceType, this.acceptTime, this.beaconId, this.cardId, this.icCardId, this.realName, this.jobNumber, this.personType, this.personTypeName, this.staffType, this.staffTypeName, this.deptId, this.deptName, this.postId, this.postName, this.contractorId, this.contractorName, this.createTime);
        }
        public String toString() {
            return "Attendance.AttendanceBuilder(attendanceId=" + this.attendanceId + ", attendanceType=" + this.attendanceType + ", acceptTime=" + this.acceptTime + ", beaconId=" + this.beaconId + ", cardId=" + this.cardId + ", icCardId=" + this.icCardId + ", realName=" + this.realName + ", jobNumber=" + this.jobNumber + ", personType=" + this.personType + ", personTypeName=" + this.personTypeName + ", staffType=" + this.staffType + ", staffTypeName=" + this.staffTypeName + ", deptId=" + this.deptId + ", deptName=" + this.deptName + ", postId=" + this.postId + ", postName=" + this.postName + ", contractorId=" + this.contractorId + ", contractorName=" + this.contractorName + ", createTime=" + this.createTime + StringPool.RIGHT_BRACKET;
        }
    }
    public String toString() {
        return "Attendance(super=" + super.toString() + ", attendanceId=" + getAttendanceId() + ", attendanceType=" + getAttendanceType() + ", acceptTime=" + getAcceptTime() + ", beaconId=" + getBeaconId() + ", cardId=" + getCardId() + ", icCardId=" + getIcCardId() + ", realName=" + getRealName() + ", jobNumber=" + getJobNumber() + ", personType=" + getPersonType() + ", personTypeName=" + getPersonTypeName() + ", staffType=" + getStaffType() + ", staffTypeName=" + getStaffTypeName() + ", deptId=" + getDeptId() + ", deptName=" + getDeptName() + ", postId=" + getPostId() + ", postName=" + getPostName() + ", contractorId=" + getContractorId() + ", contractorName=" + getContractorName() + ", createTime=" + getCreateTime() + StringPool.RIGHT_BRACKET;
    }
    public static AttendanceBuilder builder() {
        return new AttendanceBuilder();
    }
    public Attendance(Long attendanceId, String attendanceType, LocalDateTime acceptTime, Integer beaconId, Long cardId, String icCardId, String realName, String jobNumber, String personType, String personTypeName, String staffType, String staffTypeName, Long deptId, String deptName, Long postId, String postName, Long contractorId, String contractorName, LocalDateTime createTime) {
        this.attendanceId = attendanceId;
        this.attendanceType = attendanceType;
        this.acceptTime = acceptTime;
        this.beaconId = beaconId;
        this.cardId = cardId;
        this.icCardId = icCardId;
        this.realName = realName;
        this.jobNumber = jobNumber;
        this.personType = personType;
        this.personTypeName = personTypeName;
        this.staffType = staffType;
        this.staffTypeName = staffTypeName;
        this.deptId = deptId;
        this.deptName = deptName;
        this.postId = postId;
        this.postName = postName;
        this.contractorId = contractorId;
        this.contractorName = contractorName;
        this.createTime = createTime;
    }
    public Attendance() {
    }
    public Long getAttendanceId() {
        return this.attendanceId;
    }
    public String getAttendanceType() {
        return this.attendanceType;
    }
    public LocalDateTime getAcceptTime() {
        return this.acceptTime;
    }
    public Integer getBeaconId() {
        return this.beaconId;
    }
    public Long getCardId() {
        return this.cardId;
    }
    public String getIcCardId() {
        return this.icCardId;
    }
    public String getRealName() {
        return this.realName;
    }
    public String getJobNumber() {
        return this.jobNumber;
    }
    public String getPersonType() {
        return this.personType;
    }
    public String getPersonTypeName() {
        return this.personTypeName;
    }
    public String getStaffType() {
        return this.staffType;
    }
    public String getStaffTypeName() {
        return this.staffTypeName;
    }
    public Long getDeptId() {
        return this.deptId;
    }
    public String getDeptName() {
        return this.deptName;
    }
    public Long getPostId() {
        return this.postId;
    }
    public String getPostName() {
        return this.postName;
    }
    public Long getContractorId() {
        return this.contractorId;
    }
    public String getContractorName() {
        return this.contractorName;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
}
