package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
@TableName("record_facility_access")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/FacilityAccessRecord.class */
public class FacilityAccessRecord implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId("record_id")
    private Long recordId;
    private String statusId;
    private LocalDateTime acceptTime;
    private Long cardId;
    private Integer beaconId;
    private BigDecimal longitude;
    private BigDecimal latitude;
    private String layerId;
    private Integer layerHeight;
    private Long facilityId;
    private String facilityName;
    private String realName;
    private String jobNumber;
    private String personType;
    private String personTypeName;
    private String staffType;
    private String staffTypeName;
    private Long deptId;
    private String deptName;
    private Long postId;
    private String postName;
    private Long contractorId;
    private String contractorName;
    private LocalDateTime createTime;
    private Long personId;
    private LocalDateTime leaveTime;
    private Long staySecond;
    private LocalDateTime updateTime;
    public Long getRecordId() {
        return this.recordId;
    }
    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }
    public String getStatusId() {
        return this.statusId;
    }
    public void setStatusId(String statusId) {
        this.statusId = statusId;
    }
    public LocalDateTime getAcceptTime() {
        return this.acceptTime;
    }
    public void setAcceptTime(LocalDateTime acceptTime) {
        this.acceptTime = acceptTime;
    }
    public Long getCardId() {
        return this.cardId;
    }
    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }
    public Integer getBeaconId() {
        return this.beaconId;
    }
    public void setBeaconId(Integer beaconId) {
        this.beaconId = beaconId;
    }
    public BigDecimal getLongitude() {
        return this.longitude;
    }
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    public BigDecimal getLatitude() {
        return this.latitude;
    }
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
    public Long getFacilityId() {
        return this.facilityId;
    }
    public void setFacilityId(Long facilityId) {
        this.facilityId = facilityId;
    }
    public String getFacilityName() {
        return this.facilityName;
    }
    public void setFacilityName(String facilityName) {
        this.facilityName = facilityName;
    }
    public String getRealName() {
        return this.realName;
    }
    public void setRealName(String realName) {
        this.realName = realName;
    }
    public String getJobNumber() {
        return this.jobNumber;
    }
    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }
    public String getPersonType() {
        return this.personType;
    }
    public void setPersonType(String personType) {
        this.personType = personType;
    }
    public String getPersonTypeName() {
        return this.personTypeName;
    }
    public void setPersonTypeName(String personTypeName) {
        this.personTypeName = personTypeName;
    }
    public String getStaffType() {
        return this.staffType;
    }
    public void setStaffType(String staffType) {
        this.staffType = staffType;
    }
    public String getStaffTypeName() {
        return this.staffTypeName;
    }
    public void setStaffTypeName(String staffTypeName) {
        this.staffTypeName = staffTypeName;
    }
    public Long getDeptId() {
        return this.deptId;
    }
    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
    public String getDeptName() {
        return this.deptName;
    }
    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    public Long getPostId() {
        return this.postId;
    }
    public void setPostId(Long postId) {
        this.postId = postId;
    }
    public String getPostName() {
        return this.postName;
    }
    public void setPostName(String postName) {
        this.postName = postName;
    }
    public Long getContractorId() {
        return this.contractorId;
    }
    public void setContractorId(Long contractorId) {
        this.contractorId = contractorId;
    }
    public String getContractorName() {
        return this.contractorName;
    }
    public void setContractorName(String contractorName) {
        this.contractorName = contractorName;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public Integer getLayerHeight() {
        return this.layerHeight;
    }
    public void setLayerHeight(Integer layerHeight) {
        this.layerHeight = layerHeight;
    }
    public LocalDateTime getUpdateTime() {
        return this.updateTime;
    }
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    public Long getPersonId() {
        return this.personId;
    }
    public void setPersonId(Long personId) {
        this.personId = personId;
    }
    public LocalDateTime getLeaveTime() {
        return this.leaveTime;
    }
    public void setLeaveTime(LocalDateTime leaveTime) {
        this.leaveTime = leaveTime;
    }
    public Long getStaySecond() {
        return this.staySecond;
    }
    public void setStaySecond(Long staySecond) {
        this.staySecond = staySecond;
    }
}
