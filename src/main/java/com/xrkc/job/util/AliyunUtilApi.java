package com.xrkc.job.util;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.dysmsapi20170525.AsyncClient;
import com.aliyun.sdk.service.dysmsapi20170525.DefaultAsyncClientBuilder;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsResponse;
import com.xrkc.core.domain.system.SystemApi;
import com.xrkc.core.enums.NoticeType;
import com.xrkc.job.domain.AlarmNoticeRecord;
import com.xrkc.job.domain.LogSms;
import com.xrkc.job.mapper.LogSmsMapper;
import com.xrkc.job.service.IAlarmNoticeRecordPersonService;
import com.xrkc.job.service.IAlarmNoticeRecordService;
import com.xrkc.job.service.SystemApiService;
import com.xrkc.job.task.MessageTask;
import com.xrkc.job.util.sm4.SM4Utils;
import darabonba.core.client.ClientOverrideConfiguration;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import org.apache.http.ParseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
@Component
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/AliyunUtilApi.class */
public class AliyunUtilApi {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) AliyunUtilApi.class);
    @Autowired
    private SystemApiService systemApiService;
    @Autowired
    private IAlarmNoticeRecordService alarmNoticeRecordService;
    @Autowired
    private IAlarmNoticeRecordPersonService alarmNoticeRecordPersonService;
    @Autowired
    private LogSmsMapper messageMapper;
    public void sendBatchSms(SystemApi systemApi, AlarmNoticeRecord alarmNoticeRecord, String source) throws ParseException, NumberFormatException, IOException {
        String accessKeyId;
        String accessKeySecret;
        String signName;
        JSONObject templateParam;
        StringBuilder content;
        String templateCode;
        LogSms logSms;
        String sendResult;
        if (Objects.isNull(systemApi)) {
            log.error("未配置短信发送");
            return;
        }
        if (org.apache.commons.lang3.StringUtils.isAnyBlank(systemApi.getHost(), systemApi.getUserName(), systemApi.getPassword(), systemApi.getBasicTopic())) {
            log.error("短信配置缺少必要参数");
            return;
        }
        accessKeyId = "";
        accessKeySecret = "";
        if (!"HBSH".equals(systemApi.getApiOrgSn())) {
            accessKeyId = SM4Utils.decryptData_CBC(systemApi.getUserName());
            accessKeySecret = SM4Utils.decryptData_CBC(systemApi.getPassword());
            if (org.apache.commons.lang3.StringUtils.isAnyBlank(accessKeyId, accessKeySecret)) {
                log.error("短信密钥配置有误，请检查api配置");
                return;
            }
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(alarmNoticeRecord.getLayerId())) {
            alarmNoticeRecord.setLayerId("-");
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(alarmNoticeRecord.getAreaName())) {
            alarmNoticeRecord.setAreaName("-");
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(alarmNoticeRecord.getRealName())) {
            alarmNoticeRecord.setRealName("-");
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(alarmNoticeRecord.getDeptName())) {
            alarmNoticeRecord.setDeptName("-");
        }
        signName = systemApi.getHost();
        templateParam = new JSONObject();
        templateParam.put("alarmTypeName", alarmNoticeRecord.getAlarmTypeName());
        templateParam.put("layerId", alarmNoticeRecord.getLayerId());
        content = new StringBuilder("【").append(signName).append("】").append("报警类型：").append(alarmNoticeRecord.getAlarmTypeName()).append("；楼层：").append(alarmNoticeRecord.getLayerId());
        if (systemApi.getApiOrgSn().equals("HBSH")) {
            content = new StringBuilder("").append("报警类型：").append(alarmNoticeRecord.getAlarmTypeName()).append("；楼层：").append(alarmNoticeRecord.getLayerId());
        }
        if (!MessageTask.source.equals(source) || CommonUtil.areaAlarmType.contains(alarmNoticeRecord.getAlarmType())) {
            templateCode = systemApi.getBasicTopic().split(",")[1];
            templateParam.put("areaName", alarmNoticeRecord.getAreaName());
            content.append("；报警区域名：").append(alarmNoticeRecord.getAreaName());
        } else {
            templateCode = systemApi.getBasicTopic().split(",")[0];
            templateParam.put("realName", alarmNoticeRecord.getRealName());
            templateParam.put("deptName", alarmNoticeRecord.getDeptName());
            content.append("；姓名：").append(alarmNoticeRecord.getRealName());
            content.append("；部门：").append(alarmNoticeRecord.getDeptName());
        }
        content.append("；请及时处理");
        logSms = new LogSms();
        logSms.setAlarmId(alarmNoticeRecord.getAlarmId());
        logSms.setAlarmType(alarmNoticeRecord.getAlarmType());
        logSms.setAlarmTypeName(alarmNoticeRecord.getAlarmTypeName());
        logSms.setCreateTime(LocalDateTime.now());
        logSms.setPhoneNumbers(alarmNoticeRecord.getPhoneNumbers());
        logSms.setPhoneRealNames(alarmNoticeRecord.getPhoneRealNames());
        logSms.setContent(content.toString());
        logSms.setNoticeType(NoticeType.sms.getKey());
        logSms.setSource(source);
        this.messageMapper.insert(logSms);
        switch (systemApi.getApiOrgSn()) {
            case "HBSH":
                sendResult = new HttpPostXmlUtil().postXml(systemApi.getHost(), alarmNoticeRecord.getPhoneNumbers(), content.toString());
                break;
            default:
                sendResult = sendSms(accessKeyId, accessKeySecret, alarmNoticeRecord.getPhoneNumbers(), signName, templateCode, templateParam.toJSONString(new JSONWriter.Feature[0]), logSms);
                break;
        }
        if ("OK".equals(sendResult)) {
            alarmNoticeRecord.setRemark("OK");
        } else {
            alarmNoticeRecord.setRemark(sendResult);
        }
        alarmNoticeRecord.setNoticeStatic("50");
        this.alarmNoticeRecordService.updateById(alarmNoticeRecord);
        this.alarmNoticeRecordPersonService.updateFinishByRecordId(alarmNoticeRecord.getRecordId(), sendResult);
    }
    /* JADX WARN: Multi-variable type inference failed */
    private String sendSms(String accessKeyId, String accessKeySecret, String phoneNumbers, String signName, String templateCode, String templateParam, LogSms logSms) {
        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder().accessKeyId(accessKeyId).accessKeySecret(accessKeySecret).build());
        AsyncClient client = ((DefaultAsyncClientBuilder) ((DefaultAsyncClientBuilder) ((DefaultAsyncClientBuilder) AsyncClient.builder().region("cn-hangzhou")).credentialsProvider(provider)).overrideConfiguration(ClientOverrideConfiguration.create().setEndpointOverride("dysmsapi.aliyuncs.com"))).build();
        SendSmsRequest sendSmsRequest = SendSmsRequest.builder().phoneNumbers(phoneNumbers).signName(signName).templateCode(templateCode).templateParam(templateParam).build();
        CompletableFuture<SendSmsResponse> response = client.sendSms(sendSmsRequest);
        try {
            try {
                SendSmsResponse resp = response.get();
                log.info("发送短信结果：{}", JSONObject.toJSONString(resp, new JSONWriter.Feature[0]));
                logSms.setBizId(resp.getBody().getBizId());
                logSms.setBizMsg(resp.getBody().getMessage());
                logSms.setSendTime(LocalDateTime.now());
                if ("OK".equals(resp.getBody().getCode())) {
                    logSms.setResult("Y");
                    this.messageMapper.updateById(logSms);
                    client.close();
                    return "OK";
                }
                logSms.setResult("N");
                this.messageMapper.updateById(logSms);
                String message = resp.getBody().getMessage();
                client.close();
                return message;
            } catch (Exception e) {
                logSms.setResult("N");
                this.messageMapper.updateById(logSms);
                log.error(e.getMessage(), (Throwable) e);
                client.close();
                return "发送失败";
            }
        } catch (Throwable th) {
            client.close();
            throw th;
        }
    }
}
