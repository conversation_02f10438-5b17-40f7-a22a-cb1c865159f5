package com.xrkc.job.domain;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.time.LocalDateTime;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/PositionVO.class */
public class PositionVO {
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime acceptTime;
    private Integer beaconId;
    private Integer rssi;
    private Double distance;
    private Long cardId;
    private Integer stillStatus;
    private String cardType;
    private BigDecimal longitude;
    private BigDecimal latitude;
    private String layerId;
    private Integer layerHeight;
    private LocalDateTime createTime;
    private Long personId;
    private String personType;
    private String personAttribute;
    private String staffType;
    private String realName;
    private Long deptId;
    private Long postId;
    private String personPhoto;
    private String deptName;
    private String postName;
    private String jobNumber;
    private Long contractorId;
    private String contractorName;
    public LocalDateTime getAcceptTime() {
        return this.acceptTime;
    }
    public void setAcceptTime(LocalDateTime acceptTime) {
        this.acceptTime = acceptTime;
    }
    public Integer getBeaconId() {
        return this.beaconId;
    }
    public void setBeaconId(Integer beaconId) {
        this.beaconId = beaconId;
    }
    public Double getDistance() {
        return this.distance;
    }
    public void setDistance(Double distance) {
        this.distance = distance;
    }
    public Long getCardId() {
        return this.cardId;
    }
    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }
    public BigDecimal getLongitude() {
        return this.longitude;
    }
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    public BigDecimal getLatitude() {
        return this.latitude;
    }
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
    public Integer getLayerHeight() {
        return this.layerHeight;
    }
    public void setLayerHeight(Integer layerHeight) {
        this.layerHeight = layerHeight;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public Long getPersonId() {
        return this.personId;
    }
    public void setPersonId(Long personId) {
        this.personId = personId;
    }
    public String getPersonType() {
        return this.personType;
    }
    public void setPersonType(String personType) {
        this.personType = personType;
    }
    public String getStaffType() {
        return this.staffType;
    }
    public void setStaffType(String staffType) {
        this.staffType = staffType;
    }
    public String getRealName() {
        return this.realName;
    }
    public void setRealName(String realName) {
        this.realName = realName;
    }
    public Long getDeptId() {
        return this.deptId;
    }
    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
    public Long getPostId() {
        return this.postId;
    }
    public void setPostId(Long postId) {
        this.postId = postId;
    }
    public String getDeptName() {
        return this.deptName;
    }
    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    public String getPostName() {
        return this.postName;
    }
    public void setPostName(String postName) {
        this.postName = postName;
    }
    public String getJobNumber() {
        return this.jobNumber;
    }
    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }
    public Long getContractorId() {
        return this.contractorId;
    }
    public void setContractorId(Long contractorId) {
        this.contractorId = contractorId;
    }
    public String getContractorName() {
        return this.contractorName;
    }
    public void setContractorName(String contractorName) {
        this.contractorName = contractorName;
    }
    public String getPersonPhoto() {
        return this.personPhoto;
    }
    public void setPersonPhoto(String personPhoto) {
        this.personPhoto = personPhoto;
    }
    public String getPersonAttribute() {
        return this.personAttribute;
    }
    public void setPersonAttribute(String personAttribute) {
        this.personAttribute = personAttribute;
    }
    public String getCardType() {
        return this.cardType;
    }
    public void setCardType(String cardType) {
        this.cardType = cardType;
    }
    public Integer getStillStatus() {
        return this.stillStatus;
    }
    public void setStillStatus(Integer stillStatus) {
        this.stillStatus = stillStatus;
    }
    public Integer getRssi() {
        return this.rssi;
    }
    public void setRssi(Integer rssi) {
        this.rssi = rssi;
    }
}
