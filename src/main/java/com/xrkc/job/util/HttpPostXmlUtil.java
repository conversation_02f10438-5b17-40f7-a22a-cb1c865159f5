package com.xrkc.job.util;
import cn.hutool.core.util.StrUtil;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.http.HttpEntity;
import org.apache.http.ParseException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.xml.sax.SAXException;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/HttpPostXmlUtil.class */
public class HttpPostXmlUtil {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) HttpPostXmlUtil.class);
    public String postXml(String urlStr, String phoneNumber, String content) throws ParseException, NumberFormatException, IOException {
        String xml = getXmlInfo(phoneNumber, content);
        log.info("短信发送的内容为:{}", xml);
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpPost httpPost = new HttpPost(urlStr);
        httpPost.setHeader("Content-Type", "text/xml;charset=UTF-8");
        StringEntity entity = new StringEntity(xml, "UTF-8");
        httpPost.setEntity(entity);
        try {
            CloseableHttpResponse response = httpClient.execute((HttpUriRequest) httpPost);
            HttpEntity responseEntity = response.getEntity();
            String resp = EntityUtils.toString(responseEntity);
            log.info("短信发送的响应为:{}", resp);
            if (!StrUtil.isNotEmpty(resp)) {
                return "FAIL";
            }
            if (resp.contains("true")) {
                return "OK";
            }
            return "FAIL";
        } catch (IOException e) {
            log.error(e.getMessage(), (Throwable) e);
            return "FAIL";
        }
    }
    private String getXmlInfo(String phoneNumber, String content) {
        String xml = StrUtil.format("<?xml version=\"1.0\" encoding=\"utf-8\"?> <soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\"> <soap:Body> <SendMQ xmlns=\"http://www.chinsoft.com.cn/\"> <sender>华北石化汇报内容</sender> <phoneNumber>{}</phoneNumber> <content>{}</content> <sendTime>{}</sendTime> <queueName>短信平台待发队列</queueName> </SendMQ> </soap:Body> </soap:Envelope>", phoneNumber, content, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss")));
        return xml;
    }
    private static Map parseSoap(String soap) throws DocumentException, SAXException, IOException {
        Document doc = DocumentHelper.parseText(soap);
        Element root = doc.getRootElement();
        Map<String, Object> map = new HashMap<>();
        getCode(root, map);
        return map;
    }
    private static void getCode(Element root, Map<String, Object> map) {
        if (root.elements() != null) {
            List<Element> list = root.elements();
            for (Element e : list) {
                if (e.elements().size() > 0) {
                    getCode(e, map);
                }
                if (e.elements().size() == 0) {
                    map.put(e.getName(), e.getTextTrim());
                }
            }
        }
    }
}
