package com.xrkc.job.module.vehicle.mapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xrkc.core.domain.vehicle.VehicleAlarmSetting;
import com.xrkc.core.mybatisplus.BaseMapperX;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/module/vehicle/mapper/VehicleAlarmSettingMapper.class */
public interface VehicleAlarmSettingMapper extends BaseMapperX<VehicleAlarmSetting> {
    /* JADX WARN: Multi-variable type inference failed */
    default List<VehicleAlarmSetting> selectListIn(String enableStatus, List<String> vehicleAlarmTypeList) {
        return selectList((Wrapper) ((LambdaQueryWrapper) new LambdaQueryWrapper<VehicleAlarmSetting>().eq((v0) -> {
            return v0.getEnableStatus();
        }, enableStatus)).in((LambdaQueryWrapper) (v0) -> {
            return v0.getVehicleAlarmType();
        }, (Collection<?>) vehicleAlarmTypeList));
    }
    /* JADX WARN: Multi-variable type inference failed */
    default List<VehicleAlarmSetting> selectEnableSettingId() {
        return selectList((Wrapper) ((LambdaQueryWrapper) new LambdaQueryWrapper<VehicleAlarmSetting>().eq((v0) -> {
            return v0.getEnableStatus();
        }, "Y")).isNotNull((v0) -> {
            return v0.getAlarmNoticeId();
        }));
    }
}
