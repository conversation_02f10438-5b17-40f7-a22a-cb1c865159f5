package com.xrkc.job.util;
import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/JSONUtil.class */
public class JSONUtil {
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private JSONUtil() {
    }
    public static ObjectMapper getInstance() {
        return objectMapper;
    }
    public static String obj2json(Object obj) throws Exception {
        return objectMapper.writeValueAsString(obj);
    }
    public static <T> T json2pojo(String str, Class<T> cls) throws Exception {
        return (T) objectMapper.readValue(str, cls);
    }
    public static <T> Map<String, Object> json2map(String jsonStr) throws Exception {
        return (Map) objectMapper.readValue(jsonStr, Map.class);
    }
    /* renamed from: com.xrkc.job.util.JSONUtil$1 */
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/JSONUtil$1.class */
    static class AnonymousClass1<T> extends TypeReference<Map<String, T>> {
        AnonymousClass1() {
        }
    }
    public static <T> Map<String, T> json2map(String jsonStr, Class<T> clazz) throws Exception {
        Map<String, Map<String, Object>> map = (Map) objectMapper.readValue(jsonStr, new TypeReference<Map<String, T>>() { // from class: com.xrkc.job.util.JSONUtil.1
            void AnonymousClass1() {
            }
        });
        HashMap map2 = new HashMap();
        for (Map.Entry<String, Map<String, Object>> entry : map.entrySet()) {
            map2.put(entry.getKey(), map2pojo(entry.getValue(), clazz));
        }
        return map2;
    }
    /* renamed from: com.xrkc.job.util.JSONUtil$2 */
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/JSONUtil$2.class */
    static class AnonymousClass2<T> extends TypeReference<List<T>> {
        AnonymousClass2() {
        }
    }
    public static <T> List<T> json2list(String jsonArrayStr, Class<T> clazz) throws Exception {
        List<Map<String, Object>> list = (List) objectMapper.readValue(jsonArrayStr, new TypeReference<List<T>>() { // from class: com.xrkc.job.util.JSONUtil.2
            void AnonymousClass2() {
            }
        });
        ArrayList arrayList = new ArrayList();
        for (Map<String, Object> map : list) {
            arrayList.add(map2pojo(map, clazz));
        }
        return arrayList;
    }
    /* renamed from: com.xrkc.job.util.JSONUtil$3 */
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/JSONUtil$3.class */
    static class AnonymousClass3 extends TypeReference<List<Map<String, Object>>> {
        AnonymousClass3() {
        }
    }
    public static List<Map<String, Object>> json2list(String jsonArrayStr) throws IOException {
        List<Map<String, Object>> list = (List) objectMapper.readValue(jsonArrayStr, new TypeReference<List<Map<String, Object>>>() { // from class: com.xrkc.job.util.JSONUtil.3
            void AnonymousClass3() {
            }
        });
        return list;
    }
    public static <T> T map2pojo(Map map, Class<T> cls) {
        return (T) objectMapper.convertValue(map, cls);
    }
    public static List<String> json2listString(String json) {
        if (StringUtils.isNotBlank(json)) {
            return JSON.parseArray(json, String.class);
        }
        return new ArrayList();
    }
}
