package com.xrkc.job.domain;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.xrkc.core.utils.DateUtils;
import java.time.LocalDateTime;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/AccessRecordParmVO.class */
public class AccessRecordParmVO {
    private String personId;
    private String personName;
    private String personType;
    private String doorName;
    private String cardNo;
    private String picUri;
    private LocalDateTime deviceTime;
    private String readerIndexCode;
    private String readerName;
    private String uniqueValue;
    public void setPersonId(String personId) {
        this.personId = personId;
    }
    public void setPersonName(String personName) {
        this.personName = personName;
    }
    public void setPersonType(String personType) {
        this.personType = personType;
    }
    public void setDoorName(String doorName) {
        this.doorName = doorName;
    }
    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }
    public void setPicUri(String picUri) {
        this.picUri = picUri;
    }
    public void setDeviceTime(LocalDateTime deviceTime) {
        this.deviceTime = deviceTime;
    }
    public void setReaderIndexCode(String readerIndexCode) {
        this.readerIndexCode = readerIndexCode;
    }
    public void setReaderName(String readerName) {
        this.readerName = readerName;
    }
    public void setUniqueValue(String uniqueValue) {
        this.uniqueValue = uniqueValue;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof AccessRecordParmVO)) {
            return false;
        }
        AccessRecordParmVO other = (AccessRecordParmVO) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$personId = getPersonId();
        Object other$personId = other.getPersonId();
        if (this$personId == null) {
            if (other$personId != null) {
                return false;
            }
        } else if (!this$personId.equals(other$personId)) {
            return false;
        }
        Object this$personName = getPersonName();
        Object other$personName = other.getPersonName();
        if (this$personName == null) {
            if (other$personName != null) {
                return false;
            }
        } else if (!this$personName.equals(other$personName)) {
            return false;
        }
        Object this$personType = getPersonType();
        Object other$personType = other.getPersonType();
        if (this$personType == null) {
            if (other$personType != null) {
                return false;
            }
        } else if (!this$personType.equals(other$personType)) {
            return false;
        }
        Object this$doorName = getDoorName();
        Object other$doorName = other.getDoorName();
        if (this$doorName == null) {
            if (other$doorName != null) {
                return false;
            }
        } else if (!this$doorName.equals(other$doorName)) {
            return false;
        }
        Object this$cardNo = getCardNo();
        Object other$cardNo = other.getCardNo();
        if (this$cardNo == null) {
            if (other$cardNo != null) {
                return false;
            }
        } else if (!this$cardNo.equals(other$cardNo)) {
            return false;
        }
        Object this$picUri = getPicUri();
        Object other$picUri = other.getPicUri();
        if (this$picUri == null) {
            if (other$picUri != null) {
                return false;
            }
        } else if (!this$picUri.equals(other$picUri)) {
            return false;
        }
        Object this$deviceTime = getDeviceTime();
        Object other$deviceTime = other.getDeviceTime();
        if (this$deviceTime == null) {
            if (other$deviceTime != null) {
                return false;
            }
        } else if (!this$deviceTime.equals(other$deviceTime)) {
            return false;
        }
        Object this$readerIndexCode = getReaderIndexCode();
        Object other$readerIndexCode = other.getReaderIndexCode();
        if (this$readerIndexCode == null) {
            if (other$readerIndexCode != null) {
                return false;
            }
        } else if (!this$readerIndexCode.equals(other$readerIndexCode)) {
            return false;
        }
        Object this$readerName = getReaderName();
        Object other$readerName = other.getReaderName();
        if (this$readerName == null) {
            if (other$readerName != null) {
                return false;
            }
        } else if (!this$readerName.equals(other$readerName)) {
            return false;
        }
        Object this$uniqueValue = getUniqueValue();
        Object other$uniqueValue = other.getUniqueValue();
        return this$uniqueValue == null ? other$uniqueValue == null : this$uniqueValue.equals(other$uniqueValue);
    }
    protected boolean canEqual(Object other) {
        return other instanceof AccessRecordParmVO;
    }
    public int hashCode() {
        Object $personId = getPersonId();
        int result = (1 * 59) + ($personId == null ? 43 : $personId.hashCode());
        Object $personName = getPersonName();
        int result2 = (result * 59) + ($personName == null ? 43 : $personName.hashCode());
        Object $personType = getPersonType();
        int result3 = (result2 * 59) + ($personType == null ? 43 : $personType.hashCode());
        Object $doorName = getDoorName();
        int result4 = (result3 * 59) + ($doorName == null ? 43 : $doorName.hashCode());
        Object $cardNo = getCardNo();
        int result5 = (result4 * 59) + ($cardNo == null ? 43 : $cardNo.hashCode());
        Object $picUri = getPicUri();
        int result6 = (result5 * 59) + ($picUri == null ? 43 : $picUri.hashCode());
        Object $deviceTime = getDeviceTime();
        int result7 = (result6 * 59) + ($deviceTime == null ? 43 : $deviceTime.hashCode());
        Object $readerIndexCode = getReaderIndexCode();
        int result8 = (result7 * 59) + ($readerIndexCode == null ? 43 : $readerIndexCode.hashCode());
        Object $readerName = getReaderName();
        int result9 = (result8 * 59) + ($readerName == null ? 43 : $readerName.hashCode());
        Object $uniqueValue = getUniqueValue();
        return (result9 * 59) + ($uniqueValue == null ? 43 : $uniqueValue.hashCode());
    }
    public String toString() {
        return "AccessRecordParmVO(personId=" + getPersonId() + ", personName=" + getPersonName() + ", personType=" + getPersonType() + ", doorName=" + getDoorName() + ", cardNo=" + getCardNo() + ", picUri=" + getPicUri() + ", deviceTime=" + getDeviceTime() + ", readerIndexCode=" + getReaderIndexCode() + ", readerName=" + getReaderName() + ", uniqueValue=" + getUniqueValue() + StringPool.RIGHT_BRACKET;
    }
    public String getPersonId() {
        return this.personId;
    }
    public String getPersonName() {
        return this.personName;
    }
    public String getPersonType() {
        return this.personType;
    }
    public String getDoorName() {
        return this.doorName;
    }
    public String getCardNo() {
        return this.cardNo;
    }
    public String getPicUri() {
        return this.picUri;
    }
    public LocalDateTime getDeviceTime() {
        return this.deviceTime;
    }
    public String getReaderIndexCode() {
        return this.readerIndexCode;
    }
    public String getReaderName() {
        return this.readerName;
    }
    public String getUniqueValue() {
        return DateUtils.toStringTime(this.deviceTime) + "-" + this.personId;
    }
}
