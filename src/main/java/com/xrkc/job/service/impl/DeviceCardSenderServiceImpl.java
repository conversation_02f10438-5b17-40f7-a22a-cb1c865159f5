package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.core.utils.CommonUtil;
import com.xrkc.job.domain.DeviceCardSender;
import com.xrkc.job.mapper.DeviceCardSenderMapper;
import com.xrkc.job.service.IDeviceCardSenderService;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/DeviceCardSenderServiceImpl.class */
public class DeviceCardSenderServiceImpl extends ServiceImpl<DeviceCardSenderMapper, DeviceCardSender> implements IDeviceCardSenderService {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) DeviceCardSenderServiceImpl.class);
    @Autowired
    private DeviceCardSenderMapper cardSenderMapper;
    @Override // com.xrkc.job.service.IDeviceCardSenderService
    public List<DeviceCardSender> selectValidFaceDevice() {
        List<DeviceCardSender> list = this.cardSenderMapper.selectValidDevice();
        if (!CollectionUtils.isEmpty(list)) {
            list.removeIf(device -> {
                return !CommonUtil.testHttpNet(device.getFaceUrl());
            });
        }
        return list;
    }
}
