package com.xrkc.job.service.impl;
import com.xrkc.core.domain.rail.RailDrawType;
import com.xrkc.job.mapper.SystemInfoMapper;
import com.xrkc.job.service.ISystemInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/SystemInfoServiceImpl.class */
public class SystemInfoServiceImpl implements ISystemInfoService {
    @Autowired
    private SystemInfoMapper systemInfoMapper;
    @Override // com.xrkc.job.service.ISystemInfoService
    public RailDrawType findMapRailScope(Long systemId) {
        return this.systemInfoMapper.findMapRailScope(systemId);
    }
}
