package com.xrkc.job.api.DTO;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.math.BigDecimal;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/api/DTO/VehicleHistoryRes.class */
public class VehicleHistoryRes {
    private String imei;
    private String pointDt;
    private String pointType;
    private BigDecimal lat;
    private BigDecimal lon;
    private Integer speed;
    private Integer dir;
    private Boolean isStop;
    private String exData;
    public void setImei(String imei) {
        this.imei = imei;
    }
    public void setPointDt(String pointDt) {
        this.pointDt = pointDt;
    }
    public void setPointType(String pointType) {
        this.pointType = pointType;
    }
    public void setLat(BigDecimal lat) {
        this.lat = lat;
    }
    public void setLon(BigDecimal lon) {
        this.lon = lon;
    }
    public void setSpeed(Integer speed) {
        this.speed = speed;
    }
    public void setDir(Integer dir) {
        this.dir = dir;
    }
    public void setIsStop(Boolean isStop) {
        this.isStop = isStop;
    }
    public void setExData(String exData) {
        this.exData = exData;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof VehicleHistoryRes)) {
            return false;
        }
        VehicleHistoryRes other = (VehicleHistoryRes) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$speed = getSpeed();
        Object other$speed = other.getSpeed();
        if (this$speed == null) {
            if (other$speed != null) {
                return false;
            }
        } else if (!this$speed.equals(other$speed)) {
            return false;
        }
        Object this$dir = getDir();
        Object other$dir = other.getDir();
        if (this$dir == null) {
            if (other$dir != null) {
                return false;
            }
        } else if (!this$dir.equals(other$dir)) {
            return false;
        }
        Object this$isStop = getIsStop();
        Object other$isStop = other.getIsStop();
        if (this$isStop == null) {
            if (other$isStop != null) {
                return false;
            }
        } else if (!this$isStop.equals(other$isStop)) {
            return false;
        }
        Object this$imei = getImei();
        Object other$imei = other.getImei();
        if (this$imei == null) {
            if (other$imei != null) {
                return false;
            }
        } else if (!this$imei.equals(other$imei)) {
            return false;
        }
        Object this$pointDt = getPointDt();
        Object other$pointDt = other.getPointDt();
        if (this$pointDt == null) {
            if (other$pointDt != null) {
                return false;
            }
        } else if (!this$pointDt.equals(other$pointDt)) {
            return false;
        }
        Object this$pointType = getPointType();
        Object other$pointType = other.getPointType();
        if (this$pointType == null) {
            if (other$pointType != null) {
                return false;
            }
        } else if (!this$pointType.equals(other$pointType)) {
            return false;
        }
        Object this$lat = getLat();
        Object other$lat = other.getLat();
        if (this$lat == null) {
            if (other$lat != null) {
                return false;
            }
        } else if (!this$lat.equals(other$lat)) {
            return false;
        }
        Object this$lon = getLon();
        Object other$lon = other.getLon();
        if (this$lon == null) {
            if (other$lon != null) {
                return false;
            }
        } else if (!this$lon.equals(other$lon)) {
            return false;
        }
        Object this$exData = getExData();
        Object other$exData = other.getExData();
        return this$exData == null ? other$exData == null : this$exData.equals(other$exData);
    }
    protected boolean canEqual(Object other) {
        return other instanceof VehicleHistoryRes;
    }
    public int hashCode() {
        Object $speed = getSpeed();
        int result = (1 * 59) + ($speed == null ? 43 : $speed.hashCode());
        Object $dir = getDir();
        int result2 = (result * 59) + ($dir == null ? 43 : $dir.hashCode());
        Object $isStop = getIsStop();
        int result3 = (result2 * 59) + ($isStop == null ? 43 : $isStop.hashCode());
        Object $imei = getImei();
        int result4 = (result3 * 59) + ($imei == null ? 43 : $imei.hashCode());
        Object $pointDt = getPointDt();
        int result5 = (result4 * 59) + ($pointDt == null ? 43 : $pointDt.hashCode());
        Object $pointType = getPointType();
        int result6 = (result5 * 59) + ($pointType == null ? 43 : $pointType.hashCode());
        Object $lat = getLat();
        int result7 = (result6 * 59) + ($lat == null ? 43 : $lat.hashCode());
        Object $lon = getLon();
        int result8 = (result7 * 59) + ($lon == null ? 43 : $lon.hashCode());
        Object $exData = getExData();
        return (result8 * 59) + ($exData == null ? 43 : $exData.hashCode());
    }
    public String toString() {
        return "VehicleHistoryRes(imei=" + getImei() + ", pointDt=" + getPointDt() + ", pointType=" + getPointType() + ", lat=" + getLat() + ", lon=" + getLon() + ", speed=" + getSpeed() + ", dir=" + getDir() + ", isStop=" + getIsStop() + ", exData=" + getExData() + StringPool.RIGHT_BRACKET;
    }
    public String getImei() {
        return this.imei;
    }
    public String getPointDt() {
        return this.pointDt;
    }
    public String getPointType() {
        return this.pointType;
    }
    public BigDecimal getLat() {
        return this.lat;
    }
    public BigDecimal getLon() {
        return this.lon;
    }
    public Integer getSpeed() {
        return this.speed;
    }
    public Integer getDir() {
        return this.dir;
    }
    public Boolean getIsStop() {
        return this.isStop;
    }
    public String getExData() {
        return this.exData;
    }
}
