package com.xrkc.job.task;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.aliyun.core.http.BodyType;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.mysql.cj.CharsetMapping;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.constant.Constants;
import com.xrkc.core.domain.basic.JsonResult;
import com.xrkc.core.domain.device.DeviceCardSenderDetail;
import com.xrkc.core.domain.rail.RailDrawType;
import com.xrkc.core.domain.system.SystemApi;
import com.xrkc.core.utils.PersonUtils;
import com.xrkc.job.controller.WebSocketServer;
import com.xrkc.job.domain.Alarm;
import com.xrkc.job.domain.DeviceBeacon;
import com.xrkc.job.domain.DeviceCard;
import com.xrkc.job.domain.DeviceCardSenderLog;
import com.xrkc.job.domain.FacilityRailVO;
import com.xrkc.job.domain.HiddenDanger;
import com.xrkc.job.domain.PositionCurrent;
import com.xrkc.job.service.IAlarmService;
import com.xrkc.job.service.IDeviceBeaconService;
import com.xrkc.job.service.IDeviceCameraService;
import com.xrkc.job.service.IDeviceCardSenderDetailService;
import com.xrkc.job.service.IDeviceCardSenderLogService;
import com.xrkc.job.service.IDeviceCardSenderService;
import com.xrkc.job.service.IDeviceCardService;
import com.xrkc.job.service.IFacilityService;
import com.xrkc.job.service.IHiddenDangerService;
import com.xrkc.job.service.IPositionCurrentService;
import com.xrkc.job.service.SystemApiService;
import com.xrkc.job.service.impl.HttpClientService;
import com.xrkc.job.util.ApiUtils;
import com.xrkc.job.util.CoordinateSystemUtils;
import com.xrkc.job.util.MD5Util;
import com.xrkc.job.util.MQTTPublishClient;
import com.xrkc.job.vo.PushCardSenderAimsVO;
import com.xrkc.job.vo.PushCardSenderMqttDetailVO;
import com.xrkc.job.vo.PushCardSenderMqttNumVO;
import com.xrkc.job.vo.PushCardSenderNumVO;
import com.xrkc.job.vo.PushCardSenderVO;
import com.xrkc.job.vo.PushPersonPositionVO;
import com.xrkc.redis.utils.RedisCacheUtils;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.string.StringEncoder;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.lang.invoke.SerializedLambda;
import java.math.BigDecimal;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetSocketAddress;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import net.jodah.expiringmap.ExpiringMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.xmlbeans.impl.jam.xml.JamXmlElements;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.postgresql.jdbc.EscapedFunctions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
@Component("pushTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/PushTask.class */
public class PushTask {
    private static final String flag = "-";
    @Autowired
    private WebSocketServer webSocketServer;
    @Autowired
    private IAlarmService alarmService;
    @Autowired
    private IDeviceCameraService deviceCameraService;
    @Autowired
    private IPositionCurrentService positionCurrentService;
    @Autowired
    private IFacilityService facilityService;
    @Autowired
    private IDeviceCardService deviceCardService;
    @Autowired
    private IDeviceBeaconService deviceBeaconService;
    @Autowired
    private HttpClientService httpClientService;
    @Autowired
    private MQTTPublishClient mqttPublishClient;
    @Autowired
    private SystemApiService systemApiService;
    @Autowired
    private IHiddenDangerService hiddenDangerService;
    @Autowired
    private IDeviceCardSenderService cardSenderService;
    @Autowired
    private IDeviceCardSenderDetailService cardSenderDetailService;
    @Autowired
    private IDeviceCardSenderLogService cardSenderLogService;
    private static final Logger log = LoggerFactory.getLogger((Class<?>) PushTask.class);
    private static final DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    Map<String, ChannelHandlerContext> tcpContextMap = new ConcurrentHashMap();
    ExpiringMap<Long, LocalDateTime> tpcAlarmPushMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    ExpiringMap<String, LocalDateTime> tpcAlarmPushCtxMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    ExpiringMap<Long, LocalDateTime> webSocketNoticeAlarmMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    ExpiringMap<Long, LocalDateTime> webSocketInspectAlarmMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    ExpiringMap<Long, LocalDateTime> httpNoticeAlarmMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    ExpiringMap<String, LocalDateTime> alarmSendedMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    ExpiringMap<String, LocalDateTime> positionSendedMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    ExpiringMap<Long, LocalDateTime> cardSendUDPMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    ExpiringMap<Long, LocalDateTime> alarmSendUDPMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    ExpiringMap<Long, LocalDateTime> alarmPushMap15 = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    ExpiringMap<Long, LocalDateTime> alarmPushMap20 = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    @PostConstruct
    public void init() throws NumberFormatException {
        String portStr = RedisCacheUtils.getConfigRedisCache().get(ConfigValue.CONFIG_KEY_ALARM_TCP_SERVER_PORT);
        if (StringUtils.isBlank(portStr)) {
            return;
        }
        try {
            int port = Integer.parseInt(portStr);
            NioEventLoopGroup bossGroup = new NioEventLoopGroup();
            EventLoopGroup workerGroup = new NioEventLoopGroup();
            ServerBootstrap server = new ServerBootstrap();
            server.group(bossGroup, workerGroup).channel(NioServerSocketChannel.class).childHandler(new ChannelInitializer<SocketChannel>() { // from class: com.xrkc.job.task.PushTask.1
                void AnonymousClass1() {
                }
                @Override // io.netty.channel.ChannelInitializer
                public void initChannel(SocketChannel socketChannel) throws Exception {
                    socketChannel.pipeline().addLast(new StringEncoder());
                    socketChannel.pipeline().addLast(PushTask.this.new ClientHandler());
                }
            }).option(ChannelOption.SO_BACKLOG, 128).childOption(ChannelOption.SO_KEEPALIVE, true);
            try {
                server.bind("0.0.0.0", port).sync();
                log.info("声光报警器tcp port：{}", Integer.valueOf(port));
            } catch (InterruptedException e) {
                log.error(e.getMessage(), (Throwable) e);
            }
        } catch (Exception e2) {
            log.error("声光报警器服务端开启失败：{}", e2.getMessage());
        }
    }
    /* renamed from: com.xrkc.job.task.PushTask$1 */
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/PushTask$1.class */
    class AnonymousClass1 extends ChannelInitializer<SocketChannel> {
        AnonymousClass1() {
        }
        @Override // io.netty.channel.ChannelInitializer
        public void initChannel(SocketChannel socketChannel) throws Exception {
            socketChannel.pipeline().addLast(new StringEncoder());
            socketChannel.pipeline().addLast(PushTask.this.new ClientHandler());
        }
    }
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/PushTask$ClientHandler.class */
    class ClientHandler extends ChannelInboundHandlerAdapter {
        ClientHandler() {
        }
        @Override // io.netty.channel.ChannelInboundHandlerAdapter, io.netty.channel.ChannelInboundHandler
        public void channelActive(ChannelHandlerContext ctx) throws Exception {
            PushTask.log.info("tcp有新连接进来");
            PushTask.this.tcpContextMap.put(ctx.name(), ctx);
        }
        @Override // io.netty.channel.ChannelInboundHandlerAdapter, io.netty.channel.ChannelInboundHandler
        public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
            PushTask.log.debug("tcp有新数据进来");
            ByteBuf buf = (ByteBuf) msg;
            try {
                String content = new String(ByteBufUtil.getBytes(buf));
                PushTask.log.debug("tcp msg:{}", content);
                ctx.writeAndFlush(content);
                buf.release();
            } catch (Throwable th) {
                buf.release();
                throw th;
            }
        }
        @Override // io.netty.channel.ChannelInboundHandlerAdapter, io.netty.channel.ChannelHandlerAdapter, io.netty.channel.ChannelHandler, io.netty.channel.ChannelInboundHandler
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
            PushTask.log.error(cause.getMessage(), cause);
            PushTask.this.tcpContextMap.remove(ctx.name());
            ctx.close();
        }
    }
    public void pushAlarm() {
        LocalDateTime now = LocalDateTime.now();
        Map<String, String> configMap = RedisCacheUtils.getConfigRedisCache();
        if (!CollectionUtils.isEmpty(this.tcpContextMap)) {
            String tcp_alarm_seconds = configMap.get(ConfigValue.TCP_ALARM_SECONDS);
            Long seconds = Long.valueOf(NumberUtil.isLong(tcp_alarm_seconds) ? Long.valueOf(tcp_alarm_seconds).longValue() : 10L);
            this.tcpContextMap.values().removeIf(r -> {
                return Objects.isNull(r) || r.isRemoved();
            });
            this.tcpContextMap.values().forEach(ctx -> {
                String name = ctx.name();
                if (!this.tpcAlarmPushCtxMap.containsKey(name) || this.tpcAlarmPushCtxMap.get(name).isBefore(now.minusSeconds(seconds.longValue()))) {
                    ctx.channel().writeAndFlush("CLOSEALL");
                    log.info("CLOSEALL");
                }
            });
        }
        List<Alarm> list = this.alarmService.selectUnDisposedAlarm(30);
        if (!CollectionUtils.isEmpty(list)) {
            list.stream().forEach(t -> {
                JSONObject data = new JSONObject();
                data.put("alarmId", t.getAlarmId());
                data.put("acceptTime", t.getAcceptTime().format(df));
                data.put("alarmName", t.getAlarmName());
                data.put("cardId", t.getCardId());
                data.put("realName", t.getRealName());
                data.put("layerId", t.getLayerId());
                data.put("alarmTypeName", t.getAlarmTypeName());
                log.debug("正在进行报警推送……");
                if (!this.webSocketNoticeAlarmMap.containsKey(t.getAlarmId())) {
                    JSONObject alarmJson = new JSONObject();
                    String rtsp = null;
                    if (Constants.ALARM_TYPE_95.equals(t.getAlarmType())) {
                        rtsp = this.deviceCameraService.findRtspByParam(t.getLayerId(), t.getLongitude(), t.getLatitude());
                    }
                    data.put("rtsp", StringUtils.isNotBlank(rtsp) ? rtsp : "");
                    if (Constants.ALARM_TYPE_80.equals(t.getAlarmType())) {
                        alarmJson.put("translate", t.getAlarmName().substring(t.getAlarmName().indexOf("超过") + 2, t.getAlarmName().indexOf("小时")));
                        alarmJson.put("msgType", "senderAlarm");
                    } else if ("81".equals(t.getAlarmType())) {
                        data.put("alarmType", "未取卡报警");
                        alarmJson.put("msgType", "senderAlarm");
                        String alarmName = t.getAlarmName();
                        if (StringUtils.isNotEmpty(alarmName)) {
                            String[] split = alarmName.split("触发");
                            if (2 == split.length) {
                                data.put("alarmName", split[0]);
                                data.put("translate", "触发" + split[1]);
                            }
                        }
                    } else {
                        alarmJson.put("msgType", "alarm");
                    }
                    alarmJson.put("data", data);
                    this.webSocketServer.sendMessageForAPI(null, JSONObject.toJSONString(alarmJson, JSONWriter.Feature.WriteMapNullValue));
                    this.webSocketNoticeAlarmMap.put(t.getAlarmId(), t.getAcceptTime());
                }
                if (!CollectionUtils.isEmpty(this.tcpContextMap) && !this.tpcAlarmPushMap.containsKey(t.getAlarmId())) {
                    this.tcpContextMap.values().forEach(ctx2 -> {
                        ctx2.channel().writeAndFlush("OPENALL");
                        log.info("OPENALL");
                        this.tpcAlarmPushMap.put(t.getAlarmId(), t.getAcceptTime());
                        this.tpcAlarmPushCtxMap.put(ctx2.name(), now);
                    });
                }
                alarmPushMQTT(list);
                String pushAlarmHttpUrl = (String) configMap.get(ConfigValue.CONFIG_KEY_PUSH_ALARM_HTTP_URL);
                if (StringUtils.isNotBlank(pushAlarmHttpUrl) && pushAlarmHttpUrl.length() > 10 && !this.httpNoticeAlarmMap.containsKey(t.getAlarmId())) {
                    try {
                        String result = this.httpClientService.doPost(pushAlarmHttpUrl, data.toJSONString(new JSONWriter.Feature[0]));
                        log.info("http推送报警结果：{},数据：{}", result, data.toJSONString(new JSONWriter.Feature[0]));
                        if ("Y".equals(result)) {
                            this.httpNoticeAlarmMap.put(t.getAlarmId(), t.getAcceptTime());
                        }
                    } catch (Exception e) {
                        log.error("未处理报警HTTP推送失败:{}", e.getMessage());
                    }
                }
                pushUnDisposeAlarm(list);
            });
            pushPersonAlarmUDP(list);
        }
    }
    private String translateDesc(String alarmName) {
        return alarmName.substring(alarmName.indexOf("超过"), alarmName.indexOf("小时"));
    }
    private void alarmPushMQTT(List<Alarm> list) {
        MqttClient client = this.mqttPublishClient.getClient();
        if (client != null && client.isConnected()) {
            String topic = "/alarm";
            list.forEach(alarm -> {
                String key = "alarm_" + alarm.getAlarmId();
                if (!this.alarmSendedMap.containsKey(key) || this.alarmSendedMap.get(key).isBefore(alarm.getAcceptTime())) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("uniqueId", UUID.randomUUID().toString());
                    jsonObject.put("alarmId", alarm.getAlarmId());
                    jsonObject.put("cardId", alarm.getCardId());
                    jsonObject.put("layerId", alarm.getLayerId());
                    jsonObject.put("layerHeight", alarm.getLayerHeight());
                    jsonObject.put("areaName", alarm.getAreaName());
                    jsonObject.put("alarmType", alarm.getAlarmType());
                    jsonObject.put("latitude", alarm.getLatitude());
                    jsonObject.put("longitude", alarm.getLongitude());
                    jsonObject.put("alarmTime", alarm.getAcceptTime().format(df));
                    jsonObject.put("layerName", alarm.getLayerName());
                    jsonObject.put("alarmTypeName", alarm.getAlarmTypeName());
                    jsonObject.put("alarmDesc", alarm.getAlarmDesc());
                    jsonObject.put("alarmRealName", alarm.getRealName());
                    jsonObject.put("areaId", alarm.getAreaId());
                    jsonObject.put("personId", alarm.getPersonId());
                    String msg = JSON.toJSONString(jsonObject, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible);
                    if (this.mqttPublishClient.publish(client, topic, msg).booleanValue()) {
                        this.alarmSendedMap.put(key, alarm.getAcceptTime());
                    }
                }
            });
        }
    }
    private void pushPersonAlarmUDP(List<Alarm> list) {
        list.removeIf(r -> {
            return !Constants.ALARM_TYPE_95.equals(r.getAlarmType()) || this.alarmSendUDPMap.containsKey(r.getAlarmId()) || (Objects.nonNull(this.alarmSendUDPMap.get(r.getAlarmId())) && r.getAcceptTime().isBefore(this.alarmSendUDPMap.get(r.getAlarmId())));
        });
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_UDP);
        if (Objects.isNull(systemApi) || StringUtils.isBlank(systemApi.getHost()) || systemApi.getHost().split(":").length != 2) {
            return;
        }
        String now = LocalDateTime.now().toString();
        list.stream().forEach(alarm -> {
            JSONObject msg = new JSONObject();
            msg.put("deviceType", "Tag");
            msg.put("uid", alarm.getCardId());
            msg.put("warnType", "02");
            msg.put("keyCode", "1");
            msg.put("areaId", alarm.getLayerId());
            msg.put("blockId", "");
            msg.put("raiseTime", now);
            try {
                if (send(msg, "PressKey", systemApi.getHost().split(":")[0], systemApi.getHost().split(":")[1])) {
                    this.alarmSendUDPMap.put(alarm.getAlarmId(), alarm.getAcceptTime());
                }
            } catch (NoSuchAlgorithmException e) {
                throw new RuntimeException(e);
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
        });
    }
    private void pushPersonCurrentPositionWS(List<PositionCurrent> list) {
        if (CollectionUtils.isEmpty(WebSocketServer.apiSessionMap)) {
            return;
        }
        JSONObject msgJson = new JSONObject();
        msgJson.put("msgType", "personPosition");
        List<PushPersonPositionVO> pushList = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            msgJson.put("data", pushList);
            this.webSocketServer.sendMessageForAPI(null, JSONObject.toJSONString(msgJson, JSONWriter.Feature.WriteMapNullValue));
            return;
        }
        List<Long> alarmCardIds = (List) list.stream().filter(f -> {
            return f.getUndisposedAlarmCount().intValue() > 0;
        }).map((v0) -> {
            return v0.getCardId();
        }).collect(Collectors.toList());
        Map<Long, List<String>> cardAlarmType = new HashMap<>();
        if (!CollectionUtils.isEmpty(alarmCardIds)) {
            List<Alarm> alarmList = this.alarmService.selectUnDisposedAlarmCard(alarmCardIds);
            if (!CollectionUtils.isEmpty(alarmList)) {
                Map<Long, List<Alarm>> map = (Map) alarmList.stream().collect(Collectors.groupingBy((v0) -> {
                    return v0.getCardId();
                }));
                map.forEach((cardId, l) -> {
                });
            }
        }
        Map<String, String> configMap = RedisCacheUtils.getConfigRedisCache();
        String coordinateSystem = configMap.get(ConfigValue.API_KEY_WS_PUSH_COORDINATE_SYSTEM);
        list.stream().forEach(position -> {
            PushPersonPositionVO vo = new PushPersonPositionVO();
            if (StrUtil.equals(coordinateSystem, com.xrkc.job.domain.Constants.COORDINATE_SYSTEM_WGS84, true)) {
                vo.setLongitude(position.getLongitude());
                vo.setLatitude(position.getLatitude());
            } else {
                BigDecimal[] geo = CoordinateSystemUtils.Geo4326Transform3857(position.getLongitude(), position.getLatitude());
                vo.setLongitude(geo[0]);
                vo.setLatitude(geo[1]);
            }
            vo.setPersonNo(position.getIdNumber());
            vo.setCardNo(position.getCardId());
            vo.setPersonName(position.getRealName());
            vo.setPersonType(position.getPersonType());
            vo.setPersonTypeName(position.getPersonTypeName());
            vo.setPersonAttribute(position.getPersonAttribute());
            vo.setPersonPhoto(position.getPersonPhoto());
            vo.setIdNumber(position.getIdNumber());
            vo.setPhone(position.getPhone());
            vo.setDeptId(position.getDeptId());
            vo.setDeptName(position.getDeptName());
            vo.setDeviceElectric(position.getCardPower());
            vo.setTimestamp(Long.valueOf(System.currentTimeMillis()));
            vo.setFloor(position.getLayerId());
            vo.setAlarm(Boolean.valueOf(position.getUndisposedAlarmCount().intValue() > 0));
            vo.setAlarmType(position.getUndisposedAlarmCount().intValue() > 0 ? (List) cardAlarmType.get(position.getCardId()) : null);
            vo.setBuild(null);
            vo.setSpeed(null);
            vo.setAlt(position.getLayerHeight());
            vo.setJobNumber(position.getJobNumber());
            vo.setPersonId(position.getPersonId());
            vo.setCardType(position.getCardType());
            pushList.add(vo);
        });
        msgJson.put("data", pushList);
        this.webSocketServer.sendMessageForAPI(null, JSONObject.toJSONString(msgJson, JSONWriter.Feature.WriteMapNullValue));
    }
    private void pushPersonCurrentPositionUDP(List<PositionCurrent> list) {
        SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_UDP);
        if (Objects.isNull(systemApi) || StringUtils.isBlank(systemApi.getHost()) || systemApi.getHost().split(":").length != 2) {
            return;
        }
        list.forEach(position -> {
            if (!this.cardSendUDPMap.containsKey(position.getCardId()) || (Objects.nonNull(this.cardSendUDPMap.get(position.getCardId())) && position.getAcceptTime().isAfter(this.cardSendUDPMap.get(position.getCardId())))) {
                JSONObject msg = new JSONObject();
                msg.put("tagId", position.getCardId());
                msg.put("warnType", "02");
                msg.put("keyCode", "1");
                msg.put("areaId", position.getLayerId());
                msg.put("blockId", "");
                msg.put(EscapedFunctions.FLOOR_FUNC, 0);
                boolean z = (Objects.isNull(position.getStillStatus()) || position.getStillStatus().intValue() == 0) ? false : true;
                msg.put("silent", Boolean.valueOf(z));
                msg.put("x", position.getLongitude());
                msg.put(StringPool.Y, position.getLatitude());
                try {
                    if (send(msg, "Location", systemApi.getHost().split(":")[0], systemApi.getHost().split(":")[1])) {
                        this.cardSendUDPMap.put(position.getCardId(), position.getAcceptTime());
                    }
                } catch (NoSuchAlgorithmException e) {
                    throw new RuntimeException(e);
                } catch (UnsupportedEncodingException e) {
                    throw new RuntimeException(e);
                }
            }
        });
    }
    public void pushFacilityPersonCount() {
        if (CollectionUtils.isEmpty(WebSocketServer.apiSessionMap)) {
            return;
        }
        List<FacilityRailVO> facilityRailVOList = this.facilityService.selectFacilityRailVOList();
        Map<String, List<FacilityRailVO>> map = (Map) facilityRailVOList.stream().collect(Collectors.groupingBy((v0) -> {
            return v0.getFacilityName();
        }));
        List<String> legends = new ArrayList<>();
        List<Integer> datas = new ArrayList<>();
        map.forEach((facilityName, l) -> {
            List<RailDrawType> railScopeList = (List) l.stream().map(s -> {
                return RailDrawType.builder().drawType(s.getDrawType()).railScope(s.getRailScope()).build();
            }).collect(Collectors.toList());
            int count = this.positionCurrentService.countCurrentFacilityPerson(railScopeList);
            legends.add(facilityName);
            datas.add(Integer.valueOf(count));
        });
        JSONObject dataJson = new JSONObject();
        dataJson.put("datas", datas);
        List<JSONObject> series = new ArrayList<>();
        series.add(dataJson);
        JSONObject pushJson = new JSONObject();
        pushJson.put("legends", legends);
        pushJson.put("series", series);
        JSONObject msgJson = new JSONObject();
        msgJson.put("msgType", "facilityPersonCount");
        msgJson.put("data", pushJson);
        this.webSocketServer.sendMessageForAPI(null, JSONObject.toJSONString(msgJson, JSONWriter.Feature.WriteMapNullValue));
    }
    public void positionCurrentPush() {
        List<PositionCurrent> list = this.positionCurrentService.selectList();
        pushPersonCurrentPositionWS(list);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        pushPersonCurrentPositionUDP(list);
        MqttClient client = this.mqttPublishClient.getClient();
        if (client != null && client.isConnected()) {
            String topic = "/position";
            list.forEach(position -> {
                String positionId = position.getPositionId().toString();
                String key = "position" + positionId;
                if (!this.positionSendedMap.containsKey(key) || this.positionSendedMap.get(key).isBefore(position.getAcceptTime())) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("uniqueId", UUID.randomUUID().toString());
                    jsonObject.put("cardId", position.getCardId());
                    jsonObject.put("layerId", position.getLayerId());
                    jsonObject.put("layerHeight", position.getLayerHeight());
                    jsonObject.put("latitude", position.getLatitude());
                    jsonObject.put("longitude", position.getLongitude());
                    jsonObject.put("cardPower", position.getCardPower());
                    jsonObject.put("acceptTime", position.getAcceptTime());
                    jsonObject.put("cardStatus", "1");
                    jsonObject.put("stillStatus", position.getStillStatus());
                    jsonObject.put("pressure", position.getPressure());
                    jsonObject.put("personId", position.getPersonId());
                    jsonObject.put("beaconId", position.getBeaconId());
                    jsonObject.put("deptName", position.getDeptName());
                    jsonObject.put("realName", position.getRealName());
                    jsonObject.put("jobNumber", position.getJobNumber());
                    jsonObject.put("personType", position.getPersonType());
                    jsonObject.put("sex", position.getSex());
                    jsonObject.put(PersonUtils.personPhotoKeyPrefix, position.getPersonPhoto());
                    jsonObject.put("idNumber", position.getIdNumber());
                    jsonObject.put("phone", position.getPhone());
                    jsonObject.put("visitReason", position.getVisitReason());
                    jsonObject.put("visitLocation", position.getVisitLocation());
                    jsonObject.put("receiveLeader", position.getReceiveLeader());
                    jsonObject.put("companyName", position.getCompanyName());
                    String msg = JSON.toJSONString(jsonObject, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible);
                    if (this.mqttPublishClient.publish(client, topic, msg).booleanValue()) {
                        this.positionSendedMap.put(key, position.getAcceptTime());
                    }
                }
            });
        }
    }
    private void pushUnDisposeAlarm(List<Alarm> alarmList15) {
        if (!CollectionUtils.isEmpty(alarmList15)) {
            SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_GQ);
            if (Objects.isNull(systemApi) || StringUtils.isBlank(systemApi.getHost()) || StringUtils.isBlank(systemApi.getUserName()) || StringUtils.isBlank(systemApi.getPassword())) {
                return;
            }
            alarmList15.removeIf(f -> {
                return this.alarmPushMap15.containsKey(f.getAlarmId());
            });
            if (!CollectionUtils.isEmpty(alarmList15)) {
                alarmList15.stream().forEach(alarm -> {
                    String alarmType = ApiUtils.getAlarmType(alarm.getAlarmType());
                    if (StringUtils.isNotBlank(alarmType)) {
                        MultiValueMap<String, Object> linkedMap = new LinkedMultiValueMap<>();
                        linkedMap.add("id", alarm.getAlarmId());
                        linkedMap.add("type", "alarm");
                        linkedMap.add("LATITUDE", alarm.getLatitude());
                        linkedMap.add("LONGITUDE", alarm.getLongitude());
                        linkedMap.add("alarmInfo", alarm.getAlarmName());
                        linkedMap.add("alarmLocation", StringUtils.isNotBlank(alarm.getAreaName()) ? alarm.getAreaName() : "-");
                        linkedMap.add("alarmType", alarmType);
                        linkedMap.add("cardNo", Long.valueOf(Objects.nonNull(alarm.getCardId()) ? alarm.getCardId().longValue() : -1L));
                        linkedMap.add("realName", alarm.getRealName());
                        linkedMap.add("floorNo", StringUtils.isNotBlank(alarm.getLayerId()) ? alarm.getLayerId() : "-");
                        linkedMap.add("time", alarm.getAcceptTime().format(df));
                        linkedMap.add("handleTime", alarm.getAcceptTime().format(df));
                        linkedMap.add("handleInfo", "-");
                        String result = ApiUtils.pushData(systemApi, linkedMap, "/api/ppAlarm/push");
                        if ("success".equals(result)) {
                            this.alarmPushMap15.put(alarm.getAlarmId(), alarm.getAcceptTime());
                        }
                    }
                });
            }
        }
    }
    public void pushDisposeAlarm() {
        SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_GQ);
        if (Objects.isNull(systemApi) || StringUtils.isBlank(systemApi.getHost()) || StringUtils.isBlank(systemApi.getUserName()) || StringUtils.isBlank(systemApi.getPassword())) {
            return;
        }
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("alarmStatus", "20");
        queryMap.put("disposeTimeInterval", 10);
        List<Alarm> alarmList20 = this.alarmService.selectList(queryMap);
        if (!CollectionUtils.isEmpty(alarmList20)) {
            alarmList20.removeIf(f -> {
                return this.alarmPushMap20.containsKey(f.getAlarmId());
            });
            if (!CollectionUtils.isEmpty(alarmList20)) {
                alarmList20.stream().forEach(alarm -> {
                    String alarmType = ApiUtils.getAlarmType(alarm.getAlarmType());
                    if (StringUtils.isNotBlank(alarmType)) {
                        MultiValueMap<String, Object> linkedMap = new LinkedMultiValueMap<>();
                        linkedMap.add("id", alarm.getAlarmId());
                        linkedMap.add("type", "handle");
                        linkedMap.add("LATITUDE", alarm.getLatitude());
                        linkedMap.add("LONGITUDE", alarm.getLongitude());
                        linkedMap.add("alarmInfo", alarm.getAlarmName());
                        linkedMap.add("alarmLocation", StringUtils.isNotBlank(alarm.getAreaName()) ? alarm.getAreaName() : "-");
                        linkedMap.add("alarmType", alarmType);
                        linkedMap.add("cardNo", Long.valueOf(Objects.nonNull(alarm.getCardId()) ? alarm.getCardId().longValue() : -1L));
                        linkedMap.add("realName", alarm.getRealName());
                        linkedMap.add("floorNo", StringUtils.isNotBlank(alarm.getLayerId()) ? alarm.getLayerId() : "-");
                        linkedMap.add("time", alarm.getAcceptTime().format(df));
                        linkedMap.add("handleTime", alarm.getDisposeTime().format(df));
                        linkedMap.add("handleInfo", alarm.getDisposeFeedback());
                        String result = ApiUtils.pushData(systemApi, linkedMap, "/api/ppAlarm/push");
                        if ("success".equals(result)) {
                            this.alarmPushMap20.put(alarm.getAlarmId(), alarm.getAcceptTime());
                        }
                    }
                });
            }
        }
    }
    public void weeHoursPush() {
        sendBeaconUDP();
    }
    private void sendBeaconUDP() {
        SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_UDP);
        if (Objects.isNull(systemApi) || StringUtils.isBlank(systemApi.getHost()) || systemApi.getHost().split(":").length != 2) {
            return;
        }
        String now = LocalDateTime.now().toString();
        this.deviceBeaconService.query().list().forEach(beacon -> {
            JSONObject msg = new JSONObject();
            msg.put("deviceType", "Beacon");
            msg.put("uid", beacon.getBeaconId());
            msg.put("raiseTime", now);
            try {
                send(msg, "Ping", systemApi.getHost().split(":")[0], systemApi.getHost().split(":")[1]);
            } catch (NoSuchAlgorithmException e) {
                throw new RuntimeException(e);
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
        });
    }
    public void sendCardUDP() {
        SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_UDP);
        if (Objects.isNull(systemApi) || StringUtils.isBlank(systemApi.getHost()) || systemApi.getHost().split(":").length != 2) {
            return;
        }
        String now = LocalDateTime.now().toString();
        List<DeviceCard> cardList = this.deviceCardService.selectForUDP();
        cardList.stream().forEach(card -> {
            JSONObject msg = new JSONObject();
            msg.put("deviceType", "Tag");
            msg.put("uid", card.getCardId());
            msg.put("raiseTime", now);
            msg.put("volt", card.getCardPower());
            msg.put("voltUnit", "%");
            try {
                send(msg, "Ping", systemApi.getHost().split(":")[0], systemApi.getHost().split(":")[1]);
            } catch (NoSuchAlgorithmException e) {
                throw new RuntimeException(e);
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
        });
    }
    private boolean send(JSONObject content, String method, String ip, String port) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        JSONObject request = new JSONObject();
        request.put(JamXmlElements.METHOD, method);
        request.put("params", content);
        try {
            String data = buildSendContent(request.toJSONString(new JSONWriter.Feature[0]), null, null);
            log.info("发送的UDP数据为:{}", data);
            byte[] bytes = data.getBytes(CharsetMapping.MYSQL_CHARSET_NAME_utf8);
            DatagramPacket packet = new DatagramPacket(bytes, bytes.length, new InetSocketAddress(ip, Integer.parseInt(port)));
            try {
                DatagramSocket socket = new DatagramSocket();
                Throwable th = null;
                try {
                    try {
                        socket.send(packet);
                        if (socket != null) {
                            if (0 != 0) {
                                try {
                                    socket.close();
                                } catch (Throwable th2) {
                                    th.addSuppressed(th2);
                                }
                            } else {
                                socket.close();
                            }
                        }
                        return true;
                    } catch (Throwable th3) {
                        if (socket != null) {
                            if (th != null) {
                                try {
                                    socket.close();
                                } catch (Throwable th4) {
                                    th.addSuppressed(th4);
                                }
                            } else {
                                socket.close();
                            }
                        }
                        throw th3;
                    }
                } finally {
                }
            } catch (Exception e) {
                log.error("发送UDP数据异常：{}", e.getMessage());
                return false;
            }
        } catch (IOException e2) {
            log.error(e2.getMessage(), (Throwable) e2);
            return false;
        }
    }
    private String buildSendContent(String content, String tenantId, String nodeId) throws NoSuchAlgorithmException {
        String timeTag = "00000" + Long.toHexString(LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        StringBuilder builder = new StringBuilder().append(BodyType.JSON).append(",").append(tenantId).append(",").append(nodeId).append(",").append(timeTag).append(",").append(content);
        String hash = MD5Util.md5Hex(builder.toString());
        builder.append(",").append(hash);
        return builder.toString();
    }
    public void pushInspectAlarm() {
        List<HiddenDanger> list = this.hiddenDangerService.selectPushDangerList();
        Set<Long> ids = this.webSocketInspectAlarmMap.keySet();
        list.removeIf(r -> {
            return ids.contains(r.getHiddenDangerId());
        });
        if (!CollectionUtils.isEmpty(list)) {
            LocalDateTime now = LocalDateTime.now();
            list.stream().forEach(t -> {
                JSONObject data = new JSONObject();
                data.put("alarmId", t.getHiddenDangerId());
                data.put("acceptTime", t.getCreateTime().format(df));
                StringBuilder str = new StringBuilder("巡检点[").append(t.getLocationName()).append("]");
                str.append("存在隐患，请及时排查");
                data.put("alarmName", str.toString());
                JSONObject alarmJson = new JSONObject();
                alarmJson.put("msgType", "inspect");
                alarmJson.put("data", data);
                log.info("websocket推送巡检报警: {}", alarmJson.toJSONString(new JSONWriter.Feature[0]));
                this.webSocketServer.sendMessageForXR(null, alarmJson.toJSONString(new JSONWriter.Feature[0]));
                this.webSocketInspectAlarmMap.put(t.getHiddenDangerId(), now);
            });
        }
    }
    public void pushMQTTSenderCardRecord() {
        LambdaQueryWrapper<DeviceCardSenderLog> wrapper = Wrappers.lambdaQuery(DeviceCardSenderLog.class);
        wrapper.ge((v0) -> {
            return v0.getCreateTime();
        }, LocalDateTime.now().minusDays(1L));
        wrapper.ne((v0) -> {
            return v0.getNotifyStatus();
        }, "1");
        List<DeviceCardSenderLog> list = this.cardSenderLogService.list(wrapper);
        list.removeIf(log2 -> {
            return Objects.isNull(log2.getPersonId());
        });
        List<DeviceCardSenderLog> removeList = (List) list.stream().filter(f -> {
            return 1 == f.getCardSenderType().intValue();
        }).collect(Collectors.toList());
        List<DeviceCardSenderLog> returnList = (List) list.stream().filter(f2 -> {
            return 0 == f2.getCardSenderType().intValue();
        }).collect(Collectors.toList());
        Map<String, Long> deviceSnMap = new HashMap<>();
        this.cardSenderService.list().forEach(record -> {
            deviceSnMap.put(record.getDeviceSn(), record.getCardSenderId());
        });
        MqttClient mqttClient = this.mqttPublishClient.getClient();
        if (mqttClient != null && mqttClient.isConnected()) {
            String topic = "/takeCard";
            removeList.forEach(senderLog -> {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("uniqueId", UUID.randomUUID().toString());
                jsonObject.put("personId", senderLog.getPersonId());
                jsonObject.put("cardId", senderLog.getCardId());
                jsonObject.put("result", StrUtil.equalsIgnoreCase(senderLog.getResult(), "成功") ? "0" : "1");
                jsonObject.put("remark", senderLog.getRemark());
                jsonObject.put("cardSenderId", deviceSnMap.get(senderLog.getDeviceSn()));
                jsonObject.put("takeTime", senderLog.getCreateTime());
                if (this.mqttPublishClient.publish(mqttClient, topic, jsonObject.toString()).booleanValue()) {
                    senderLog.setNotifyStatus("1");
                } else {
                    senderLog.setNotifyStatus("2");
                }
                this.cardSenderLogService.updateById(senderLog);
            });
            String topic02 = "/returnCard";
            returnList.forEach(senderLog2 -> {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("uniqueId", UUID.randomUUID().toString());
                jsonObject.put("personId", senderLog2.getPersonId());
                jsonObject.put("cardId", senderLog2.getCardId());
                jsonObject.put("result", StrUtil.equalsIgnoreCase(senderLog2.getResult(), "成功") ? "0" : "1");
                jsonObject.put("cardSenderId", deviceSnMap.get(senderLog2.getDeviceSn()));
                jsonObject.put("returnTime", senderLog2.getCreateTime());
                jsonObject.put("remark", senderLog2.getRemark());
                if (this.mqttPublishClient.publish(mqttClient, topic02, jsonObject.toString()).booleanValue()) {
                    senderLog2.setNotifyStatus("1");
                } else {
                    senderLog2.setNotifyStatus("2");
                }
                this.cardSenderLogService.updateById(senderLog2);
            });
        }
    }
    public void pushHttpSenderTakeCardRecord() {
        SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_HTTP_PUSH);
        if (systemApi == null) {
            return;
        }
        LambdaQueryWrapper<DeviceCardSenderLog> wrapper = Wrappers.lambdaQuery(DeviceCardSenderLog.class);
        wrapper.ge((v0) -> {
            return v0.getCreateTime();
        }, LocalDateTime.now().minusDays(1L));
        wrapper.ne((v0) -> {
            return v0.getNotifyStatus();
        }, "1");
        wrapper.eq((v0) -> {
            return v0.getCardSenderType();
        }, "1");
        List<DeviceCardSenderLog> list = this.cardSenderLogService.list(wrapper);
        list.removeIf(log2 -> {
            return Objects.isNull(log2.getPersonId());
        });
        Map<String, Long> deviceSnMap = new HashMap<>();
        this.cardSenderService.list().forEach(record -> {
            deviceSnMap.put(record.getDeviceSn(), record.getCardSenderId());
        });
        list.forEach(senderLog -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", senderLog.getId());
            jsonObject.put("personId", senderLog.getPersonId());
            jsonObject.put("cardId", senderLog.getCardId());
            jsonObject.put("result", StrUtil.equalsIgnoreCase(senderLog.getResult(), "成功") ? "0" : "1");
            jsonObject.put("remark", senderLog.getRemark());
            jsonObject.put("cardSenderId", deviceSnMap.get(senderLog.getDeviceSn()));
            jsonObject.put("takeTime", senderLog.getCreateTime());
            String result = HttpUtil.post(systemApi.getHost() + "/cardSender/person/takeCard", jsonObject.toJSONString(new JSONWriter.Feature[0]), 30000);
            log.info("发卡结果通知响应：{},参数：{}", result, jsonObject.toJSONString(new JSONWriter.Feature[0]));
            if (JSONObject.parseObject(result).getIntValue("code") == 200) {
                senderLog.setNotifyStatus("1");
            } else {
                senderLog.setNotifyStatus("2");
            }
            this.cardSenderLogService.updateById(senderLog);
        });
    }
    public void pushHttpSenderReturnCardRecord() {
        SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_HTTP_PUSH);
        if (systemApi == null) {
            return;
        }
        LambdaQueryWrapper<DeviceCardSenderLog> wrapper = Wrappers.lambdaQuery(DeviceCardSenderLog.class);
        wrapper.ge((v0) -> {
            return v0.getCreateTime();
        }, LocalDateTime.now().minusDays(1L));
        wrapper.ne((v0) -> {
            return v0.getNotifyStatus();
        }, "1");
        wrapper.eq((v0) -> {
            return v0.getCardSenderType();
        }, "0");
        List<DeviceCardSenderLog> list = this.cardSenderLogService.list(wrapper);
        list.removeIf(log2 -> {
            return Objects.isNull(log2.getPersonId());
        });
        Map<String, Long> deviceSnMap = new HashMap<>();
        this.cardSenderService.list().forEach(record -> {
            deviceSnMap.put(record.getDeviceSn(), record.getCardSenderId());
        });
        list.forEach(senderLog -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", senderLog.getId());
            jsonObject.put("personId", senderLog.getPersonId());
            jsonObject.put("cardId", senderLog.getCardId());
            jsonObject.put("cardSenderId", deviceSnMap.get(senderLog.getDeviceSn()));
            jsonObject.put("returnTime", senderLog.getCreateTime());
            String result = HttpUtil.post(systemApi.getHost() + "/cardSender/person/returnCard", jsonObject.toJSONString(new JSONWriter.Feature[0]), 30000);
            log.info("还卡结果通知响应：{},参数：{}", result, jsonObject.toJSONString(new JSONWriter.Feature[0]));
            if (JSONObject.parseObject(result).getIntValue("code") == 200) {
                senderLog.setNotifyStatus("1");
            } else {
                senderLog.setNotifyStatus("2");
            }
            this.cardSenderLogService.updateById(senderLog);
        });
    }
    public void pushMQTTBeacon() {
        List<DeviceBeacon> list = this.deviceBeaconService.list();
        MqttClient mqttClient = this.mqttPublishClient.getClient();
        if (mqttClient != null && mqttClient.isConnected()) {
            String topic = "/beaconStatus";
            list.forEach(beacon -> {
                String key = Constants.RAIL_TYPE_BEACON + beacon.getBeaconId();
                if (!this.alarmSendedMap.containsKey(key) || this.alarmSendedMap.get(key).isBefore(beacon.getUpdateTime())) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("uniqueId", beacon.getId());
                    jsonObject.put("beaconId", beacon.getBeaconId());
                    jsonObject.put("layerId", beacon.getLayerId());
                    jsonObject.put("layerHeight", beacon.getLayerHeight());
                    jsonObject.put("latitude", beacon.getLatitude());
                    jsonObject.put("longitude", beacon.getLongitude());
                    jsonObject.put("beaconPower", beacon.getBeaconPower());
                    if (beacon.getBeaconTransfer().intValue() == -1) {
                        jsonObject.put("beaconStatus", 0);
                    } else {
                        jsonObject.put("beaconStatus", beacon.getBeaconTransfer());
                    }
                    if (this.mqttPublishClient.publish(mqttClient, topic, jsonObject.toString()).booleanValue()) {
                        this.alarmSendedMap.put(key, beacon.getUpdateTime());
                    }
                }
            });
        }
    }
    /* JADX WARN: Multi-variable type inference failed */
    public void pushWSCardSenderDetail() {
        if (CollectionUtils.isEmpty(WebSocketServer.systemSessionMap)) {
            return;
        }
        List<PushCardSenderVO> senderVOList = new ArrayList<>();
        (this.cardSenderService.lambdaQuery().eq((v0) -> {
            return v0.getDeviceEnable();
        }, "Y")).list().forEach(device -> {
            Map<Integer, List<DeviceCardSenderDetail>> map = (Map) ( this.cardSenderDetailService.lambdaQuery().eq((v0) -> {
                return v0.getDeviceSn();
            }, device.getDeviceSn())).list().stream().collect(Collectors.groupingBy((v0) -> {
                return v0.getDeviceAims();
            }));
            if (!CollectionUtils.isEmpty(map)) {
                List<PushCardSenderAimsVO> aimsVOList = new ArrayList<>();
                map.forEach((deviceAims, numList) -> {
                    List<PushCardSenderNumVO> numVOList = new ArrayList<>();
                    numList.forEach(num -> {
                        PushCardSenderNumVO numVO = new PushCardSenderNumVO();
                        numVO.setCardId(num.getCardId());
                        numVO.setDeviceNum(num.getDeviceNum());
                        numVO.setElectricity(num.getElectricity());
                        numVO.setDeviceExist(num.getDeviceExist());
                        numVO.setCreateTime(num.getCreateTime());
                        numVO.setAims(num.getDeviceAims());
                        numVOList.add(numVO);
                    });
                    PushCardSenderAimsVO senderVO = new PushCardSenderAimsVO();
                    senderVO.setDeviceAims(deviceAims);
                    senderVO.setNumVOList(numVOList.stream().sorted(Comparator.comparing(PushCardSenderNumVO::getDeviceNum).reversed()).collect(Collectors.toList()));
                    aimsVOList.add(senderVO);
                });
                PushCardSenderVO senderVO = new PushCardSenderVO();
                senderVO.setDeviceSn(device.getDeviceSn());
                senderVO.setDeviceName(device.getDeviceName());
                senderVO.setAimsVOList((List) aimsVOList.stream().sorted(Comparator.comparing((v0) -> {
                    return v0.getDeviceAims();
                })).collect(Collectors.toList()));
                senderVO.setAimsCount(Integer.valueOf(aimsVOList.size() * 10));
                senderVOList.add(senderVO);
            }
        });
        JSONObject pushJson = new JSONObject();
        pushJson.put("msgType", "cardSenderView");
        pushJson.put(JsonResult.TOTAL, Integer.valueOf(senderVOList.size()));
        pushJson.put("data", senderVOList.stream().sorted(Comparator.comparing((v0) -> {
            return v0.getAimsCount();
        })).collect(Collectors.toList()));
        this.webSocketServer.sendMessageForXR(null, JSONObject.toJSONString(pushJson, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible));
    }
    /* JADX WARN: Multi-variable type inference failed */
    public void pushMQTTCardSenderDetail() {
        MqttClient mqttClient = this.mqttPublishClient.getClient();
        if (mqttClient != null && mqttClient.isConnected()) {
            LocalDateTime offlineTime = LocalDateTime.now().minusMinutes(30L);
            String topic = "/cardSender";
            (this.cardSenderService.lambdaQuery().eq((v0) -> {
                return v0.getDeviceEnable();
            }, "Y")).list().forEach(device -> {
                List<PushCardSenderMqttNumVO> slotList = new ArrayList<>();
                Map<Integer, List<DeviceCardSenderDetail>> map = (this.cardSenderDetailService.<DeviceCardSenderDetail>lambdaQuery().eq((v0) -> {
                    return v0.getDeviceSn();
                }, device.getDeviceSn())).list().stream().collect(Collectors.groupingBy((v0) -> {
                    return v0.getDeviceAims();
                }));
                if (!CollectionUtils.isEmpty(map)) {
                    map.forEach((deviceAims, numList) -> {
                        numList.forEach(num -> {
                            PushCardSenderMqttNumVO mqttNumVO = new PushCardSenderMqttNumVO();
                            mqttNumVO.setCardId(num.getCardId());
                            mqttNumVO.setNum(num.getDeviceNum());
                            mqttNumVO.setElectricity(num.getElectricity());
                            mqttNumVO.setExist(num.getDeviceExist());
                            mqttNumVO.setAims(num.getDeviceAims());
                            slotList.add(mqttNumVO);
                        });
                    });
                }
                try {
                    PushCardSenderMqttDetailVO senderDetailMqttVO = (PushCardSenderMqttDetailVO) BeanUtil.copyProperties((Object) device, PushCardSenderMqttDetailVO.class, new String[0]);
                    senderDetailMqttVO.setUniqueId(UUID.randomUUID().toString());
                    senderDetailMqttVO.setDeviceUrl(device.getFaceUrl());
                    senderDetailMqttVO.setHeartTime(device.getFaceHeartTime());
                    senderDetailMqttVO.setDeviceStatus((device.getFaceHeartTime() == null || device.getDeviceHeartTime() == null || !device.getFaceHeartTime().isAfter(offlineTime) || !device.getDeviceHeartTime().isAfter(offlineTime)) ? "0" : "1");
                    senderDetailMqttVO.setSenderCount(Integer.valueOf(map.size() * 10));
                    senderDetailMqttVO.setUseSenderCount(Long.valueOf(slotList.stream().filter(f -> {
                        return Objects.nonNull(f.getCardId()) && f.getCardId().compareTo((Long) 0L) > 0;
                    }).count()));
                    senderDetailMqttVO.setSlotList(slotList);
                    this.mqttPublishClient.publish(mqttClient, topic, JSONObject.toJSONString(senderDetailMqttVO, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible));
                } catch (Exception e) {
                    log.error("推送发卡机详情失败：{}", e.getMessage());
                }
            });
        }
    }
}
