package com.xrkc.job.domain;
import java.math.BigDecimal;
import java.time.LocalDate;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/DeptStatisticsVo.class */
public class DeptStatisticsVo {
    private Long deptId;
    private String deptName;
    private Long count;
    private Long deptTotal;
    private BigDecimal onlineRate;
    private LocalDate statisticsTime;
    public Long getDeptId() {
        return this.deptId;
    }
    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
    public String getDeptName() {
        return this.deptName;
    }
    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    public Long getCount() {
        return this.count;
    }
    public void setCount(Long count) {
        this.count = count;
    }
    public Long getDeptTotal() {
        return this.deptTotal;
    }
    public void setDeptTotal(Long deptTotal) {
        this.deptTotal = deptTotal;
    }
    public BigDecimal getOnlineRate() {
        return this.onlineRate;
    }
    public void setOnlineRate(BigDecimal onlineRate) {
        this.onlineRate = onlineRate;
    }
    public LocalDate getStatisticsTime() {
        return this.statisticsTime;
    }
    public void setStatisticsTime(LocalDate statisticsTime) {
        this.statisticsTime = statisticsTime;
    }
}
