package com.xrkc.job.service;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.xrkc.core.domain.cardDispenser.CardDispenserPerson;
import com.xrkc.job.domain.AreaAlarmTempVO;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IDeviceCardSenderPersonService.class */
public interface IDeviceCardSenderPersonService {
    List<CardDispenserPerson> selectTimeoutList(List<String> list, List<String> list2, int i);
    void batchUpdate(List<CardDispenserPerson> list);
    List<CardDispenserPerson> selectNoReturnAlarmList(Long l, AreaAlarmTempVO areaAlarmTempVO);
    List<CardDispenserPerson> timeWithinSuccessCardPerson(Long l);
    void updateOfflineStatus(List<Long> list);
    void updateOnlineStatus(List<Long> list);
    LambdaQueryChainWrapper<CardDispenserPerson> lambdaSelect();
    LambdaUpdateChainWrapper<CardDispenserPerson> lambdaUpdate();
}
