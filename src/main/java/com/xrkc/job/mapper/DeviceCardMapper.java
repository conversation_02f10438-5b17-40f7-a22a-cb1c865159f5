package com.xrkc.job.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xrkc.job.domain.DeviceCard;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/DeviceCardMapper.class */
public interface DeviceCardMapper extends BaseMapper<DeviceCard> {
    List<DeviceCard> selectLowPowerCard();
    List<DeviceCard> selectForUDP();
    void updateUseStatus(@Param("cardId") Long l, @Param("useStatus") String str);
    int updateUseStatus2(Map<String, Object> map);
    int resetCardHeart();
    DeviceCard findByCardId(Long l);
    @Select({"SELECT * FROM `core_person` where card_id =#{cardId};"})
    Long getPersonId(@Param("cardId") Long l);
}
