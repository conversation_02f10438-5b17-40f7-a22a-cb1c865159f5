package com.xrkc.job.task;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.mysql.cj.CharsetMapping;
import com.xrkc.core.domain.person.Person;
import com.xrkc.core.utils.PersonUtils;
import com.xrkc.job.domain.DeviceCard;
import com.xrkc.job.mapper.InspectRecordMapper;
import com.xrkc.job.mapper.PersonMapper;
import com.xrkc.job.mapper.SysConfigMapper;
import com.xrkc.job.service.IDeviceCardService;
import com.xrkc.job.service.IPersonService;
import com.xrkc.job.service.IPositionCurrentService;
import com.xrkc.job.util.FileUtil;
import com.xrkc.job.util.MQTTPublishClient;
import com.xrkc.job.util.StringUtils;
import java.io.UnsupportedEncodingException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
@Component("uploadTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/UploadTask.class */
public class UploadTask {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) UploadTask.class);
    @Autowired
    private MQTTPublishClient mqttClient;
    @Autowired
    private IPersonService personService;
    @Autowired
    private PersonMapper personMapper;
    @Autowired
    private IDeviceCardService deviceCardService;
    @Autowired
    private IPositionCurrentService positionCurrentService;
    private final String defaultCharset = CharsetMapping.MYSQL_CHARSET_NAME_utf8;
    private MqttClient client;
    @Autowired
    private InspectRecordMapper inspectMapper;
    @Autowired
    private SysConfigMapper sysConfigMapper;
    public void personUpload() {
        if (this.client != null && this.client.isConnected()) {
            String topic = "/uploadPerson";
            try {
                this.client.subscribe("/uploadPerson", 2, (topic1, message) -> {
                    String msg = new String(message.getPayload(), CharsetMapping.MYSQL_CHARSET_NAME_utf8);
                    StringBuilder error = new StringBuilder();
                    log.info(" topic:{} 接受到的消息为：{}", topic, message);
                    JSONObject json = JSONObject.parseObject(msg);
                    Long personId = json.getLong("personId");
                    String uniqueId = json.getString("uniqueId");
                    try {
                        try {
                            Person person = new Person();
                            String realName = json.getString("realName");
                            String jobNumber = json.getString("jobNumber");
                            String idNumber = json.getString("idNumber");
                            String personPhotoBase64 = json.getString(PersonUtils.personPhotoKeyPrefix);
                            String personType = json.getString("personType");
                            String phone = json.getString("phone");
                            String sex = json.getString("sex");
                            String cardType = json.getString("cardType");
                            String personTypeName = json.getString("personTypeName");
                            String cengNumber = json.getString("cengNumber");
                            String diplomaNumber = json.getString("diplomaNumber");
                            String duty = json.getString("duty");
                            String email = json.getString("email");
                            String experience = json.getString("experience");
                            person.setPhone(phone);
                            person.setSex(sex);
                            person.setCardType(cardType);
                            person.setPersonTypeName(personTypeName);
                            person.setCengNumber(cengNumber);
                            person.setDiplomaNumber(diplomaNumber);
                            person.setDuty(duty);
                            person.setEmail(email);
                            person.setExperience(experience);
                            Long cardId = json.getLong("cardId");
                            Integer operateType = json.getInteger("operateType");
                            if (Objects.isNull(operateType) || (!operateType.equals(1) && !operateType.equals(2) && !operateType.equals(3))) {
                                error.append("操作类型未提供或提供的值不合法").append(";");
                            }
                            String personPhoto = "";
                            if (StringUtils.isNotBlank(personPhotoBase64)) {
                                personPhoto = FileUtil.saveBase64UpLoadFile(personPhotoBase64, "/xrkc/", "xrkc");
                                if (StringUtils.isBlank(personPhoto)) {
                                    error.append("图片格式base64异常").append(";");
                                }
                                person.setPersonPhoto(personPhoto);
                            }
                            if (StringUtils.isBlank(personType)) {
                                personType = "staff";
                            }
                            if (Objects.isNull(personId)) {
                                error.append("人员personId未提供,无法删除或编辑").append(";");
                            }
                            if (operateType.equals(1) && StringUtils.isBlank(realName)) {
                                error.append("人员姓名未提供,无法新增").append(";");
                            }
                            if (error.length() == 0) {
                                if (operateType.equals(1)) {
                                    if (Objects.nonNull(cardId)) {
                                        person.setCardId(cardId);
                                        String bind = bindPersonCardId(null, cardId, null);
                                        if (!"success".equals(bind)) {
                                            error.append(bind).append(";");
                                            if (error.length() > 0) {
                                                publishErrorMsg(topic, uniqueId, error.toString());
                                                return;
                                            }
                                            return;
                                        }
                                    }
                                    if (StringUtils.isNotBlank(jobNumber) && existJobNumberByParams(jobNumber, null) > 0) {
                                        person.setJobNumber(jobNumber);
                                        error.append("该工号已经录入").append(";");
                                        if (error.length() > 0) {
                                            publishErrorMsg(topic, uniqueId, error.toString());
                                            return;
                                        }
                                        return;
                                    }
                                    if (StringUtils.isNotBlank(idNumber) && existIdNumberByParams(idNumber, null) > 0) {
                                        person.setIdNumber(idNumber);
                                        error.append("该证件号已经录入").append(";");
                                        if (error.length() > 0) {
                                            publishErrorMsg(topic, uniqueId, error.toString());
                                            return;
                                        }
                                        return;
                                    }
                                    person.setCreateBy("mqtt传输");
                                    person.setJobStatus("Y");
                                    person.setPersonType("staff");
                                    person.setRealName(realName);
                                    person.setJobNumber(jobNumber);
                                    person.setPersonPhoto(personPhoto);
                                    person.setPersonType(personType);
                                    this.personService.insertEntity(person);
                                } else if (operateType.equals(2)) {
                                    person.setPersonId(personId);
                                    person.setRealName(realName);
                                    person.setJobNumber(jobNumber);
                                    person.setPersonPhoto(personPhoto);
                                    person.setPersonType(personType);
                                    Person oldEntity = findByPersonId(personId, null);
                                    if (Objects.isNull(oldEntity)) {
                                        error.append("不存在相关数据，请刷新后重试").append(";");
                                        if (error.length() > 0) {
                                            publishErrorMsg(topic, uniqueId, error.toString());
                                            return;
                                        }
                                        return;
                                    }
                                    if ("N".equals(oldEntity.getJobStatus())) {
                                        error.append("员工已离职，不可编辑").append(";");
                                        if (error.length() > 0) {
                                            publishErrorMsg(topic, uniqueId, error.toString());
                                            return;
                                        }
                                        return;
                                    }
                                    if (StringUtils.isNotBlank(jobNumber) && existJobNumberByParams(jobNumber, personId) > 0) {
                                        error.append("该工号已经录入").append(";");
                                        if (error.length() > 0) {
                                            publishErrorMsg(topic, uniqueId, error.toString());
                                            return;
                                        }
                                        return;
                                    }
                                    if (StringUtils.isNotBlank(idNumber) && existIdNumberByParams(idNumber, personId) > 0) {
                                        error.append("该证件号已经录入").append(";");
                                        if (error.length() > 0) {
                                            publishErrorMsg(topic, uniqueId, error.toString());
                                            return;
                                        }
                                        return;
                                    }
                                    if (Objects.nonNull(oldEntity.getCardId()) && Objects.isNull(cardId)) {
                                        unbindPersonCardId(oldEntity.getCardId());
                                    }
                                    if (Objects.nonNull(cardId)) {
                                        String bind2 = bindPersonCardId(personId, cardId, oldEntity.getCardId());
                                        if (!"success".equals(bind2)) {
                                            error.append(bind2).append(";");
                                            if (error.length() > 0) {
                                                publishErrorMsg(topic, uniqueId, error.toString());
                                                return;
                                            }
                                            return;
                                        }
                                    }
                                    if (Objects.nonNull(oldEntity.getCardId()) && Objects.isNull(cardId)) {
                                        unbindPersonCardId(oldEntity.getCardId());
                                    }
                                    person.setUpdateBy("mqtt传输");
                                    if (this.personService.updateEntity(person) < 0) {
                                        error.append("当前操作未改变数据，请刷新后重试").append(";");
                                    }
                                } else if (operateType.equals(3)) {
                                    String result = checkInspect(personId);
                                    if (!"success".equals(result)) {
                                        error.append(result).append(";");
                                        if (error.length() > 0) {
                                            publishErrorMsg(topic, uniqueId, error.toString());
                                            return;
                                        }
                                        return;
                                    }
                                    Long oldCardId = this.personMapper.findCardIdByPersonId(personId);
                                    if (Objects.nonNull(oldCardId)) {
                                        ArrayList<Long> cardIds = new ArrayList<>();
                                        cardIds.add(oldCardId);
                                        this.deviceCardService.updateUseStatus(null, "N", cardIds);
                                        this.positionCurrentService.offlineByCard(null, cardIds);
                                    }
                                    this.personService.deleteByPersonId(personId);
                                }
                            }
                            if (error.length() > 0) {
                                publishErrorMsg(topic, uniqueId, error.toString());
                            }
                        } catch (JSONException e) {
                            error.append("json处理出错，").append(e.getMessage()).append(";");
                            if (error.length() > 0) {
                                publishErrorMsg(topic, uniqueId, error.toString());
                            }
                        } catch (Exception e2) {
                            error.append("服务器处理出错，").append(e2.getMessage()).append(";");
                            if (error.length() > 0) {
                                publishErrorMsg(topic, uniqueId, error.toString());
                            }
                        }
                    } catch (Throwable th) {
                        if (error.length() > 0) {
                            publishErrorMsg(topic, uniqueId, error.toString());
                        }
                        throw th;
                    }
                });
                return;
            } catch (Exception e) {
                e.printStackTrace();
                log.error("subscribeBeacon错误", (Throwable) e);
                return;
            }
        }
        this.client = this.mqttClient.getClient();
    }
    private String checkInspect(Long personId) {
        int count = this.inspectMapper.existTeamPerson();
        if (count == 0) {
            return "success";
        }
        String personName = this.personMapper.findPersonName(personId);
        if (StringUtils.isBlank(personName)) {
            return "人员id不存在数据";
        }
        String TeamName = this.inspectMapper.findTeamBypersonId(personId);
        Set<String> roadLeader = this.inspectMapper.findRoadLeaderNames();
        Set<String> locationLeader = this.inspectMapper.findLocationLeaderNames();
        if (StringUtils.isNotBlank(TeamName)) {
            return personName + "在" + TeamName + "中引用、";
        }
        if (roadLeader.contains(personName)) {
            return personName + "在巡检路线中引用、";
        }
        if (locationLeader.contains(personName)) {
            return personName + "在巡检点中引用、";
        }
        return "success";
    }
    private Person findByPersonId(Long personId, String personType) {
        Map<String, Object> params = new HashMap<>();
        params.put("personId", personId);
        params.put("personType", personType);
        Integer intervalSecond = this.sysConfigMapper.getByIntervalSecond();
        LocalDateTime minusSeconds = LocalDateTime.now().minusSeconds(intervalSecond.intValue());
        params.put("minusSeconds", minusSeconds);
        Person person = this.personMapper.findByPersonId(params);
        if (Objects.isNull(personId) || Objects.isNull(person)) {
            return null;
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(person.getAccessory())) {
            person.setAccessoryList(Arrays.asList(person.getAccessory().split(",")));
        }
        person.setAccessory(null);
        return person;
    }
    private int existJobNumberByParams(String jobNumber, Long personId) {
        HashMap<String, Object> query = new HashMap<>();
        query.put("jobNumber", jobNumber);
        query.put("personId", personId);
        return this.personMapper.existByParams(query);
    }
    public int existIdNumberByParams(String idNumber, Long personId) {
        HashMap<String, Object> query = new HashMap<>();
        query.put("idNumber", idNumber);
        query.put("personId", personId);
        return this.personMapper.existByParams(query);
    }
    private int existByParams(Long personId, Long cardId) {
        Map<String, Object> query = new HashMap<>();
        query.put("personId", personId);
        query.put("cardId", cardId);
        return this.personMapper.existByParams(query);
    }
    private String bindPersonCardId(Long personId, Long cardId, Long oldCardId) {
        if (Objects.nonNull(cardId)) {
            if (existByParams(personId, cardId) > 0) {
                return "该定位卡号已被使用，换其它试试";
            }
            DeviceCard card = this.deviceCardService.findByCardId(cardId);
            if (Objects.isNull(card)) {
                return "设备库中不存在该定位卡号，请先添加";
            }
            if (!Objects.equals(cardId, oldCardId) && "Y".equals(card.getUseStatus()) && this.deviceCardService.getExistByCardIdEqPersonId(cardId, personId)) {
                log.info("人员绑卡失败，" + cardId + "该定位卡已被使用，换其它试试");
                return "该定位卡已被使用，换其它试试";
            }
            if (Objects.nonNull(oldCardId) && !Objects.equals(cardId, oldCardId)) {
                String result = unbindPersonCardId(oldCardId);
                if (!"success".equals(result)) {
                    return result;
                }
            }
            this.deviceCardService.updateUseStatus(cardId, "Y", null);
            return "success";
        }
        return "卡号不能为空";
    }
    private String unbindPersonCardId(Long cardId) {
        if (Objects.nonNull(cardId)) {
            this.deviceCardService.updateUseStatus(cardId, "N", null);
            this.positionCurrentService.offlineByCard(cardId, null);
            return "success";
        }
        return "卡号不能为空";
    }
    private void publishErrorMsg(String pushTopic, String uniqueId, String errorMsg) throws IllegalStateException, UnsupportedEncodingException {
        JSONObject msg = new JSONObject();
        msg.put("uniqueId", uniqueId);
        msg.put("msg", errorMsg);
        String topic = pushTopic + "/error";
        log.error("errorMsg:{}", JSONObject.toJSONString(msg, new JSONWriter.Feature[0]));
        this.mqttClient.publish(this.client, topic, msg.toString());
    }
}
