package com.xrkc.job.task;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.domain.person.Person;
import com.xrkc.core.domain.system.SystemApi;
import com.xrkc.core.enums.NoticeType;
import com.xrkc.job.domain.Alarm;
import com.xrkc.job.domain.AlarmNotice;
import com.xrkc.job.domain.AlarmNoticeRecord;
import com.xrkc.job.domain.AlarmNoticeRecordPerson;
import com.xrkc.job.mapper.AlarmNoticeSosMapper;
import com.xrkc.job.service.IAlarmNoticeRecordPersonService;
import com.xrkc.job.service.IAlarmNoticeRecordService;
import com.xrkc.job.service.IAlarmService;
import com.xrkc.job.service.IPersonService;
import com.xrkc.job.service.SystemApiService;
import com.xrkc.job.util.AliyunUtilApi;
import com.xrkc.job.util.CollStrConver;
import com.xrkc.job.util.CommonUtil;
import com.xrkc.job.util.EmailUtilApi;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import net.jodah.expiringmap.ExpiringMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
@Component("messageTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/MessageTask.class */
public class MessageTask {
    @Autowired
    private IPersonService personService;
    @Autowired
    private AlarmNoticeSosMapper alarmNoticeSosMapper;
    @Autowired
    private AliyunUtilApi aliyunUtilApi;
    @Autowired
    private EmailUtilApi emailUtilApi;
    @Autowired
    private IAlarmService alarmService;
    @Autowired
    private IAlarmNoticeRecordService alarmNoticeRecordService;
    @Autowired
    private IAlarmNoticeRecordPersonService alarmNoticeRecordPersonService;
    @Autowired
    private SystemApiService systemApiService;
    public static final String source = "人员";
    private static final String ALARM_TYPE_96 = "96";
    ExpiringMap<String, LocalDateTime> noticeAlarmMap = ExpiringMap.builder().expiration(5, TimeUnit.DAYS).build();
    ExpiringMap<Long, LocalDateTime> AlarmSmsMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    ExpiringMap<Long, LocalDateTime> recordSmsMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    private static final Logger log = LogManager.getLogger((Class<?>) MessageTask.class);
    private static final Long ALARM_NOTICE_SOS_ID = 2021995L;
    @Transactional(rollbackFor = {Exception.class})
    public void insertAlarmNoticeRecord() {
        SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_SMS_PERSON);
        SystemApi emailApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_SEND_EMAIL);
        if (Objects.nonNull(systemApi) || Objects.nonNull(emailApi)) {
            List<Alarm> alarmRailList = this.alarmService.selectUnDisposedAlarmRail();
            List<Alarm> alarmSOSList = this.alarmService.selectUnDisposedAlarmSOS();
            if (!CollectionUtils.isEmpty(alarmRailList)) {
                Map<AlarmNotice, List<Alarm>> map = getNoticeMap(alarmRailList);
                map.forEach((alarmNotice, alarmList) -> {
                    insertAlarmRecord(alarmNotice, alarmList);
                });
            }
            if (!CollectionUtils.isEmpty(alarmSOSList)) {
                AlarmNotice alarmNoticeSos = this.alarmNoticeSosMapper.findByAlarmNoticeSosId(ALARM_NOTICE_SOS_ID);
                if (Objects.nonNull(alarmNoticeSos)) {
                    insertAlarmRecord(alarmNoticeSos, alarmSOSList);
                }
            }
        }
    }
    private Map<AlarmNotice, List<Alarm>> getNoticeMap(List<Alarm> alarmRailList) {
        AlarmNotice notice;
        Map<AlarmNotice, List<Alarm>> map = new HashMap<>();
        for (Alarm alarm : alarmRailList) {
            Long noticeId = alarm.getAlarmNoticeId();
            if ("96".equals(alarm.getAlarmType())) {
                if (alarm.getHighestAlarmLevel().equals("2")) {
                    noticeId = alarm.getSecondAlarmNoticeId();
                } else if (alarm.getHighestAlarmLevel().equals("3")) {
                    noticeId = alarm.getThirdAlarmNoticeId();
                }
            }
            if (noticeId != null && (notice = this.alarmNoticeSosMapper.findByAlarmNoticeId(noticeId)) != null) {
                if (!map.containsKey(notice)) {
                    map.put(notice, new ArrayList<>());
                }
                map.get(notice).add(alarm);
            }
        }
        return map;
    }
    private void insertAlarmRecord(AlarmNotice alarmNotice, List<Alarm> alarmList) {
        if (Objects.isNull(alarmNotice)) {
            log.error("alarmNotice is null，alarmList={}", JSONObject.toJSONString(alarmList, new JSONWriter.Feature[0]));
        } else {
            alarmList.stream().forEach(personAlarm -> {
                String noticeKey = StrUtil.concat(true, personAlarm.getAlarmId().toString(), ",", alarmNotice.getAlarmNoticeId().toString());
                if (this.noticeAlarmMap.containsKey(noticeKey)) {
                    return;
                }
                Long alarmNoticeId = alarmNotice.getAlarmNoticeId();
                List<AlarmNoticeRecord> allSosNoticeRecord = new ArrayList<>();
                ArrayList arrayList = new ArrayList();
                List<Long> firstPersons = CollStrConver.str2longList(alarmNotice.getFirstPersonId());
                List<Long> secondPersons = CollStrConver.str2longList(alarmNotice.getSecondPersonId());
                List<Long> thirdPersons = CollStrConver.str2longList(alarmNotice.getThirdPersonId());
                LocalDateTime now = LocalDateTime.now();
                String noticeType = alarmNotice.getNoticeType();
                if (!CollectionUtils.isEmpty(firstPersons)) {
                    ArrayList arrayList2 = new ArrayList();
                    AlarmNoticeRecord firstRecord = new AlarmNoticeRecord();
                    firstRecord.setNoticeId(alarmNoticeId);
                    firstRecord.setAlarmId(personAlarm.getAlarmId());
                    firstRecord.setNoticeInterval(0L);
                    firstRecord.setNoticeTime(now);
                    firstRecord.setAlarmTime(personAlarm.getAcceptTime());
                    firstRecord.setAlarmType(personAlarm.getAlarmType());
                    firstRecord.setAlarmTypeName(personAlarm.getAlarmTypeName());
                    firstRecord.setAreaName(personAlarm.getAreaName());
                    firstRecord.setLayerId(personAlarm.getLayerId());
                    firstRecord.setDeptName(personAlarm.getDeptName());
                    firstRecord.setRealName(personAlarm.getRealName());
                    firstRecord.setNoticeStatic("20");
                    firstRecord.setNoticeLevel(1);
                    List<Person> personList = this.personService.getPersonsByIds(firstPersons);
                    firstRecord.setNoticeType(noticeType);
                    firstRecord.setSource(source);
                    if (CollectionUtils.isEmpty(personList)) {
                        firstRecord.setNoticeStatic("10");
                        firstRecord.setRemark("无有效接收人");
                        allSosNoticeRecord.add(firstRecord);
                        this.alarmNoticeRecordService.saveEntity(firstRecord);
                    } else {
                        this.alarmNoticeRecordService.saveEntity(firstRecord);
                        personList.forEach(person -> {
                            AlarmNoticeRecordPerson recordPerson = new AlarmNoticeRecordPerson();
                            recordPerson.setRecordId(firstRecord.getRecordId());
                            recordPerson.setNoticeTime(firstRecord.getNoticeTime());
                            recordPerson.setPersonId(person.getPersonId());
                            if (CommonUtil.isValidNumberOrMail(person, noticeType)) {
                                recordPerson.setNoticeStatic("20");
                                recordPerson.setNoticeResult("已处理待验证");
                            } else {
                                recordPerson.setNoticeStatic("10");
                                recordPerson.setNoticeResult("无效接收人");
                            }
                            recordPerson.setPhone(CommonUtil.setPersonPhone(person, noticeType));
                            recordPerson.setRealName(person.getRealName());
                            arrayList2.add(recordPerson);
                        });
                        allSosNoticeRecord.add(firstRecord);
                    }
                    arrayList.addAll(arrayList2);
                }
                if (!CollectionUtils.isEmpty(secondPersons)) {
                    ArrayList arrayList3 = new ArrayList();
                    AlarmNoticeRecord secondRecord = new AlarmNoticeRecord();
                    secondRecord.setNoticeId(alarmNoticeId);
                    secondRecord.setNoticeInterval(alarmNotice.getSecondIntervalSecond());
                    secondRecord.setNoticeTime(now.plusMinutes(alarmNotice.getSecondIntervalSecond().longValue()));
                    secondRecord.setAlarmTime(personAlarm.getAcceptTime());
                    secondRecord.setAlarmId(personAlarm.getAlarmId());
                    secondRecord.setAlarmType(personAlarm.getAlarmType());
                    secondRecord.setAlarmTypeName(personAlarm.getAlarmTypeName());
                    secondRecord.setAreaName(personAlarm.getAreaName());
                    secondRecord.setLayerId(personAlarm.getLayerId());
                    secondRecord.setDeptName(personAlarm.getDeptName());
                    secondRecord.setRealName(personAlarm.getRealName());
                    secondRecord.setNoticeStatic("20");
                    secondRecord.setNoticeLevel(2);
                    allSosNoticeRecord.add(secondRecord);
                    secondRecord.setNoticeType(noticeType);
                    secondRecord.setSource(source);
                    List<Person> personList2 = this.personService.getPersonsByIds(secondPersons);
                    if (CollectionUtils.isEmpty(personList2)) {
                        secondRecord.setNoticeStatic("10");
                        secondRecord.setRemark("无有效接收人");
                        this.alarmNoticeRecordService.saveEntity(secondRecord);
                        allSosNoticeRecord.add(secondRecord);
                    } else {
                        this.alarmNoticeRecordService.saveEntity(secondRecord);
                        personList2.forEach(person2 -> {
                            AlarmNoticeRecordPerson recordPerson = new AlarmNoticeRecordPerson();
                            recordPerson.setRecordId(secondRecord.getRecordId());
                            recordPerson.setNoticeTime(secondRecord.getNoticeTime());
                            recordPerson.setPersonId(person2.getPersonId());
                            if (CommonUtil.isValidNumberOrMail(person2, noticeType)) {
                                recordPerson.setNoticeStatic("20");
                                recordPerson.setNoticeResult("已处理待验证");
                            } else {
                                recordPerson.setNoticeStatic("10");
                                recordPerson.setNoticeResult("无效接收人");
                            }
                            recordPerson.setPhone(CommonUtil.setPersonPhone(person2, noticeType));
                            recordPerson.setRealName(person2.getRealName());
                            arrayList3.add(recordPerson);
                        });
                    }
                    arrayList.addAll(arrayList3);
                }
                if (!CollectionUtils.isEmpty(thirdPersons)) {
                    ArrayList arrayList4 = new ArrayList();
                    AlarmNoticeRecord thirdRecord = new AlarmNoticeRecord();
                    thirdRecord.setNoticeInterval(alarmNotice.getThirdIntervalSecond());
                    thirdRecord.setNoticeId(alarmNoticeId);
                    thirdRecord.setNoticeTime(now.plusMinutes(alarmNotice.getThirdIntervalSecond().longValue()));
                    thirdRecord.setAlarmTime(personAlarm.getAcceptTime());
                    thirdRecord.setAlarmId(personAlarm.getAlarmId());
                    thirdRecord.setAlarmType(personAlarm.getAlarmType());
                    thirdRecord.setAlarmTypeName(personAlarm.getAlarmTypeName());
                    thirdRecord.setAreaName(personAlarm.getAreaName());
                    thirdRecord.setLayerId(personAlarm.getLayerId());
                    thirdRecord.setDeptName(personAlarm.getDeptName());
                    thirdRecord.setRealName(personAlarm.getRealName());
                    thirdRecord.setNoticeStatic("20");
                    thirdRecord.setNoticeLevel(3);
                    thirdRecord.setNoticeType(noticeType);
                    thirdRecord.setSource(source);
                    List<Person> personList3 = this.personService.getPersonsByIds(thirdPersons);
                    if (CollectionUtils.isEmpty(personList3)) {
                        thirdRecord.setNoticeStatic("10");
                        thirdRecord.setRemark("无有效接收人");
                        this.alarmNoticeRecordService.saveEntity(thirdRecord);
                        allSosNoticeRecord.add(thirdRecord);
                    } else {
                        this.alarmNoticeRecordService.saveEntity(thirdRecord);
                        personList3.forEach(person3 -> {
                            AlarmNoticeRecordPerson recordPerson = new AlarmNoticeRecordPerson();
                            recordPerson.setRecordId(thirdRecord.getRecordId());
                            recordPerson.setNoticeTime(thirdRecord.getNoticeTime());
                            recordPerson.setPersonId(person3.getPersonId());
                            if (CommonUtil.isValidNumberOrMail(person3, noticeType)) {
                                recordPerson.setNoticeStatic("20");
                                recordPerson.setNoticeResult("已处理待验证");
                            } else {
                                recordPerson.setNoticeStatic("10");
                                recordPerson.setNoticeResult("无效接收人");
                            }
                            recordPerson.setPhone(CommonUtil.setPersonPhone(person3, noticeType));
                            recordPerson.setRealName(person3.getRealName());
                            arrayList4.add(recordPerson);
                        });
                    }
                    arrayList.addAll(arrayList4);
                }
                if (!CollectionUtils.isEmpty(arrayList)) {
                    this.alarmNoticeRecordPersonService.saveBatch(arrayList);
                }
                this.noticeAlarmMap.put(noticeKey, personAlarm.getAcceptTime());
            });
        }
    }
    @Transactional(rollbackFor = {Exception.class})
    public void verifyAlarmNotice() {
        LocalDateTime now = LocalDateTime.now();
        List<AlarmNoticeRecord> alarmRecords = this.alarmNoticeRecordService.selectListByParams("20", source, now.minusSeconds(60L), now);
        if (CollectionUtils.isEmpty(alarmRecords)) {
            return;
        }
        List<Long> disposedAlarmIds = this.alarmService.selectDisposedAlarmId((List) alarmRecords.stream().map((v0) -> {
            return v0.getAlarmId();
        }).collect(Collectors.toList()));
        alarmRecords.forEach(alarmRecord -> {
            if (this.AlarmSmsMap.containsKey(alarmRecord.getRecordId())) {
                return;
            }
            if (disposedAlarmIds.contains(alarmRecord.getAlarmId())) {
                alarmRecord.setNoticeStatic("30");
                alarmRecord.setRemark("报警已处理，无需通知");
                this.alarmNoticeRecordService.updateById(alarmRecord);
                AlarmNoticeRecordPerson recordPerson = new AlarmNoticeRecordPerson();
                recordPerson.setRecordId(alarmRecord.getRecordId());
                recordPerson.setNoticeStatic("30");
                recordPerson.setNoticeResult("报警已处理，无需通知");
                this.alarmNoticeRecordPersonService.updateByRecordId(recordPerson);
            } else {
                List<AlarmNoticeRecordPerson> recordPeoples = this.alarmNoticeRecordPersonService.getListByRecordId(alarmRecord.getRecordId());
                if (CollectionUtils.isEmpty(recordPeoples)) {
                    alarmRecord.setNoticeStatic("10");
                    alarmRecord.setRemark("无有效通知人");
                    this.alarmNoticeRecordService.updateById(alarmRecord);
                    return;
                } else {
                    StringBuilder phoneRealNames = new StringBuilder();
                    recordPeoples.forEach(recordPeople -> {
                        recordPeople.setNoticeStatic("40");
                        recordPeople.setNoticeResult("已验证待通知");
                        LambdaQueryWrapper<AlarmNoticeRecordPerson> wrapper3 = Wrappers.lambdaQuery();
                        ( wrapper3.eq((v0) -> {
                            return v0.getRecordId();
                        }, recordPeople.getRecordId())).eq((v0) -> {
                            return v0.getPersonId();
                        }, recordPeople.getPersonId());
                        this.alarmNoticeRecordPersonService.update(recordPeople, wrapper3);
                        phoneRealNames.append(recordPeople.getRealName()).append("：").append(recordPeople.getPhone()).append("；");
                    });
                    alarmRecord.setNoticeStatic("40");
                    alarmRecord.setRemark("已验证待通知");
                    alarmRecord.setPhoneNumbers((String) recordPeoples.stream().map((v0) -> {
                        return v0.getPhone();
                    }).collect(Collectors.joining(",")));
                    alarmRecord.setPhoneRealNames(phoneRealNames.toString());
                    this.alarmNoticeRecordService.updateById(alarmRecord);
                }
            }
            this.AlarmSmsMap.put(alarmRecord.getRecordId(), alarmRecord.getCreateTime());
        });
    }
    public void sendPersonAlarmSms() {
        LocalDateTime now = LocalDateTime.now();
        List<AlarmNoticeRecord> alarmRecords = this.alarmNoticeRecordService.selectListByParams("40", source, now.minusSeconds(30L), now);
        alarmRecords.removeIf(r -> {
            return StringUtils.isBlank(r.getPhoneNumbers());
        });
        if (CollectionUtils.isEmpty(alarmRecords)) {
            return;
        }
        SystemApi smsApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_SMS_PERSON);
        SystemApi mailApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_SEND_EMAIL);
        alarmRecords.stream().forEach(alarmRecord -> {
            if (this.recordSmsMap.containsKey(alarmRecord.getRecordId())) {
                return;
            }
            this.recordSmsMap.put(alarmRecord.getRecordId(), now);
            try {
                if (NoticeType.sms.getKey().equals(alarmRecord.getNoticeType())) {
                    this.aliyunUtilApi.sendBatchSms(smsApi, alarmRecord, source);
                } else if (NoticeType.email.getKey().equals(alarmRecord.getNoticeType())) {
                    this.emailUtilApi.sendMail(mailApi, alarmRecord, source);
                } else {
                    log.error("短信发送通知类型异常:{}", alarmRecord.getRecordId());
                }
            } catch (Exception e) {
                log.error(e.getMessage(), (Throwable) e);
            }
        });
    }
}
