package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.job.domain.DeviceCardSenderLog;
import com.xrkc.job.mapper.DeviceCardSenderLogMapper;
import com.xrkc.job.service.IDeviceCardSenderLogService;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/DeviceCardSenderServiceLogImpl.class */
public class DeviceCardSenderServiceLogImpl extends ServiceImpl<DeviceCardSenderLogMapper, DeviceCardSenderLog> implements IDeviceCardSenderLogService {
    @Autowired
    private DeviceCardSenderLogMapper deviceCardSenderLogMapper;
    /* JADX WARN: Incorrect condition in loop: B:28:0x0020 */
    @Override // com.xrkc.job.service.IDeviceCardSenderLogService
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void batchDelete(int r5) {
        /*
            r4 = this;
            java.time.LocalDateTime r0 = java.time.LocalDateTime.now()
            r1 = r5
            long r1 = (long) r1
            java.time.LocalDateTime r0 = r0.minusDays(r1)
            r6 = r0
            r0 = r4
            com.xrkc.job.mapper.DeviceCardSenderLogMapper r0 = r0.deviceCardSenderLogMapper
            r1 = r6
            java.lang.Long r0 = r0.findDelMaxId(r1)
            r7 = r0
            r0 = r7
            boolean r0 = java.util.Objects.nonNull(r0)
            if (r0 == 0) goto L35
            r0 = 1
            r8 = r0
        L1e:
            r0 = r8
            if (r0 <= 0) goto L35
            r0 = r4
            com.xrkc.job.mapper.DeviceCardSenderLogMapper r0 = r0.deviceCardSenderLogMapper
            r1 = r7
            r2 = 1000(0x3e8, float:1.401E-42)
            int r0 = r0.delByMaxId(r1, r2)
            r8 = r0
            goto L1e
        L35:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: com.xrkc.job.service.impl.DeviceCardSenderServiceLogImpl.batchDelete(int):void");
    }
    @Override // com.xrkc.job.service.IDeviceCardSenderLogService
    public void batchInsert(List<DeviceCardSenderLog> list) {
        list.stream().forEach(t -> {
            t.setId(null);
            t.setCreateTime(LocalDateTime.now());
        });
        this.deviceCardSenderLogMapper.insertBatch(list);
    }
}
