package com.xrkc.job.service.impl;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.domain.rssi.RssiData;
import com.xrkc.core.domain.rssi.RssiTemp;
import com.xrkc.job.domain.PositionVO;
import com.xrkc.job.mapper.RssiMapper;
import com.xrkc.job.mapper.RssiTempMapper;
import com.xrkc.job.service.IDeviceBeaconService;
import com.xrkc.job.service.IDeviceCardService;
import com.xrkc.job.service.IRssiService;
import com.xrkc.job.service.ISystemConfigService;
import com.xrkc.job.util.StringUtils;
import java.lang.invoke.SerializedLambda;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeSet;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/RssiServiceImpl.class */
public class RssiServiceImpl implements IRssiService {
    @Autowired
    private RssiMapper rssiMapper;
    @Autowired
    private RssiTempMapper rssiTempMapper;
    @Autowired
    private ISystemConfigService systemConfigService;
    @Autowired
    private IDeviceCardService deviceCardService;
    @Autowired
    private IDeviceBeaconService deviceBeaconService;
    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.xrkc.job.service.IRssiService
    public int batchReplace(Map<String, Object> map) {
        LocalDateTime creatTime = LocalDateTime.now();
        LocalDateTime minusSeconds = LocalDateTime.now().minusSeconds(3L);
        this.rssiMapper.delByCreatTime(creatTime);
        Map<String, String> configMap = this.systemConfigService.getConfig();
        String v_distance = configMap.get(ConfigValue.CONFIG_KEY_SINGLE_VALID_DISTANCE);
        Integer validDistance = Integer.valueOf(StringUtils.isNotBlank(v_distance) ? Integer.parseInt(v_distance) : 11);
        String v_rssi_min = configMap.get(ConfigValue.CONFIG_KEY_RSSI_MIN);
        Integer rssiMin = Integer.valueOf(StringUtils.isNotBlank(v_rssi_min) ? Integer.parseInt(v_rssi_min) : -100);
        List<RssiTemp> rssiTempList = this.rssiTempMapper.selectList((Wrapper) (( ( new LambdaQueryWrapper<PositionVO>().lt((v0) -> {
            return v0.getDistance();
        }, validDistance)).gt((v0) -> {
            return v0.getRssi();
        }, rssiMin)).gt((v0) -> {
            return v0.getCreateTime();
        }, minusSeconds)).orderByDesc( (v0) -> {
            return v0.getCreateTime();
        }));
        if (!CollectionUtils.isEmpty(rssiTempList)) {
            List<Long> cardIdList = this.deviceCardService.selectCardIdByRssi("Y");
            List<Integer> beaconIdList = this.deviceBeaconService.selectBeaconIdByRssi("Y");
            if (!CollectionUtils.isEmpty(cardIdList) && !CollectionUtils.isEmpty(beaconIdList)) {
                rssiTempList.removeIf(r -> {
                    return Objects.isNull(r.getCardId()) || Objects.isNull(r.getBeaconId()) || !cardIdList.contains(r.getCardId()) || !beaconIdList.contains(r.getBeaconId());
                });
                if (!CollectionUtils.isEmpty(rssiTempList)) {
                    List<RssiTemp> insertRssiTempList = rssiTempList.stream().collect(
                            Collectors.collectingAndThen(
                                    Collectors.toCollection(
                                            () -> new TreeSet<>(
                                                    Comparator.comparing(o -> o.getCardId() + ";" + o.getBeaconId() + ";" + o.getAcceptTime()))), (v1) -> new ArrayList(v1)));
                    List<RssiData> list = BeanUtil.copyToList(insertRssiTempList, RssiData.class);
                    this.rssiMapper.insertBatch(list);
                    return list.size();
                }
                return 0;
            }
            return 0;
        }
        return 0;
    }
    @Override // com.xrkc.job.service.IRssiService
    public List<Long> selectCardIdListByMod(int mod) {
        LocalDateTime minusSeconds = LocalDateTime.now().minusSeconds(10L);
        return this.rssiMapper.selectCardIdListByMod(mod, minusSeconds);
    }
    @Override // com.xrkc.job.service.IRssiService
    public List<PositionVO> selectRssiList(Map<String, Object> map) {
        Integer second = (Integer) map.get("samplingSecond");
        LocalDateTime minusSeconds = LocalDateTime.now().minusSeconds(second.intValue());
        map.put("minusSeconds", minusSeconds);
        return this.rssiMapper.selectRssiList(map);
    }
    /* JADX WARN: Incorrect condition in loop: B:42:0x0022 */
    /* JADX WARN: Incorrect condition in loop: B:48:0x0050 */
    @Override // com.xrkc.job.service.IRssiService
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void batchDelete(java.lang.Long r5) {
        /*
            r4 = this;
            java.time.LocalDateTime r0 = java.time.LocalDateTime.now()
            r1 = r5
            long r1 = r1.longValue()
            java.time.LocalDateTime r0 = r0.minusSeconds(r1)
            r6 = r0
            r0 = r4
            com.xrkc.job.mapper.RssiMapper r0 = r0.rssiMapper
            r1 = r6
            java.lang.Long r0 = r0.findDelDataMaxId(r1)
            r7 = r0
            r0 = r7
            boolean r0 = java.util.Objects.nonNull(r0)
            if (r0 == 0) goto L37
            r0 = 1
            r8 = r0
        L20:
            r0 = r8
            if (r0 <= 0) goto L37
            r0 = r4
            com.xrkc.job.mapper.RssiMapper r0 = r0.rssiMapper
            r1 = r7
            r2 = 1000(0x3e8, float:1.401E-42)
            int r0 = r0.delByDataMaxId(r1, r2)
            r8 = r0
            goto L20
        L37:
            r0 = r4
            com.xrkc.job.mapper.RssiMapper r0 = r0.rssiMapper
            r1 = r6
            java.lang.Long r0 = r0.findDelTempMaxId(r1)
            r8 = r0
            r0 = r8
            boolean r0 = java.util.Objects.nonNull(r0)
            if (r0 == 0) goto L66
            r0 = 1
            r9 = r0
        L4e:
            r0 = r9
            if (r0 <= 0) goto L66
            r0 = r4
            com.xrkc.job.mapper.RssiMapper r0 = r0.rssiMapper
            r1 = r8
            r2 = 1000(0x3e8, float:1.401E-42)
            int r0 = r0.delByTempMaxId(r1, r2)
            r9 = r0
            goto L4e
        L66:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: com.xrkc.job.service.impl.RssiServiceImpl.batchDelete(java.lang.Long):void");
    }
    /* JADX WARN: Incorrect condition in loop: B:28:0x0020 */
    @Override // com.xrkc.job.service.IRssiService
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void batchDeleteBak(int r5) {
        /*
            r4 = this;
            java.time.LocalDateTime r0 = java.time.LocalDateTime.now()
            r1 = r5
            long r1 = (long) r1
            java.time.LocalDateTime r0 = r0.minusHours(r1)
            r6 = r0
            r0 = r4
            com.xrkc.job.mapper.RssiMapper r0 = r0.rssiMapper
            r1 = r6
            java.lang.Long r0 = r0.findDelBakMaxId(r1)
            r7 = r0
            r0 = r7
            boolean r0 = java.util.Objects.nonNull(r0)
            if (r0 == 0) goto L35
            r0 = 1
            r8 = r0
        L1e:
            r0 = r8
            if (r0 <= 0) goto L35
            r0 = r4
            com.xrkc.job.mapper.RssiMapper r0 = r0.rssiMapper
            r1 = r7
            r2 = 1000(0x3e8, float:1.401E-42)
            int r0 = r0.delByBakMaxId(r1, r2)
            r8 = r0
            goto L1e
        L35:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: com.xrkc.job.service.impl.RssiServiceImpl.batchDeleteBak(int):void");
    }
}
