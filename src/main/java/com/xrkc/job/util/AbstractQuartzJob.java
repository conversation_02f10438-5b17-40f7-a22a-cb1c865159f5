package com.xrkc.job.util;
import com.xrkc.job.domain.SystemJob;
import com.xrkc.job.domain.SystemJobLog;
import com.xrkc.job.service.ISystemJobLogService;
import java.time.Duration;
import java.time.LocalDateTime;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/AbstractQuartzJob.class */
public abstract class AbstractQuartzJob implements Job {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) AbstractQuartzJob.class);
    private static ThreadLocal<LocalDateTime> threadLocal = new ThreadLocal<>();
    protected abstract void doExecute(JobExecutionContext jobExecutionContext, SystemJob systemJob) throws Exception;
    @Override // org.quartz.Job
    public void execute(JobExecutionContext context) {
        SystemJob sysJob = new SystemJob();
        BeanUtils.copyBeanProp(sysJob, context.getMergedJobDataMap().get("TASK_PROPERTIES"));
        try {
            before(context, sysJob);
            if (sysJob != null) {
                doExecute(context, sysJob);
            }
            after(context, sysJob, null);
        } catch (Exception e) {
            log.error("任务执行异常  - ：", (Throwable) e);
            after(context, sysJob, e);
        }
    }
    protected void before(JobExecutionContext context, SystemJob sysJob) {
        threadLocal.set(LocalDateTime.now());
    }
    protected void after(JobExecutionContext context, SystemJob sysJob, Exception e) {
        LocalDateTime startTime = threadLocal.get();
        threadLocal.remove();
        SystemJobLog sysJobLog = new SystemJobLog();
        sysJobLog.setJobId(sysJob.getJobId());
        sysJobLog.setJobName(sysJob.getJobName());
        sysJobLog.setJobGroup(sysJob.getJobGroup());
        sysJobLog.setJobTask(sysJob.getJobTask());
        sysJobLog.setStartTime(startTime);
        sysJobLog.setStopTime(LocalDateTime.now());
        long runMs = Duration.between(sysJobLog.getStartTime(), sysJobLog.getStopTime()).toMillis();
        sysJobLog.setJobTime(Long.valueOf(runMs));
        sysJobLog.setJobMsg(sysJobLog.getJobName() + " 总共耗时：" + runMs + "毫秒");
        if (e != null) {
            sysJobLog.setJobStatus("1");
            String errorMsg = StringUtils.substring(ExceptionUtil.getExceptionMessage(e), 0, 2000);
            sysJobLog.setExceptionInfo(errorMsg);
        } else {
            sysJobLog.setJobStatus("0");
        }
        ((ISystemJobLogService) SpringUtils.getBean(ISystemJobLogService.class)).addJobLog(sysJobLog);
    }
}
