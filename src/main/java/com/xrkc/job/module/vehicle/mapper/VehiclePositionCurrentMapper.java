package com.xrkc.job.module.vehicle.mapper;
import com.xrkc.core.domain.vehicle.VehiclePositionCurrent;
import com.xrkc.core.mybatisplus.BaseMapperX;
import com.xrkc.core.mybatisplus.LambdaQueryWrapperX;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/module/vehicle/mapper/VehiclePositionCurrentMapper.class */
public interface VehiclePositionCurrentMapper extends BaseMapperX<VehiclePositionCurrent> {
    default List<VehiclePositionCurrent> selectCurrentList(Long onlineMinute) {
        return selectList(new LambdaQueryWrapperX<VehiclePositionCurrent>().gtIfPresent((v0) -> {
            return v0.getAcceptTime();
        }, LocalDateTime.now().minusMinutes(onlineMinute.longValue())));
    }
}
