package com.xrkc.job.domain;
import com.alibaba.nacos.client.config.common.ConfigConstants;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xrkc.core.domain.vehicle.VehicleAlarm;
import java.io.Serializable;
import java.time.LocalDateTime;
@TableName("log_sms")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/LogSms.class */
public class LogSms implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId("sms_id")
    private Long smsId;
    @TableField("alarm_id")
    private Long alarmId;
    @TableField(VehicleAlarm.ALARM_TYPE)
    private String alarmType;
    @TableField(VehicleAlarm.ALARM_TYPE_NAME)
    private String alarmTypeName;
    @TableField("phone_numbers")
    private String phoneNumbers;
    @TableField("phone_real_names")
    private String phoneRealNames;
    @TableField(ConfigConstants.CONTENT)
    private String content;
    @TableField("send_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sendTime;
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @TableField("update_time")
    private LocalDateTime updateTime;
   // @TableField("result")
    private String result;
    @TableField("biz_id")
    private String bizId;
    @TableField("biz_msg")
    private String bizMsg;
    private String noticeType;
    private String source;
    public void setSmsId(Long smsId) {
        this.smsId = smsId;
    }
    public void setAlarmId(Long alarmId) {
        this.alarmId = alarmId;
    }
    public void setAlarmType(String alarmType) {
        this.alarmType = alarmType;
    }
    public void setAlarmTypeName(String alarmTypeName) {
        this.alarmTypeName = alarmTypeName;
    }
    public void setPhoneNumbers(String phoneNumbers) {
        this.phoneNumbers = phoneNumbers;
    }
    public void setPhoneRealNames(String phoneRealNames) {
        this.phoneRealNames = phoneRealNames;
    }
    public void setContent(String content) {
        this.content = content;
    }
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public void setSendTime(LocalDateTime sendTime) {
        this.sendTime = sendTime;
    }
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    public void setResult(String result) {
        this.result = result;
    }
    public void setBizId(String bizId) {
        this.bizId = bizId;
    }
    public void setBizMsg(String bizMsg) {
        this.bizMsg = bizMsg;
    }
    public void setNoticeType(String noticeType) {
        this.noticeType = noticeType;
    }
    public void setSource(String source) {
        this.source = source;
    }
    public String toString() {
        return "LogSms(smsId=" + getSmsId() + ", alarmId=" + getAlarmId() + ", alarmType=" + getAlarmType() + ", alarmTypeName=" + getAlarmTypeName() + ", phoneNumbers=" + getPhoneNumbers() + ", phoneRealNames=" + getPhoneRealNames() + ", content=" + getContent() + ", sendTime=" + getSendTime() + ", createTime=" + getCreateTime() + ", updateTime=" + getUpdateTime() + ", result=" + getResult() + ", bizId=" + getBizId() + ", bizMsg=" + getBizMsg() + ", noticeType=" + getNoticeType() + ", source=" + getSource() + StringPool.RIGHT_BRACKET;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof LogSms)) {
            return false;
        }
        LogSms other = (LogSms) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$smsId = getSmsId();
        Object other$smsId = other.getSmsId();
        if (this$smsId == null) {
            if (other$smsId != null) {
                return false;
            }
        } else if (!this$smsId.equals(other$smsId)) {
            return false;
        }
        Object this$alarmId = getAlarmId();
        Object other$alarmId = other.getAlarmId();
        if (this$alarmId == null) {
            if (other$alarmId != null) {
                return false;
            }
        } else if (!this$alarmId.equals(other$alarmId)) {
            return false;
        }
        Object this$alarmType = getAlarmType();
        Object other$alarmType = other.getAlarmType();
        if (this$alarmType == null) {
            if (other$alarmType != null) {
                return false;
            }
        } else if (!this$alarmType.equals(other$alarmType)) {
            return false;
        }
        Object this$alarmTypeName = getAlarmTypeName();
        Object other$alarmTypeName = other.getAlarmTypeName();
        if (this$alarmTypeName == null) {
            if (other$alarmTypeName != null) {
                return false;
            }
        } else if (!this$alarmTypeName.equals(other$alarmTypeName)) {
            return false;
        }
        Object this$phoneNumbers = getPhoneNumbers();
        Object other$phoneNumbers = other.getPhoneNumbers();
        if (this$phoneNumbers == null) {
            if (other$phoneNumbers != null) {
                return false;
            }
        } else if (!this$phoneNumbers.equals(other$phoneNumbers)) {
            return false;
        }
        Object this$phoneRealNames = getPhoneRealNames();
        Object other$phoneRealNames = other.getPhoneRealNames();
        if (this$phoneRealNames == null) {
            if (other$phoneRealNames != null) {
                return false;
            }
        } else if (!this$phoneRealNames.equals(other$phoneRealNames)) {
            return false;
        }
        Object this$content = getContent();
        Object other$content = other.getContent();
        if (this$content == null) {
            if (other$content != null) {
                return false;
            }
        } else if (!this$content.equals(other$content)) {
            return false;
        }
        Object this$sendTime = getSendTime();
        Object other$sendTime = other.getSendTime();
        if (this$sendTime == null) {
            if (other$sendTime != null) {
                return false;
            }
        } else if (!this$sendTime.equals(other$sendTime)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        if (this$createTime == null) {
            if (other$createTime != null) {
                return false;
            }
        } else if (!this$createTime.equals(other$createTime)) {
            return false;
        }
        Object this$updateTime = getUpdateTime();
        Object other$updateTime = other.getUpdateTime();
        if (this$updateTime == null) {
            if (other$updateTime != null) {
                return false;
            }
        } else if (!this$updateTime.equals(other$updateTime)) {
            return false;
        }
        Object this$result = getResult();
        Object other$result = other.getResult();
        if (this$result == null) {
            if (other$result != null) {
                return false;
            }
        } else if (!this$result.equals(other$result)) {
            return false;
        }
        Object this$bizId = getBizId();
        Object other$bizId = other.getBizId();
        if (this$bizId == null) {
            if (other$bizId != null) {
                return false;
            }
        } else if (!this$bizId.equals(other$bizId)) {
            return false;
        }
        Object this$bizMsg = getBizMsg();
        Object other$bizMsg = other.getBizMsg();
        if (this$bizMsg == null) {
            if (other$bizMsg != null) {
                return false;
            }
        } else if (!this$bizMsg.equals(other$bizMsg)) {
            return false;
        }
        Object this$noticeType = getNoticeType();
        Object other$noticeType = other.getNoticeType();
        if (this$noticeType == null) {
            if (other$noticeType != null) {
                return false;
            }
        } else if (!this$noticeType.equals(other$noticeType)) {
            return false;
        }
        Object this$source = getSource();
        Object other$source = other.getSource();
        return this$source == null ? other$source == null : this$source.equals(other$source);
    }
    protected boolean canEqual(Object other) {
        return other instanceof LogSms;
    }
    public int hashCode() {
        Object $smsId = getSmsId();
        int result = (1 * 59) + ($smsId == null ? 43 : $smsId.hashCode());
        Object $alarmId = getAlarmId();
        int result2 = (result * 59) + ($alarmId == null ? 43 : $alarmId.hashCode());
        Object $alarmType = getAlarmType();
        int result3 = (result2 * 59) + ($alarmType == null ? 43 : $alarmType.hashCode());
        Object $alarmTypeName = getAlarmTypeName();
        int result4 = (result3 * 59) + ($alarmTypeName == null ? 43 : $alarmTypeName.hashCode());
        Object $phoneNumbers = getPhoneNumbers();
        int result5 = (result4 * 59) + ($phoneNumbers == null ? 43 : $phoneNumbers.hashCode());
        Object $phoneRealNames = getPhoneRealNames();
        int result6 = (result5 * 59) + ($phoneRealNames == null ? 43 : $phoneRealNames.hashCode());
        Object $content = getContent();
        int result7 = (result6 * 59) + ($content == null ? 43 : $content.hashCode());
        Object $sendTime = getSendTime();
        int result8 = (result7 * 59) + ($sendTime == null ? 43 : $sendTime.hashCode());
        Object $createTime = getCreateTime();
        int result9 = (result8 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $updateTime = getUpdateTime();
        int result10 = (result9 * 59) + ($updateTime == null ? 43 : $updateTime.hashCode());
        Object $result = getResult();
        int result11 = (result10 * 59) + ($result == null ? 43 : $result.hashCode());
        Object $bizId = getBizId();
        int result12 = (result11 * 59) + ($bizId == null ? 43 : $bizId.hashCode());
        Object $bizMsg = getBizMsg();
        int result13 = (result12 * 59) + ($bizMsg == null ? 43 : $bizMsg.hashCode());
        Object $noticeType = getNoticeType();
        int result14 = (result13 * 59) + ($noticeType == null ? 43 : $noticeType.hashCode());
        Object $source = getSource();
        return (result14 * 59) + ($source == null ? 43 : $source.hashCode());
    }
    public Long getSmsId() {
        return this.smsId;
    }
    public Long getAlarmId() {
        return this.alarmId;
    }
    public String getAlarmType() {
        return this.alarmType;
    }
    public String getAlarmTypeName() {
        return this.alarmTypeName;
    }
    public String getPhoneNumbers() {
        return this.phoneNumbers;
    }
    public String getPhoneRealNames() {
        return this.phoneRealNames;
    }
    public String getContent() {
        return this.content;
    }
    public LocalDateTime getSendTime() {
        return this.sendTime;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public LocalDateTime getUpdateTime() {
        return this.updateTime;
    }
    public String getResult() {
        return this.result;
    }
    public String getBizId() {
        return this.bizId;
    }
    public String getBizMsg() {
        return this.bizMsg;
    }
    public String getNoticeType() {
        return this.noticeType;
    }
    public String getSource() {
        return this.source;
    }
}
