package com.xrkc.job.service.impl;
import com.xrkc.core.domain.statistics.StatisticsDeptOnlineLog;
import com.xrkc.job.mapper.StatisticsDeptOnlineLogMapper;
import com.xrkc.job.service.IStatisticsDeptOnlineLogService;
import java.time.LocalDate;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/StatisticsDeptOnlineLogServiceImpl.class */
public class StatisticsDeptOnlineLogServiceImpl implements IStatisticsDeptOnlineLogService {
    @Autowired
    private StatisticsDeptOnlineLogMapper statisticsDeptOnlineLogMapper;
    @Override // com.xrkc.job.service.IStatisticsDeptOnlineLogService
    public List<StatisticsDeptOnlineLog> selectListByStatisticsTime(LocalDate statisticsTime) {
        return this.statisticsDeptOnlineLogMapper.selectList((v0) -> {
            return v0.getStatisticsTime();
        }, statisticsTime);
    }
    @Override // com.xrkc.job.service.IStatisticsDeptOnlineLogService
    public void insert(StatisticsDeptOnlineLog t) {
        this.statisticsDeptOnlineLogMapper.insert((StatisticsDeptOnlineLogMapper) t);
    }
    @Override // com.xrkc.job.service.IStatisticsDeptOnlineLogService
    public void updateById(StatisticsDeptOnlineLog t) {
        this.statisticsDeptOnlineLogMapper.updateById((StatisticsDeptOnlineLogMapper) t);
    }
}
