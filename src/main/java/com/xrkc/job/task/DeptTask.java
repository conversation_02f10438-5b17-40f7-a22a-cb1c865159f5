package com.xrkc.job.task;
import com.xrkc.core.domain.system.SystemDept;
import com.xrkc.core.utils.CommonUtil;
import com.xrkc.job.service.ISystemDeptService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
@Component("deptTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/DeptTask.class */
public class DeptTask {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) DeptTask.class);
    @Autowired
    private ISystemDeptService systemDeptService;
    public void updateDeptInfo() {
        List<SystemDept> deptList = this.systemDeptService.selectList();
        Map<Long, SystemDept> allDeptMap = (Map) deptList.stream().collect(Collectors.toMap((v0) -> {
            return v0.getDeptId();
        }, Function.identity()));
        deptList.removeIf(r -> {
            return Objects.nonNull(r.getPrimaryId()) && r.getParentId().compareTo((Long) 0L) == 0 && r.getDeptId().compareTo(r.getPrimaryId()) == 0;
        });
        if (!CollectionUtils.isEmpty(deptList)) {
            List<SystemDept> selfDeptList = (List) deptList.stream().filter(f -> {
                return f.getParentId().compareTo((Long) 0L) == 0;
            }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(selfDeptList)) {
                selfDeptList.stream().forEach(f2 -> {
                    f2.setPrimaryId(f2.getDeptId());
                    this.systemDeptService.updateEntity(f2);
                });
                deptList.removeIf(r2 -> {
                    return r2.getParentId().compareTo((Long) 0L) == 0;
                });
            }
            if (!CollectionUtils.isEmpty(deptList)) {
                Map<Long, Long> deptMap = (Map) deptList.stream().collect(Collectors.toMap((v0) -> {
                    return v0.getDeptId();
                }, (v0) -> {
                    return v0.getParentId();
                }));
                Map<Long, List<Long>> parentMap = new HashMap<>();
                for (Long key : deptMap.keySet()) {
                    Long value = deptMap.get(key);
                    if (parentMap.containsKey(value)) {
                        parentMap.get(value).add(key);
                    } else {
                        List<Long> list = new ArrayList<>();
                        list.add(key);
                        parentMap.put(value, list);
                    }
                }
                parentMap.forEach((parentId, deptIdList) -> {
                    SystemDept primaryDept = CommonUtil.recursionPrimaryDept(parentId, allDeptMap);
                    if (Objects.nonNull(primaryDept)) {
                        this.systemDeptService.updatePrimaryId(primaryDept.getDeptId(), deptIdList);
                    }
                });
            }
        }
    }
}
