package com.xrkc.job.vo;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/vo/TypeCountVo.class */
public class TypeCountVo {
    private String type;
    private String label;
    private Long count;
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/vo/TypeCountVo$TypeCountVoBuilder.class */
    public static class TypeCountVoBuilder {
        private String type;
        private String label;
        private Long count;
        TypeCountVoBuilder() {
        }
        public TypeCountVoBuilder type(String type) {
            this.type = type;
            return this;
        }
        public TypeCountVoBuilder label(String label) {
            this.label = label;
            return this;
        }
        public TypeCountVoBuilder count(Long count) {
            this.count = count;
            return this;
        }
        public TypeCountVo build() {
            return new TypeCountVo(this.type, this.label, this.count);
        }
        public String toString() {
            return "TypeCountVo.TypeCountVoBuilder(type=" + this.type + ", label=" + this.label + ", count=" + this.count + StringPool.RIGHT_BRACKET;
        }
    }
    public void setType(String type) {
        this.type = type;
    }
    public void setLabel(String label) {
        this.label = label;
    }
    public void setCount(Long count) {
        this.count = count;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof TypeCountVo)) {
            return false;
        }
        TypeCountVo other = (TypeCountVo) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$count = getCount();
        Object other$count = other.getCount();
        if (this$count == null) {
            if (other$count != null) {
                return false;
            }
        } else if (!this$count.equals(other$count)) {
            return false;
        }
        Object this$type = getType();
        Object other$type = other.getType();
        if (this$type == null) {
            if (other$type != null) {
                return false;
            }
        } else if (!this$type.equals(other$type)) {
            return false;
        }
        Object this$label = getLabel();
        Object other$label = other.getLabel();
        return this$label == null ? other$label == null : this$label.equals(other$label);
    }
    protected boolean canEqual(Object other) {
        return other instanceof TypeCountVo;
    }
    public int hashCode() {
        Object $count = getCount();
        int result = (1 * 59) + ($count == null ? 43 : $count.hashCode());
        Object $type = getType();
        int result2 = (result * 59) + ($type == null ? 43 : $type.hashCode());
        Object $label = getLabel();
        return (result2 * 59) + ($label == null ? 43 : $label.hashCode());
    }
    public String toString() {
        return "TypeCountVo(type=" + getType() + ", label=" + getLabel() + ", count=" + getCount() + StringPool.RIGHT_BRACKET;
    }
    TypeCountVo(String type, String label, Long count) {
        this.type = type;
        this.label = label;
        this.count = count;
    }
    public static TypeCountVoBuilder builder() {
        return new TypeCountVoBuilder();
    }
    public String getType() {
        return this.type;
    }
    public String getLabel() {
        return this.label;
    }
    public Long getCount() {
        return this.count;
    }
}
