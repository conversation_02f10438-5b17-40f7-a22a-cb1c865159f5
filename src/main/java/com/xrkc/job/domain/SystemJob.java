package com.xrkc.job.domain;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.time.LocalDateTime;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/SystemJob.class */
public class SystemJob implements Serializable {
    private static final long serialVersionUID = 1;
    private Long jobId;
    private String jobName;
    private String jobGroup;
    private String jobTask;
    private String jobCron;
    private String jobPlan;
    private String jobConcurrent;
    private String jobEnable;
    private String createBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    private String updateBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    private String remark;
    public Long getJobId() {
        return this.jobId;
    }
    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }
    public String getJobName() {
        return this.jobName;
    }
    public void setJobName(String jobName) {
        this.jobName = jobName;
    }
    public String getJobGroup() {
        return this.jobGroup;
    }
    public void setJobGroup(String jobGroup) {
        this.jobGroup = jobGroup;
    }
    public String getJobTask() {
        return this.jobTask;
    }
    public void setJobTask(String jobTask) {
        this.jobTask = jobTask;
    }
    public String getJobCron() {
        return this.jobCron;
    }
    public void setJobCron(String jobCron) {
        this.jobCron = jobCron;
    }
    public String getJobPlan() {
        return this.jobPlan;
    }
    public void setJobPlan(String jobPlan) {
        this.jobPlan = jobPlan;
    }
    public String getJobConcurrent() {
        return this.jobConcurrent;
    }
    public void setJobConcurrent(String jobConcurrent) {
        this.jobConcurrent = jobConcurrent;
    }
    public String getJobEnable() {
        return this.jobEnable;
    }
    public void setJobEnable(String jobEnable) {
        this.jobEnable = jobEnable;
    }
    public String getCreateBy() {
        return this.createBy;
    }
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getUpdateTime() {
        return this.updateTime;
    }
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    public String getUpdateBy() {
        return this.updateBy;
    }
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public String getRemark() {
        return this.remark;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }
}
