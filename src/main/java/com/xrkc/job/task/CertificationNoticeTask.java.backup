package com.xrkc.job.task;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.domain.certification.CertificationContractor;
import com.xrkc.core.domain.certification.CertificationNotice;
import com.xrkc.core.domain.certification.CertificationPerson;
import com.xrkc.core.domain.certification.CertificationWarrant;
import com.xrkc.core.domain.person.Person;
import com.xrkc.core.domain.system.SystemApi;
import com.xrkc.job.mapper.CertificationContractorMapper;
import com.xrkc.job.mapper.CertificationNoticeMapper;
import com.xrkc.job.mapper.CertificationNoticeMapper;
import com.xrkc.job.mapper.CertificationPersonMapper;
import com.xrkc.job.mapper.CertificationWarrantMapper;
import com.xrkc.job.service.IPersonService;
import com.xrkc.job.service.SystemApiService;
import com.xrkc.job.util.EmailUtilApi;
import java.lang.invoke.SerializedLambda;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import net.jodah.expiringmap.ExpiringMap;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
@Component("certificationNoticeTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/CertificationNoticeTask.class */
public class CertificationNoticeTask {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) CertificationNoticeTask.class);
    @Autowired
    private CertificationNoticeMapper certificationNoticeMapper;
    @Autowired
    private CertificationNoticeMapper certificationNoticeObjMapper;
    @Autowired
    private CertificationContractorMapper certificationContractorMapper;
    @Autowired
    private CertificationPersonMapper certificationPersonMapper;
    @Autowired
    private CertificationWarrantMapper certificationWarrantMapper;
    @Autowired
    private IPersonService personService;
    @Autowired
    private SystemApiService systemApiService;
    @Autowired
    private EmailUtilApi emailUtilApi;
    private final Map<String, LocalDateTime> existIdMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    private static /* synthetic */ Object $deserializeLambda$(SerializedLambda lambda) {
        switch (lambda.getImplMethodName()) {
            case "getEnableStatus":
                if (lambda.getImplMethodKind() == 5 && lambda.getFunctionalInterfaceClass().equals("com/baomidou/mybatisplus/core/toolkit/support/SFunction") && lambda.getFunctionalInterfaceMethodName().equals("apply") && lambda.getFunctionalInterfaceMethodSignature().equals("(Ljava/lang/Object;)Ljava/lang/Object;") && lambda.getImplClass().equals("com/xrkc/core/domain/certification/CertificationNotice") && lambda.getImplMethodSignature().equals("()Ljava/lang/String;")) {
                    return (v0) -> {
                        return v0.getEnableStatus();
                    };
                }
                break;
            case "getEarly":
                if (lambda.getImplMethodKind() == 5 && lambda.getFunctionalInterfaceClass().equals("com/baomidou/mybatisplus/core/toolkit/support/SFunction") && lambda.getFunctionalInterfaceMethodName().equals("apply") && lambda.getFunctionalInterfaceMethodSignature().equals("(Ljava/lang/Object;)Ljava/lang/Object;") && lambda.getImplClass().equals("com/xrkc/core/domain/certification/CertificationNotice") && lambda.getImplMethodSignature().equals("()Ljava/lang/Integer;")) {
                    return (v0) -> {
                        return v0.getEarly();
                    };
                }
                break;
            case "getExpireDate":
                if (lambda.getImplMethodKind() == 5 && lambda.getFunctionalInterfaceClass().equals("com/baomidou/mybatisplus/core/toolkit/support/SFunction") && lambda.getFunctionalInterfaceMethodName().equals("apply") && lambda.getFunctionalInterfaceMethodSignature().equals("(Ljava/lang/Object;)Ljava/lang/Object;") && lambda.getImplClass().equals("com/xrkc/core/domain/certification/CertificationContractor") && lambda.getImplMethodSignature().equals("()Ljava/time/LocalDate;")) {
                    return (v0) -> {
                        return v0.getExpireDate();
                    };
                }
                if (lambda.getImplMethodKind() == 5 && lambda.getFunctionalInterfaceClass().equals("com/baomidou/mybatisplus/core/toolkit/support/SFunction") && lambda.getFunctionalInterfaceMethodName().equals("apply") && lambda.getFunctionalInterfaceMethodSignature().equals("(Ljava/lang/Object;)Ljava/lang/Object;") && lambda.getImplClass().equals("com/xrkc/core/domain/certification/CertificationPerson") && lambda.getImplMethodSignature().equals("()Ljava/time/LocalDate;")) {
                    return (v0) -> {
                        return v0.getExpireDate();
                    };
                }
                if (lambda.getImplMethodKind() == 5 && lambda.getFunctionalInterfaceClass().equals("com/baomidou/mybatisplus/core/toolkit/support/SFunction") && lambda.getFunctionalInterfaceMethodName().equals("apply") && lambda.getFunctionalInterfaceMethodSignature().equals("(Ljava/lang/Object;)Ljava/lang/Object;") && lambda.getImplClass().equals("com/xrkc/core/domain/certification/CertificationWarrant") && lambda.getImplMethodSignature().equals("()Ljava/time/LocalDate;")) {
                    return (v0) -> {
                        return v0.getExpireDate();
                    };
                }
                break;
            case "getNoticeId":
                if (lambda.getImplMethodKind() == 5 && lambda.getFunctionalInterfaceClass().equals("com/baomidou/mybatisplus/core/toolkit/support/SFunction") && lambda.getFunctionalInterfaceMethodName().equals("apply") && lambda.getFunctionalInterfaceMethodSignature().equals("(Ljava/lang/Object;)Ljava/lang/Object;") && lambda.getImplClass().equals("com/xrkc/core/domain/certification/CertificationContractor") && lambda.getImplMethodSignature().equals("()Ljava/lang/Long;")) {
                    return (v0) -> {
                        return v0.getNoticeId();
                    };
                }
                if (lambda.getImplMethodKind() == 5 && lambda.getFunctionalInterfaceClass().equals("com/baomidou/mybatisplus/core/toolkit/support/SFunction") && lambda.getFunctionalInterfaceMethodName().equals("apply") && lambda.getFunctionalInterfaceMethodSignature().equals("(Ljava/lang/Object;)Ljava/lang/Object;") && lambda.getImplClass().equals("com/xrkc/core/domain/certification/CertificationContractor") && lambda.getImplMethodSignature().equals("()Ljava/lang/Long;")) {
                    return (v0) -> {
                        return v0.getNoticeId();
                    };
                }
                if (lambda.getImplMethodKind() == 5 && lambda.getFunctionalInterfaceClass().equals("com/baomidou/mybatisplus/core/toolkit/support/SFunction") && lambda.getFunctionalInterfaceMethodName().equals("apply") && lambda.getFunctionalInterfaceMethodSignature().equals("(Ljava/lang/Object;)Ljava/lang/Object;") && lambda.getImplClass().equals("com/xrkc/core/domain/certification/CertificationPerson") && lambda.getImplMethodSignature().equals("()Ljava/lang/Long;")) {
                    return (v0) -> {
                        return v0.getNoticeId();
                    };
                }
                if (lambda.getImplMethodKind() == 5 && lambda.getFunctionalInterfaceClass().equals("com/baomidou/mybatisplus/core/toolkit/support/SFunction") && lambda.getFunctionalInterfaceMethodName().equals("apply") && lambda.getFunctionalInterfaceMethodSignature().equals("(Ljava/lang/Object;)Ljava/lang/Object;") && lambda.getImplClass().equals("com/xrkc/core/domain/certification/CertificationPerson") && lambda.getImplMethodSignature().equals("()Ljava/lang/Long;")) {
                    return (v0) -> {
                        return v0.getNoticeId();
                    };
                }
                if (lambda.getImplMethodKind() == 5 && lambda.getFunctionalInterfaceClass().equals("com/baomidou/mybatisplus/core/toolkit/support/SFunction") && lambda.getFunctionalInterfaceMethodName().equals("apply") && lambda.getFunctionalInterfaceMethodSignature().equals("(Ljava/lang/Object;)Ljava/lang/Object;") && lambda.getImplClass().equals("com/xrkc/core/domain/certification/CertificationWarrant") && lambda.getImplMethodSignature().equals("()Ljava/lang/Long;")) {
                    return (v0) -> {
                        return v0.getNoticeId();
                    };
                }
                if (lambda.getImplMethodKind() == 5 && lambda.getFunctionalInterfaceClass().equals("com/baomidou/mybatisplus/core/toolkit/support/SFunction") && lambda.getFunctionalInterfaceMethodName().equals("apply") && lambda.getFunctionalInterfaceMethodSignature().equals("(Ljava/lang/Object;)Ljava/lang/Object;") && lambda.getImplClass().equals("com/xrkc/core/domain/certification/CertificationWarrant") && lambda.getImplMethodSignature().equals("()Ljava/lang/Long;")) {
                    return (v0) -> {
                        return v0.getNoticeId();
                    };
                }
                if (lambda.getImplMethodKind() == 5 && lambda.getFunctionalInterfaceClass().equals("com/baomidou/mybatisplus/core/toolkit/support/SFunction") && lambda.getFunctionalInterfaceMethodName().equals("apply") && lambda.getFunctionalInterfaceMethodSignature().equals("(Ljava/lang/Object;)Ljava/lang/Object;") && lambda.getImplClass().equals("com/xrkc/core/domain/certification/CertificationNotice") && lambda.getImplMethodSignature().equals("()Ljava/lang/Long;")) {
                    return (v0) -> {
                        return v0.getNoticeId();
                    };
                }
                break;
        }
        throw new IllegalArgumentException("Invalid lambda deserialization");
    }
    /* JADX WARN: Multi-variable type inference failed */
    public void sendCertificationExpireNotice() {
        SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_SEND_EMAIL);
        if (Objects.isNull(systemApi) || StringUtils.isAnyBlank(systemApi.getHost(), systemApi.getUserName(), systemApi.getPassword())) {
            return;
        }
        List<CertificationNotice> noticeList = this.certificationNoticeMapper.selectList((Wrapper) ((LambdaQueryWrapper) new LambdaQueryWrapper<CertificationNotice>().eq((v0) -> {
            return v0.getEnableStatus();
        }, "Y")).isNotNull((v0) -> {
            return v0.getEarly();
        }));
        if (CollectionUtils.isEmpty(noticeList)) {
            return;
        }
        List<Long> noticeIdList = (List) noticeList.stream().map((v0) -> {
            return v0.getNoticeId();
        }).collect(Collectors.toList());
        LocalDate today = LocalDate.now();
        LocalDateTime now = LocalDateTime.now();
        Map<Long, Person> personNoticeMap = this.personService.findNoticeMap();
        Map<String, List<String>> noticeObjMap = new HashMap<>();
        try {
            List<CertificationContractor> certificationContractorList = this.certificationContractorMapper.selectList((Wrapper) ((LambdaQueryWrapper) ((LambdaQueryWrapper) new LambdaQueryWrapper<CertificationContractor>().isNotNull((v0) -> {
                return v0.getNoticeId();
            })).ge((v0) -> {
                return v0.getExpireDate();
            }, today)).in((LambdaQueryWrapper) (v0) -> {
                return v0.getNoticeId();
            }, (Collection<?>) noticeIdList));
            certificationContractorList.stream().filter(f -> {
                return !this.existIdMap.containsKey(new StringBuilder().append("c_").append(f.getCertificationId()).toString());
            }).forEach(t -> {
                if (handCertificationNotice(now, systemApi, "相关方资质即将到期通知", noticeList, personNoticeMap, noticeObjMap, t.getNoticeId(), t.getCertificationName(), t.getCertificationCode(), t.getIssueDate(), t.getExpireDate())) {
                    String idKey = "c_" + t.getCertificationId();
                    this.existIdMap.put(idKey, now);
                }
            });
        } catch (Exception e) {
            log.error("相关方资质通知处理失败：{}", e.getMessage());
        }
        try {
            List<CertificationPerson> certificationPersonList = this.certificationPersonMapper.selectList((Wrapper) ((LambdaQueryWrapper) ((LambdaQueryWrapper) new LambdaQueryWrapper<CertificationPerson>().isNotNull((v0) -> {
                return v0.getNoticeId();
            })).ge((v0) -> {
                return v0.getExpireDate();
            }, today)).in((LambdaQueryWrapper) (v0) -> {
                return v0.getNoticeId();
            }, (Collection<?>) noticeIdList));
            certificationPersonList.stream().filter(f2 -> {
                return !this.existIdMap.containsKey(new StringBuilder().append("p_").append(f2.getId()).toString());
            }).forEach(t2 -> {
                if (handCertificationNotice(now, systemApi, "人员资质即将到期通知", noticeList, personNoticeMap, noticeObjMap, t2.getNoticeId(), t2.getCertificationName(), t2.getCertificationCode(), t2.getIssueDate(), t2.getExpireDate())) {
                    String idKey = "p_" + t2.getId();
                    this.existIdMap.put(idKey, now);
                }
            });
        } catch (Exception e2) {
            log.error("人员资质通知处理失败：{}", e2.getMessage());
        }
        try {
            List<CertificationWarrant> certificationWarrantList = this.certificationWarrantMapper.selectList((Wrapper) ((LambdaQueryWrapper) ((LambdaQueryWrapper) new LambdaQueryWrapper<CertificationWarrant>().isNotNull((v0) -> {
                return v0.getNoticeId();
            })).ge((v0) -> {
                return v0.getExpireDate();
            }, today)).in((LambdaQueryWrapper) (v0) -> {
                return v0.getNoticeId();
            }, (Collection<?>) noticeIdList));
            certificationWarrantList.stream().filter(f3 -> {
                return !this.existIdMap.containsKey(new StringBuilder().append("w_").append(f3.getWarrantId()).toString());
            }).forEach(t3 -> {
                if (handCertificationNotice(now, systemApi, "权证批文即将到期通知", noticeList, personNoticeMap, noticeObjMap, t3.getNoticeId(), t3.getCertificationName(), t3.getCertificationCode(), t3.getIssueDate(), t3.getExpireDate())) {
                    String idKey = "w_" + t3.getWarrantId();
                    this.existIdMap.put(idKey, now);
                }
            });
        } catch (Exception e3) {
            log.error("权证批文通知处理失败：{}", e3.getMessage());
        }
    }
    /* JADX WARN: Multi-variable type inference failed */
    private boolean handCertificationNotice(LocalDateTime now, SystemApi systemApi, String subject, List<CertificationNotice> noticeList, Map<Long, Person> personNoticeMap, Map<String, List<String>> noticeObjMap, Long relationNoticeId, String certificationName, String certificationCode, LocalDate issueDate, LocalDate expireDate) {
        CertificationNotice notice = noticeList.stream().filter(f -> {
            return f.getNoticeId().compareTo(relationNoticeId) == 0;
        }).findFirst().get();
        Integer early = notice.getEarly();
        LocalDateTime expireTime = expireDate.atStartOfDay();
        Duration duration = Duration.between(now, expireTime);
        if (duration.toHours() == early.longValue()) {
            String noticeObjKey = notice.getNoticeId() + "_" + notice.getNoticeType();
            List<String> noticeObjList = noticeObjMap.get(noticeObjKey);
            if (CollectionUtils.isEmpty(noticeObjList)) {
                List<Long> personIdList = (List) this.certificationNoticeObjMapper.selectList((Wrapper) new LambdaQueryWrapper<CertificationNotice>().eq((v0) -> {
                    return v0.getNoticeId();
                }, notice.getNoticeId())).stream().map((v0) -> {
                    return ((CertificationNotice)v0).getPersonId();
                }).distinct().collect(Collectors.toList());
                List<String> personRefreshList = new ArrayList<>();
                personIdList.forEach(personId -> {
                    Person person = (Person) personNoticeMap.get(personId);
                    if (Objects.nonNull(person)) {
                        if ("email".equals(notice.getNoticeType()) && StringUtils.isNotBlank(person.getEmail())) {
                            personRefreshList.add(person.getEmail());
                        }
                        if ("sms".equals(notice.getNoticeType()) && StringUtils.isNotBlank(person.getPhone())) {
                            personRefreshList.add(person.getPhone());
                        }
                    }
                });
                noticeObjMap.put(noticeObjKey, personRefreshList);
                noticeObjList = personRefreshList;
            }
            String text = "证书名称：" + certificationName + "，证书编码：" + certificationCode + "，有效期：" + issueDate + "至" + expireDate + "，请及时处理。";
            sendNotice(notice.getNoticeType(), noticeObjList, subject, text, systemApi.getHost(), systemApi.getUserName(), systemApi.getPassword(), systemApi.getApiOrgSn());
            return true;
        }
        return false;
    }
    private void sendNotice(String noticeType, List<String> noticeObjList, String subject, String text, String host, String userName, String password, String port) {
        if (CollectionUtils.isEmpty(noticeObjList)) {
            return;
        }
        if ("email".equals(noticeType)) {
            this.emailUtilApi.commonSend(subject, text, StringUtils.join(noticeObjList, ","), host, userName, password, port);
        }
        if ("sms".equals(noticeType)) {
        }
    }
}
