package com.xrkc.job.service.impl;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.xrkc.job.domain.PositionDataCalc;
import com.xrkc.job.domain.PositionVO;
import com.xrkc.job.mapper.PositionDataCalcMapper;
import com.xrkc.job.service.IPositionDataCalcService;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/PositionDataCalcServiceImpl.class */
public class PositionDataCalcServiceImpl implements IPositionDataCalcService {
    @Autowired
    private PositionDataCalcMapper positionDataCalcMapper;
    @Override // com.xrkc.job.service.IPositionDataCalcService
    public int replaceVO(PositionVO t) {
        PositionDataCalc positionDataCalc = this.positionDataCalcMapper.selectByAcceptTimeAndCardId(t.getAcceptTime(), t.getCardId());
        t.setCreateTime(LocalDateTime.now());
        if (ObjectUtil.isEmpty(positionDataCalc)) {
            PositionDataCalc entity = (PositionDataCalc) BeanUtil.copyProperties((Object) t, PositionDataCalc.class, new String[0]);
            return this.positionDataCalcMapper.insert((PositionDataCalcMapper) entity);
        }
        return this.positionDataCalcMapper.updateByAcceptTimeAndCardId(t);
    }
    /* JADX WARN: Incorrect condition in loop: B:28:0x0020 */
    @Override // com.xrkc.job.service.IPositionDataCalcService
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void batchDelete(int r5) {
        /*
            r4 = this;
            java.time.LocalDateTime r0 = java.time.LocalDateTime.now()
            r1 = r5
            long r1 = (long) r1
            java.time.LocalDateTime r0 = r0.minusMinutes(r1)
            r6 = r0
            r0 = r4
            com.xrkc.job.mapper.PositionDataCalcMapper r0 = r0.positionDataCalcMapper
            r1 = r6
            java.lang.Long r0 = r0.findMaxId(r1)
            r7 = r0
            r0 = r7
            boolean r0 = java.util.Objects.nonNull(r0)
            if (r0 == 0) goto L35
            r0 = 1
            r8 = r0
        L1e:
            r0 = r8
            if (r0 <= 0) goto L35
            r0 = r4
            com.xrkc.job.mapper.PositionDataCalcMapper r0 = r0.positionDataCalcMapper
            r1 = r7
            r2 = 1000(0x3e8, float:1.401E-42)
            int r0 = r0.delByMaxId(r1, r2)
            r8 = r0
            goto L1e
        L35:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: com.xrkc.job.service.impl.PositionDataCalcServiceImpl.batchDelete(int):void");
    }
    @Override // com.xrkc.job.service.IPositionDataCalcService
    public void insertByCurrent(PositionVO lastPosition) {
        PositionDataCalc positionDataCalc = this.positionDataCalcMapper.selectByAcceptTimeAndCardId(lastPosition.getAcceptTime(), lastPosition.getCardId());
        lastPosition.setCreateTime(LocalDateTime.now());
        if (ObjectUtil.isEmpty(positionDataCalc)) {
            PositionDataCalc entity = (PositionDataCalc) BeanUtil.copyProperties((Object) lastPosition, PositionDataCalc.class, new String[0]);
            this.positionDataCalcMapper.insert((PositionDataCalcMapper) entity);
        } else {
            this.positionDataCalcMapper.updateByAcceptTimeAndCardId(lastPosition);
        }
    }
    @Override // com.xrkc.job.service.IPositionDataCalcService
    public List<PositionDataCalc> selectList(int bakSecond) {
        return this.positionDataCalcMapper.selectListByBakTime(bakSecond);
    }
}
