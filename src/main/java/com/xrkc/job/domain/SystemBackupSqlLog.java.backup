package com.xrkc.job.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
import java.time.LocalDateTime;
//import org.springframework.cache.interceptor.CacheOperationExpressionEvaluator;

@TableName("system_backup_sql_log")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/SystemBackupSqlLog.class */
public class SystemBackupSqlLog implements Serializable {
    private static final long serialVersionUID = 1;

    @TableId("id")
    private Long id;

    @TableField("back_path")
    private String backPath;

  //  @TableField(CacheOperationExpressionEvaluator.RESULT_VARIABLE)
    private String result;

    @TableField("remark")
    private String remark;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;

    public void setId(Long id) {
        this.id = id;
    }

    public void setBackPath(String backPath) {
        this.backPath = backPath;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof SystemBackupSqlLog)) {
            return false;
        }
        SystemBackupSqlLog other = (SystemBackupSqlLog) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$id = getId();
        Object other$id = other.getId();
        if (this$id == null) {
            if (other$id != null) {
                return false;
            }
        } else if (!this$id.equals(other$id)) {
            return false;
        }
        Object this$backPath = getBackPath();
        Object other$backPath = other.getBackPath();
        if (this$backPath == null) {
            if (other$backPath != null) {
                return false;
            }
        } else if (!this$backPath.equals(other$backPath)) {
            return false;
        }
        Object this$result = getResult();
        Object other$result = other.getResult();
        if (this$result == null) {
            if (other$result != null) {
                return false;
            }
        } else if (!this$result.equals(other$result)) {
            return false;
        }
        Object this$remark = getRemark();
        Object other$remark = other.getRemark();
        if (this$remark == null) {
            if (other$remark != null) {
                return false;
            }
        } else if (!this$remark.equals(other$remark)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        if (this$createTime == null) {
            if (other$createTime != null) {
                return false;
            }
        } else if (!this$createTime.equals(other$createTime)) {
            return false;
        }
        Object this$updateTime = getUpdateTime();
        Object other$updateTime = other.getUpdateTime();
        return this$updateTime == null ? other$updateTime == null : this$updateTime.equals(other$updateTime);
    }

    protected boolean canEqual(Object other) {
        return other instanceof SystemBackupSqlLog;
    }

    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $backPath = getBackPath();
        int result2 = (result * 59) + ($backPath == null ? 43 : $backPath.hashCode());
        Object $result = getResult();
        int result3 = (result2 * 59) + ($result == null ? 43 : $result.hashCode());
        Object $remark = getRemark();
        int result4 = (result3 * 59) + ($remark == null ? 43 : $remark.hashCode());
        Object $createTime = getCreateTime();
        int result5 = (result4 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $updateTime = getUpdateTime();
        return (result5 * 59) + ($updateTime == null ? 43 : $updateTime.hashCode());
    }

    public String toString() {
        return "SystemBackupSqlLog(id=" + getId() + ", backPath=" + getBackPath() + ", result=" + getResult() + ", remark=" + getRemark() + ", createTime=" + getCreateTime() + ", updateTime=" + getUpdateTime() + StringPool.RIGHT_BRACKET;
    }

    public Long getId() {
        return this.id;
    }

    public String getBackPath() {
        return this.backPath;
    }

    public String getResult() {
        return this.result;
    }

    public String getRemark() {
        return this.remark;
    }

    public LocalDateTime getCreateTime() {
        return this.createTime;
    }

    public LocalDateTime getUpdateTime() {
        return this.updateTime;
    }
}
