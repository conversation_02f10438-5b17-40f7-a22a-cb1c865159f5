package com.xrkc.job.service.impl;
import com.xrkc.job.domain.StatisticsPersonFlow;
import com.xrkc.job.mapper.StatisticsPersonFlowMapper;
import com.xrkc.job.service.IStatisticsPersonFlowService;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/StatisticsPersonFlowServiceImpl.class */
public class StatisticsPersonFlowServiceImpl implements IStatisticsPersonFlowService {
    @Autowired
    private StatisticsPersonFlowMapper statisticsPersonFlowMapper;
    @Override // com.xrkc.job.service.IStatisticsPersonFlowService
    public int batchReplace(List<StatisticsPersonFlow> list) {
        LocalDate statisticsDate = null;
        int result = 0;
        Set<String> set = new HashSet<>();
        Iterator<StatisticsPersonFlow> it = list.iterator();
        while (it.hasNext()) {
            statisticsDate = it.next().getStatisticsDate();
        }
        List<StatisticsPersonFlow> oldList = this.statisticsPersonFlowMapper.selectByStatisticsDate(statisticsDate);
        if (CollectionUtils.isEmpty(oldList)) {
            Iterator<StatisticsPersonFlow> it2 = list.iterator();
            while (it2.hasNext()) {
                result += this.statisticsPersonFlowMapper.insert((StatisticsPersonFlowMapper) it2.next());
            }
            return result;
        }
        Iterator<StatisticsPersonFlow> it3 = oldList.iterator();
        while (it3.hasNext()) {
            set.add(it3.next().getPersonType());
        }
        for (StatisticsPersonFlow flow : list) {
            if (set.contains(flow.getPersonType())) {
                result += this.statisticsPersonFlowMapper.updateByParams(flow);
            } else {
                result += this.statisticsPersonFlowMapper.insert((StatisticsPersonFlowMapper) flow);
            }
        }
        return result;
    }
}
