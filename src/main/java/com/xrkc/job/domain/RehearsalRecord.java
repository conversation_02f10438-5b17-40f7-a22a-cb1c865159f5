package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/RehearsalRecord.class */
public class RehearsalRecord {
    @TableId
    private Long rehearsalRecordId;
    private Long rehearsalId;
    private String rehearsalName;
    private String rehearsalLocation;
    private LocalDate rehearsalDate;
    private String leader;
    private String deptNames;
    private String rehearsalStatus;
    private String layerId;
    private Integer layerHeight;
    private String railScope;
    private String drawType;
    private LocalTime validBeginTime;
    private LocalTime validEndTime;
    private Integer personCount;
    private Integer actualCount;
    @TableField(exist = false)
    private Integer didNotArrive;
    private String description;
    private String courseRecord;
    private String evaluate;
    private String assessor;
    private String problem;
    private String accessory;
    private LocalDateTime createTime;
    private String createBy;
    private LocalDateTime updateTime;
    @TableField(exist = false)
    private List<Long> deptIds;
    @TableField(exist = false)
    private String tableName;
    public void setRehearsalRecordId(Long rehearsalRecordId) {
        this.rehearsalRecordId = rehearsalRecordId;
    }
    public void setRehearsalId(Long rehearsalId) {
        this.rehearsalId = rehearsalId;
    }
    public void setRehearsalName(String rehearsalName) {
        this.rehearsalName = rehearsalName;
    }
    public void setRehearsalLocation(String rehearsalLocation) {
        this.rehearsalLocation = rehearsalLocation;
    }
    public void setRehearsalDate(LocalDate rehearsalDate) {
        this.rehearsalDate = rehearsalDate;
    }
    public void setLeader(String leader) {
        this.leader = leader;
    }
    public void setDeptNames(String deptNames) {
        this.deptNames = deptNames;
    }
    public void setRehearsalStatus(String rehearsalStatus) {
        this.rehearsalStatus = rehearsalStatus;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
    public void setLayerHeight(Integer layerHeight) {
        this.layerHeight = layerHeight;
    }
    public void setRailScope(String railScope) {
        this.railScope = railScope;
    }
    public void setDrawType(String drawType) {
        this.drawType = drawType;
    }
    public void setValidBeginTime(LocalTime validBeginTime) {
        this.validBeginTime = validBeginTime;
    }
    public void setValidEndTime(LocalTime validEndTime) {
        this.validEndTime = validEndTime;
    }
    public void setPersonCount(Integer personCount) {
        this.personCount = personCount;
    }
    public void setActualCount(Integer actualCount) {
        this.actualCount = actualCount;
    }
    public void setDidNotArrive(Integer didNotArrive) {
        this.didNotArrive = didNotArrive;
    }
    public void setDescription(String description) {
        this.description = description;
    }
    public void setCourseRecord(String courseRecord) {
        this.courseRecord = courseRecord;
    }
    public void setEvaluate(String evaluate) {
        this.evaluate = evaluate;
    }
    public void setAssessor(String assessor) {
        this.assessor = assessor;
    }
    public void setProblem(String problem) {
        this.problem = problem;
    }
    public void setAccessory(String accessory) {
        this.accessory = accessory;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    public void setDeptIds(List<Long> deptIds) {
        this.deptIds = deptIds;
    }
    public void setTableName(String tableName) {
        this.tableName = tableName;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof RehearsalRecord)) {
            return false;
        }
        RehearsalRecord other = (RehearsalRecord) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$rehearsalRecordId = getRehearsalRecordId();
        Object other$rehearsalRecordId = other.getRehearsalRecordId();
        if (this$rehearsalRecordId == null) {
            if (other$rehearsalRecordId != null) {
                return false;
            }
        } else if (!this$rehearsalRecordId.equals(other$rehearsalRecordId)) {
            return false;
        }
        Object this$rehearsalId = getRehearsalId();
        Object other$rehearsalId = other.getRehearsalId();
        if (this$rehearsalId == null) {
            if (other$rehearsalId != null) {
                return false;
            }
        } else if (!this$rehearsalId.equals(other$rehearsalId)) {
            return false;
        }
        Object this$layerHeight = getLayerHeight();
        Object other$layerHeight = other.getLayerHeight();
        if (this$layerHeight == null) {
            if (other$layerHeight != null) {
                return false;
            }
        } else if (!this$layerHeight.equals(other$layerHeight)) {
            return false;
        }
        Object this$personCount = getPersonCount();
        Object other$personCount = other.getPersonCount();
        if (this$personCount == null) {
            if (other$personCount != null) {
                return false;
            }
        } else if (!this$personCount.equals(other$personCount)) {
            return false;
        }
        Object this$actualCount = getActualCount();
        Object other$actualCount = other.getActualCount();
        if (this$actualCount == null) {
            if (other$actualCount != null) {
                return false;
            }
        } else if (!this$actualCount.equals(other$actualCount)) {
            return false;
        }
        Object this$didNotArrive = getDidNotArrive();
        Object other$didNotArrive = other.getDidNotArrive();
        if (this$didNotArrive == null) {
            if (other$didNotArrive != null) {
                return false;
            }
        } else if (!this$didNotArrive.equals(other$didNotArrive)) {
            return false;
        }
        Object this$rehearsalName = getRehearsalName();
        Object other$rehearsalName = other.getRehearsalName();
        if (this$rehearsalName == null) {
            if (other$rehearsalName != null) {
                return false;
            }
        } else if (!this$rehearsalName.equals(other$rehearsalName)) {
            return false;
        }
        Object this$rehearsalLocation = getRehearsalLocation();
        Object other$rehearsalLocation = other.getRehearsalLocation();
        if (this$rehearsalLocation == null) {
            if (other$rehearsalLocation != null) {
                return false;
            }
        } else if (!this$rehearsalLocation.equals(other$rehearsalLocation)) {
            return false;
        }
        Object this$rehearsalDate = getRehearsalDate();
        Object other$rehearsalDate = other.getRehearsalDate();
        if (this$rehearsalDate == null) {
            if (other$rehearsalDate != null) {
                return false;
            }
        } else if (!this$rehearsalDate.equals(other$rehearsalDate)) {
            return false;
        }
        Object this$leader = getLeader();
        Object other$leader = other.getLeader();
        if (this$leader == null) {
            if (other$leader != null) {
                return false;
            }
        } else if (!this$leader.equals(other$leader)) {
            return false;
        }
        Object this$deptNames = getDeptNames();
        Object other$deptNames = other.getDeptNames();
        if (this$deptNames == null) {
            if (other$deptNames != null) {
                return false;
            }
        } else if (!this$deptNames.equals(other$deptNames)) {
            return false;
        }
        Object this$rehearsalStatus = getRehearsalStatus();
        Object other$rehearsalStatus = other.getRehearsalStatus();
        if (this$rehearsalStatus == null) {
            if (other$rehearsalStatus != null) {
                return false;
            }
        } else if (!this$rehearsalStatus.equals(other$rehearsalStatus)) {
            return false;
        }
        Object this$layerId = getLayerId();
        Object other$layerId = other.getLayerId();
        if (this$layerId == null) {
            if (other$layerId != null) {
                return false;
            }
        } else if (!this$layerId.equals(other$layerId)) {
            return false;
        }
        Object this$railScope = getRailScope();
        Object other$railScope = other.getRailScope();
        if (this$railScope == null) {
            if (other$railScope != null) {
                return false;
            }
        } else if (!this$railScope.equals(other$railScope)) {
            return false;
        }
        Object this$drawType = getDrawType();
        Object other$drawType = other.getDrawType();
        if (this$drawType == null) {
            if (other$drawType != null) {
                return false;
            }
        } else if (!this$drawType.equals(other$drawType)) {
            return false;
        }
        Object this$validBeginTime = getValidBeginTime();
        Object other$validBeginTime = other.getValidBeginTime();
        if (this$validBeginTime == null) {
            if (other$validBeginTime != null) {
                return false;
            }
        } else if (!this$validBeginTime.equals(other$validBeginTime)) {
            return false;
        }
        Object this$validEndTime = getValidEndTime();
        Object other$validEndTime = other.getValidEndTime();
        if (this$validEndTime == null) {
            if (other$validEndTime != null) {
                return false;
            }
        } else if (!this$validEndTime.equals(other$validEndTime)) {
            return false;
        }
        Object this$description = getDescription();
        Object other$description = other.getDescription();
        if (this$description == null) {
            if (other$description != null) {
                return false;
            }
        } else if (!this$description.equals(other$description)) {
            return false;
        }
        Object this$courseRecord = getCourseRecord();
        Object other$courseRecord = other.getCourseRecord();
        if (this$courseRecord == null) {
            if (other$courseRecord != null) {
                return false;
            }
        } else if (!this$courseRecord.equals(other$courseRecord)) {
            return false;
        }
        Object this$evaluate = getEvaluate();
        Object other$evaluate = other.getEvaluate();
        if (this$evaluate == null) {
            if (other$evaluate != null) {
                return false;
            }
        } else if (!this$evaluate.equals(other$evaluate)) {
            return false;
        }
        Object this$assessor = getAssessor();
        Object other$assessor = other.getAssessor();
        if (this$assessor == null) {
            if (other$assessor != null) {
                return false;
            }
        } else if (!this$assessor.equals(other$assessor)) {
            return false;
        }
        Object this$problem = getProblem();
        Object other$problem = other.getProblem();
        if (this$problem == null) {
            if (other$problem != null) {
                return false;
            }
        } else if (!this$problem.equals(other$problem)) {
            return false;
        }
        Object this$accessory = getAccessory();
        Object other$accessory = other.getAccessory();
        if (this$accessory == null) {
            if (other$accessory != null) {
                return false;
            }
        } else if (!this$accessory.equals(other$accessory)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        if (this$createTime == null) {
            if (other$createTime != null) {
                return false;
            }
        } else if (!this$createTime.equals(other$createTime)) {
            return false;
        }
        Object this$createBy = getCreateBy();
        Object other$createBy = other.getCreateBy();
        if (this$createBy == null) {
            if (other$createBy != null) {
                return false;
            }
        } else if (!this$createBy.equals(other$createBy)) {
            return false;
        }
        Object this$updateTime = getUpdateTime();
        Object other$updateTime = other.getUpdateTime();
        if (this$updateTime == null) {
            if (other$updateTime != null) {
                return false;
            }
        } else if (!this$updateTime.equals(other$updateTime)) {
            return false;
        }
        Object this$deptIds = getDeptIds();
        Object other$deptIds = other.getDeptIds();
        if (this$deptIds == null) {
            if (other$deptIds != null) {
                return false;
            }
        } else if (!this$deptIds.equals(other$deptIds)) {
            return false;
        }
        Object this$tableName = getTableName();
        Object other$tableName = other.getTableName();
        return this$tableName == null ? other$tableName == null : this$tableName.equals(other$tableName);
    }
    protected boolean canEqual(Object other) {
        return other instanceof RehearsalRecord;
    }
    public int hashCode() {
        Object $rehearsalRecordId = getRehearsalRecordId();
        int result = (1 * 59) + ($rehearsalRecordId == null ? 43 : $rehearsalRecordId.hashCode());
        Object $rehearsalId = getRehearsalId();
        int result2 = (result * 59) + ($rehearsalId == null ? 43 : $rehearsalId.hashCode());
        Object $layerHeight = getLayerHeight();
        int result3 = (result2 * 59) + ($layerHeight == null ? 43 : $layerHeight.hashCode());
        Object $personCount = getPersonCount();
        int result4 = (result3 * 59) + ($personCount == null ? 43 : $personCount.hashCode());
        Object $actualCount = getActualCount();
        int result5 = (result4 * 59) + ($actualCount == null ? 43 : $actualCount.hashCode());
        Object $didNotArrive = getDidNotArrive();
        int result6 = (result5 * 59) + ($didNotArrive == null ? 43 : $didNotArrive.hashCode());
        Object $rehearsalName = getRehearsalName();
        int result7 = (result6 * 59) + ($rehearsalName == null ? 43 : $rehearsalName.hashCode());
        Object $rehearsalLocation = getRehearsalLocation();
        int result8 = (result7 * 59) + ($rehearsalLocation == null ? 43 : $rehearsalLocation.hashCode());
        Object $rehearsalDate = getRehearsalDate();
        int result9 = (result8 * 59) + ($rehearsalDate == null ? 43 : $rehearsalDate.hashCode());
        Object $leader = getLeader();
        int result10 = (result9 * 59) + ($leader == null ? 43 : $leader.hashCode());
        Object $deptNames = getDeptNames();
        int result11 = (result10 * 59) + ($deptNames == null ? 43 : $deptNames.hashCode());
        Object $rehearsalStatus = getRehearsalStatus();
        int result12 = (result11 * 59) + ($rehearsalStatus == null ? 43 : $rehearsalStatus.hashCode());
        Object $layerId = getLayerId();
        int result13 = (result12 * 59) + ($layerId == null ? 43 : $layerId.hashCode());
        Object $railScope = getRailScope();
        int result14 = (result13 * 59) + ($railScope == null ? 43 : $railScope.hashCode());
        Object $drawType = getDrawType();
        int result15 = (result14 * 59) + ($drawType == null ? 43 : $drawType.hashCode());
        Object $validBeginTime = getValidBeginTime();
        int result16 = (result15 * 59) + ($validBeginTime == null ? 43 : $validBeginTime.hashCode());
        Object $validEndTime = getValidEndTime();
        int result17 = (result16 * 59) + ($validEndTime == null ? 43 : $validEndTime.hashCode());
        Object $description = getDescription();
        int result18 = (result17 * 59) + ($description == null ? 43 : $description.hashCode());
        Object $courseRecord = getCourseRecord();
        int result19 = (result18 * 59) + ($courseRecord == null ? 43 : $courseRecord.hashCode());
        Object $evaluate = getEvaluate();
        int result20 = (result19 * 59) + ($evaluate == null ? 43 : $evaluate.hashCode());
        Object $assessor = getAssessor();
        int result21 = (result20 * 59) + ($assessor == null ? 43 : $assessor.hashCode());
        Object $problem = getProblem();
        int result22 = (result21 * 59) + ($problem == null ? 43 : $problem.hashCode());
        Object $accessory = getAccessory();
        int result23 = (result22 * 59) + ($accessory == null ? 43 : $accessory.hashCode());
        Object $createTime = getCreateTime();
        int result24 = (result23 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $createBy = getCreateBy();
        int result25 = (result24 * 59) + ($createBy == null ? 43 : $createBy.hashCode());
        Object $updateTime = getUpdateTime();
        int result26 = (result25 * 59) + ($updateTime == null ? 43 : $updateTime.hashCode());
        Object $deptIds = getDeptIds();
        int result27 = (result26 * 59) + ($deptIds == null ? 43 : $deptIds.hashCode());
        Object $tableName = getTableName();
        return (result27 * 59) + ($tableName == null ? 43 : $tableName.hashCode());
    }
    public String toString() {
        return "RehearsalRecord(rehearsalRecordId=" + getRehearsalRecordId() + ", rehearsalId=" + getRehearsalId() + ", rehearsalName=" + getRehearsalName() + ", rehearsalLocation=" + getRehearsalLocation() + ", rehearsalDate=" + getRehearsalDate() + ", leader=" + getLeader() + ", deptNames=" + getDeptNames() + ", rehearsalStatus=" + getRehearsalStatus() + ", layerId=" + getLayerId() + ", layerHeight=" + getLayerHeight() + ", railScope=" + getRailScope() + ", drawType=" + getDrawType() + ", validBeginTime=" + getValidBeginTime() + ", validEndTime=" + getValidEndTime() + ", personCount=" + getPersonCount() + ", actualCount=" + getActualCount() + ", didNotArrive=" + getDidNotArrive() + ", description=" + getDescription() + ", courseRecord=" + getCourseRecord() + ", evaluate=" + getEvaluate() + ", assessor=" + getAssessor() + ", problem=" + getProblem() + ", accessory=" + getAccessory() + ", createTime=" + getCreateTime() + ", createBy=" + getCreateBy() + ", updateTime=" + getUpdateTime() + ", deptIds=" + getDeptIds() + ", tableName=" + getTableName() + StringPool.RIGHT_BRACKET;
    }
    public Long getRehearsalRecordId() {
        return this.rehearsalRecordId;
    }
    public Long getRehearsalId() {
        return this.rehearsalId;
    }
    public String getRehearsalName() {
        return this.rehearsalName;
    }
    public String getRehearsalLocation() {
        return this.rehearsalLocation;
    }
    public LocalDate getRehearsalDate() {
        return this.rehearsalDate;
    }
    public String getLeader() {
        return this.leader;
    }
    public String getDeptNames() {
        return this.deptNames;
    }
    public String getRehearsalStatus() {
        return this.rehearsalStatus;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public Integer getLayerHeight() {
        return this.layerHeight;
    }
    public String getRailScope() {
        return this.railScope;
    }
    public String getDrawType() {
        return this.drawType;
    }
    public LocalTime getValidBeginTime() {
        return this.validBeginTime;
    }
    public LocalTime getValidEndTime() {
        return this.validEndTime;
    }
    public Integer getPersonCount() {
        return this.personCount;
    }
    public Integer getActualCount() {
        return this.actualCount;
    }
    public Integer getDidNotArrive() {
        return this.didNotArrive;
    }
    public String getDescription() {
        return this.description;
    }
    public String getCourseRecord() {
        return this.courseRecord;
    }
    public String getEvaluate() {
        return this.evaluate;
    }
    public String getAssessor() {
        return this.assessor;
    }
    public String getProblem() {
        return this.problem;
    }
    public String getAccessory() {
        return this.accessory;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public String getCreateBy() {
        return this.createBy;
    }
    public LocalDateTime getUpdateTime() {
        return this.updateTime;
    }
    public List<Long> getDeptIds() {
        return this.deptIds;
    }
    public String getTableName() {
        return this.tableName;
    }
}
