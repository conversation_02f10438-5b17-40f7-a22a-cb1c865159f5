package com.xrkc.job.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xrkc.job.domain.DeviceCard;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IDeviceCardService.class */
public interface IDeviceCardService extends IService<DeviceCard> {
    List<DeviceCard> selectLowPowerCard();
    List<DeviceCard> selectForUDP();
    void updateUseStatus(Long l, String str);
    int updateUseStatus(Long l, String str, List<Long> list);
    void resetCardHeart();
    DeviceCard findByCardId(Long l);
    boolean getExistByCardIdEqPersonId(Long l, Long l2);
    List<DeviceCard> selectListByCardIdList(List<Long> list);
    List<Long> selectCardIdByRssi(String str);
}
