package com.xrkc.job.service;
import com.xrkc.job.domain.DeptStatisticsVo;
import com.xrkc.job.domain.SystemDict;
import java.util.List;
import java.util.Map;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/ISystemDictService.class */
public interface ISystemDictService {
    List<SystemDict> findDictType(String str);
    List<DeptStatisticsVo> selectCountDeptList();
    Map<String, SystemDict> findValEntityMap(String str);
}
