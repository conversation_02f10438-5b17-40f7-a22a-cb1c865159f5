package com.xrkc.job.task;
import com.xrkc.job.service.ISystemBackupSqlLogService;
import com.xrkc.job.service.ISystemConfigService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
@Component("backupTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/BackupTask.class */
public class BackupTask {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) BackupTask.class);
    @Autowired
    private ISystemBackupSqlLogService systemBackupSqlLogService;
    @Autowired
    private ISystemConfigService systemConfigService;
    public void backupSqlTask() {
        String backUpPath = this.systemConfigService.findConfigVal("back_up_path");
        if (StringUtils.isBlank(backUpPath)) {
            return;
        }
        this.systemBackupSqlLogService.createSqlLog(backUpPath);
    }
}
