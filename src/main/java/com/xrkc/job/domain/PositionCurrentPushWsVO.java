package com.xrkc.job.domain;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.math.BigDecimal;
import java.time.LocalDateTime;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/PositionCurrentPushWsVO.class */
public class PositionCurrentPushWsVO {
    private LocalDateTime acceptTime;
    private Integer beaconId;
    private Long cardId;
    private Integer cardPower;
    private BigDecimal longitude;
    private BigDecimal latitude;
    private String layerId;
    private Integer layerHeight;
    private String layerName;
    private Double layerMoveSpeed;
    private Long personId;
    private String personTypeName;
    private String personCategory;
    private String personAttribute;
    private String staffType;
    private String realName;
    private String personPhoto;
    private Long deptId;
    private String deptName;
    private String postName;
    private String phone;
    private String jobNumber;
    private String contractorName;
    private Integer undisposedAlarmCount;
    public void setAcceptTime(LocalDateTime acceptTime) {
        this.acceptTime = acceptTime;
    }
    public void setBeaconId(Integer beaconId) {
        this.beaconId = beaconId;
    }
    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }
    public void setCardPower(Integer cardPower) {
        this.cardPower = cardPower;
    }
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
    public void setLayerHeight(Integer layerHeight) {
        this.layerHeight = layerHeight;
    }
    public void setLayerName(String layerName) {
        this.layerName = layerName;
    }
    public void setLayerMoveSpeed(Double layerMoveSpeed) {
        this.layerMoveSpeed = layerMoveSpeed;
    }
    public void setPersonId(Long personId) {
        this.personId = personId;
    }
    public void setPersonTypeName(String personTypeName) {
        this.personTypeName = personTypeName;
    }
    public void setPersonCategory(String personCategory) {
        this.personCategory = personCategory;
    }
    public void setPersonAttribute(String personAttribute) {
        this.personAttribute = personAttribute;
    }
    public void setStaffType(String staffType) {
        this.staffType = staffType;
    }
    public void setRealName(String realName) {
        this.realName = realName;
    }
    public void setPersonPhoto(String personPhoto) {
        this.personPhoto = personPhoto;
    }
    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    public void setPostName(String postName) {
        this.postName = postName;
    }
    public void setPhone(String phone) {
        this.phone = phone;
    }
    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }
    public void setContractorName(String contractorName) {
        this.contractorName = contractorName;
    }
    public void setUndisposedAlarmCount(Integer undisposedAlarmCount) {
        this.undisposedAlarmCount = undisposedAlarmCount;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof PositionCurrentPushWsVO)) {
            return false;
        }
        PositionCurrentPushWsVO other = (PositionCurrentPushWsVO) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$beaconId = getBeaconId();
        Object other$beaconId = other.getBeaconId();
        if (this$beaconId == null) {
            if (other$beaconId != null) {
                return false;
            }
        } else if (!this$beaconId.equals(other$beaconId)) {
            return false;
        }
        Object this$cardId = getCardId();
        Object other$cardId = other.getCardId();
        if (this$cardId == null) {
            if (other$cardId != null) {
                return false;
            }
        } else if (!this$cardId.equals(other$cardId)) {
            return false;
        }
        Object this$cardPower = getCardPower();
        Object other$cardPower = other.getCardPower();
        if (this$cardPower == null) {
            if (other$cardPower != null) {
                return false;
            }
        } else if (!this$cardPower.equals(other$cardPower)) {
            return false;
        }
        Object this$layerHeight = getLayerHeight();
        Object other$layerHeight = other.getLayerHeight();
        if (this$layerHeight == null) {
            if (other$layerHeight != null) {
                return false;
            }
        } else if (!this$layerHeight.equals(other$layerHeight)) {
            return false;
        }
        Object this$layerMoveSpeed = getLayerMoveSpeed();
        Object other$layerMoveSpeed = other.getLayerMoveSpeed();
        if (this$layerMoveSpeed == null) {
            if (other$layerMoveSpeed != null) {
                return false;
            }
        } else if (!this$layerMoveSpeed.equals(other$layerMoveSpeed)) {
            return false;
        }
        Object this$personId = getPersonId();
        Object other$personId = other.getPersonId();
        if (this$personId == null) {
            if (other$personId != null) {
                return false;
            }
        } else if (!this$personId.equals(other$personId)) {
            return false;
        }
        Object this$deptId = getDeptId();
        Object other$deptId = other.getDeptId();
        if (this$deptId == null) {
            if (other$deptId != null) {
                return false;
            }
        } else if (!this$deptId.equals(other$deptId)) {
            return false;
        }
        Object this$undisposedAlarmCount = getUndisposedAlarmCount();
        Object other$undisposedAlarmCount = other.getUndisposedAlarmCount();
        if (this$undisposedAlarmCount == null) {
            if (other$undisposedAlarmCount != null) {
                return false;
            }
        } else if (!this$undisposedAlarmCount.equals(other$undisposedAlarmCount)) {
            return false;
        }
        Object this$acceptTime = getAcceptTime();
        Object other$acceptTime = other.getAcceptTime();
        if (this$acceptTime == null) {
            if (other$acceptTime != null) {
                return false;
            }
        } else if (!this$acceptTime.equals(other$acceptTime)) {
            return false;
        }
        Object this$longitude = getLongitude();
        Object other$longitude = other.getLongitude();
        if (this$longitude == null) {
            if (other$longitude != null) {
                return false;
            }
        } else if (!this$longitude.equals(other$longitude)) {
            return false;
        }
        Object this$latitude = getLatitude();
        Object other$latitude = other.getLatitude();
        if (this$latitude == null) {
            if (other$latitude != null) {
                return false;
            }
        } else if (!this$latitude.equals(other$latitude)) {
            return false;
        }
        Object this$layerId = getLayerId();
        Object other$layerId = other.getLayerId();
        if (this$layerId == null) {
            if (other$layerId != null) {
                return false;
            }
        } else if (!this$layerId.equals(other$layerId)) {
            return false;
        }
        Object this$layerName = getLayerName();
        Object other$layerName = other.getLayerName();
        if (this$layerName == null) {
            if (other$layerName != null) {
                return false;
            }
        } else if (!this$layerName.equals(other$layerName)) {
            return false;
        }
        Object this$personTypeName = getPersonTypeName();
        Object other$personTypeName = other.getPersonTypeName();
        if (this$personTypeName == null) {
            if (other$personTypeName != null) {
                return false;
            }
        } else if (!this$personTypeName.equals(other$personTypeName)) {
            return false;
        }
        Object this$personCategory = getPersonCategory();
        Object other$personCategory = other.getPersonCategory();
        if (this$personCategory == null) {
            if (other$personCategory != null) {
                return false;
            }
        } else if (!this$personCategory.equals(other$personCategory)) {
            return false;
        }
        Object this$personAttribute = getPersonAttribute();
        Object other$personAttribute = other.getPersonAttribute();
        if (this$personAttribute == null) {
            if (other$personAttribute != null) {
                return false;
            }
        } else if (!this$personAttribute.equals(other$personAttribute)) {
            return false;
        }
        Object this$staffType = getStaffType();
        Object other$staffType = other.getStaffType();
        if (this$staffType == null) {
            if (other$staffType != null) {
                return false;
            }
        } else if (!this$staffType.equals(other$staffType)) {
            return false;
        }
        Object this$realName = getRealName();
        Object other$realName = other.getRealName();
        if (this$realName == null) {
            if (other$realName != null) {
                return false;
            }
        } else if (!this$realName.equals(other$realName)) {
            return false;
        }
        Object this$personPhoto = getPersonPhoto();
        Object other$personPhoto = other.getPersonPhoto();
        if (this$personPhoto == null) {
            if (other$personPhoto != null) {
                return false;
            }
        } else if (!this$personPhoto.equals(other$personPhoto)) {
            return false;
        }
        Object this$deptName = getDeptName();
        Object other$deptName = other.getDeptName();
        if (this$deptName == null) {
            if (other$deptName != null) {
                return false;
            }
        } else if (!this$deptName.equals(other$deptName)) {
            return false;
        }
        Object this$postName = getPostName();
        Object other$postName = other.getPostName();
        if (this$postName == null) {
            if (other$postName != null) {
                return false;
            }
        } else if (!this$postName.equals(other$postName)) {
            return false;
        }
        Object this$phone = getPhone();
        Object other$phone = other.getPhone();
        if (this$phone == null) {
            if (other$phone != null) {
                return false;
            }
        } else if (!this$phone.equals(other$phone)) {
            return false;
        }
        Object this$jobNumber = getJobNumber();
        Object other$jobNumber = other.getJobNumber();
        if (this$jobNumber == null) {
            if (other$jobNumber != null) {
                return false;
            }
        } else if (!this$jobNumber.equals(other$jobNumber)) {
            return false;
        }
        Object this$contractorName = getContractorName();
        Object other$contractorName = other.getContractorName();
        return this$contractorName == null ? other$contractorName == null : this$contractorName.equals(other$contractorName);
    }
    protected boolean canEqual(Object other) {
        return other instanceof PositionCurrentPushWsVO;
    }
    public int hashCode() {
        Object $beaconId = getBeaconId();
        int result = (1 * 59) + ($beaconId == null ? 43 : $beaconId.hashCode());
        Object $cardId = getCardId();
        int result2 = (result * 59) + ($cardId == null ? 43 : $cardId.hashCode());
        Object $cardPower = getCardPower();
        int result3 = (result2 * 59) + ($cardPower == null ? 43 : $cardPower.hashCode());
        Object $layerHeight = getLayerHeight();
        int result4 = (result3 * 59) + ($layerHeight == null ? 43 : $layerHeight.hashCode());
        Object $layerMoveSpeed = getLayerMoveSpeed();
        int result5 = (result4 * 59) + ($layerMoveSpeed == null ? 43 : $layerMoveSpeed.hashCode());
        Object $personId = getPersonId();
        int result6 = (result5 * 59) + ($personId == null ? 43 : $personId.hashCode());
        Object $deptId = getDeptId();
        int result7 = (result6 * 59) + ($deptId == null ? 43 : $deptId.hashCode());
        Object $undisposedAlarmCount = getUndisposedAlarmCount();
        int result8 = (result7 * 59) + ($undisposedAlarmCount == null ? 43 : $undisposedAlarmCount.hashCode());
        Object $acceptTime = getAcceptTime();
        int result9 = (result8 * 59) + ($acceptTime == null ? 43 : $acceptTime.hashCode());
        Object $longitude = getLongitude();
        int result10 = (result9 * 59) + ($longitude == null ? 43 : $longitude.hashCode());
        Object $latitude = getLatitude();
        int result11 = (result10 * 59) + ($latitude == null ? 43 : $latitude.hashCode());
        Object $layerId = getLayerId();
        int result12 = (result11 * 59) + ($layerId == null ? 43 : $layerId.hashCode());
        Object $layerName = getLayerName();
        int result13 = (result12 * 59) + ($layerName == null ? 43 : $layerName.hashCode());
        Object $personTypeName = getPersonTypeName();
        int result14 = (result13 * 59) + ($personTypeName == null ? 43 : $personTypeName.hashCode());
        Object $personCategory = getPersonCategory();
        int result15 = (result14 * 59) + ($personCategory == null ? 43 : $personCategory.hashCode());
        Object $personAttribute = getPersonAttribute();
        int result16 = (result15 * 59) + ($personAttribute == null ? 43 : $personAttribute.hashCode());
        Object $staffType = getStaffType();
        int result17 = (result16 * 59) + ($staffType == null ? 43 : $staffType.hashCode());
        Object $realName = getRealName();
        int result18 = (result17 * 59) + ($realName == null ? 43 : $realName.hashCode());
        Object $personPhoto = getPersonPhoto();
        int result19 = (result18 * 59) + ($personPhoto == null ? 43 : $personPhoto.hashCode());
        Object $deptName = getDeptName();
        int result20 = (result19 * 59) + ($deptName == null ? 43 : $deptName.hashCode());
        Object $postName = getPostName();
        int result21 = (result20 * 59) + ($postName == null ? 43 : $postName.hashCode());
        Object $phone = getPhone();
        int result22 = (result21 * 59) + ($phone == null ? 43 : $phone.hashCode());
        Object $jobNumber = getJobNumber();
        int result23 = (result22 * 59) + ($jobNumber == null ? 43 : $jobNumber.hashCode());
        Object $contractorName = getContractorName();
        return (result23 * 59) + ($contractorName == null ? 43 : $contractorName.hashCode());
    }
    public String toString() {
        return "PositionCurrentPushWsVO(acceptTime=" + getAcceptTime() + ", beaconId=" + getBeaconId() + ", cardId=" + getCardId() + ", cardPower=" + getCardPower() + ", longitude=" + getLongitude() + ", latitude=" + getLatitude() + ", layerId=" + getLayerId() + ", layerHeight=" + getLayerHeight() + ", layerName=" + getLayerName() + ", layerMoveSpeed=" + getLayerMoveSpeed() + ", personId=" + getPersonId() + ", personTypeName=" + getPersonTypeName() + ", personCategory=" + getPersonCategory() + ", personAttribute=" + getPersonAttribute() + ", staffType=" + getStaffType() + ", realName=" + getRealName() + ", personPhoto=" + getPersonPhoto() + ", deptId=" + getDeptId() + ", deptName=" + getDeptName() + ", postName=" + getPostName() + ", phone=" + getPhone() + ", jobNumber=" + getJobNumber() + ", contractorName=" + getContractorName() + ", undisposedAlarmCount=" + getUndisposedAlarmCount() + StringPool.RIGHT_BRACKET;
    }
    public LocalDateTime getAcceptTime() {
        return this.acceptTime;
    }
    public Integer getBeaconId() {
        return this.beaconId;
    }
    public Long getCardId() {
        return this.cardId;
    }
    public Integer getCardPower() {
        return this.cardPower;
    }
    public BigDecimal getLongitude() {
        return this.longitude;
    }
    public BigDecimal getLatitude() {
        return this.latitude;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public Integer getLayerHeight() {
        return this.layerHeight;
    }
    public String getLayerName() {
        return this.layerName;
    }
    public Double getLayerMoveSpeed() {
        return this.layerMoveSpeed;
    }
    public Long getPersonId() {
        return this.personId;
    }
    public String getPersonTypeName() {
        return this.personTypeName;
    }
    public String getPersonCategory() {
        return this.personCategory;
    }
    public String getPersonAttribute() {
        return this.personAttribute;
    }
    public String getStaffType() {
        return this.staffType;
    }
    public String getRealName() {
        return this.realName;
    }
    public String getPersonPhoto() {
        return this.personPhoto;
    }
    public Long getDeptId() {
        return this.deptId;
    }
    public String getDeptName() {
        return this.deptName;
    }
    public String getPostName() {
        return this.postName;
    }
    public String getPhone() {
        return this.phone;
    }
    public String getJobNumber() {
        return this.jobNumber;
    }
    public String getContractorName() {
        return this.contractorName;
    }
    public Integer getUndisposedAlarmCount() {
        return this.undisposedAlarmCount;
    }
}
