package com.xrkc.job.service;
import com.xrkc.job.domain.FacilityAccessRecord;
import com.xrkc.job.domain.StatisticsFacilityStay;
import java.time.LocalDateTime;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IStatisticsFacilityStayService.class */
public interface IStatisticsFacilityStayService {
    int insert(StatisticsFacilityStay statisticsFacilityStay);
    List<FacilityAccessRecord> selectList();
    int updateDisposeStatus(Long l, LocalDateTime localDateTime, Long l2);
    void batchDelete(int i);
    void delByDisposeStatus();
}
