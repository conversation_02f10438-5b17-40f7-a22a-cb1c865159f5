package com.xrkc.job.vo;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/vo/PushCardSenderMqttDetailVO.class */
public class PushCardSenderMqttDetailVO {
    private String uniqueId;
    private Long cardSenderId;
    private String deviceSn;
    private String deviceUrl;
    private String deviceName;
    private Long useSenderCount;
    private Integer senderCount;
    private BigDecimal longitude;
    private BigDecimal latitude;
    private String layerId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime heartTime;
    private String deviceStatus;
    private List<PushCardSenderMqttNumVO> slotList;
    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }
    public void setCardSenderId(Long cardSenderId) {
        this.cardSenderId = cardSenderId;
    }
    public void setDeviceSn(String deviceSn) {
        this.deviceSn = deviceSn;
    }
    public void setDeviceUrl(String deviceUrl) {
        this.deviceUrl = deviceUrl;
    }
    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }
    public void setUseSenderCount(Long useSenderCount) {
        this.useSenderCount = useSenderCount;
    }
    public void setSenderCount(Integer senderCount) {
        this.senderCount = senderCount;
    }
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public void setHeartTime(LocalDateTime heartTime) {
        this.heartTime = heartTime;
    }
    public void setDeviceStatus(String deviceStatus) {
        this.deviceStatus = deviceStatus;
    }
    public void setSlotList(List<PushCardSenderMqttNumVO> slotList) {
        this.slotList = slotList;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof PushCardSenderMqttDetailVO)) {
            return false;
        }
        PushCardSenderMqttDetailVO other = (PushCardSenderMqttDetailVO) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$cardSenderId = getCardSenderId();
        Object other$cardSenderId = other.getCardSenderId();
        if (this$cardSenderId == null) {
            if (other$cardSenderId != null) {
                return false;
            }
        } else if (!this$cardSenderId.equals(other$cardSenderId)) {
            return false;
        }
        Object this$useSenderCount = getUseSenderCount();
        Object other$useSenderCount = other.getUseSenderCount();
        if (this$useSenderCount == null) {
            if (other$useSenderCount != null) {
                return false;
            }
        } else if (!this$useSenderCount.equals(other$useSenderCount)) {
            return false;
        }
        Object this$senderCount = getSenderCount();
        Object other$senderCount = other.getSenderCount();
        if (this$senderCount == null) {
            if (other$senderCount != null) {
                return false;
            }
        } else if (!this$senderCount.equals(other$senderCount)) {
            return false;
        }
        Object this$uniqueId = getUniqueId();
        Object other$uniqueId = other.getUniqueId();
        if (this$uniqueId == null) {
            if (other$uniqueId != null) {
                return false;
            }
        } else if (!this$uniqueId.equals(other$uniqueId)) {
            return false;
        }
        Object this$deviceSn = getDeviceSn();
        Object other$deviceSn = other.getDeviceSn();
        if (this$deviceSn == null) {
            if (other$deviceSn != null) {
                return false;
            }
        } else if (!this$deviceSn.equals(other$deviceSn)) {
            return false;
        }
        Object this$deviceUrl = getDeviceUrl();
        Object other$deviceUrl = other.getDeviceUrl();
        if (this$deviceUrl == null) {
            if (other$deviceUrl != null) {
                return false;
            }
        } else if (!this$deviceUrl.equals(other$deviceUrl)) {
            return false;
        }
        Object this$deviceName = getDeviceName();
        Object other$deviceName = other.getDeviceName();
        if (this$deviceName == null) {
            if (other$deviceName != null) {
                return false;
            }
        } else if (!this$deviceName.equals(other$deviceName)) {
            return false;
        }
        Object this$longitude = getLongitude();
        Object other$longitude = other.getLongitude();
        if (this$longitude == null) {
            if (other$longitude != null) {
                return false;
            }
        } else if (!this$longitude.equals(other$longitude)) {
            return false;
        }
        Object this$latitude = getLatitude();
        Object other$latitude = other.getLatitude();
        if (this$latitude == null) {
            if (other$latitude != null) {
                return false;
            }
        } else if (!this$latitude.equals(other$latitude)) {
            return false;
        }
        Object this$layerId = getLayerId();
        Object other$layerId = other.getLayerId();
        if (this$layerId == null) {
            if (other$layerId != null) {
                return false;
            }
        } else if (!this$layerId.equals(other$layerId)) {
            return false;
        }
        Object this$heartTime = getHeartTime();
        Object other$heartTime = other.getHeartTime();
        if (this$heartTime == null) {
            if (other$heartTime != null) {
                return false;
            }
        } else if (!this$heartTime.equals(other$heartTime)) {
            return false;
        }
        Object this$deviceStatus = getDeviceStatus();
        Object other$deviceStatus = other.getDeviceStatus();
        if (this$deviceStatus == null) {
            if (other$deviceStatus != null) {
                return false;
            }
        } else if (!this$deviceStatus.equals(other$deviceStatus)) {
            return false;
        }
        Object this$slotList = getSlotList();
        Object other$slotList = other.getSlotList();
        return this$slotList == null ? other$slotList == null : this$slotList.equals(other$slotList);
    }
    protected boolean canEqual(Object other) {
        return other instanceof PushCardSenderMqttDetailVO;
    }
    public int hashCode() {
        Object $cardSenderId = getCardSenderId();
        int result = (1 * 59) + ($cardSenderId == null ? 43 : $cardSenderId.hashCode());
        Object $useSenderCount = getUseSenderCount();
        int result2 = (result * 59) + ($useSenderCount == null ? 43 : $useSenderCount.hashCode());
        Object $senderCount = getSenderCount();
        int result3 = (result2 * 59) + ($senderCount == null ? 43 : $senderCount.hashCode());
        Object $uniqueId = getUniqueId();
        int result4 = (result3 * 59) + ($uniqueId == null ? 43 : $uniqueId.hashCode());
        Object $deviceSn = getDeviceSn();
        int result5 = (result4 * 59) + ($deviceSn == null ? 43 : $deviceSn.hashCode());
        Object $deviceUrl = getDeviceUrl();
        int result6 = (result5 * 59) + ($deviceUrl == null ? 43 : $deviceUrl.hashCode());
        Object $deviceName = getDeviceName();
        int result7 = (result6 * 59) + ($deviceName == null ? 43 : $deviceName.hashCode());
        Object $longitude = getLongitude();
        int result8 = (result7 * 59) + ($longitude == null ? 43 : $longitude.hashCode());
        Object $latitude = getLatitude();
        int result9 = (result8 * 59) + ($latitude == null ? 43 : $latitude.hashCode());
        Object $layerId = getLayerId();
        int result10 = (result9 * 59) + ($layerId == null ? 43 : $layerId.hashCode());
        Object $heartTime = getHeartTime();
        int result11 = (result10 * 59) + ($heartTime == null ? 43 : $heartTime.hashCode());
        Object $deviceStatus = getDeviceStatus();
        int result12 = (result11 * 59) + ($deviceStatus == null ? 43 : $deviceStatus.hashCode());
        Object $slotList = getSlotList();
        return (result12 * 59) + ($slotList == null ? 43 : $slotList.hashCode());
    }
    public String toString() {
        return "PushCardSenderMqttDetailVO(uniqueId=" + getUniqueId() + ", cardSenderId=" + getCardSenderId() + ", deviceSn=" + getDeviceSn() + ", deviceUrl=" + getDeviceUrl() + ", deviceName=" + getDeviceName() + ", useSenderCount=" + getUseSenderCount() + ", senderCount=" + getSenderCount() + ", longitude=" + getLongitude() + ", latitude=" + getLatitude() + ", layerId=" + getLayerId() + ", heartTime=" + getHeartTime() + ", deviceStatus=" + getDeviceStatus() + ", slotList=" + getSlotList() + StringPool.RIGHT_BRACKET;
    }
    public String getUniqueId() {
        return this.uniqueId;
    }
    public Long getCardSenderId() {
        return this.cardSenderId;
    }
    public String getDeviceSn() {
        return this.deviceSn;
    }
    public String getDeviceUrl() {
        return this.deviceUrl;
    }
    public String getDeviceName() {
        return this.deviceName;
    }
    public Long getUseSenderCount() {
        return this.useSenderCount;
    }
    public Integer getSenderCount() {
        return this.senderCount;
    }
    public BigDecimal getLongitude() {
        return this.longitude;
    }
    public BigDecimal getLatitude() {
        return this.latitude;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public LocalDateTime getHeartTime() {
        return this.heartTime;
    }
    public String getDeviceStatus() {
        return this.deviceStatus;
    }
    public List<PushCardSenderMqttNumVO> getSlotList() {
        return this.slotList;
    }
}
