package com.xrkc.job.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xrkc.job.domain.SystemUserDataScopeVO;
import com.xrkc.job.domain.SystemUserRole;
import java.util.List;
import java.util.Set;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/ISystemUserRoleService.class */
public interface ISystemUserRoleService extends IService<SystemUserRole> {
    List<SystemUserRole> getUserRoleList();
    List<SystemUserDataScopeVO> findDataScopeByUserIds(Set<Long> set);
}
