package com.xrkc.job.util;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.xrkc.job.domain.SystemJob;
import com.xrkc.redis.utils.RedisCacheUtils;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/JobInvokeUtil.class */
public class JobInvokeUtil {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) JobInvokeUtil.class);
    public static void invokeMethod(SystemJob sysJob) throws Exception {
        try {
            try {
                String invokeTarget = sysJob.getJobTask();
                String beanName = getBeanName(invokeTarget);
                String methodName = getMethodName(invokeTarget);
                List<Object[]> methodParams = getMethodParams(invokeTarget);
                if ("N".equals(sysJob.getJobConcurrent()) && !RedisCacheUtils.setIfAbsent(sysJob.getJobTask(), "", 30L, TimeUnit.MINUTES)) {
                    RedisCacheUtils.removeKey(sysJob.getJobTask());
                    return;
                }
                if (isValidClassName(beanName)) {
                    Object bean = Class.forName(beanName).newInstance();
                    invokeMethod(bean, methodName, methodParams);
                } else {
                    Object bean2 = SpringUtils.getBean(beanName);
                    invokeMethod(bean2, methodName, methodParams);
                }
                RedisCacheUtils.removeKey(sysJob.getJobTask());
            } catch (Throwable e) {
                log.error(e.getMessage(), e);
                RedisCacheUtils.removeKey(sysJob.getJobTask());
            }
        } catch (Throwable th) {
            RedisCacheUtils.removeKey(sysJob.getJobTask());
            throw th;
        }
    }
    private static void invokeMethod(Object bean, String methodName, List<Object[]> methodParams) throws IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException {
        if (!CollectionUtils.isEmpty(methodParams)) {
            Method method = bean.getClass().getDeclaredMethod(methodName, getMethodParamsType(methodParams));
            method.invoke(bean, getMethodParamsValue(methodParams));
            return;
        }
        Method method2 = bean.getClass().getDeclaredMethod(methodName, new Class[0]);
        try {
            method2.invoke(bean, new Object[0]);
        } catch (Exception e) {
            log.error("任务处理失败,methodName:{},msg:{}", methodName, e.getMessage());
            log.error(e.getMessage(), (Throwable) e);
        }
    }
    public static boolean isValidClassName(String invokeTarget) {
        return StringUtils.countMatches(invokeTarget, ".") > 1;
    }
    public static String getBeanName(String invokeTarget) {
        String beanName = StringUtils.substringBefore(invokeTarget, StringPool.LEFT_BRACKET);
        return StringUtils.substringBeforeLast(beanName, ".");
    }
    public static String getMethodName(String invokeTarget) {
        String methodName = StringUtils.substringBefore(invokeTarget, StringPool.LEFT_BRACKET);
        return StringUtils.substringAfterLast(methodName, ".");
    }
    public static List<Object[]> getMethodParams(String invokeTarget) {
        String methodStr = StringUtils.substringBetween(invokeTarget, StringPool.LEFT_BRACKET, StringPool.RIGHT_BRACKET);
        if (StringUtils.isEmpty(methodStr)) {
            return null;
        }
        String[] methodParams = methodStr.split(",");
        List<Object[]> classs = new LinkedList<>();
        for (String str : methodParams) {
            String str2 = StringUtils.trimToEmpty(str);
            if (StringUtils.contains(str2, StringPool.SINGLE_QUOTE)) {
                classs.add(new Object[]{StringUtils.replace(str2, StringPool.SINGLE_QUOTE, ""), String.class});
            } else if (StringUtils.equals(str2, "true") || StringUtils.equalsIgnoreCase(str2, "false")) {
                classs.add(new Object[]{Boolean.valueOf(str2), Boolean.class});
            } else if (StringUtils.containsIgnoreCase(str2, "L")) {
                classs.add(new Object[]{Long.valueOf(StringUtils.replaceIgnoreCase(str2, "L", "")), Long.class});
            } else if (StringUtils.containsIgnoreCase(str2, "D")) {
                classs.add(new Object[]{Double.valueOf(StringUtils.replaceIgnoreCase(str2, "D", "")), Double.class});
            } else {
                classs.add(new Object[]{Integer.valueOf(str2), Integer.class});
            }
        }
        return classs;
    }
    public static Class<?>[] getMethodParamsType(List<Object[]> methodParams) {
        Class<?>[] classs = new Class[methodParams.size()];
        int index = 0;
        for (Object[] os : methodParams) {
            classs[index] = (Class) os[1];
            index++;
        }
        return classs;
    }
    public static Object[] getMethodParamsValue(List<Object[]> methodParams) {
        Object[] classs = new Object[methodParams.size()];
        int index = 0;
        for (Object[] os : methodParams) {
            classs[index] = os[0];
            index++;
        }
        return classs;
    }
}
