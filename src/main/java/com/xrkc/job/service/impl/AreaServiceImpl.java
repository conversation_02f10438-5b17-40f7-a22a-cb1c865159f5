package com.xrkc.job.service.impl;
import com.xrkc.job.domain.AreaAlarmTempVO;
import com.xrkc.job.mapper.AreaMapper;
import com.xrkc.job.service.IAreaAlarmPersonService;
import com.xrkc.job.service.IAreaAlarmPostService;
import com.xrkc.job.service.IAreaService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/AreaServiceImpl.class */
public class AreaServiceImpl implements IAreaService {
    @Autowired
    private AreaMapper areaMapper;
    @Autowired
    IAreaAlarmPostService areaAlarmPostService;
    @Autowired
    IAreaAlarmPersonService areaAlarmPersonService;
    @Override // com.xrkc.job.service.IAreaService
    public List<AreaAlarmTempVO> selectList() {
        return this.areaMapper.selectList();
    }
    @Override // com.xrkc.job.service.IAreaService
    public List<Long> findDeptIdsByAreaId(Long areaId) {
        return this.areaMapper.findDeptIdsByAreaId(areaId);
    }
    @Override // com.xrkc.job.service.IAreaService
    public List<Long> findPersonIdsByAreaId(Long areaId) {
        return this.areaMapper.findPersonIdsByAreaId(areaId);
    }
    @Override // com.xrkc.job.service.IAreaService
    public List<AreaAlarmTempVO> selectAreaAlarmByAlarmType(String alarmType) {
        return this.areaMapper.selectAreaAlarmByAlarmType(alarmType);
    }
    @Override // com.xrkc.job.service.IAreaService
    public List<Long> findPostIdsByAreaId(Long areaId) {
        return this.areaAlarmPostService.findPostIdsByAreaId(areaId);
    }
    @Override // com.xrkc.job.service.IAreaService
    public List<Long> findAlarmPersonIdsByAreaId(Long areaId) {
        return this.areaAlarmPersonService.findAlarmPersonIdsByAreaId(areaId);
    }
}
