package com.xrkc.job.service.impl;
import cn.hutool.core.collection.CollUtil;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.domain.record.GateRecord;
import com.xrkc.core.domain.system.SystemApi;
import com.xrkc.core.domain.vo.GateAreaVo;
import com.xrkc.job.domain.AccessRecordParmVO;
import com.xrkc.job.domain.DeviceGateVO;
import com.xrkc.job.domain.GatePeople;
import com.xrkc.job.mapper.GatePeopleMapper;
import com.xrkc.job.service.IGatePeopleService;
import com.xrkc.job.util.PersonSynchronizerUtil;
import com.xrkc.job.util.StringUtils;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/GatePeopleServiceImpl.class */
public class GatePeopleServiceImpl implements IGatePeopleService {
    private static final Logger log = LogManager.getLogger((Class<?>) GatePeopleServiceImpl.class);
    @Autowired
    private GatePeopleMapper deviceGateMapper;
    @Override // com.xrkc.job.service.IGatePeopleService
    public void replacementPersonDeparture(LocalDateTime initializationTime) {
        ArrayList<GateRecord> list = new ArrayList<>();
        List<GateAreaVo> areaStaffRecord = this.deviceGateMapper.getAreaStaffRecord(null, initializationTime);
        if (CollUtil.isNotEmpty((Collection<?>) areaStaffRecord)) {
            areaStaffRecord.forEach(s -> {
                GateRecord gateRecord = new GateRecord();
                BeanUtils.copyProperties(s, gateRecord);
                gateRecord.setAcceptTime(initializationTime);
                gateRecord.setCreateTime(initializationTime);
                gateRecord.setStatusId(ConfigValue.STATUS_ID_99);
                gateRecord.setGateLocation("系统重置");
                list.add(gateRecord);
            });
        }
        if (CollUtil.isNotEmpty((Collection<?>) list)) {
            this.deviceGateMapper.insertBatch(list, 1000);
        }
        log.info("重置人员离开数量:{}", Integer.valueOf(list.size()));
    }
    @Override // com.xrkc.job.service.IGatePeopleService
    public Boolean historicalAccessRecord(SystemApi historySystemApi, LocalDateTime currentTime) throws BeansException {
        Map<String, String> staffmap = new HashMap<>();
        staffmap.put("staff", "员工");
        staffmap.put("visitor", "访客");
        staffmap.put("contractor", "承包商");
        if (Objects.isNull(historySystemApi.getUpdateTime())) {
            historySystemApi.setUpdateTime(historySystemApi.getCreateTime());
        }
        List<AccessRecordParmVO> accessRecordParmVOS = PersonSynchronizerUtil.addHistoricalAccessRecord(historySystemApi, currentTime);
        ArrayList<GateRecord> list = new ArrayList<>();
        if (Objects.isNull(accessRecordParmVOS)) {
            return false;
        }
        if (CollUtil.isNotEmpty((Collection<?>) accessRecordParmVOS)) {
            List<DeviceGateVO> deviceGateVO = this.deviceGateMapper.getAllGate();
            Map<String, DeviceGateVO> map = (Map) deviceGateVO.stream().collect(Collectors.toMap((v0) -> {
                return v0.getReaderIndexCode();
            }, t -> {
                return t;
            }));
            List<String> recordlist = this.deviceGateMapper.getAccessRecord(historySystemApi.getUpdateTime(), currentTime);
            List<GatePeople> peopleList = this.deviceGateMapper.getAllPeople();
            Map<String, GatePeople> peopleMap = (Map) peopleList.stream().collect(Collectors.toMap((v0) -> {
                return v0.getPersonCode();
            }, s -> {
                return s;
            }));
            for (AccessRecordParmVO s2 : accessRecordParmVOS) {
                if (CheckForExistence(s2, recordlist).booleanValue()) {
                    GatePeople vo = peopleMap.get(s2.getPersonId());
                    log.info("当前海康人员是:{}", s2.getPersonId());
                    if (Objects.nonNull(vo)) {
                        if (Objects.nonNull(vo.getPersonId())) {
                            log.info("新增定位系统人员id进出记录:{}", vo.getPersonId());
                            GateRecord gateRecord = new GateRecord();
                            BeanUtils.copyProperties(vo, gateRecord);
                            gateRecord.setAcceptTime(s2.getDeviceTime());
                            gateRecord.setCreateTime(currentTime);
                            gateRecord.setStaffTypeName(staffmap.get(gateRecord.getStaffType()));
                            if (map.containsKey(s2.getReaderIndexCode())) {
                                DeviceGateVO gateVO = map.get(s2.getReaderIndexCode());
                                gateRecord.setStatusId(gateVO.getGateAccess());
                                gateRecord.setDeviceId(String.valueOf(gateVO.getId()));
                                gateRecord.setGateLocation(gateVO.getGateName());
                                gateRecord.setGateArea(gateVO.getGateArea());
                            } else {
                                gateRecord.setGateLocation(s2.getReaderName());
                                gateRecord.setDeviceId(s2.getReaderIndexCode());
                                if (StringUtils.isNotEmpty(s2.getReaderName())) {
                                    gateRecord.setStatusId(s2.getDoorName().contains("IN") ? ConfigValue.STATUS_ID_98 : ConfigValue.STATUS_ID_99);
                                }
                            }
                            list.add(gateRecord);
                        }
                    } else {
                        log.info("当前海康人员不在人员定位系统中:{}", s2.getPersonId());
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty((Collection<?>) list)) {
            this.deviceGateMapper.insertBatch(list, 1000);
        }
        return true;
    }
    public Boolean CheckForExistence(AccessRecordParmVO v, List<String> recordlist) {
        if (!CollUtil.isEmpty((Collection<?>) recordlist) && recordlist.contains(v.getUniqueValue())) {
            log.info("当前时间,该人员:{}已存在记录,不同步", v.getUniqueValue());
            return false;
        }
        return true;
    }
}
