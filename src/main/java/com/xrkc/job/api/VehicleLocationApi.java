package com.xrkc.job.api;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONReader;
import com.alibaba.nacos.api.common.Constants;
import com.xrkc.core.utils.CommonUtil;
import com.xrkc.core.utils.http5.HttpResponseVO;
import com.xrkc.core.utils.http5.HttpUtils5;
import com.xrkc.job.api.DTO.VehicleCurrentRes;
import com.xrkc.job.api.DTO.VehicleHistoryRes;
import com.xrkc.job.api.DTO.VehicleInfoRes;
import com.xrkc.job.api.DTO.VehicleInfoResVO;
import com.xrkc.job.api.DTO.VehiclePositionResVO;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import net.jodah.expiringmap.ExpiringMap;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/api/VehicleLocationApi.class */
public class VehicleLocationApi {
    public static final String DEFAULT_VEHICLE_URL = "http://api.xinruikc.cn/vehicle_api";
    public static final String datasourceId = "master";
    private static final Logger log = LoggerFactory.getLogger((Class<?>) VehicleLocationApi.class);
    private static final Integer MAP_TYPE = 2;
    public static Map<String, String> VEHICLE_URL_MAP = new HashMap();
    public static Map<String, Boolean> VEHICLE_SCOPE_MAP = new HashMap();
    private static Map<String, String> VEHICLE_TOKEN_MAP = new HashMap();
    private static Map<String, Long> VEHICLE_USER_ID_MAP = new HashMap();
    private static Map<String, String> VEHICLE_USERNAME_MAP = new HashMap();
    private static Map<String, String> VEHICLE_PASSWORD_MAP = new HashMap();
    private static Map<String, Map<Long, Long>> VEHICLE_CAR_ID_MAP = ExpiringMap.builder().expiration(3, TimeUnit.DAYS).build();
    private static String getVehicleUrl() {
        String vehicleUrl = VEHICLE_URL_MAP.get("master");
        return StringUtils.isNotBlank(vehicleUrl) ? vehicleUrl : DEFAULT_VEHICLE_URL;
    }
    public static String getVehicleToken() {
        String vehicleToken = VEHICLE_TOKEN_MAP.get("master");
        return vehicleToken;
    }
    private static Long getVehicleUserId() {
        Long vehicleUserId = VEHICLE_USER_ID_MAP.get("master");
        return vehicleUserId;
    }
    public static Boolean getVehicleScope() {
        Boolean vehicleScope = VEHICLE_SCOPE_MAP.get("master");
        return vehicleScope;
    }
    private static String getVehicleUsername() {
        String username = VEHICLE_USERNAME_MAP.get("master");
        return username;
    }
    private static String getVehiclePassword() {
        String password = VEHICLE_PASSWORD_MAP.get("master");
        return password;
    }
    public static void login(String username, String password) {
        if (StringUtils.isAnyBlank(username, password)) {
            return;
        }
        String url = getVehicleUrl() + "/user/login.do";
        JSONObject params = new JSONObject();
        params.put("name", username);
        params.put("password", password);
        HttpResponseVO httpResponseVO = HttpUtils5.executeFormPOST(url, params);
        if (200 == httpResponseVO.getResponseCode() && httpResponseVO.getResponseData().getIntValue("ret") == 1) {
            JSONObject data = httpResponseVO.getResponseData().getJSONObject("data");
            String token = data.getString(Constants.TOKEN);
            Long userId = data.getLong("userId");
            VEHICLE_TOKEN_MAP.put("master", token);
            VEHICLE_USER_ID_MAP.put("master", userId);
            VEHICLE_USERNAME_MAP.put("master", username);
            VEHICLE_PASSWORD_MAP.put("master", password);
            log.info("[车载-登录]成功");
            return;
        }
        log.info("[车载-登录]请求失败：{}", httpResponseVO);
    }
    public static List<VehicleInfoResVO> queryVehicleInfo() {
        String url = getVehicleUrl() + "/car/getByUserId.do";
        JSONObject params = new JSONObject();
        params.put(Constants.TOKEN, getVehicleToken());
        params.put("userId", getVehicleUserId());
        HttpResponseVO httpResponseVO = HttpUtils5.executeFormPOST(url, params);
        if (200 == httpResponseVO.getResponseCode()) {
            JSONObject responseData = httpResponseVO.getResponseData();
            int retCode = responseData.getIntValue("ret");
            if (retCode == 1) {
                List<VehicleInfoRes> list = responseData.getJSONArray("data").toJavaList(VehicleInfoRes.class, new JSONReader.Feature[0]);
                if (!CollectionUtils.isEmpty(list)) {
                    List<VehicleInfoResVO> voList = new ArrayList<>();
                    Map<Long, Long> map = new HashMap<>();
                    list.stream().forEach(t -> {
                        VehicleInfoResVO vo = new VehicleInfoResVO();
                        vo.setCardId(Long.valueOf(Long.parseLong(t.getImei())));
                        vo.setVehicleCarId(t.getCarId());
                        map.put(vo.getVehicleCarId(), vo.getCardId());
                        voList.add(vo);
                    });
                    VEHICLE_CAR_ID_MAP.put("master", map);
                    return voList;
                }
                return null;
            }
            String code = responseData.getString("code");
            if ("-1001".equals(code) || "-1002".equals(code)) {
                log.info("[车载-信息]凭证失效，需重新生成凭证：{}", httpResponseVO);
                login(getVehicleUsername(), getVehiclePassword());
                return null;
            }
            log.info("[车载-信息]请求失败：{}", httpResponseVO);
            return null;
        }
        log.info("[车载-信息]请求错误：{}", httpResponseVO);
        return null;
    }
    public static List<VehiclePositionResVO> queryHistoryLocation(Long carId, LocalDateTime startTime, LocalDateTime endTime) {
        String url = getVehicleUrl() + "/position/queryHistory.do";
        JSONObject params = new JSONObject();
        params.put("mapType", MAP_TYPE);
        params.put(Constants.TOKEN, getVehicleToken());
        params.put("userId", getVehicleUserId());
        params.put("carId", carId);
        params.put("startTime", CommonUtil.getUtcTimeFormat(startTime));
        params.put("endTime", CommonUtil.getUtcTimeFormat(endTime));
        HttpResponseVO httpResponseVO = HttpUtils5.executeFormPOST(url, params);
        if (200 == httpResponseVO.getResponseCode()) {
            JSONObject responseData = httpResponseVO.getResponseData();
            int retCode = responseData.getIntValue("ret");
            if (retCode == 1) {
                List<VehicleHistoryRes> list = responseData.getJSONArray("data").toJavaList(VehicleHistoryRes.class, new JSONReader.Feature[0]);
                return convertHistoryPositionList(list);
            }
            String code = responseData.getString("code");
            if ("-1001".equals(code) || "-1002".equals(code)) {
                log.info("[车载-历史]凭证失效，需重新生成凭证：{}", httpResponseVO);
                login(getVehicleUsername(), getVehiclePassword());
                return null;
            }
            log.info("[车载-历史]请求失败：{}", httpResponseVO);
            return null;
        }
        log.info("[车载-历史]请求错误：{}", httpResponseVO);
        return null;
    }
    private static List<VehiclePositionResVO> convertHistoryPositionList(List<VehicleHistoryRes> reqList) {
        List<VehiclePositionResVO> list = new ArrayList<>();
        reqList.stream().forEach(t -> {
            VehiclePositionResVO history = new VehiclePositionResVO();
            history.setCardId(NumberUtil.isLong(t.getImei()) ? Long.valueOf(Long.parseLong(t.getImei())) : null);
            history.setPointType("BDS");
            history.setAcceptTime(CommonUtil.getBeiJingLocalDateTime(t.getPointDt()));
            history.setLongitude(t.getLon());
            history.setLatitude(t.getLat());
            history.setSpeed(t.getSpeed());
            history.setDirection(t.getDir());
            history.setStillStatus(Integer.valueOf(t.getIsStop().booleanValue() ? 1 : 0));
            if (StringUtils.isNotBlank(t.getExData())) {
                String powerStr = t.getExData().split(";")[0];
                if (StringUtils.isNotBlank(powerStr)) {
                    String electricityStr = powerStr.split("=")[1];
                    if (NumberUtil.isInteger(electricityStr)) {
                        history.setElectricity(Integer.valueOf(Integer.parseInt(electricityStr)));
                    }
                }
            }
            list.add(history);
        });
        return list;
    }
    private static List<VehiclePositionResVO> convertCurrentPositionList(List<VehicleCurrentRes> reqList) {
        List<VehiclePositionResVO> list = new ArrayList<>();
        Map<Long, Long> map = VEHICLE_CAR_ID_MAP.get("master");
        if (CollectionUtils.isEmpty(map)) {
            return list;
        }
        reqList.stream().forEach(t -> {
            if (t.getLon().compareTo(BigDecimal.ZERO) > 0 && t.getLat().compareTo(BigDecimal.ZERO) > 0) {
                VehiclePositionResVO history = new VehiclePositionResVO();
                Long vehicleCarId = t.getCarId();
                history.setCardId((Long) map.get(vehicleCarId));
                history.setPointType("BDS");
                boolean heartPoint = t.getHeartTime().compareTo(t.getPointTime()) > 0;
                history.setPointRemark(heartPoint ? "基于心跳保持在线" : null);
                history.setAcceptTime(heartPoint ? CommonUtil.getDateTimeOfTimestamp(t.getHeartTime().longValue()) : CommonUtil.getDateTimeOfTimestamp(t.getPointTime().longValue()));
                history.setLongitude(t.getLon());
                history.setLatitude(t.getLat());
                history.setSpeed(t.getSpeed());
                history.setDirection(t.getDir());
                int i = (Objects.isNull(t.getRun()) || t.getRun().intValue() == 0) ? 1 : 0;
                history.setStillStatus(Integer.valueOf(i));
                if (StringUtils.isNotBlank(t.getExData())) {
                    String powerStr = t.getExData().split(";")[0];
                    if (StringUtils.isNotBlank(powerStr)) {
                        String electricityStr = powerStr.split("=")[1];
                        if (NumberUtil.isInteger(electricityStr)) {
                            history.setElectricity(Integer.valueOf(Integer.parseInt(electricityStr)));
                        }
                    }
                }
                list.add(history);
            }
        });
        return list;
    }
    public static List<VehiclePositionResVO> queryCurrentLocation() {
        String url = getVehicleUrl() + "/carStatus/getByUserId.do";
        JSONObject params = new JSONObject();
        params.put("mapType", MAP_TYPE);
        params.put(Constants.TOKEN, getVehicleToken());
        params.put("targetUserId", getVehicleUserId());
        HttpResponseVO httpResponseVO = HttpUtils5.executeFormPOST(url, params);
        if (200 == httpResponseVO.getResponseCode()) {
            JSONObject responseData = httpResponseVO.getResponseData();
            if (Objects.nonNull(responseData)) {
                int retCode = responseData.getIntValue("ret");
                if (retCode == 1) {
                    List<VehicleCurrentRes> list = httpResponseVO.getResponseData().getJSONArray("data").toJavaList(VehicleCurrentRes.class, new JSONReader.Feature[0]);
                    return convertCurrentPositionList(list);
                }
                String code = responseData.getString("code");
                if ("-1001".equals(code) || "-1002".equals(code)) {
                    log.info("[车载-实时]凭证失效，需重新生成凭证：{}", httpResponseVO);
                    login(getVehicleUsername(), getVehiclePassword());
                    return null;
                }
                log.info("[车载-实时]请求失败：{}", httpResponseVO);
                return null;
            }
            log.info("[车载-实时]请求未返回数据：{}", httpResponseVO);
            return null;
        }
        log.info("[车载-实时]请求错误：{}", httpResponseVO);
        return null;
    }
    public static Long toSecondStamp(LocalDateTime localDateTime) {
        Timestamp ts = Timestamp.valueOf(localDateTime);
        Instant instant = ts.toInstant();
        long secondStamp = instant.getEpochSecond();
        return Long.valueOf(secondStamp);
    }
}
