package com.xrkc.job.util;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.Writer;
import org.apache.commons.lang3.exception.ExceptionUtils;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/ExceptionUtil.class */
public class ExceptionUtil {
    public static String getExceptionMessage(Throwable e) {
        StringWriter sw = new StringWriter();
        e.printStackTrace(new PrintWriter((Writer) sw, true));
        String str = sw.toString();
        return str;
    }
    public static String getRootErrorMessage(Exception e) {
        Throwable root = ExceptionUtils.getRootCause(e);
        Throwable root2 = root == null ? e : root;
        if (root2 == null) {
            return "";
        }
        String msg = root2.getMessage();
        if (msg == null) {
            return "null";
        }
        return StringUtils.defaultString(msg);
    }
}
