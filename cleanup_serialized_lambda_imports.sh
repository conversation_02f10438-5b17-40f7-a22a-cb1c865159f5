#!/bin/bash

# 清理无用的 SerializedLambda 导入语句

echo "开始清理无用的 SerializedLambda 导入语句..."

# 统计清理的文件数量
total_count=0

# 查找所有包含 SerializedLambda 导入的文件
files=$(find src/main/java -name "*.java" -exec grep -l "import java.lang.invoke.SerializedLambda" {} \;)

if [ -z "$files" ]; then
    echo "没有找到包含 SerializedLambda 导入的文件"
    exit 0
fi

echo "找到以下文件需要清理 SerializedLambda 导入："
echo "$files"
echo ""

# 遍历每个文件进行清理
for file in $files; do
    echo "正在清理文件: $file"
    
    # 备份原文件
    cp "$file" "$file.backup"
    
    # 删除 SerializedLambda 导入语句
    sed -i '' '/import java.lang.invoke.SerializedLambda/d' "$file"
    
    total_count=$((total_count + 1))
    echo "  - 清理完成"
done

echo ""
echo "批量清理完成！"
echo "总共清理了 $total_count 个文件中的 SerializedLambda 导入语句"
echo ""
echo "清理的文件："
for file in $files; do
    echo "  - $file"
done
echo ""
echo "注意：原文件已备份为 .backup 文件，如需恢复请手动操作" 