package com.xrkc.job.task;
import com.xrkc.job.domain.RehearsalDept;
import com.xrkc.job.domain.RehearsalPerson;
import com.xrkc.job.domain.RehearsalPlan;
import com.xrkc.job.domain.RehearsalRecord;
import com.xrkc.job.mapper.RehearsalDeptMapper;
import com.xrkc.job.service.IRehearsalPersonService;
import com.xrkc.job.service.IRehearsalPlanService;
import com.xrkc.job.service.IRehearsalRecordService;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
@Component("rehearsalTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/RehearsalPlanTask.class */
public class RehearsalPlanTask {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) RehearsalPlanTask.class);
    @Autowired
    private IRehearsalPlanService rehearsalPlanService;
    @Autowired
    private IRehearsalRecordService rehearsalRecordService;
    @Autowired
    private IRehearsalPersonService rehearsalPersonService;
    @Autowired
    private RehearsalDeptMapper rehearsalDeptMapper;
    public void updateRehearsalStatus() {
        LocalTime nowTime = LocalTime.now();
        List<RehearsalPlan> list = this.rehearsalPlanService.selectRehearsalPlanList();
        List<Long> rehearsalStatus2 = new ArrayList<>();
        List<Long> rehearsalStatus3 = new ArrayList<>();
        list.stream().forEach(e -> {
            if (!"3".equals(e.getRehearsalStatus()) && e.getValidEndTime().isBefore(nowTime)) {
                rehearsalStatus3.add(e.getRehearsalId());
            } else if (!"2".equals(e.getRehearsalStatus()) && e.getValidBeginTime().isBefore(nowTime) && e.getValidEndTime().isAfter(nowTime)) {
                rehearsalStatus2.add(e.getRehearsalId());
            }
        });
        if (!CollectionUtils.isEmpty(rehearsalStatus2)) {
            this.rehearsalPlanService.batchUpdateStatus("2", rehearsalStatus2);
        }
        if (!CollectionUtils.isEmpty(rehearsalStatus3)) {
            this.rehearsalPlanService.batchUpdateStatus("3", rehearsalStatus3);
        }
    }
    public void insertRehearsalRecord() {
        List<RehearsalRecord> list = this.rehearsalRecordService.selectUnCompletePlan();
        if (!CollectionUtils.isEmpty(list)) {
            List<RehearsalPerson> insertRehearsalPersonList = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();
            list.forEach(e -> {
                List<RehearsalDept> rehearsalDeptList = this.rehearsalDeptMapper.selectListByRehearsalId(e.getRehearsalId());
                List<Long> deptIds = (List) rehearsalDeptList.stream().map((v0) -> {
                    return v0.getDeptId();
                }).collect(Collectors.toList());
                String deptNames = (String) rehearsalDeptList.stream().map((v0) -> {
                    return v0.getDeptName();
                }).collect(Collectors.joining(","));
                e.setDeptIds(deptIds);
                e.setDeptNames(deptNames);
                e.setRehearsalStatus("4");
                e.setCreateBy("task");
                e.setCreateTime(now);
                List<Long> cardIds = this.rehearsalRecordService.findRehearsalCard(e);
                List<RehearsalPerson> rehearsalPersonList = this.rehearsalPersonService.findRehearsalPersonList(deptIds);
                e.setPersonCount(Integer.valueOf(rehearsalPersonList.size()));
                e.setActualCount(Integer.valueOf(cardIds.size()));
                rehearsalPersonList.forEach(p -> {
                    p.setRehearsalId(e.getRehearsalId());
                    p.setPositionStatus(cardIds.contains(p.getCardId()) ? "Y" : "N");
                    insertRehearsalPersonList.add(p);
                });
            });
            this.rehearsalRecordService.batchInsert(list);
            this.rehearsalPersonService.batchInsert(insertRehearsalPersonList);
            this.rehearsalPlanService.batchUpdateStatus("4", (List) list.stream().map((v0) -> {
                return v0.getRehearsalId();
            }).collect(Collectors.toList()));
        }
    }
    public void updateRehearsalPerson() {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("rehearsalStatus", "4");
        queryParams.put("toDay", "true");
        queryParams.put("flag", "Y");
        List<RehearsalRecord> list = this.rehearsalRecordService.selectList(queryParams);
        if (!CollectionUtils.isEmpty(list)) {
            List<RehearsalRecord> updateList = new ArrayList<>();
            List<Long> completeIds = new ArrayList<>();
            list.stream().forEach(e -> {
                List<Long> deptIds = (List) this.rehearsalDeptMapper.selectListByRehearsalId(e.getRehearsalId()).stream().map((v0) -> {
                    return v0.getDeptId();
                }).collect(Collectors.toList());
                e.setDeptIds(deptIds);
                List<Long> cardIds = this.rehearsalRecordService.findRehearsalCard(e);
                e.setActualCount(Integer.valueOf(cardIds.size()));
                List<RehearsalPerson> rehearsalPersonList = this.rehearsalPersonService.findUnCompletePersonByRehearsalId(e.getRehearsalId());
                rehearsalPersonList.stream().filter(f -> {
                    return cardIds.contains(f.getCardId());
                }).forEach(p -> {
                    completeIds.add(p.getId());
                });
                updateList.add(e);
            });
            if (!CollectionUtils.isEmpty(updateList)) {
                this.rehearsalRecordService.batchUpdate(updateList);
            }
            this.rehearsalPersonService.batchCompleteUpdate(completeIds);
        }
    }
}
