package com.xrkc.job.task;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.system.oshi.OshiUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.domain.alarm.CoreAlarmPeople;
import com.xrkc.core.domain.cardDispenser.CardDispenserPerson;
import com.xrkc.core.domain.person.Person;
import com.xrkc.core.domain.rail.RailDrawType;
import com.xrkc.core.domain.system.SystemDept;
import com.xrkc.core.enums.AlarmType;
import com.xrkc.core.utils.RsaUtils;
import com.xrkc.datascope.aspect.DataScopeAspect;
import com.xrkc.job.domain.Alarm;
import com.xrkc.job.domain.AlarmAreaTempVO;
import com.xrkc.job.domain.AreaAlarmTempVO;
import com.xrkc.job.domain.CoreAlarmLamps;
import com.xrkc.job.domain.DeviceBuilding;
import com.xrkc.job.domain.DeviceCardSenderLog;
import com.xrkc.job.domain.DeviceLamps;
import com.xrkc.job.domain.GatherAlarmVo;
import com.xrkc.job.domain.PositionCurrent;
import com.xrkc.job.domain.SystemDict;
import com.xrkc.job.service.DeviceBuildingService;
import com.xrkc.job.service.IAlarmPeopleService;
import com.xrkc.job.service.IAlarmService;
import com.xrkc.job.service.IAreaService;
import com.xrkc.job.service.ICoreAlarmLampsService;
import com.xrkc.job.service.IDeviceCardSenderLogService;
import com.xrkc.job.service.IDeviceCardSenderPersonService;
import com.xrkc.job.service.IDeviceLampsService;
import com.xrkc.job.service.IGateRecordService;
import com.xrkc.job.service.IPersonService;
import com.xrkc.job.service.IPositionCurrentService;
import com.xrkc.job.service.ISystemDeptService;
import com.xrkc.job.service.ISystemDictService;
import com.xrkc.job.util.CommonUtil;
import com.xrkc.redis.service.RedisService;
import com.xrkc.redis.utils.RedisCacheUtils;
import dm.jdbc.util.StringUtil;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.SQLException;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.chrono.ChronoLocalDate;
import java.time.chrono.ChronoLocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.sql.DataSource;
import net.jodah.expiringmap.ExpiringMap;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
@Component("alarmTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/AlarmTask.class */
public class AlarmTask {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) AlarmTask.class);
    @Autowired
    private IAreaService areaService;
    @Autowired
    private IAlarmService alarmService;
    @Autowired
    private ISystemDictService systemDictService;
    @Autowired
    private ICoreAlarmLampsService alarmLampsService;
    @Autowired
    private IDeviceLampsService deviceLampsService;
    @Autowired
    private IDeviceCardSenderLogService deviceCardSenderLogService;
    @Autowired
    private IDeviceCardSenderPersonService deviceCardSenderPersonService;
    @Autowired
    private IPositionCurrentService positionCurrentService;
    @Autowired
    private IPersonService personService;
    @Autowired
    private DeviceBuildingService deviceBuildingService;
    @Autowired
    private IAlarmPeopleService alarmPeopleService;
    @Autowired
    private DataSource dataSource;
    @Autowired
    private ISystemDeptService systemDeptService;
    @Autowired
    private IGateRecordService gateRecordService;
    @Autowired
    private RedisService redisService;
    private static final String ALARM_TYPE_10 = "10";
    private static final String ALARM_TYPE_20 = "20";
    private static final String ALARM_TYPE_30 = "30";
    private static final String ALARM_TYPE_40 = "40";
    private static final String ALARM_TYPE_50 = "50";
    private static final String ALARM_TYPE_70 = "70";
    private static final String ALARM_TYPE_90 = "90";
    private static final String ALARM_TYPE_90_NUMBER = "003";
    private static final String ALARM_TYPE_96 = "96";
    private static final String ALARM_TYPE_3 = "3";
    private static final String ALARM_TYPE_3_NUMBER = "002";
    public static final String ALARM_TYPE_81 = "81";
    public static final String ALARM_TYPE_82 = "82";
    private static final String ALARM_TYPE_81_NUMBER = "004";
    private Map<Long, Map<String, LocalDateTime>> areaAggregationMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    private Map<String, LocalDateTime> areaAggregationAlarmMap = ExpiringMap.builder().expiration(6, TimeUnit.MINUTES).build();
    private Map<String, Set<Long>> areaAlarmPeopleMap = ExpiringMap.builder().expiration(2, TimeUnit.DAYS).build();
    private Map<Long, List<PositionCurrent>> areaLackAlarmPeopleMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    private Map<String, Map<Long, LocalDateTime>> overManMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    Map<Long, List<GatherAlarmVo>> gatherAlarmMap = ExpiringMap.builder().expiration(2, TimeUnit.DAYS).build();
    private Map<Long, Alarm> railByAlarm50Map = new HashMap();
    private Map<String, Set<Long>> timeoutNotReturnCardMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    public void calcPersonAlarm_10() {
        calcPersonAlarm_10_20_40_50("10");
    }
    public void calcPersonAlarm_20() {
        calcPersonAlarm_10_20_40_50("20");
    }
    public void calcPersonAlarm_30() {
        calcPersonAlarm_30_2023("30");
    }
    public void calcPersonAlarm_40() {
        calcPersonAlarm_10_20_40_50("40");
    }
    public void calcPersonAlarm_50() {
        String keep_alarm50_second = RedisCacheUtils.getConfigRedisCache().get(ConfigValue.CONFIG_KEY_KEEP_ALARM50_SECOND);
        if (StringUtils.isNotBlank(keep_alarm50_second) && Integer.parseInt(keep_alarm50_second) > 0) {
            keepAlarm50("50", Integer.parseInt(keep_alarm50_second));
        } else {
            calcPersonAlarm_10_20_40_50("50");
        }
    }
    public void calcPersonAlarm_70() {
        calcPersonAlarm_70("70");
    }
    public void calcPersonAlarm_90() throws NumberFormatException {
        calcPersonAlarm_90("90");
    }
    public void calcPersonAlarm_96() throws SQLException {
        calcPersonAlarm_96("96");
    }
    public void calcCardLowPowerAlarm() throws NumberFormatException {
        String v = RedisCacheUtils.getConfigRedisCache().getOrDefault("card_low_power", "20");
        int cardPower = Integer.parseInt(v);
        List<CoreAlarmPeople> lowPowerList = this.alarmPeopleService.selectPersonLowPowerCardList(cardPower);
        if (CollectionUtils.isEmpty(lowPowerList)) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        Alarm alarm = new Alarm();
        alarm.setAcceptTime(now);
        alarm.setAlarmType("3");
        alarm.setAlarmTypeName("低电报警");
        alarm.setAlarmName("人员定位卡低电提醒，请及时充电");
        StringBuilder descBuilder = new StringBuilder("低电卡片").append(lowPowerList.size()).append("张，人员：");
        lowPowerList.forEach(card -> {
            descBuilder.append(card.getCardId()).append("-").append(card.getRealName()).append(";");
        });
        String alarmDesc = descBuilder.toString();
        if (alarmDesc.length() > 498) {
            alarmDesc = alarmDesc.substring(0, 498) + StringUtil.INVISIBLE_USERNAME;
        }
        alarm.setAlarmDesc(alarmDesc);
        alarm.setCreateBy("task");
        int r = this.alarmService.insertAlarm(alarm);
        if (r > 0) {
            lowPowerList.forEach(f -> {
                f.setCoreAlarmId(alarm.getAlarmId());
                f.setAcceptTime(now);
                f.setCreateTime(now);
                f.setCreateBy("task");
            });
            this.alarmPeopleService.batchInsert(lowPowerList);
        }
    }
    private List<PositionCurrent> stopAlarmByStillStatus(List<Long> positionIds, List<PositionCurrent> positionData, Long rule) {
        List<PositionCurrent> alarmPersonList = new ArrayList<>();
        positionData.stream().collect(Collectors.groupingBy((v0) -> {
            return v0.getCardId();
        })).forEach((calcCardId, calcList) -> {
            if (calcList.size() < 2) {
                return;
            }
            long stopSecond = 0;
            LocalDateTime nextTime = null;
            for (int i = 0; i < calcList.size(); i++) {
                PositionCurrent t = (PositionCurrent) calcList.get(i);
                Integer stillStatus = t.getStillStatus();
                if (i == 0) {
                    t.getAcceptTime();
                    nextTime = t.getAcceptTime();
                } else {
                    LocalDateTime prefTime = nextTime;
                    nextTime = t.getAcceptTime();
                    if (Objects.isNull(stillStatus) || stillStatus.intValue() == 0 || !positionIds.contains(t.getId())) {
                        stopSecond = 0;
                    } else {
                        long interval = Duration.between(nextTime, prefTime).toMillis() / 1000;
                        stopSecond += interval;
                    }
                    if (stopSecond > rule.longValue()) {
                        alarmPersonList.add(t);
                        return;
                    }
                }
            }
        });
        return alarmPersonList;
    }
    private List<PositionCurrent> stopAlarmByPoint(List<Long> positionIds, List<PositionCurrent> positionData, Long rule) {
        List<PositionCurrent> alarmPersonList = new ArrayList<>();
        positionData.stream().collect(Collectors.groupingBy((v0) -> {
            return v0.getCardId();
        })).forEach((calcCardId, calcList) -> {
            if (calcList.size() < 2) {
                return;
            }
            long stopSecond = 0;
            LocalDateTime nextTime = null;
            BigDecimal prefLongitude = null;
            BigDecimal prefLatitude = null;
            for (int i = 0; i < calcList.size(); i++) {
                PositionCurrent t = (PositionCurrent) calcList.get(i);
                if (i == 0) {
                    t.getAcceptTime();
                    nextTime = t.getAcceptTime();
                    prefLongitude = t.getLongitude();
                    prefLatitude = t.getLatitude();
                    stopSecond = -1;
                } else {
                    LocalDateTime prefTime = nextTime;
                    nextTime = t.getAcceptTime();
                    if (stopSecond == -1) {
                        stopSecond = 0;
                    } else {
                        long interval = Duration.between(nextTime, prefTime).toMillis() / 1000;
                        stopSecond += interval;
                    }
                    if (!positionIds.contains(t.getId()) || (prefLongitude.compareTo(t.getLongitude()) != 0 && prefLatitude.compareTo(t.getLatitude()) != 0)) {
                        stopSecond = 0;
                    }
                    if (stopSecond > rule.longValue()) {
                        alarmPersonList.add(t);
                        return;
                    } else {
                        prefLongitude = t.getLongitude();
                        prefLatitude = t.getLatitude();
                    }
                }
            }
        });
        return alarmPersonList;
    }
    private void calcPersonAlarm_70(String alarmType) {
        if (!"70".equals(alarmType)) {
            return;
        }
        List<AreaAlarmTempVO> tempVOList = this.areaService.selectAreaAlarmByAlarmType(alarmType);
        if (CollectionUtils.isEmpty(tempVOList)) {
            return;
        }
        LocalDate nowDate = LocalDate.now();
        tempVOList.removeIf(f -> {
            return removeInvalid(f, nowDate);
        });
        if (CollectionUtils.isEmpty(tempVOList)) {
            return;
        }
        Set<Long> closeAreaAlarm = new HashSet<>();
        Map<Long, Set<Long>> leaveCurrentPesonSets = new HashMap<>();
        Map<String, String> configMap = RedisCacheUtils.getConfigRedisCache();
        String CALC_STOP_ALARM_TYPE = configMap.get(ConfigValue.CONFIG_KEY_CALC_STOP_ALARM_TYPE);
        String CALC_STOP_ALARM_SECOND = configMap.getOrDefault(ConfigValue.CONFIG_KEY_CALC_STOP_ALARM_SECOND, "60");
        Long alarmAddSecond = Long.valueOf(NumberUtil.isLong(CALC_STOP_ALARM_SECOND) ? Long.parseLong(CALC_STOP_ALARM_SECOND) : 60L);
        Map<Long, List<AreaAlarmTempVO>> areaVoMap = (Map) tempVOList.stream().collect(Collectors.groupingBy((v0) -> {
            return v0.getAreaId();
        }));
        areaVoMap.forEach((areaId, areaVoList) -> {
            AreaAlarmTempVO areaVO = (AreaAlarmTempVO) tempVOList.stream().filter(f2 -> {
                return f2.getAreaId().compareTo(areaId) == 0;
            }).findFirst().get();
            if (!NumberUtil.isLong(areaVO.getRule())) {
                return;
            }
            Long durationSecond = Long.valueOf(Long.parseLong(areaVO.getRule()));
            Long ruleDurationSecond = Long.valueOf(durationSecond.longValue() + alarmAddSecond.longValue());
            areaVO.setRailDrawList((List) areaVoList.stream().map(s -> {
                return RailDrawType.builder().drawType(s.getDrawType()).railScope(s.getRailScope()).layerId(s.getLayerId()).build();
            }).collect(Collectors.toList()));
            areaVO.setLayerIds((List) areaVoList.stream().map((v0) -> {
                return v0.getLayerId();
            }).collect(Collectors.toList()));
            areaVO.setDeptIds(this.areaService.findDeptIdsByAreaId(areaId));
            areaVO.setPersonIds(this.areaService.findPersonIdsByAreaId(areaId));
            areaVO.setAlarmPersonIds(this.areaService.findAlarmPersonIdsByAreaId(areaId));
            areaVO.setPostIds(this.areaService.findPostIdsByAreaId(areaId));
            List<PositionCurrent> calcList = this.positionCurrentService.selectStopAlarmPerson(areaVO, ruleDurationSecond);
            if (calcList.size() < 2) {
                return;
            }
            List<Long> positionIds = this.positionCurrentService.selectStopAlarmPositionId(areaVO, ruleDurationSecond);
            if (positionIds.size() < 2) {
                return;
            }
            String key = alarmType + "_" + areaId;
            List<PositionCurrent> alarmPersonList = "2".equals(CALC_STOP_ALARM_TYPE) ? stopAlarmByPoint(positionIds, calcList, durationSecond) : stopAlarmByStillStatus(positionIds, calcList, durationSecond);
            if (CollectionUtils.isEmpty(alarmPersonList)) {
                Set<Long> lastAreaCurrentPerson = this.areaAlarmPeopleMap.get(key);
                if (!CollectionUtils.isEmpty(lastAreaCurrentPerson)) {
                    leaveCurrentPesonSets.put(areaId, lastAreaCurrentPerson);
                    this.areaAlarmPeopleMap.remove(key);
                    return;
                }
                return;
            }
            Set<Long> lastAreaCurrentPeson = this.areaAlarmPeopleMap.get(key);
            HashSet hashSet = new HashSet();
            if (CollUtil.isNotEmpty((Collection<?>) lastAreaCurrentPeson)) {
                hashSet.addAll(lastAreaCurrentPeson);
            }
            Set<Long> currentPersonSets = (Set) alarmPersonList.stream().map((v0) -> {
                return v0.getPersonId();
            }).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty((Collection<?>) hashSet)) {
                if (!CollUtil.isEmpty((Collection<?>) currentPersonSets)) {
                    hashSet.removeAll(currentPersonSets);
                }
                leaveCurrentPesonSets.put(areaId, hashSet);
            }
            this.areaAlarmPeopleMap.put(key, currentPersonSets);
            AlarmAreaTempVO alarmAreaTempVO = new AlarmAreaTempVO();
            alarmAreaTempVO.setAreaId(areaId);
            alarmAreaTempVO.setAlarmPersonCount(Integer.valueOf(alarmPersonList.size()));
            alarmAreaTempVO.setAlarmType(alarmType);
            alarmAreaTempVO.setAreaVO(areaVO);
            closeAreaAlarm.add(areaId);
            Map<String, Object> map = new HashMap<>();
            map.put("alarmType", alarmType);
            map.put("areaId", areaId);
            alarmPersonList.stream().forEach(positionCurrent -> {
                map.put("cardId", positionCurrent.getCardId());
                if (!this.alarmService.existAlarmByParams(map)) {
                    insertPersonAlarm(alarmAreaTempVO, positionCurrent, areaVoList);
                }
            });
        });
        this.alarmService.closeAlarmEndTime(alarmType, leaveCurrentPesonSets, closeAreaAlarm, null);
    }
    private void calcPersonAlarm_90(String alarmType) throws NumberFormatException {
        if (!"90".equals(alarmType)) {
            return;
        }
        List<AreaAlarmTempVO> tempVOList = this.areaService.selectAreaAlarmByAlarmType(alarmType);
        if (CollectionUtils.isEmpty(tempVOList)) {
            return;
        }
        LocalDate nowDate = LocalDate.now();
        tempVOList.removeIf(f -> {
            return removeInvalid(f, nowDate);
        });
        if (CollectionUtils.isEmpty(tempVOList)) {
            return;
        }
        Map<String, String> configMap = RedisCacheUtils.getConfigRedisCache();
        Double validDifference = Double.valueOf(configMap.getOrDefault(ConfigValue.CONFIG_KEY_ONE_PERSON_MULTIPLE_CARD_ALARM_DISTANCE, "10"));
        Map<Long, List<AreaAlarmTempVO>> areaVoMap = (Map) tempVOList.stream().collect(Collectors.groupingBy((v0) -> {
            return v0.getAreaId();
        }));
        Set<Long> currentAreaId = new HashSet<>();
        areaVoMap.forEach((areaId, areaVoList) -> {
            String key;
            AreaAlarmTempVO areaVO = (AreaAlarmTempVO) tempVOList.stream().filter(f2 -> {
                return f2.getAreaId().compareTo(areaId) == 0;
            }).findFirst().get();
            areaVO.setRailDrawList((List) areaVoList.stream().map(s -> {
                return RailDrawType.builder().drawType(s.getDrawType()).railScope(s.getRailScope()).layerId(s.getLayerId()).build();
            }).collect(Collectors.toList()));
            areaVO.setLayerIds((List) areaVoList.stream().map((v0) -> {
                return v0.getLayerId();
            }).collect(Collectors.toList()));
            areaVO.setDeptIds(this.areaService.findDeptIdsByAreaId(areaId));
            areaVO.setPersonIds(this.areaService.findPersonIdsByAreaId(areaId));
            areaVO.setAlarmPersonIds(this.areaService.findAlarmPersonIdsByAreaId(areaId));
            areaVO.setPostIds(this.areaService.findPostIdsByAreaId(areaId));
            List<PositionCurrent> positionCurrentList = this.positionCurrentService.selectAlarmPerson(areaVO);
            Map<String, LocalDateTime> aggregationMap = this.areaAggregationMap.get(areaId);
            if (aggregationMap == null) {
                aggregationMap = ExpiringMap.builder().expiration(1L, TimeUnit.DAYS).build();
                this.areaAggregationMap.put(areaId, aggregationMap);
            }
            Set<Long> currentPersonIdSet = (Set) positionCurrentList.stream().map((v0) -> {
                return v0.getPersonId();
            }).collect(Collectors.toSet());
            aggregationMap.keySet().removeIf(key2 -> {
                String[] items = key2.split("-");
                return (currentPersonIdSet.contains(Long.valueOf(items[0])) && currentPersonIdSet.contains(Long.valueOf(items[1]))) ? false : true;
            });
            for (int i = 0; i < positionCurrentList.size(); i++) {
                PositionCurrent prevPositionCurrent = positionCurrentList.get(i);
                for (int j = 0; j < positionCurrentList.size(); j++) {
                    if (i != j) {
                        PositionCurrent positionCurrent = positionCurrentList.get(j);
                        Double difference = Double.valueOf(CommonUtil.getDistance(prevPositionCurrent.getLongitude().doubleValue(), prevPositionCurrent.getLatitude().doubleValue(), positionCurrent.getLongitude().doubleValue(), positionCurrent.getLatitude().doubleValue()));
                        if (prevPositionCurrent.getPersonId().longValue() > positionCurrent.getPersonId().longValue()) {
                            key = StrUtil.concat(true, String.valueOf(prevPositionCurrent.getPersonId()), "-", String.valueOf(positionCurrent.getPersonId()));
                        } else {
                            key = StrUtil.concat(true, String.valueOf(positionCurrent.getPersonId()), "-", String.valueOf(prevPositionCurrent.getPersonId()));
                        }
                        if (difference.doubleValue() < validDifference.doubleValue()) {
                            if (!aggregationMap.containsKey(key)) {
                                aggregationMap.put(key, LocalDateTime.now());
                            }
                        } else {
                            aggregationMap.remove(key);
                        }
                    }
                }
            }
            Map<String, Set<String>> aggregationPerson = new HashMap<>();
            Map<String, LocalDateTime> finalAggregationMap = aggregationMap;
            int durationSecond = Integer.parseInt(areaVO.getRule());
            if (Objects.nonNull(aggregationMap) && aggregationMap.values().size() > 0) {
                currentAreaId.add(areaVO.getAreaId());
            }
            MapUtil.filter(aggregationMap, entry -> {
                return ((LocalDateTime) entry.getValue()).isBefore(LocalDateTime.now().minusSeconds(durationSecond));
            }).keySet().stream().sorted().forEach(key3 -> {
                finalAggregationMap.remove(key3);
                String[] items = key3.split("-");
                if (aggregationPerson.containsKey(items[0])) {
                    ((Set) aggregationPerson.get(items[0])).add(items[1]);
                    return;
                }
                if (aggregationPerson.containsKey(items[1])) {
                    ((Set) aggregationPerson.get(items[1])).add(items[0]);
                    return;
                }
                AtomicBoolean isExist = new AtomicBoolean(false);
                aggregationPerson.values().forEach(pIdSet -> {
                    if (pIdSet.contains(items[0]) || pIdSet.contains(items[1])) {
                        isExist.set(true);
                    }
                });
                if (!isExist.get()) {
                    aggregationPerson.put(items[0], new HashSet());
                    ((Set) aggregationPerson.get(items[0])).add(items[0]);
                    ((Set) aggregationPerson.get(items[0])).add(items[1]);
                }
            });
            aggregationPerson.values().forEach(pIdSet -> {
                StringBuilder descBuilder = new StringBuilder();
                Iterator it = pIdSet.iterator();
                while (it.hasNext()) {
                    if (this.areaAggregationAlarmMap.containsKey((String) it.next())) {
                        return;
                    }
                }
                Iterator it2 = pIdSet.iterator();
                while (it2.hasNext()) {
                    String pId = (String) it2.next();
                    this.areaAggregationAlarmMap.put(pId, null);
                    Person person = this.personService.getPersonById(Long.valueOf(pId));
                    descBuilder.append(StrUtil.concat(true, person.getRealName(), "-", String.valueOf(person.getCardId()), ";"));
                }
                PositionCurrent alarmPositon = new PositionCurrent();
                Long firstPId = Long.valueOf((String) pIdSet.iterator().next());
                Iterator it3 = positionCurrentList.iterator();
                while (true) {
                    if (!it3.hasNext()) {
                        break;
                    }
                    PositionCurrent positionCurrent2 = (PositionCurrent) it3.next();
                    if (positionCurrent2.getPersonId().equals(firstPId)) {
                        alarmPositon = positionCurrent2;
                        break;
                    }
                }
                Alarm alarm = new Alarm();
                alarm.setAcceptTime(LocalDateTime.now());
                alarm.setAlarmType("90");
                alarm.setAreaId(areaVO.getAreaId());
                alarm.setAreaName(areaVO.getAreaName());
                alarm.setLongitude(alarmPositon.getLongitude());
                alarm.setLatitude(alarmPositon.getLatitude());
                alarm.setLayerId(alarmPositon.getLayerId());
                alarm.setLayerName(alarmPositon.getLayerName());
                alarm.setLayerHeight(alarmPositon.getLayerHeight());
                alarm.setAlarmTypeName("一人多卡报警");
                alarm.setAlarmName("一人多卡报警");
                alarm.setBuildingName(areaVO.getBuildingName());
                alarm.setBuildingId(areaVO.getBuildingId());
                String alarmDesc = descBuilder.toString();
                if (alarmDesc.length() > 498) {
                    alarmDesc = alarmDesc.substring(0, 498) + StringUtil.INVISIBLE_USERNAME;
                }
                alarm.setAlarmDesc(alarmDesc);
                alarm.setCreateBy("task");
                this.alarmService.insertAlarm(alarm);
            });
        });
        Set<Long> lastCurrentAreaId = this.areaAlarmPeopleMap.get("90_003");
        this.areaAlarmPeopleMap.put("90_003", currentAreaId);
        if (Objects.nonNull(lastCurrentAreaId) && !SetUtils.isEqualSet(lastCurrentAreaId, currentAreaId)) {
            this.alarmService.closeAlarmEndTime("90", null, currentAreaId, null);
        }
    }
    private void calcPersonAlarm_96(String alarmType) throws SQLException {
        Object obj = null;
        Connection connection = this.dataSource.getConnection();
        try {
            String originAuthInfo = StrUtil.concat(true, OshiUtil.getHardware().getProcessor().getProcessorIdentifier().getProcessorID(), connection.getCatalog());
            Map<String, String> config = RedisCacheUtils.getConfigRedisCache();
            String gatherAuth = config.getOrDefault(ConfigValue.CONFIG_KEY_GATHER_ALARM_AUTH, "");
            if (StrUtil.isEmpty(gatherAuth)) {
                log.info("[聚集报警]未配置授权，服务器授权信息为：{}", originAuthInfo);
                if (Collections.singletonList(connection).get(0) != null) {
                    connection.close();
                    return;
                }
                return;
            }
            String gatherAuthInfo = RsaUtils.privateKeyDecrypt(gatherAuth);
            if (!originAuthInfo.equals(gatherAuthInfo)) {
                log.error("[聚集报警]授权验证不通过,服务器授权信息为：{}， 识别的授权信息为:{}", originAuthInfo, gatherAuthInfo);
                if (Collections.singletonList(connection).get(0) != null) {
                    connection.close();
                    return;
                }
                return;
            }
            if (this.gatherAlarmMap.size() == 0) {
                handleDbUnendGatherAlarm();
            }
            if (!"96".equals(alarmType)) {
                if (obj != null) {
                    return;
                } else {
                    return;
                }
            }
            List<AreaAlarmTempVO> tempVOList = this.areaService.selectAreaAlarmByAlarmType(alarmType);
            if (CollectionUtils.isEmpty(tempVOList)) {
                if (Collections.singletonList(connection).get(0) != null) {
                    connection.close();
                    return;
                }
                return;
            }
            LocalDate nowDate = LocalDate.now();
            tempVOList.removeIf(f -> {
                return removeInvalid(f, nowDate);
            });
            if (CollectionUtils.isEmpty(tempVOList)) {
                if (Collections.singletonList(connection).get(0) != null) {
                    connection.close();
                    return;
                }
                return;
            }
            List<DeviceBuilding> buildingList = this.deviceBuildingService.lambdaQuery().list();
            int repeatALarmMinute = Integer.parseInt(RedisCacheUtils.getConfigRedisCache().getOrDefault("alarm_monitor_interval_96", DataScopeAspect.DATA_SCOPE_CUSTOM_AND_CHILD));
            int basicDurationMinute = Integer.parseInt(RedisCacheUtils.getConfigRedisCache().getOrDefault(ConfigValue.CONFIG_KEY_GATHER_ALARM_BASIC_DURATION, "0"));
            Map<Long, List<AreaAlarmTempVO>> areaVoMap = (Map) tempVOList.stream().collect(Collectors.groupingBy((v0) -> {
                return v0.getAreaId();
            }));
            areaVoMap.forEach((areaId, areaVoList) -> {
                AtomicBoolean isMerge;
                AreaAlarmTempVO areaVO = (AreaAlarmTempVO) areaVoList.get(0);
                String[] firstRuleItems = areaVO.getRule().split(",");
                String[] secondRuleItems = areaVO.getSecondAlarmRule().split(",");
                String[] threeRuleItems = areaVO.getThirdAlarmRule().split(",");
                double validDifference = Double.parseDouble(firstRuleItems[0]);
                long durationSecond = Long.parseLong(firstRuleItems[1]) + (basicDurationMinute * 60);
                double firstAlarmPeopleCount = Double.parseDouble(firstRuleItems[2]);
                double secondAlarmPeopleCount = Double.parseDouble(secondRuleItems[2]);
                double threeAlarmPeopleCount = Double.parseDouble(threeRuleItems[2]);
                areaVO.setLayerIds((List) areaVoList.stream().filter(alarm -> {
                    return "1".equals(alarm.getAreaType());
                }).map((v0) -> {
                    return v0.getLayerId();
                }).filter(layerId -> {
                    return layerId != null;
                }).collect(Collectors.toList()));
                areaVO.setDeptIds(this.areaService.findDeptIdsByAreaId(areaId));
                areaVO.setPersonIds(this.areaService.findPersonIdsByAreaId(areaId));
                areaVO.setAlarmPersonIds(this.areaService.findAlarmPersonIdsByAreaId(areaId));
                areaVO.setPostIds(this.areaService.findPostIdsByAreaId(areaId));
                List<PositionCurrent> positionCurrentList = this.positionCurrentService.selectAlarmPerson(areaVO);
                List<AreaAlarmTempVO> enableAreaList = (List) areaVoList.stream().filter(alarm2 -> {
                    return "1".equals(alarm2.getAreaType());
                }).collect(Collectors.toList());
                List<AreaAlarmTempVO> disableAreaList = (List) areaVoList.stream().filter(alarm3 -> {
                    return "2".equals(alarm3.getAreaType());
                }).collect(Collectors.toList());
                positionCurrentList.removeIf(positionCurrent -> {
                    Iterator it = disableAreaList.iterator();
                    while (it.hasNext()) {
                        AreaAlarmTempVO area = (AreaAlarmTempVO) it.next();
                        if (area.getLayerId().equals(positionCurrent.getLayerId()) && CommonUtil.containsRailScope(positionCurrent.getLongitude(), positionCurrent.getLatitude(), area.getRailScope(), area.getDrawType())) {
                            return true;
                        }
                    }
                    if (enableAreaList.size() > 0) {
                        Iterator it2 = enableAreaList.iterator();
                        while (it2.hasNext()) {
                            AreaAlarmTempVO area2 = (AreaAlarmTempVO) it2.next();
                            if (area2.getLayerId().equals(positionCurrent.getLayerId()) && CommonUtil.containsRailScope(positionCurrent.getLongitude(), positionCurrent.getLatitude(), area2.getRailScope(), area2.getDrawType())) {
                                return false;
                            }
                        }
                        return true;
                    }
                    return false;
                });
                Map<Long, PositionCurrent> personPositionMap = (Map) positionCurrentList.stream().collect(Collectors.toMap((v0) -> {
                    return v0.getPersonId();
                }, Function.identity()));
                log.info("【聚集报警】 {} 定位人数为:{}", areaId, Integer.valueOf(personPositionMap.size()));
                Map<Long, Set<Long>> gatherPersonMap = new HashMap<>();
                for (int i = 0; i < positionCurrentList.size(); i++) {
                    PositionCurrent prevPositionCurrent = positionCurrentList.get(i);
                    Set<Long> gatherPersonSet = new HashSet<>();
                    gatherPersonSet.add(prevPositionCurrent.getPersonId());
                    for (int j = 0; j < positionCurrentList.size(); j++) {
                        PositionCurrent positionCurrent2 = positionCurrentList.get(j);
                        if (i != j && prevPositionCurrent.getLayerId().equals(positionCurrent2.getLayerId())) {
                            double difference = CommonUtil.getDistance(prevPositionCurrent.getLongitude().doubleValue(), prevPositionCurrent.getLatitude().doubleValue(), positionCurrent2.getLongitude().doubleValue(), positionCurrent2.getLatitude().doubleValue());
                            log.info("【聚集报警】{},{} 与 {} 距离为:{},半径为：{}", areaId, prevPositionCurrent.getPersonId(), positionCurrent2.getPersonId(), Double.valueOf(difference), Double.valueOf(validDifference));
                            if (difference <= validDifference) {
                                gatherPersonSet.add(positionCurrent2.getPersonId());
                            }
                        }
                    }
                    log.info("【聚集报警】{},{} 聚集人员数量为：{}，报警阈值为：{}", areaId, prevPositionCurrent.getPersonId(), Integer.valueOf(gatherPersonSet.size()), Double.valueOf(threeAlarmPeopleCount));
                    if (gatherPersonSet.size() >= threeAlarmPeopleCount) {
                        gatherPersonMap.put(prevPositionCurrent.getPersonId(), gatherPersonSet);
                    }
                }
                log.info("【聚集报警】 {} 最新一次聚集情况:{}", areaId, JSONObject.toJSONString(gatherPersonMap, new JSONWriter.Feature[0]));
                do {
                    Set<Long> deletePerson = new HashSet<>();
                    isMerge = new AtomicBoolean(false);
                    Long[] keyArr = (Long[]) gatherPersonMap.keySet().toArray(new Long[0]);
                    for (int i2 = 0; i2 < keyArr.length; i2++) {
                        Long outerPersonId = keyArr[i2];
                        Set<Long> outerPersonIdSet = gatherPersonMap.get(keyArr[i2]);
                        for (int j2 = i2 + 1; j2 < keyArr.length; j2++) {
                            long innerPersonId = keyArr[j2].longValue();
                            if (!deletePerson.contains(outerPersonId) && !deletePerson.contains(Long.valueOf(innerPersonId))) {
                                Set<Long> innerPersonSet = gatherPersonMap.get(keyArr[j2]);
                                if (outerPersonIdSet.contains(Long.valueOf(innerPersonId))) {
                                    deletePerson.add(Long.valueOf(innerPersonId));
                                    outerPersonIdSet.addAll(innerPersonSet);
                                    isMerge.set(true);
                                }
                            }
                        }
                    }
                    gatherPersonMap.getClass();
                    deletePerson.forEach((v1) -> {
                        r1.remove(v1);
                    });
                } while (isMerge.get());
                log.info("【聚集报警】 {} 最新一次聚集结果:{}", areaId, JSONObject.toJSONString(gatherPersonMap, new JSONWriter.Feature[0]));
                if (!this.gatherAlarmMap.containsKey(areaId)) {
                    this.gatherAlarmMap.put(areaId, new LinkedList());
                }
                List<GatherAlarmVo> gatherAlarmVoList = this.gatherAlarmMap.get(areaId);
                log.info("【聚集报警】 {} 历史聚集统计情况:{}", areaId, JSONObject.toJSONString(gatherAlarmVoList, new JSONWriter.Feature[0]));
                gatherAlarmVoList.forEach(gatherAlarmVo -> {
                    gatherAlarmVo.setGatherEndTime(LocalDateTime.now());
                });
                gatherPersonMap.values().forEach(personIdSet -> {
                    GatherAlarmVo finalAlarm;
                    double longitudeSum = 0.0d;
                    double latitudeSum = 0.0d;
                    Iterator it = personIdSet.iterator();
                    while (it.hasNext()) {
                        PositionCurrent positionCurrent3 = (PositionCurrent) personPositionMap.get((Long) it.next());
                        longitudeSum += positionCurrent3.getLongitude().doubleValue();
                        latitudeSum += positionCurrent3.getLatitude().doubleValue();
                    }
                    double centerLongitude = longitudeSum / personIdSet.size();
                    double centerLatitude = latitudeSum / personIdSet.size();
                    double radius = 5.0d;
                    PositionCurrent positionCurrent4 = null;
                    Iterator it2 = personIdSet.iterator();
                    while (it2.hasNext()) {
                        positionCurrent4 = (PositionCurrent) personPositionMap.get((Long) it2.next());
                        double distance = CommonUtil.getDistance(centerLongitude, centerLatitude, positionCurrent4.getLongitude().doubleValue(), positionCurrent4.getLatitude().doubleValue());
                        radius = Math.max(radius, distance);
                    }
                    boolean isRepeatAlarm = false;
                    List<GatherAlarmVo> mergeAlarmList = new ArrayList<>();
                    Iterator<GatherAlarmVo> gatherAlarmVoIterator = gatherAlarmVoList.iterator();
                    while (true) {
                        if (!gatherAlarmVoIterator.hasNext()) {
                            break;
                        }
                        GatherAlarmVo alarm4 = gatherAlarmVoIterator.next();
                        if (alarm4.getLayerId().equals(positionCurrent4.getLayerId())) {
                            double distance2 = CommonUtil.getDistance(centerLongitude, centerLatitude, alarm4.getLongitude().doubleValue(), alarm4.getLatitude().doubleValue());
                            if (distance2 >= radius + alarm4.getRadius().doubleValue()) {
                                continue;
                            } else if (alarm4.getEndTime() != null && alarm4.getEndTime().isAfter(LocalDateTime.now().minusMinutes(repeatALarmMinute))) {
                                log.info("【聚集报警】 {} 重复报警不处理:{}", areaId, JSONObject.toJSONString(alarm4, new JSONWriter.Feature[0]));
                                isRepeatAlarm = true;
                                break;
                            } else if (alarm4.getEndTime() != null) {
                                gatherAlarmVoIterator.remove();
                            } else {
                                mergeAlarmList.add(alarm4);
                                alarm4.setGatherEndTime(null);
                            }
                        }
                    }
                    if (isRepeatAlarm) {
                        return;
                    }
                    if (mergeAlarmList.size() > 0) {
                        finalAlarm = mergeAlarmList.get(0);
                        for (int i3 = 1; i3 < mergeAlarmList.size(); i3++) {
                            GatherAlarmVo alarmVo = mergeAlarmList.get(i3);
                            if (alarmVo.getGatherStartTime().isBefore(finalAlarm.getGatherStartTime())) {
                                finalAlarm.setGatherStartTime(alarmVo.getGatherStartTime());
                            }
                            for (CoreAlarmPeople person : alarmVo.getAlarmPeopleMap().values()) {
                                CoreAlarmPeople firstPerson = finalAlarm.getAlarmPeopleMap().get(person.getPersonId());
                                if (firstPerson == null || firstPerson.getAcceptTime().isAfter(person.getAcceptTime())) {
                                    finalAlarm.getAlarmPeopleMap().put(person.getPersonId(), person);
                                }
                            }
                            gatherAlarmVoList.remove(alarmVo);
                            if (alarmVo.getAlarmId() != null) {
                                this.alarmService.removeById(alarmVo);
                                this.alarmPeopleService.remove((Wrapper) new LambdaQueryWrapper<CoreAlarmPeople>().eq((v0) -> {
                                    return v0.getCoreAlarmId();
                                }, alarmVo.getAlarmId()));
                            }
                        }
                    } else {
                        finalAlarm = new GatherAlarmVo();
                        finalAlarm.setAcceptTime(LocalDateTime.now());
                        finalAlarm.setGatherStartTime(LocalDateTime.now());
                        finalAlarm.setAlarmPeopleMap(new HashMap());
                        finalAlarm.setAlarmedPeopleList(new ArrayList());
                        finalAlarm.setLayerId(positionCurrent4.getLayerId());
                        gatherAlarmVoList.add(finalAlarm);
                    }
                    finalAlarm.setLongitude(BigDecimal.valueOf(centerLongitude));
                    finalAlarm.setLatitude(BigDecimal.valueOf(centerLatitude));
                    finalAlarm.setRadius(Double.valueOf(radius));
                    finalAlarm.setLastGatherPeopleSet(personIdSet);
                    Set<Long> tempPersonIdSet = CollectionUtil.newHashSet(personIdSet);
                    GatherAlarmVo tempFinalAlarm = finalAlarm;
                    finalAlarm.getAlarmPeopleMap().entrySet().removeIf(item -> {
                        Long personId = (Long) item.getKey();
                        CoreAlarmPeople person2 = (CoreAlarmPeople) item.getValue();
                        if (tempPersonIdSet.contains(personId)) {
                            tempPersonIdSet.remove(personId);
                            return false;
                        }
                        person2.setEndTime(LocalDateTime.now());
                        tempFinalAlarm.getAlarmedPeopleList().add(person2);
                        return true;
                    });
                    for (Long personId : tempPersonIdSet) {
                        PositionCurrent positionCurrent5 = (PositionCurrent) personPositionMap.get(personId);
                        CoreAlarmPeople coreAlarmPeolpe = new CoreAlarmPeople();
                        coreAlarmPeolpe.setPersonId(personId);
                        coreAlarmPeolpe.setPhone(positionCurrent5.getPhone());
                        coreAlarmPeolpe.setPostName(positionCurrent5.getPostName());
                        coreAlarmPeolpe.setJobNumber(positionCurrent5.getJobNumber());
                        coreAlarmPeolpe.setRealName(positionCurrent5.getRealName());
                        coreAlarmPeolpe.setAcceptTime(LocalDateTime.now());
                        coreAlarmPeolpe.setLayerId(positionCurrent5.getLayerId());
                        coreAlarmPeolpe.setLayerHeight(positionCurrent5.getLayerHeight());
                        coreAlarmPeolpe.setCardId(positionCurrent5.getCardId());
                        coreAlarmPeolpe.setDeptName(positionCurrent5.getDeptName());
                        coreAlarmPeolpe.setDeptId(positionCurrent5.getDeptId());
                        coreAlarmPeolpe.setLatitude(positionCurrent5.getLatitude());
                        coreAlarmPeolpe.setLongitude(positionCurrent5.getLongitude());
                        finalAlarm.getAlarmPeopleMap().put(personId, coreAlarmPeolpe);
                    }
                });
                gatherAlarmVoList.forEach(gatherAlarm -> {
                    if (gatherAlarm.getEndTime() == null && gatherAlarm.getGatherStartTime().isBefore(LocalDateTime.now().minusSeconds(durationSecond))) {
                        log.info("【聚集报警】 {} 触发聚集报警:{}", areaId, JSONObject.toJSONString(gatherAlarm, new JSONWriter.Feature[0]));
                        int personCnt = gatherAlarm.getAlarmPeopleMap().size();
                        CoreAlarmPeople person = gatherAlarm.getAlarmPeopleMap().values().stream().findFirst().get();
                        String alarmLevel = ((double) personCnt) >= firstAlarmPeopleCount ? "1" : ((double) personCnt) >= secondAlarmPeopleCount ? "2" : "3";
                        if (gatherAlarm.getAlarmPeopleMap().size() > gatherAlarm.getMaxGatherPerson()) {
                            gatherAlarm.setMaxGatherPerson(gatherAlarm.getAlarmPeopleMap().size());
                        }
                        gatherAlarm.setAlarmDesc(StrUtil.format("发生聚集报警，聚集人数：{}", Integer.valueOf(gatherAlarm.getMaxGatherPerson())));
                        gatherAlarm.setLayerId(person.getLayerId());
                        gatherAlarm.setLayerHeight(person.getLayerHeight());
                        gatherAlarm.setRailScope(StrUtil.format("{},{},{}", gatherAlarm.getLongitude(), gatherAlarm.getLatitude(), gatherAlarm.getRadius()));
                        gatherAlarm.setRailHeight(2);
                        gatherAlarm.setDrawType(0);
                        if (gatherAlarm.getHighestAlarmLevel() == null || StrUtil.compare(gatherAlarm.getHighestAlarmLevel(), alarmLevel, true) > 0) {
                            gatherAlarm.setHighestAlarmLevel(alarmLevel);
                        }
                        gatherAlarm.setAlarmLevel(alarmLevel);
                        gatherAlarm.setEndTime(gatherAlarm.getGatherEndTime());
                        DeviceBuilding inBuilding = null;
                        Iterator it = buildingList.iterator();
                        while (true) {
                            if (!it.hasNext()) {
                                break;
                            }
                            DeviceBuilding deviceBuilding = (DeviceBuilding) it.next();
                            if (StrUtil.isNotEmpty(deviceBuilding.getRailScope()) && CommonUtil.containsRailScope(gatherAlarm.getLongitude(), gatherAlarm.getLatitude(), deviceBuilding.getRailScope(), String.valueOf(deviceBuilding.getDrawType()))) {
                                inBuilding = deviceBuilding;
                                break;
                            }
                        }
                        if (inBuilding != null) {
                            gatherAlarm.setBuildingId(inBuilding.getBuildingId());
                            gatherAlarm.setBuildingName(inBuilding.getBuildingName());
                        } else {
                            gatherAlarm.setBuildingId(null);
                            gatherAlarm.setBuildingName(null);
                        }
                        if (gatherAlarm.getAlarmId() == null) {
                            if (gatherAlarm.getEndTime() != null) {
                                return;
                            }
                            LocalDateTime now = LocalDateTime.now();
                            gatherAlarm.setAcceptTime(now);
                            gatherAlarm.setAreaId(areaId);
                            gatherAlarm.setAreaName(areaVO.getAreaName());
                            gatherAlarm.setAlarmType("96");
                            gatherAlarm.setAlarmTypeName(getAlarmTypeName("96"));
                            gatherAlarm.setAlarmName(getAlarmTypeName("96"));
                            gatherAlarm.setAlarmStatus("15");
                            gatherAlarm.setAlarmId(Long.valueOf(IdUtil.getSnowflakeNextId()));
                            gatherAlarm.setAlarmedPeopleList(new ArrayList());
                            gatherAlarm.setCreateTime(now);
                            gatherAlarm.getAlarmPeopleMap().values().forEach(people -> {
                                people.setAcceptTime(now);
                            });
                            this.alarmService.save(gatherAlarm);
                        } else {
                            Alarm oldAlarm = this.alarmService.selectOne(gatherAlarm.getAlarmId());
                            if (oldAlarm == null) {
                                gatherAlarm.setEndTime(LocalDateTime.now());
                            } else if (StrUtil.equals(oldAlarm.getAlarmStatus(), "15")) {
                                this.alarmService.updateById(gatherAlarm);
                            } else {
                                gatherAlarm.setEndTime(oldAlarm.getEndTime());
                            }
                        }
                        gatherAlarm.getAlarmedPeopleList().forEach(alarmPeople -> {
                            alarmPeople.setCoreAlarmId(gatherAlarm.getAlarmId());
                            if (alarmPeople.getCoreAlarmId() == null) {
                                alarmPeople.setCreateTime(LocalDateTime.now());
                            } else {
                                alarmPeople.setUpdateTime(LocalDateTime.now());
                            }
                        });
                        gatherAlarm.getAlarmPeopleMap().values().forEach(alarmPeople2 -> {
                            alarmPeople2.setEndTime(gatherAlarm.getEndTime());
                            alarmPeople2.setCoreAlarmId(gatherAlarm.getAlarmId());
                            if (alarmPeople2.getCoreAlarmId() == null) {
                                alarmPeople2.setCreateTime(LocalDateTime.now());
                            } else {
                                alarmPeople2.setUpdateTime(LocalDateTime.now());
                            }
                        });
                        this.alarmPeopleService.saveOrUpdateBatch(gatherAlarm.getAlarmedPeopleList());
                        this.alarmPeopleService.saveOrUpdateBatch(gatherAlarm.getAlarmPeopleMap().values());
                    }
                });
                gatherAlarmVoList.removeIf(gatherAlarm2 -> {
                    return gatherAlarm2.getGatherEndTime() != null && gatherAlarm2.getAlarmId() == null;
                });
            });
            if (Collections.singletonList(connection).get(0) != null) {
                connection.close();
            }
        } finally {
            if (Collections.singletonList(connection).get(0) != null) {
                connection.close();
            }
        }
    }
    /* JADX WARN: Multi-variable type inference failed */
    private void handleDbUnendGatherAlarm() {
        (( (this.alarmService.lambdaQuery().eq((v0) -> {
            return v0.getAlarmStatus();
        }, "15")).eq((v0) -> {
            return v0.getAlarmType();
        }, "96")).isNull((v0) -> {
            return v0.getEndTime();
        })).list().forEach(alarm -> {
            this.alarmService.removeById((Serializable) alarm.getAlarmId());
            this.alarmPeopleService.remove((Wrapper) new LambdaQueryWrapper<CoreAlarmPeople>().eq((v0) -> {
                return v0.getCoreAlarmId();
            }, alarm.getAlarmId()));
        });
    }
    private void calcPersonAlarm_30_2023(String alarmType) {
        if (!"30".equals(alarmType)) {
            return;
        }
        List<AreaAlarmTempVO> tempVOList = this.areaService.selectAreaAlarmByAlarmType(alarmType);
        if (CollectionUtils.isEmpty(tempVOList)) {
            return;
        }
        LocalDate nowDate = LocalDate.now();
        tempVOList.removeIf(f -> {
            return removeInvalid(f, nowDate);
        });
        if (CollectionUtils.isEmpty(tempVOList)) {
            return;
        }
        Set<Long> closeAreaAlarm = new HashSet<>();
        Map<Long, Set<Long>> leaveCurrentPesonSets = new HashMap<>();
        Map<Long, List<AreaAlarmTempVO>> areaVoMap = (Map) tempVOList.stream().collect(Collectors.groupingBy((v0) -> {
            return v0.getAreaId();
        }));
        areaVoMap.forEach((areaId, areaVoList) -> {
            AreaAlarmTempVO areaVO = (AreaAlarmTempVO) tempVOList.stream().filter(f2 -> {
                return f2.getAreaId().compareTo(areaId) == 0;
            }).findFirst().get();
            areaVO.setAlarmPersonIds(this.areaService.findAlarmPersonIdsByAreaId(areaId));
            areaVO.setPostIds(this.areaService.findPostIdsByAreaId(areaId));
            areaVO.setDeptIds(this.areaService.findDeptIdsByAreaId(areaId));
            areaVO.setPersonIds(this.areaService.findPersonIdsByAreaId(areaId));
            Map<Long, Long> cardAreaIdMap = new HashMap<>();
            areaVoList.stream().forEach(areaVo -> {
                Long durationSecond = NumberUtil.isLong(areaVO.getRule()) ? Long.valueOf(Long.parseLong(areaVO.getRule())) : null;
                List<PositionCurrent> positionCurrentList = this.positionCurrentService.selectAlarmPersonPosition(areaVO);
                List<PositionCurrent> alarmPersonList = waitAlarmByPoint(areaId, cardAreaIdMap, areaVO.getLayerId(), positionCurrentList, durationSecond);
                if (CollectionUtils.isEmpty(alarmPersonList)) {
                    return;
                }
                String key = alarmType + "_" + areaId;
                Set<Long> lastAreaCurrentPeson = this.areaAlarmPeopleMap.get(key);
                HashSet hashSet = new HashSet();
                if (CollUtil.isNotEmpty((Collection<?>) lastAreaCurrentPeson)) {
                    hashSet.addAll(lastAreaCurrentPeson);
                }
                Set<Long> currentPersonSets = (Set) alarmPersonList.stream().map((v0) -> {
                    return v0.getPersonId();
                }).collect(Collectors.toSet());
                if (CollUtil.isNotEmpty((Collection<?>) hashSet)) {
                    if (!CollUtil.isEmpty((Collection<?>) currentPersonSets)) {
                        hashSet.removeAll(currentPersonSets);
                    }
                    leaveCurrentPesonSets.put(areaId, hashSet);
                }
                this.areaAlarmPeopleMap.put(key, currentPersonSets);
                closeAreaAlarm.add(areaId);
                Map<String, Object> map = new HashMap<>();
                map.put("alarmType", alarmType);
                map.put("areaId", areaId);
                map.put("endTime", LocalDateTime.now());
                alarmPersonList.stream().forEach(positionCurrent -> {
                    map.put("cardId", positionCurrent.getCardId());
                    if (!this.alarmService.existAlarmByParams(map)) {
                        insertPersonAlarmByVo(areaVo, positionCurrent, areaVoList);
                    }
                });
            });
        });
        this.alarmService.closeAlarmEndTime(alarmType, leaveCurrentPesonSets, closeAreaAlarm, null);
    }
    private void insertPersonAlarmByVo(AreaAlarmTempVO areaVO, PositionCurrent positionCurrent, List<AreaAlarmTempVO> areaVoList) {
        String alarmTypeName = getAlarmTypeName(areaVO.getAlarmType());
        Alarm alarm = new Alarm();
        alarm.setCreateBy("task");
        alarm.setAlarmType(areaVO.getAlarmType());
        alarm.setAlarmTypeName(alarmTypeName);
        alarm.setAcceptTime(LocalDateTime.now());
        alarm.setAreaId(areaVO.getAreaId());
        alarm.setAreaName(areaVO.getAreaName());
        alarm.setPersonId(positionCurrent.getPersonId());
        alarm.setPersonType(positionCurrent.getPersonType());
        alarm.setRealName(positionCurrent.getRealName());
        alarm.setCardId(positionCurrent.getCardId());
        alarm.setStaffType(positionCurrent.getStaffType());
        alarm.setContractorId(positionCurrent.getContractorId());
        alarm.setContractorName(positionCurrent.getContractorName());
        alarm.setDeptId(positionCurrent.getDeptId());
        alarm.setDeptName(positionCurrent.getDeptName());
        alarm.setPostId(positionCurrent.getPostId());
        alarm.setPostName(positionCurrent.getPostName());
        alarm.setJobNumber(positionCurrent.getJobNumber());
        alarm.setBeaconId(positionCurrent.getBeaconId());
        alarm.setLongitude(positionCurrent.getLongitude());
        alarm.setLatitude(positionCurrent.getLatitude());
        alarm.setLayerId(positionCurrent.getLayerId());
        alarm.setLayerHeight(positionCurrent.getLayerHeight());
        alarm.setRailId(areaVO.getRailId());
        alarm.setRailName(areaVO.getRailName());
        alarm.setRailScope(areaVO.getRailScope());
        alarm.setRailHeight(areaVO.getRailHeight());
        StringBuilder alarmNameBuilder = new StringBuilder(alarm.getRealName());
        alarmNameBuilder.append("10".equals(alarm.getAlarmType()) ? "进入" : "20".equals(alarm.getAlarmType()) ? "离开" : "30".equals(alarm.getAlarmType()) ? "滞留" : "70".equals(alarm.getAlarmType()) ? "静止" : "未配置").append(" ").append(alarm.getLayerId()).append(",").append(alarm.getAreaName()).append(" 请及时处理");
        alarm.setAlarmName(alarmNameBuilder.toString());
        StringBuilder descBuilder = new StringBuilder();
        descBuilder.append("报警类型:").append(alarmTypeName).append(",楼层:").append(alarm.getLayerId()).append(",报警区域名:").append(alarm.getAreaName()).append(",姓名:").append(alarm.getRealName()).append(",卡号:").append(alarm.getCardId()).append(",部门:").append(StringUtils.isNotBlank(alarm.getDeptName()) ? alarm.getDeptName() : "").append(",岗位:").append(StringUtils.isNotBlank(alarm.getPostName()) ? alarm.getPostName() : "").append(",工号:").append(StringUtils.isNotBlank(alarm.getJobNumber()) ? alarm.getJobNumber() : "").append(",手机号:").append(StringUtils.isNotBlank(positionCurrent.getPhone()) ? positionCurrent.getPhone() : "");
        alarm.setAlarmDesc(descBuilder.toString());
        alarm.setBuildingName(areaVO.getBuildingName());
        alarm.setBuildingId(areaVO.getBuildingId());
        insertAlarm(alarm);
    }
    private List<PositionCurrent> waitAlarmByPoint(Long areaId, Map<Long, Long> cardAreaIdMap, String layerId, List<PositionCurrent> positionData, Long rule) {
        List<PositionCurrent> alarmPersonList = new ArrayList<>();
        positionData.stream().collect(Collectors.groupingBy((v0) -> {
            return v0.getCardId();
        })).forEach((calcCardId, calcList) -> {
            Long alarmAreaId = (Long) cardAreaIdMap.get(calcCardId);
            if ((Objects.nonNull(alarmAreaId) && areaId.compareTo(alarmAreaId) == 0) || calcList.size() < 2) {
                return;
            }
            long waitSecond = 0;
            LocalDateTime nextTime = null;
            for (int i = 0; i < calcList.size(); i++) {
                PositionCurrent t = (PositionCurrent) calcList.get(i);
                if (i == 0) {
                    t.getAcceptTime();
                    nextTime = t.getAcceptTime();
                } else {
                    LocalDateTime prefTime = nextTime;
                    nextTime = t.getAcceptTime();
                    if (t.getInRailScope().booleanValue() && layerId.equals(t.getLayerId())) {
                        long interval = Duration.between(nextTime, prefTime).toMillis() / 1000;
                        waitSecond += interval;
                    } else {
                        waitSecond = 0;
                    }
                    if (waitSecond > rule.longValue()) {
                        alarmPersonList.add(t);
                        cardAreaIdMap.put(calcCardId, areaId);
                        return;
                    }
                }
            }
        });
        return alarmPersonList;
    }
    private void keepAlarm50(String alarmType, int maxCount) {
        List<AreaAlarmTempVO> tempVOList = this.areaService.selectAreaAlarmByAlarmType(alarmType);
        if (CollectionUtils.isEmpty(tempVOList)) {
            this.railByAlarm50Map = new HashMap();
            return;
        }
        LocalDate nowDate = LocalDate.now();
        tempVOList.removeIf(f -> {
            return removeInvalid(f, nowDate);
        });
        if (CollectionUtils.isEmpty(tempVOList)) {
            this.railByAlarm50Map = new HashMap();
            return;
        }
        String modeType = RedisCacheUtils.getConfigRedisCache().get(ConfigValue.CONFIG_KEY_ALARM50_BURST_MODE);
        String burstMode = StringUtils.isNotEmpty(modeType) ? modeType : "1";
        Map<Long, Set<Long>> leaveCurrentPesonSets = new HashMap<>();
        Set<Long> closeRailAlarm = new HashSet<>();
        tempVOList.stream().forEach(areaVO -> {
            boolean verifyAlarm = false;
            int personCount = 0;
            int personCountRule = Integer.parseInt(areaVO.getRule());
            List<PositionCurrent> positionCurrentList = new ArrayList<>();
            if (personCountRule > 0) {
                areaVO.setDeptIds(this.areaService.findDeptIdsByAreaId(areaVO.getAreaId()));
                areaVO.setPersonIds(this.areaService.findPersonIdsByAreaId(areaVO.getAreaId()));
                areaVO.setAlarmPersonIds(this.areaService.findAlarmPersonIdsByAreaId(areaVO.getAreaId()));
                areaVO.setPostIds(this.areaService.findPostIdsByAreaId(areaVO.getAreaId()));
                positionCurrentList = this.positionCurrentService.selectRailAlarmPerson(areaVO);
                personCount = positionCurrentList.size();
                if (positionCurrentList.size() > personCountRule) {
                    verifyAlarm = true;
                }
            }
            String key = alarmType + "_railId_" + areaVO.getRailId();
            Set<Long> currentPersonSets = (Set) positionCurrentList.stream().map((v0) -> {
                return v0.getPersonId();
            }).collect(Collectors.toSet());
            Map<Long, LocalDateTime> overManPersonMap = this.overManMap.get(key);
            Map<Long, LocalDateTime> userOverManPersonMap = new HashMap<>();
            if (Objects.isNull(overManPersonMap)) {
                overManPersonMap = new HashMap();
            }
            if (CollUtil.isNotEmpty((Collection<?>) positionCurrentList)) {
                for (PositionCurrent s : positionCurrentList) {
                    if (!overManPersonMap.containsKey(s.getPersonId())) {
                        overManPersonMap.put(s.getPersonId(), s.getAcceptTime());
                    }
                }
                overManPersonMap.keySet().removeIf(s2 -> {
                    return !currentPersonSets.contains(s2);
                });
                if (overManPersonMap != null) {
                    userOverManPersonMap.putAll(overManPersonMap);
                }
            }
            this.overManMap.put(key, overManPersonMap);
            if (verifyAlarm) {
                Map<Long, List<PositionCurrent>> coreAlarmPeopleMap = new HashMap<>();
                String alarmTypeName = getAlarmTypeName(alarmType);
                Alarm alarm = new Alarm();
                LocalDateTime now = LocalDateTime.now();
                alarm.setAlarmType(alarmType);
                alarm.setAlarmTypeName(alarmTypeName);
                alarm.setAcceptTime(now);
                alarm.setAreaId(areaVO.getAreaId());
                alarm.setAreaName(areaVO.getAreaName());
                alarm.setLayerId(areaVO.getLayerId());
                alarm.setLayerHeight(areaVO.getLayerHeight());
                alarm.setRailId(areaVO.getRailId());
                alarm.setRailName(areaVO.getRailName());
                alarm.setRailScope(areaVO.getRailScope());
                alarm.setRailHeight(areaVO.getRailHeight());
                alarm.setAlarmName(alarm.getLayerId() + "," + areaVO.getAreaName() + "触发" + alarmTypeName + ",请及时处理");
                StringBuilder descBuilder = new StringBuilder("楼层:").append(alarm.getLayerId()).append(",报警区域名:").append(areaVO.getAreaName()).append(",最多").append(areaVO.getRule()).append("人").append(",实到").append(personCount).append("人").append(",存在超员");
                alarm.setAlarmDesc(descBuilder.toString());
                alarm.setCreateBy("task");
                alarm.setBuildingName(areaVO.getBuildingName());
                alarm.setBuildingId(areaVO.getBuildingId());
                alarm.setTimeCount(0);
                Alarm oldAlarm = this.railByAlarm50Map.get(areaVO.getRailId());
                if (Objects.nonNull(oldAlarm)) {
                    long interval = Duration.between(oldAlarm.getAcceptTime(), now).toMillis() / 1000;
                    int timeCount = (int) (0 + interval);
                    alarm.setTimeCount(Integer.valueOf(timeCount));
                    if (timeCount > maxCount) {
                        closeRailAlarm.add(areaVO.getRailId());
                        positionCurrentList.forEach(s3 -> {
                            LocalDateTime initTime = (LocalDateTime) userOverManPersonMap.get(s3.getPersonId());
                            if (Objects.nonNull(initTime)) {
                                s3.setAcceptTime(initTime);
                            }
                        });
                        if ("2".equals(burstMode)) {
                            positionCurrentList.stream().forEach(positionCurrent -> {
                                Long alarmId = insertkeepAlarm50Alarm(alarm, positionCurrent);
                                if (Objects.nonNull(alarmId)) {
                                    coreAlarmPeopleMap.put(alarmId, Arrays.asList(positionCurrent));
                                    alarm.setAlarmId(null);
                                }
                            });
                        } else {
                            PositionCurrent positionCurrent2 = positionCurrentList.get(0);
                            Long alarmId = insertkeepAlarm50Alarm(alarm, positionCurrent2);
                            if (Objects.nonNull(alarmId)) {
                                coreAlarmPeopleMap.put(alarmId, positionCurrentList);
                            }
                        }
                        this.railByAlarm50Map.remove(areaVO.getRailId());
                    }
                } else {
                    this.railByAlarm50Map.put(areaVO.getRailId(), alarm);
                }
                this.alarmPeopleService.saveAlarmPeople(coreAlarmPeopleMap, alarmType);
                return;
            }
            this.railByAlarm50Map.remove(areaVO.getRailId());
        });
        this.alarmService.closeAlarmEndTime("keep_" + alarmType, leaveCurrentPesonSets, closeRailAlarm, null);
    }
    private void calcPersonAlarm_10_20_40_50(String alarmType) {
        String modeType = RedisCacheUtils.getConfigRedisCache().get(ConfigValue.CONFIG_KEY_ALARM50_BURST_MODE);
        String burstMode = StringUtils.isNotEmpty(modeType) ? modeType : "1";
        List<AreaAlarmTempVO> tempVOList = this.areaService.selectAreaAlarmByAlarmType(alarmType);
        if (CollectionUtils.isEmpty(tempVOList)) {
            return;
        }
        LocalDate nowDate = LocalDate.now();
        tempVOList.removeIf(f -> {
            return removeInvalid(f, nowDate);
        });
        if (CollectionUtils.isEmpty(tempVOList)) {
            return;
        }
        Map<Long, List<AreaAlarmTempVO>> areaVoMap = (Map) tempVOList.stream().collect(Collectors.groupingBy((v0) -> {
            return v0.getAreaId();
        }));
        Map<Long, Set<Long>> currentOnleave = new HashMap<>();
        Set<Long> closeAreaAlarm = new HashSet<>();
        areaVoMap.forEach((areaId, areaVoList) -> {
            List<Long> newSortPersonList;
            AreaAlarmTempVO areaVO = (AreaAlarmTempVO) tempVOList.stream().filter(f2 -> {
                return f2.getAreaId().compareTo(areaId) == 0;
            }).findFirst().get();
            areaVO.setRailDrawList((List) areaVoList.stream().map(s -> {
                return RailDrawType.builder().drawType(s.getDrawType()).railScope(s.getRailScope()).layerId(s.getLayerId()).build();
            }).collect(Collectors.toList()));
            areaVO.setLayerIds((List) areaVoList.stream().map((v0) -> {
                return v0.getLayerId();
            }).collect(Collectors.toList()));
            areaVO.setDeptIds(this.areaService.findDeptIdsByAreaId(areaId));
            areaVO.setPersonIds(this.areaService.findPersonIdsByAreaId(areaId));
            areaVO.setPostIds(this.areaService.findPostIdsByAreaId(areaId));
            areaVO.setAlarmPersonIds(this.areaService.findAlarmPersonIdsByAreaId(areaId));
            List<PositionCurrent> positionCurrentList = this.positionCurrentService.selectAlarmPerson(areaVO);
            AlarmAreaTempVO alarmAreaTempVO = new AlarmAreaTempVO();
            alarmAreaTempVO.setAreaId(areaId);
            alarmAreaTempVO.setAlarmPersonCount(Integer.valueOf(positionCurrentList.size()));
            alarmAreaTempVO.setAlarmType(alarmType);
            alarmAreaTempVO.setAreaVO(areaVO);
            Long personCountRule = NumberUtil.isLong(areaVO.getRule()) ? Long.valueOf(Long.parseLong(areaVO.getRule())) : null;
            if ("50".equals(alarmType) && positionCurrentList.size() > personCountRule.longValue()) {
                alarmAreaTempVO.setAlarm(true);
            } else if ("40".equals(alarmType) && positionCurrentList.size() < personCountRule.longValue()) {
                alarmAreaTempVO.setAlarm(true);
            } else if (("10".equals(alarmType) || "20".equals(alarmType)) && positionCurrentList.size() > 0) {
                alarmAreaTempVO.setAlarm(true);
            } else {
                alarmAreaTempVO.setAlarm(false);
            }
            String key = alarmType + "_" + areaId;
            if ("50".equals(alarmType) && CollUtil.isEmpty((Collection<?>) positionCurrentList)) {
                this.areaAlarmPeopleMap.remove(key);
            }
            Collection<? extends Long> lastAreaCurrentPeson = (Set) this.areaAlarmPeopleMap.get(key);
            Set<Long> lastAreaCurrentPesonSets = new HashSet<>();
            if (CollUtil.isNotEmpty((Collection<?>) lastAreaCurrentPeson)) {
                lastAreaCurrentPesonSets.addAll(lastAreaCurrentPeson);
            }
            Set<Long> currentPersonSets = (Set) positionCurrentList.stream().map((v0) -> {
                return v0.getPersonId();
            }).collect(Collectors.toSet());
            if ("10".equals(alarmType) || "20".equals(alarmType)) {
                if (alarmAreaTempVO.getAlarm().booleanValue()) {
                    if (!CollUtil.isEmpty((Collection<?>) currentPersonSets)) {
                        lastAreaCurrentPesonSets.removeAll(currentPersonSets);
                    }
                    currentOnleave.put(areaId, lastAreaCurrentPesonSets);
                }
                this.areaAlarmPeopleMap.put(key, currentPersonSets);
            }
            Map<Long, LocalDateTime> overManPersonMap = this.overManMap.get(key);
            Map<Long, LocalDateTime> userOverManPersonMap = new HashMap<>();
            if ("50".equals(alarmType) || ("40".equals(alarmType) && !alarmAreaTempVO.getAlarm().booleanValue())) {
                if (Objects.isNull(overManPersonMap)) {
                    overManPersonMap = new HashMap();
                }
                if (CollUtil.isNotEmpty((Collection<?>) positionCurrentList)) {
                    for (PositionCurrent s2 : positionCurrentList) {
                        if (!overManPersonMap.containsKey(s2.getPersonId())) {
                            overManPersonMap.put(s2.getPersonId(), s2.getAcceptTime());
                        }
                    }
                    overManPersonMap.keySet().removeIf(s3 -> {
                        return !currentPersonSets.contains(s3);
                    });
                    if (overManPersonMap != null) {
                        userOverManPersonMap.putAll(overManPersonMap);
                    }
                }
                this.overManMap.put(key, overManPersonMap);
            }
            if ("40".equals(alarmType) && alarmAreaTempVO.getAlarm().booleanValue() && overManPersonMap != null) {
                userOverManPersonMap.putAll(overManPersonMap);
            }
            if (alarmAreaTempVO.getAlarm().booleanValue()) {
                Map<String, Object> map = new HashMap<>();
                map.put("alarmType", alarmType);
                map.put("areaId", areaId);
                map.put("endTime", LocalDateTime.now());
                if ("10".equals(alarmType) || "20".equals(alarmType)) {
                    positionCurrentList.stream().forEach(positionCurrent -> {
                        map.put("cardId", positionCurrent.getCardId());
                        insertPersonAlarm(alarmAreaTempVO, positionCurrent, areaVoList);
                    });
                } else {
                    Integer attendancePersonnelCount = Integer.valueOf(positionCurrentList.size());
                    Map<Long, List<PositionCurrent>> coreAlarmPeopleMap = new HashMap<>();
                    Map<Long, LocalDateTime> personMap = MapUtil.sortByValue(userOverManPersonMap, false);
                    List<Long> sortPersonList = (List) personMap.keySet().stream().collect(Collectors.toList());
                    if ("50".equals(alarmType)) {
                        List<Long> newSortPersonList2 = sortPersonList.subList(Math.toIntExact(personCountRule.longValue()), positionCurrentList.size());
                        positionCurrentList.removeIf(s4 -> {
                            return !newSortPersonList2.contains(s4.getPersonId());
                        });
                        Set<Long> personIdSet = (Set) newSortPersonList2.stream().collect(Collectors.toSet());
                        log.info("超员报警:当前人员:{},超员人数:{}", Integer.valueOf(currentPersonSets.size()), Integer.valueOf(personIdSet.size()));
                        if (!SetUtils.isEqualSet(lastAreaCurrentPesonSets, personIdSet)) {
                            currentOnleave.put(areaId, personIdSet);
                            this.areaAlarmPeopleMap.put(key, personIdSet);
                        }
                    } else if ("40".equals(alarmType)) {
                        if (CollUtil.isEmpty((Collection<?>) positionCurrentList)) {
                            attendancePersonnelCount = 0;
                        }
                        List<PositionCurrent> userLastPositionCurrents = new ArrayList<>();
                        List<PositionCurrent> lastPositionCurrents = this.areaLackAlarmPeopleMap.get(areaId);
                        if (CollUtil.isNotEmpty((Collection<?>) lastPositionCurrents)) {
                            userLastPositionCurrents.addAll(lastPositionCurrents);
                        }
                        List<Long> excludePersonIds = new ArrayList<>();
                        excludePersonIds.addAll(areaVO.getPersonIds());
                        excludePersonIds.addAll(currentPersonSets);
                        PositionCurrent positionCurrent2 = this.personService.findPersonByDeptId(areaVO.getDeptIds(), excludePersonIds, areaVO.getAlarmPersonIds(), areaVO.getPostIds());
                        if (Objects.nonNull(positionCurrent2)) {
                            positionCurrent2.setLayerId(areaVO.getLayerId());
                            if (CollUtil.isNotEmpty((Collection<?>) userLastPositionCurrents)) {
                                sortPersonList.removeIf(s5 -> {
                                    return currentPersonSets.contains(s5);
                                });
                                int index = Math.toIntExact(personCountRule.longValue()) - positionCurrentList.size();
                                if (sortPersonList.size() >= index) {
                                    List<Long> personIds = areaVO.getPersonIds();
                                    if (CollUtil.isNotEmpty((Collection<?>) personIds)) {
                                        sortPersonList.removeIf(s6 -> {
                                            return personIds.contains(s6);
                                        });
                                    }
                                    if (sortPersonList.size() >= index) {
                                        newSortPersonList = sortPersonList.subList(0, index);
                                    } else {
                                        newSortPersonList = sortPersonList;
                                    }
                                    Set<Long> personIdSet2 = (Set) newSortPersonList.stream().collect(Collectors.toSet());
                                    if (CollUtil.isEmpty((Collection<?>) personIdSet2)) {
                                        closeAreaAlarm.add(areaId);
                                    }
                                    if (!SetUtils.isEqualSet(lastAreaCurrentPesonSets, personIdSet2)) {
                                        currentOnleave.put(areaId, personIdSet2);
                                        this.areaAlarmPeopleMap.put(key, personIdSet2);
                                    }
                                    userLastPositionCurrents.removeIf(s7 -> {
                                        return !personIdSet2.contains(s7.getPersonId());
                                    });
                                    positionCurrentList = userLastPositionCurrents;
                                    log.info("缺员报警:当前人员数量:{},缺员人数:{}", Integer.valueOf(currentPersonSets.size()), Integer.valueOf(positionCurrentList.size()));
                                } else {
                                    log.info("缺员报警异常:当前缺失人员数量:{},最近一次正常人员:{}", Integer.valueOf(index), sortPersonList);
                                    positionCurrentList.clear();
                                    positionCurrentList = Arrays.asList(positionCurrent2);
                                }
                            } else {
                                positionCurrentList = Arrays.asList(positionCurrent2);
                            }
                        } else {
                            positionCurrentList.clear();
                            closeAreaAlarm.add(areaId);
                        }
                    }
                    if ("2".equals(burstMode)) {
                        positionCurrentList.stream().forEach(positionCurrent3 -> {
                            map.put("cardId", positionCurrent3.getCardId());
                            Alarm alarm = insertPersonAlarm(alarmAreaTempVO, positionCurrent3, areaVoList);
                            if (Objects.nonNull(alarm.getAlarmId())) {
                                coreAlarmPeopleMap.put(alarm.getAlarmId(), Arrays.asList(positionCurrent3));
                            }
                        });
                    } else {
                        Alarm alarm = insertAreaAlarm(alarmAreaTempVO, positionCurrentList, areaVoList, attendancePersonnelCount);
                        if (Objects.nonNull(alarm.getAlarmId())) {
                            coreAlarmPeopleMap.put(alarm.getAlarmId(), positionCurrentList);
                        }
                    }
                    this.alarmPeopleService.saveAlarmPeople(coreAlarmPeopleMap, alarmType);
                }
                if (!"40".equals(alarmType)) {
                    closeAreaAlarm.add(areaId);
                    return;
                }
                return;
            }
            if ("40".equals(alarmType)) {
                this.areaLackAlarmPeopleMap.put(areaId, positionCurrentList);
                closeAreaAlarm.add(areaId);
            }
        });
        this.alarmService.closeAlarmEndTime(alarmType, currentOnleave, closeAreaAlarm, null);
    }
    private Alarm insertPersonAlarm(AlarmAreaTempVO alarmAreaTempVO, PositionCurrent positionCurrent, List<AreaAlarmTempVO> areaVoList) {
        Person person = this.personService.getPersonById(positionCurrent.getPersonId());
        Long deptId = positionCurrent.getDeptId();
        Map<Long, SystemDept> deptInfoMap = this.systemDeptService.findDeptInfoMap();
        String deptPath = null;
        if (!CollectionUtils.isEmpty(deptInfoMap)) {
            deptPath = com.xrkc.core.utils.CommonUtil.getDeptPath(deptId, deptInfoMap);
        }
        AreaAlarmTempVO areaVO = alarmAreaTempVO.getAreaVO();
        String alarmTypeName = getAlarmTypeName(alarmAreaTempVO.getAlarmType());
        Alarm alarm = new Alarm();
        alarm.setCreateBy("task");
        alarm.setAlarmType(alarmAreaTempVO.getAlarmType());
        alarm.setAlarmTypeName(alarmTypeName);
        alarm.setAcceptTime(Objects.nonNull(alarmAreaTempVO.getAcceptTime()) ? alarmAreaTempVO.getAcceptTime() : LocalDateTime.now());
        alarm.setAreaId(alarmAreaTempVO.getAreaId());
        alarm.setAreaName(areaVO.getAreaName());
        alarm.setPersonId(positionCurrent.getPersonId());
        alarm.setPersonType(positionCurrent.getPersonType());
        alarm.setRealName(positionCurrent.getRealName());
        alarm.setCardId(positionCurrent.getCardId());
        alarm.setStaffType(positionCurrent.getStaffType());
        alarm.setContractorId(positionCurrent.getContractorId());
        alarm.setContractorName(positionCurrent.getContractorName());
        alarm.setDeptId(deptId);
        alarm.setDeptName(deptPath);
        alarm.setPostId(positionCurrent.getPostId());
        alarm.setPostName(positionCurrent.getPostName());
        alarm.setJobNumber(positionCurrent.getJobNumber());
        alarm.setBeaconId(positionCurrent.getBeaconId());
        alarm.setLongitude(positionCurrent.getLongitude());
        alarm.setLatitude(positionCurrent.getLatitude());
        alarm.setLayerId(positionCurrent.getLayerId());
        alarm.setLayerHeight(positionCurrent.getLayerHeight());
        alarm.setAdministratorName(person.getAdministratorName());
        alarm.setAdministratorPhone(person.getAdministratorPhone());
        if (areaVoList.size() == 1) {
            AreaAlarmTempVO railVO = areaVoList.stream().findFirst().get();
            alarm.setRailId(railVO.getRailId());
            alarm.setRailName(railVO.getRailName());
            alarm.setRailScope(railVO.getRailScope());
            alarm.setRailHeight(railVO.getRailHeight());
            alarm.setBuildingName(railVO.getBuildingName());
            alarm.setBuildingId(railVO.getBuildingId());
        } else if ("20".equals(alarmAreaTempVO.getAlarmType())) {
            areaVoList.stream().filter(f -> {
                return !CommonUtil.containsRailScope(positionCurrent.getLongitude(), positionCurrent.getLatitude(), f.getRailScope(), f.getDrawType());
            }).findFirst().ifPresent(railVO2 -> {
                alarm.setRailId(railVO2.getRailId());
                alarm.setRailName(railVO2.getRailName());
                alarm.setRailScope(railVO2.getRailScope());
                alarm.setRailHeight(railVO2.getRailHeight());
                alarm.setBuildingName(railVO2.getBuildingName());
                alarm.setBuildingId(railVO2.getBuildingId());
            });
        } else {
            areaVoList.stream().filter(f2 -> {
                return f2.getLayerId().equals(positionCurrent.getLayerId()) && CommonUtil.containsRailScope(positionCurrent.getLongitude(), positionCurrent.getLatitude(), f2.getRailScope(), f2.getDrawType());
            }).findFirst().ifPresent(railVO3 -> {
                alarm.setRailId(railVO3.getRailId());
                alarm.setRailName(railVO3.getRailName());
                alarm.setRailScope(railVO3.getRailScope());
                alarm.setRailHeight(railVO3.getRailHeight());
                alarm.setBuildingName(railVO3.getBuildingName());
                alarm.setBuildingId(railVO3.getBuildingId());
            });
        }
        StringBuilder alarmNameBuilder = new StringBuilder(alarm.getRealName());
        alarmNameBuilder.append("10".equals(alarm.getAlarmType()) ? "进入" : "20".equals(alarm.getAlarmType()) ? "离开" : "30".equals(alarm.getAlarmType()) ? "滞留" : "70".equals(alarm.getAlarmType()) ? "静止" : "未配置").append(" ").append(alarm.getLayerId()).append(",").append(alarm.getAreaName()).append(" 请及时处理");
        alarm.setAlarmName(alarmNameBuilder.toString());
        StringBuilder descBuilder = new StringBuilder();
        descBuilder.append("报警类型:").append(alarmTypeName).append(",楼层:").append(alarm.getLayerId()).append(",报警区域名:").append(alarm.getAreaName()).append(",姓名:").append(alarm.getRealName()).append(",卡号:").append(alarm.getCardId()).append(",部门:").append(StringUtils.isNotBlank(alarm.getDeptName()) ? alarm.getDeptName() : "").append(",岗位:").append(StringUtils.isNotBlank(alarm.getPostName()) ? alarm.getPostName() : "").append(",工号:").append(StringUtils.isNotBlank(alarm.getJobNumber()) ? alarm.getJobNumber() : "").append(",手机号:").append(StringUtils.isNotBlank(positionCurrent.getPhone()) ? positionCurrent.getPhone() : "").append(",管理人员姓名:").append(StringUtils.isNotBlank(alarm.getAdministratorName()) ? alarm.getAdministratorName() : "").append(",管理人员号码:").append(StringUtils.isNotBlank(alarm.getAdministratorPhone()) ? alarm.getAdministratorPhone() : "");
        alarm.setAlarmDesc(descBuilder.toString());
        insertAlarm(alarm);
        return alarm;
    }
    private Alarm insertAreaAlarm(AlarmAreaTempVO alarmAreaTempVO, List<PositionCurrent> positionCurrentList, List<AreaAlarmTempVO> areaVoList, Integer attendancePersonnelCount) {
        AreaAlarmTempVO areaVO = alarmAreaTempVO.getAreaVO();
        String alarmTypeName = getAlarmTypeName(alarmAreaTempVO.getAlarmType());
        Alarm alarm = new Alarm();
        alarm.setAlarmType(alarmAreaTempVO.getAlarmType());
        alarm.setAlarmTypeName(alarmTypeName);
        alarm.setAcceptTime(LocalDateTime.now());
        alarm.setAreaId(alarmAreaTempVO.getAreaId());
        alarm.setAreaName(areaVO.getAreaName());
        if (areaVoList.size() == 1 || ("40".equals(alarm.getAlarmType()) && CollectionUtils.isEmpty(positionCurrentList))) {
            AreaAlarmTempVO railVO = areaVoList.stream().findFirst().get();
            alarm.setLayerId(railVO.getLayerId());
            alarm.setLayerHeight(railVO.getLayerHeight());
            alarm.setRailId(railVO.getRailId());
            alarm.setRailName(railVO.getRailName());
            alarm.setRailScope(railVO.getRailScope());
            alarm.setRailHeight(railVO.getRailHeight());
            alarm.setBuildingName(railVO.getBuildingName());
            alarm.setBuildingId(railVO.getBuildingId());
        } else {
            Map<String, Long> a = (Map) positionCurrentList.stream().collect(Collectors.groupingBy((v0) -> {
                return v0.getLayerId();
            }, Collectors.counting()));
            String layerId = CommonUtil.getKeyByMaxValue(a);
            List<AreaAlarmTempVO> areaVoList1 = (List) areaVoList.stream().filter(f -> {
                return layerId.equals(f.getLayerId());
            }).collect(Collectors.toList());
            if (areaVoList1.size() == 1) {
                AreaAlarmTempVO railVO2 = areaVoList.stream().findFirst().get();
                alarm.setLayerId(railVO2.getLayerId());
                alarm.setLayerHeight(railVO2.getLayerHeight());
                alarm.setRailId(railVO2.getRailId());
                alarm.setRailName(railVO2.getRailName());
                alarm.setRailScope(railVO2.getRailScope());
                alarm.setRailHeight(railVO2.getRailHeight());
                alarm.setBuildingName(railVO2.getBuildingName());
                alarm.setBuildingId(railVO2.getBuildingId());
            } else {
                getMaxPersonRail((List) positionCurrentList.stream().filter(f2 -> {
                    return layerId.equals(f2.getLayerId());
                }).collect(Collectors.toList()), areaVoList1, alarm);
            }
        }
        alarm.setAlarmName(alarm.getLayerId() + "," + areaVO.getAreaName() + "触发" + alarmTypeName + ",请及时处理");
        StringBuilder descBuilder = new StringBuilder("楼层:").append(alarm.getLayerId()).append(",报警区域名:").append(areaVO.getAreaName());
        if ("40".equals(alarmAreaTempVO.getAlarmType())) {
            descBuilder.append(",应到").append(areaVO.getRule()).append("人").append(",实到").append(attendancePersonnelCount).append("人").append(",存在缺员");
        } else if ("50".equals(alarmAreaTempVO.getAlarmType())) {
            descBuilder.append(",最多").append(areaVO.getRule()).append("人").append(",实到").append(attendancePersonnelCount).append("人").append(",存在超员");
        }
        alarm.setAlarmDesc(descBuilder.toString());
        alarm.setCreateBy("task");
        insert50AreaAlarm(alarm);
        return alarm;
    }
    private void getMaxPersonRail(List<PositionCurrent> positionCurrentList, List<AreaAlarmTempVO> areaVoList, Alarm alarm) {
        int max = 0;
        for (AreaAlarmTempVO railVO : areaVoList) {
            int personCount = (int) positionCurrentList.stream().filter(f -> {
                return f.getLayerId().equals(railVO.getLayerId()) && CommonUtil.containsRailScope(f.getLongitude(), f.getLatitude(), railVO.getRailScope(), railVO.getDrawType());
            }).count();
            if (personCount > max) {
                max = personCount;
                alarm.setLayerId(railVO.getLayerId());
                alarm.setLayerHeight(railVO.getLayerHeight());
                alarm.setRailId(railVO.getRailId());
                alarm.setRailName(railVO.getRailName());
                alarm.setRailScope(railVO.getRailScope());
                alarm.setRailHeight(railVO.getRailHeight());
                alarm.setBuildingName(railVO.getBuildingName());
                alarm.setBuildingId(railVO.getBuildingId());
            }
        }
        if (StringUtils.isEmpty(alarm.getLayerId()) && CollUtil.isNotEmpty((Collection<?>) positionCurrentList)) {
            alarm.setLayerId(positionCurrentList.get(0).getLayerId());
        }
    }
    private boolean removeInvalid(AreaAlarmTempVO areaAlarmTempVO, LocalDate nowDate) {
        if (Objects.isNull(areaAlarmTempVO.getValidBeginDate()) && Objects.isNull(areaAlarmTempVO.getValidEndDate())) {
            return false;
        }
        if (Objects.nonNull(areaAlarmTempVO.getValidBeginDate()) && Objects.nonNull(areaAlarmTempVO.getValidEndDate()) && (nowDate.compareTo((ChronoLocalDate) areaAlarmTempVO.getValidBeginDate()) < 0 || nowDate.compareTo((ChronoLocalDate) areaAlarmTempVO.getValidEndDate()) > 0)) {
            return true;
        }
        if (Objects.nonNull(areaAlarmTempVO.getValidBeginDate()) && Objects.isNull(areaAlarmTempVO.getValidEndDate()) && nowDate.compareTo((ChronoLocalDate) areaAlarmTempVO.getValidBeginDate()) < 0) {
            return true;
        }
        if (Objects.isNull(areaAlarmTempVO.getValidBeginDate()) && Objects.nonNull(areaAlarmTempVO.getValidEndDate()) && nowDate.compareTo((ChronoLocalDate) areaAlarmTempVO.getValidEndDate()) > 0) {
            return true;
        }
        return false;
    }
    private String getAlarmTypeName(String alarmType) {
        switch (alarmType) {
            case "10":
                return "进入报警";
            case "20":
                return "越界报警";
            case "30":
                return "滞留报警";
            case "40":
                return "缺员报警";
            case "50":
                return "超员报警";
            case "70":
                return "静止报警";
            case "90":
                return "一人多卡报警";
            case "96":
                return "聚集报警";
            default:
                return "";
        }
    }
    private void insertAlarm(Alarm alarm) {
        this.alarmService.insertAlarm(alarm);
    }
    private Long insertkeepAlarm50Alarm(Alarm alarm, PositionCurrent positionCurrent) {
        alarm.setCardId(positionCurrent.getCardId());
        alarm.setStaffType(positionCurrent.getStaffType());
        alarm.setContractorId(positionCurrent.getContractorId());
        alarm.setContractorName(positionCurrent.getContractorName());
        alarm.setDeptId(positionCurrent.getDeptId());
        alarm.setDeptName(positionCurrent.getDeptName());
        alarm.setPostId(positionCurrent.getPostId());
        alarm.setPostName(positionCurrent.getPostName());
        alarm.setJobNumber(positionCurrent.getJobNumber());
        alarm.setBeaconId(positionCurrent.getBeaconId());
        alarm.setLayerId(positionCurrent.getLayerId());
        alarm.setLayerHeight(positionCurrent.getLayerHeight());
        alarm.setAcceptTime(positionCurrent.getAcceptTime());
        this.alarmService.insertAlarm(alarm);
        return alarm.getAlarmId();
    }
    private void insertAreaAlarm(Alarm alarm) {
        this.alarmService.insertAreaAlarm(alarm);
    }
    private void insert50AreaAlarm(Alarm alarm) {
        this.alarmService.insert50AreaAlarm(alarm);
    }
    private void gtAlarm(String flag, String alarmType, String alarmTypeName, BigDecimal value, BigDecimal maxValue, int alarm_interval, DeviceLamps lamps, LocalDateTime now) {
        if (Objects.isNull(value)) {
            return;
        }
        if ((">".equals(flag) && value.compareTo(maxValue) > 0) || ("<".equals(flag) && value.compareTo(maxValue) < 0)) {
            lampsAlarm(alarmType, alarmTypeName, alarm_interval, lamps, now);
        }
    }
    private void lampsAlarm(String alarmType, String alarmTypeName, int alarm_interval, DeviceLamps lamps, LocalDateTime now) {
        Map<String, Object> map = new HashMap<>();
        map.put("alarmType", alarmType);
        map.put("lampsId", lamps.getLampsId());
        map.put("alarmStatus", 15);
        map.put("alarmInterval", Integer.valueOf(alarm_interval));
        if (!this.alarmLampsService.existAlarmByParams(map)) {
            CoreAlarmLamps alarmLamps = CoreAlarmLamps.builder().lampsId(lamps.getLampsId()).alarmStatus("15").layerId(lamps.getLayerId()).layerHeight(lamps.getLayerHeight()).latitude(lamps.getLatitude()).longitude(lamps.getLongitude()).acceptTime(now).createTime(now).createBy("task").alarmType(alarmType).alarmTypeName(alarmTypeName).build();
            this.alarmLampsService.save(alarmLamps);
        }
    }
    private void gtOfflineAlarm(String alarmType, String alarmTypeName, LocalDateTime time, int maxValue, int alarm_interval, DeviceLamps lamps, LocalDateTime now) {
        if (Objects.isNull(time)) {
            return;
        }
        long minutes = Duration.between(time, now).toMinutes();
        if (minutes > maxValue) {
            lampsAlarm(alarmType, alarmTypeName, alarm_interval, lamps, now);
        }
    }
    public void calcAlarmLamps() {
        List<SystemDict> dicts = this.systemDictService.findDictType("lamps_alarm_type");
        if (CollectionUtils.isEmpty(dicts)) {
            return;
        }
        List<DeviceLamps> lampsList = this.deviceLampsService.list();
        if (CollectionUtils.isEmpty(lampsList)) {
            return;
        }
        Map<String, String> dictMap = (Map) dicts.stream().collect(Collectors.toMap((v0) -> {
            return v0.getDictValue();
        }, (v0) -> {
            return v0.getDictLabel();
        }, (o, n) -> {
            return n;
        }));
        Map<String, String> configMap = RedisCacheUtils.getConfigRedisCache();
        String lamps_overvoltage = configMap.get(ConfigValue.CONFIG_KEY_LAMPS_OVERVOLTAGE);
        BigDecimal overvoltage = StringUtils.isNotBlank(lamps_overvoltage) ? new BigDecimal(lamps_overvoltage) : ConfigValue.DEFAULT_LAMPS_OVERVOLTAGE;
        String lamps_undervoltage = configMap.get(ConfigValue.CONFIG_KEY_LAMPS_UNDERVOLTAGE);
        BigDecimal undervoltage = StringUtils.isNotBlank(lamps_undervoltage) ? new BigDecimal(lamps_undervoltage) : ConfigValue.DEFAULT_LAMPS_UNDERVOLTAGE;
        String lamps_offline_minute = configMap.get(ConfigValue.CONFIG_KEY_LAMPS_OFFLINE_MINUTE);
        int offline_minute = StringUtils.isNotBlank(lamps_offline_minute) ? Integer.parseInt(lamps_offline_minute) : ConfigValue.DEFAULT_OFFLINE_MINUTE.intValue();
        String lamps_overcurrent = configMap.get(ConfigValue.CONFIG_KEY_LAMPS_OVERCURRENT);
        BigDecimal overcurrent = StringUtils.isNotBlank(lamps_overcurrent) ? new BigDecimal(lamps_overcurrent) : ConfigValue.DEFAULT_LAMPS_OVERCURRENT;
        String lamps_alarm_interval = configMap.get(ConfigValue.CONFIG_KEY_LAMPS_ALARM_INTERVAL);
        int alarm_interval = StringUtils.isNotBlank(lamps_alarm_interval) ? Integer.parseInt(lamps_alarm_interval) : ConfigValue.DEFAULT_LAMPS_ALARM_INTERVAL.intValue();
        LocalDateTime now = LocalDateTime.now();
        dictMap.forEach((alarmType, alarmTypeName) -> {
            lampsList.stream().forEach(lamps -> {
                switch (alarmType) {
                    case "10":
                        gtAlarm(">", alarmType, alarmTypeName, lamps.getLampsVoltage(), overvoltage, alarm_interval, lamps, now);
                        break;
                    case "20":
                        gtAlarm("<", alarmType, alarmTypeName, lamps.getLampsVoltage(), undervoltage, alarm_interval, lamps, now);
                        break;
                    case "30":
                        gtOfflineAlarm(alarmType, alarmTypeName, lamps.getHeartTime(), offline_minute, alarm_interval, lamps, now);
                        break;
                    case "40":
                        gtAlarm(">", alarmType, alarmTypeName, lamps.getLampsElectricity(), overcurrent, alarm_interval, lamps, now);
                        break;
                    default:
                        log.info("【灯具报警】暂不处理的报警类型:{}", alarmType);
                        break;
                }
            });
        });
    }
    @Transactional(rollbackFor = {Exception.class})
    public void calcSendCardAlarm() {
        timeoutNotReturnCard();
    }
    public void timeoutNotReturnCard() {
        String modeType = RedisCacheUtils.getConfigRedisCache().getOrDefault(ConfigValue.ALARM_RETURN_CARD, "N");
        List<AreaAlarmTempVO> tempVOList = this.areaService.selectAreaAlarmByAlarmType(AlarmType.ALARM_TYPE_80.getAlarmType());
        if (CollectionUtils.isEmpty(tempVOList)) {
            return;
        }
        LocalDate nowDate = LocalDate.now();
        tempVOList.removeIf(f -> {
            return removeInvalid(f, nowDate);
        });
        if (CollectionUtils.isEmpty(tempVOList)) {
            return;
        }
        boolean alarmReturnCard = false;
        if ("Y".equals(modeType)) {
            alarmReturnCard = true;
        }
        Map<Long, List<AreaAlarmTempVO>> areaVoMap = (Map) tempVOList.stream().collect(Collectors.groupingBy((v0) -> {
            return v0.getAreaId();
        }));
        Set<Long> closeAreaAlarmId = new HashSet<>();
        Set<Long> closeAreaAlarmPersonId = new HashSet<>();
        List<CardDispenserPerson> updateList = new ArrayList<>();
        boolean isAlarmReturnCard = alarmReturnCard;
        areaVoMap.forEach((areaId, areaVoList) -> {
            AreaAlarmTempVO areaVO = (AreaAlarmTempVO) areaVoList.get(0);
            areaVO.setDeptIds(this.areaService.findDeptIdsByAreaId(areaId));
            areaVO.setPersonIds(this.areaService.findPersonIdsByAreaId(areaId));
            areaVO.setPostIds(this.areaService.findPostIdsByAreaId(areaId));
            areaVO.setAlarmPersonIds(this.areaService.findAlarmPersonIdsByAreaId(areaId));
            List<PositionCurrent> positionCurrentList = this.positionCurrentService.selectAlarmPerson(areaVO);
            String rule = areaVO.getRule();
            if (NumberUtil.isLong(rule)) {
                Long alarmSecond = Long.valueOf(Long.parseLong(rule));
                List<CardDispenserPerson> list = this.deviceCardSenderPersonService.selectNoReturnAlarmList(alarmSecond, areaVO);
                String alarm_type_80_key = areaId + "_" + AlarmType.ALARM_TYPE_80.getAlarmType();
                Set<Long> lastPersonIds = this.timeoutNotReturnCardMap.get(alarm_type_80_key);
                HashSet hashSet = new HashSet();
                if (CollectionUtils.isEmpty(list)) {
                    closeAreaAlarmId.add(areaId);
                    return;
                }
                LocalDateTime now = LocalDateTime.now();
                Map<Long, PositionCurrent> currentMap = (Map) positionCurrentList.stream().collect(Collectors.toMap((v0) -> {
                    return v0.getPersonId();
                }, Function.identity()));
                Set<Long> currentPersonIds = (Set) list.stream().map((v0) -> {
                    return v0.getPersonId();
                }).collect(Collectors.toSet());
                if (Objects.nonNull(lastPersonIds)) {
                    hashSet.addAll(lastPersonIds);
                    if (CollUtil.isNotEmpty((Collection<?>) hashSet)) {
                        hashSet.removeAll(currentPersonIds);
                        if (CollUtil.isNotEmpty((Collection<?>) hashSet)) {
                            closeAreaAlarmPersonId.addAll(hashSet);
                        }
                    }
                    this.timeoutNotReturnCardMap.put(alarm_type_80_key, currentPersonIds);
                    for (CardDispenserPerson t : list) {
                        if (isAlarmReturnCard) {
                            t.setCardSenderType(0);
                            t.setResult("成功");
                            t.setClosedTime(now);
                            t.setRemark("报警还卡");
                            t.setPersonPhoto(null);
                            t.setIdentifyType(null);
                            t.setIdentifyTime(null);
                        }
                        t.setReturnCardStatus("超时未还卡");
                        t.setUpdateTime(now);
                        updateList.add(t);
                        Alarm alarm = new Alarm();
                        alarm.setAreaId(areaId);
                        alarm.setAlarmId(null);
                        alarm.setAreaName(areaVO.getAreaName());
                        alarm.setAcceptTime(now);
                        alarm.setCreateTime(now);
                        alarm.setCreateBy("task");
                        alarm.setAlarmType(AlarmType.ALARM_TYPE_80.getAlarmType());
                        alarm.setAlarmTypeName(AlarmType.ALARM_TYPE_80.getAlarmTypeDesc());
                        alarm.setAlarmStatus("15");
                        alarm.setCardId(t.getCardId());
                        alarm.setAlarmName(t.getRealName() + "超过" + rule + "秒 未还卡 请及时处理");
                        alarm.setRealName(t.getRealName());
                        alarm.setDeptId(t.getDeptId());
                        alarm.setDeptName(t.getDeptName());
                        alarm.setPersonId(t.getPersonId());
                        StringBuilder desc = new StringBuilder();
                        desc.append("报警类型:");
                        desc.append(AlarmType.ALARM_TYPE_80.getAlarmTypeDesc());
                        desc.append(",超时");
                        desc.append(rule).append("秒");
                        desc.append(",姓名: ");
                        desc.append(t.getRealName());
                        desc.append(",卡号:");
                        desc.append(t.getCardId());
                        desc.append(",发卡机设备号:");
                        desc.append(t.getDeviceSn());
                        desc.append(",部门:");
                        desc.append(t.getDeptName());
                        desc.append(",工号:");
                        desc.append(t.getJobNumber());
                        alarm.setAlarmDesc(desc.toString());
                        PositionCurrent current = currentMap.get(t.getPersonId());
                        if (Objects.nonNull(current)) {
                            alarm.setLongitude(current.getLongitude());
                            alarm.setLatitude(current.getLatitude());
                            alarm.setBeaconId(current.getBeaconId());
                            alarm.setLayerId(current.getLayerId());
                            alarm.setLayerHeight(current.getLayerHeight());
                            alarm.setContractorName(current.getContractorName());
                            alarm.setPostName(current.getPostName());
                        }
                        this.alarmService.insertAlarm(alarm);
                    }
                    return;
                }
                closeAreaAlarmId.add(areaId);
                this.timeoutNotReturnCardMap.put(alarm_type_80_key, currentPersonIds);
            }
        });
        if (CollUtil.isNotEmpty((Collection<?>) updateList)) {
            this.deviceCardSenderPersonService.batchUpdate(updateList);
        }
        if (isAlarmReturnCard && CollUtil.isNotEmpty((Collection<?>) updateList)) {
            List<DeviceCardSenderLog> logList = BeanUtil.copyToList(updateList, DeviceCardSenderLog.class);
            this.deviceCardSenderLogService.batchInsert(logList);
        }
        this.alarmService.closeAlarmEndTime(AlarmType.ALARM_TYPE_80.getAlarmType(), null, closeAreaAlarmId, closeAreaAlarmPersonId);
    }
    public static String getTimeDesc(Long alarmSecond) {
        StringBuilder res = new StringBuilder();
        long hours = TimeUnit.SECONDS.toHours(alarmSecond.longValue());
        if (hours > 0) {
            res.append(hours).append("小时");
            alarmSecond = Long.valueOf(alarmSecond.longValue() - TimeUnit.HOURS.toSeconds(hours));
        }
        long minutes = TimeUnit.SECONDS.toMinutes(alarmSecond.longValue());
        if (minutes > 0) {
            res.append(minutes).append("分钟");
            alarmSecond = Long.valueOf(alarmSecond.longValue() - TimeUnit.MINUTES.toSeconds(minutes));
        }
        long seconds = TimeUnit.SECONDS.toSeconds(alarmSecond.longValue());
        if (seconds > 0) {
            res.append(seconds).append("秒");
        }
        return res.toString();
    }
    public void calcPersonAlarm_81() {
        Set<Long> lastClosePersonId = this.areaAlarmPeopleMap.get("81_004");
        Set<Long> currentClosePersonId = this.alarmService.selectNoClose81AlarmEndTime();
        if (Objects.isNull(lastClosePersonId) || !SetUtils.isEqualSet(lastClosePersonId, currentClosePersonId)) {
            this.alarmService.closeAlarmEndTime("81", null, null, currentClosePersonId);
        }
        this.areaAlarmPeopleMap.put("81_004", currentClosePersonId);
    }
    /* JADX WARN: Multi-variable type inference failed */
//    public void calcPersonAlarm_82() {
//        List<T> list = ((LambdaQueryChainWrapper) this.gateRecordService.lambdaQuery().gt((v0) -> {
//            return v0.getAcceptTime();
//        }, LocalDateTime.now().minusMinutes(10L))).orderBy(true, false, (boolean) (v0) -> {
//            return v0.getAcceptTime();
//        }).list();
//        Map<Long, LocalDateTime> personOutMap = (Map) list.stream().filter(gateRecord -> {
//            return StrUtil.equals(gateRecord.getStatusId(), ConfigValue.STATUS_ID_99);
//        }).collect(Collectors.toMap((v0) -> {
//            return v0.getPersonId();
//        }, (v0) -> {
//            return v0.getAcceptTime();
//        }));
//        list.removeIf(gateRecord2 -> {
//            return StrUtil.equals(gateRecord2.getStatusId(), ConfigValue.STATUS_ID_99) || (personOutMap.containsKey(gateRecord2.getPersonId()) && gateRecord2.getAcceptTime().isBefore((ChronoLocalDateTime) personOutMap.get(gateRecord2.getPersonId())));
//        });
//        Map<Long, PositionCurrent> personPositionMap = (Map) this.positionCurrentService.selectList().stream().collect(Collectors.toMap((v0) -> {
//            return v0.getPersonId();
//        }, Function.identity()));
//        list.forEach(gateRecord3 -> {
//            String redisKey = StrUtil.format("alarm:82:{}", gateRecord3.getPersonId());
//            String gateInTime = StrUtil.toStringOrNull(this.redisService.get(redisKey));
//            if (StrUtil.isNotEmpty(gateInTime) && !gateRecord3.getAcceptTime().isAfter(LocalDateTimeUtil.parse(gateInTime, "yyyy-MM-dd HH:mm:ss"))) {
//                return;
//            }
//            this.redisService.set(redisKey, LocalDateTimeUtil.format(gateRecord3.getAcceptTime(), "yyyy-MM-dd HH:mm:ss"), 1200L);
//            if (personPositionMap.containsKey(gateRecord3.getPersonId())) {
//                return;
//            }
//            Alarm alarm = new Alarm();
//            alarm.setAcceptTime(LocalDateTime.now());
//            alarm.setAlarmType(ALARM_TYPE_82);
//            alarm.setAlarmTypeName("未带卡报警");
//            alarm.setCardId(gateRecord3.getCardId());
//            alarm.setPersonId(gateRecord3.getPersonId());
//            alarm.setRealName(gateRecord3.getRealName());
//            alarm.setAlarmName(gateRecord3.getRealName() + "触发进入二道门未带卡报警请及时处理");
//            alarm.setCreateBy("task");
//            alarm.setRailName("");
//            StringBuilder descBuilder = new StringBuilder();
//            descBuilder.append("报警类型:").append("未带卡报警").append(",姓名:").append(gateRecord3.getRealName()).append(",部门:").append(StringUtils.isNotBlank(gateRecord3.getDeptName()) ? gateRecord3.getDeptName() : "");
//            alarm.setAlarmDesc(descBuilder.toString());
//            this.alarmService.insertAlarm(alarm);
//        });
//    }
}
