package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.xrkc.core.domain.vehicle.VehicleAlarm;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
@TableName("core_alarm_lamps")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/CoreAlarmLamps.class */
public class CoreAlarmLamps implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId("id")
    private Long id;
    @TableField("lamps_id")
    private Long lampsId;
    @TableField(VehicleAlarm.ALARM_STATUS)
    private String alarmStatus;
    @TableField("accept_time")
    private LocalDateTime acceptTime;
    @TableField("layer_id")
    private String layerId;
    @TableField("layer_height")
    private Integer layerHeight;
    @TableField("longitude")
    private BigDecimal longitude;
    @TableField("latitude")
    private BigDecimal latitude;
    @TableField(VehicleAlarm.ALARM_TYPE)
    private String alarmType;
    @TableField(VehicleAlarm.ALARM_TYPE_NAME)
    private String alarmTypeName;
    @TableField(VehicleAlarm.DISPOSE_BY)
    private String disposeBy;
    @TableField(VehicleAlarm.DISPOSE_TIME)
    private LocalDateTime disposeTime;
    @TableField("dispose_remark")
    private String disposeRemark;
    @TableField("create_by")
    private String createBy;
    @TableField("create_time")
    private LocalDateTime createTime;
    @TableField("update_time")
    private LocalDateTime updateTime;
    @TableField("update_by")
    private String updateBy;
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/CoreAlarmLamps$CoreAlarmLampsBuilder.class */
    public static class CoreAlarmLampsBuilder {
        private Long id;
        private Long lampsId;
        private String alarmStatus;
        private LocalDateTime acceptTime;
        private String layerId;
        private Integer layerHeight;
        private BigDecimal longitude;
        private BigDecimal latitude;
        private String alarmType;
        private String alarmTypeName;
        private String disposeBy;
        private LocalDateTime disposeTime;
        private String disposeRemark;
        private String createBy;
        private LocalDateTime createTime;
        private LocalDateTime updateTime;
        private String updateBy;
        CoreAlarmLampsBuilder() {
        }
        public CoreAlarmLampsBuilder id(Long id) {
            this.id = id;
            return this;
        }
        public CoreAlarmLampsBuilder lampsId(Long lampsId) {
            this.lampsId = lampsId;
            return this;
        }
        public CoreAlarmLampsBuilder alarmStatus(String alarmStatus) {
            this.alarmStatus = alarmStatus;
            return this;
        }
        public CoreAlarmLampsBuilder acceptTime(LocalDateTime acceptTime) {
            this.acceptTime = acceptTime;
            return this;
        }
        public CoreAlarmLampsBuilder layerId(String layerId) {
            this.layerId = layerId;
            return this;
        }
        public CoreAlarmLampsBuilder layerHeight(Integer layerHeight) {
            this.layerHeight = layerHeight;
            return this;
        }
        public CoreAlarmLampsBuilder longitude(BigDecimal longitude) {
            this.longitude = longitude;
            return this;
        }
        public CoreAlarmLampsBuilder latitude(BigDecimal latitude) {
            this.latitude = latitude;
            return this;
        }
        public CoreAlarmLampsBuilder alarmType(String alarmType) {
            this.alarmType = alarmType;
            return this;
        }
        public CoreAlarmLampsBuilder alarmTypeName(String alarmTypeName) {
            this.alarmTypeName = alarmTypeName;
            return this;
        }
        public CoreAlarmLampsBuilder disposeBy(String disposeBy) {
            this.disposeBy = disposeBy;
            return this;
        }
        public CoreAlarmLampsBuilder disposeTime(LocalDateTime disposeTime) {
            this.disposeTime = disposeTime;
            return this;
        }
        public CoreAlarmLampsBuilder disposeRemark(String disposeRemark) {
            this.disposeRemark = disposeRemark;
            return this;
        }
        public CoreAlarmLampsBuilder createBy(String createBy) {
            this.createBy = createBy;
            return this;
        }
        public CoreAlarmLampsBuilder createTime(LocalDateTime createTime) {
            this.createTime = createTime;
            return this;
        }
        public CoreAlarmLampsBuilder updateTime(LocalDateTime updateTime) {
            this.updateTime = updateTime;
            return this;
        }
        public CoreAlarmLampsBuilder updateBy(String updateBy) {
            this.updateBy = updateBy;
            return this;
        }
        public CoreAlarmLamps build() {
            return new CoreAlarmLamps(this.id, this.lampsId, this.alarmStatus, this.acceptTime, this.layerId, this.layerHeight, this.longitude, this.latitude, this.alarmType, this.alarmTypeName, this.disposeBy, this.disposeTime, this.disposeRemark, this.createBy, this.createTime, this.updateTime, this.updateBy);
        }
        public String toString() {
            return "CoreAlarmLamps.CoreAlarmLampsBuilder(id=" + this.id + ", lampsId=" + this.lampsId + ", alarmStatus=" + this.alarmStatus + ", acceptTime=" + this.acceptTime + ", layerId=" + this.layerId + ", layerHeight=" + this.layerHeight + ", longitude=" + this.longitude + ", latitude=" + this.latitude + ", alarmType=" + this.alarmType + ", alarmTypeName=" + this.alarmTypeName + ", disposeBy=" + this.disposeBy + ", disposeTime=" + this.disposeTime + ", disposeRemark=" + this.disposeRemark + ", createBy=" + this.createBy + ", createTime=" + this.createTime + ", updateTime=" + this.updateTime + ", updateBy=" + this.updateBy + StringPool.RIGHT_BRACKET;
        }
    }
    public void setId(Long id) {
        this.id = id;
    }
    public void setLampsId(Long lampsId) {
        this.lampsId = lampsId;
    }
    public void setAlarmStatus(String alarmStatus) {
        this.alarmStatus = alarmStatus;
    }
    public void setAcceptTime(LocalDateTime acceptTime) {
        this.acceptTime = acceptTime;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
    public void setLayerHeight(Integer layerHeight) {
        this.layerHeight = layerHeight;
    }
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    public void setAlarmType(String alarmType) {
        this.alarmType = alarmType;
    }
    public void setAlarmTypeName(String alarmTypeName) {
        this.alarmTypeName = alarmTypeName;
    }
    public void setDisposeBy(String disposeBy) {
        this.disposeBy = disposeBy;
    }
    public void setDisposeTime(LocalDateTime disposeTime) {
        this.disposeTime = disposeTime;
    }
    public void setDisposeRemark(String disposeRemark) {
        this.disposeRemark = disposeRemark;
    }
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public String toString() {
        return "CoreAlarmLamps(id=" + getId() + ", lampsId=" + getLampsId() + ", alarmStatus=" + getAlarmStatus() + ", acceptTime=" + getAcceptTime() + ", layerId=" + getLayerId() + ", layerHeight=" + getLayerHeight() + ", longitude=" + getLongitude() + ", latitude=" + getLatitude() + ", alarmType=" + getAlarmType() + ", alarmTypeName=" + getAlarmTypeName() + ", disposeBy=" + getDisposeBy() + ", disposeTime=" + getDisposeTime() + ", disposeRemark=" + getDisposeRemark() + ", createBy=" + getCreateBy() + ", createTime=" + getCreateTime() + ", updateTime=" + getUpdateTime() + ", updateBy=" + getUpdateBy() + StringPool.RIGHT_BRACKET;
    }
    CoreAlarmLamps(Long id, Long lampsId, String alarmStatus, LocalDateTime acceptTime, String layerId, Integer layerHeight, BigDecimal longitude, BigDecimal latitude, String alarmType, String alarmTypeName, String disposeBy, LocalDateTime disposeTime, String disposeRemark, String createBy, LocalDateTime createTime, LocalDateTime updateTime, String updateBy) {
        this.id = id;
        this.lampsId = lampsId;
        this.alarmStatus = alarmStatus;
        this.acceptTime = acceptTime;
        this.layerId = layerId;
        this.layerHeight = layerHeight;
        this.longitude = longitude;
        this.latitude = latitude;
        this.alarmType = alarmType;
        this.alarmTypeName = alarmTypeName;
        this.disposeBy = disposeBy;
        this.disposeTime = disposeTime;
        this.disposeRemark = disposeRemark;
        this.createBy = createBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updateBy = updateBy;
    }
    public static CoreAlarmLampsBuilder builder() {
        return new CoreAlarmLampsBuilder();
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof CoreAlarmLamps)) {
            return false;
        }
        CoreAlarmLamps other = (CoreAlarmLamps) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$id = getId();
        Object other$id = other.getId();
        if (this$id == null) {
            if (other$id != null) {
                return false;
            }
        } else if (!this$id.equals(other$id)) {
            return false;
        }
        Object this$lampsId = getLampsId();
        Object other$lampsId = other.getLampsId();
        if (this$lampsId == null) {
            if (other$lampsId != null) {
                return false;
            }
        } else if (!this$lampsId.equals(other$lampsId)) {
            return false;
        }
        Object this$layerHeight = getLayerHeight();
        Object other$layerHeight = other.getLayerHeight();
        if (this$layerHeight == null) {
            if (other$layerHeight != null) {
                return false;
            }
        } else if (!this$layerHeight.equals(other$layerHeight)) {
            return false;
        }
        Object this$alarmStatus = getAlarmStatus();
        Object other$alarmStatus = other.getAlarmStatus();
        if (this$alarmStatus == null) {
            if (other$alarmStatus != null) {
                return false;
            }
        } else if (!this$alarmStatus.equals(other$alarmStatus)) {
            return false;
        }
        Object this$acceptTime = getAcceptTime();
        Object other$acceptTime = other.getAcceptTime();
        if (this$acceptTime == null) {
            if (other$acceptTime != null) {
                return false;
            }
        } else if (!this$acceptTime.equals(other$acceptTime)) {
            return false;
        }
        Object this$layerId = getLayerId();
        Object other$layerId = other.getLayerId();
        if (this$layerId == null) {
            if (other$layerId != null) {
                return false;
            }
        } else if (!this$layerId.equals(other$layerId)) {
            return false;
        }
        Object this$longitude = getLongitude();
        Object other$longitude = other.getLongitude();
        if (this$longitude == null) {
            if (other$longitude != null) {
                return false;
            }
        } else if (!this$longitude.equals(other$longitude)) {
            return false;
        }
        Object this$latitude = getLatitude();
        Object other$latitude = other.getLatitude();
        if (this$latitude == null) {
            if (other$latitude != null) {
                return false;
            }
        } else if (!this$latitude.equals(other$latitude)) {
            return false;
        }
        Object this$alarmType = getAlarmType();
        Object other$alarmType = other.getAlarmType();
        if (this$alarmType == null) {
            if (other$alarmType != null) {
                return false;
            }
        } else if (!this$alarmType.equals(other$alarmType)) {
            return false;
        }
        Object this$alarmTypeName = getAlarmTypeName();
        Object other$alarmTypeName = other.getAlarmTypeName();
        if (this$alarmTypeName == null) {
            if (other$alarmTypeName != null) {
                return false;
            }
        } else if (!this$alarmTypeName.equals(other$alarmTypeName)) {
            return false;
        }
        Object this$disposeBy = getDisposeBy();
        Object other$disposeBy = other.getDisposeBy();
        if (this$disposeBy == null) {
            if (other$disposeBy != null) {
                return false;
            }
        } else if (!this$disposeBy.equals(other$disposeBy)) {
            return false;
        }
        Object this$disposeTime = getDisposeTime();
        Object other$disposeTime = other.getDisposeTime();
        if (this$disposeTime == null) {
            if (other$disposeTime != null) {
                return false;
            }
        } else if (!this$disposeTime.equals(other$disposeTime)) {
            return false;
        }
        Object this$disposeRemark = getDisposeRemark();
        Object other$disposeRemark = other.getDisposeRemark();
        if (this$disposeRemark == null) {
            if (other$disposeRemark != null) {
                return false;
            }
        } else if (!this$disposeRemark.equals(other$disposeRemark)) {
            return false;
        }
        Object this$createBy = getCreateBy();
        Object other$createBy = other.getCreateBy();
        if (this$createBy == null) {
            if (other$createBy != null) {
                return false;
            }
        } else if (!this$createBy.equals(other$createBy)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        if (this$createTime == null) {
            if (other$createTime != null) {
                return false;
            }
        } else if (!this$createTime.equals(other$createTime)) {
            return false;
        }
        Object this$updateTime = getUpdateTime();
        Object other$updateTime = other.getUpdateTime();
        if (this$updateTime == null) {
            if (other$updateTime != null) {
                return false;
            }
        } else if (!this$updateTime.equals(other$updateTime)) {
            return false;
        }
        Object this$updateBy = getUpdateBy();
        Object other$updateBy = other.getUpdateBy();
        return this$updateBy == null ? other$updateBy == null : this$updateBy.equals(other$updateBy);
    }
    protected boolean canEqual(Object other) {
        return other instanceof CoreAlarmLamps;
    }
    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $lampsId = getLampsId();
        int result2 = (result * 59) + ($lampsId == null ? 43 : $lampsId.hashCode());
        Object $layerHeight = getLayerHeight();
        int result3 = (result2 * 59) + ($layerHeight == null ? 43 : $layerHeight.hashCode());
        Object $alarmStatus = getAlarmStatus();
        int result4 = (result3 * 59) + ($alarmStatus == null ? 43 : $alarmStatus.hashCode());
        Object $acceptTime = getAcceptTime();
        int result5 = (result4 * 59) + ($acceptTime == null ? 43 : $acceptTime.hashCode());
        Object $layerId = getLayerId();
        int result6 = (result5 * 59) + ($layerId == null ? 43 : $layerId.hashCode());
        Object $longitude = getLongitude();
        int result7 = (result6 * 59) + ($longitude == null ? 43 : $longitude.hashCode());
        Object $latitude = getLatitude();
        int result8 = (result7 * 59) + ($latitude == null ? 43 : $latitude.hashCode());
        Object $alarmType = getAlarmType();
        int result9 = (result8 * 59) + ($alarmType == null ? 43 : $alarmType.hashCode());
        Object $alarmTypeName = getAlarmTypeName();
        int result10 = (result9 * 59) + ($alarmTypeName == null ? 43 : $alarmTypeName.hashCode());
        Object $disposeBy = getDisposeBy();
        int result11 = (result10 * 59) + ($disposeBy == null ? 43 : $disposeBy.hashCode());
        Object $disposeTime = getDisposeTime();
        int result12 = (result11 * 59) + ($disposeTime == null ? 43 : $disposeTime.hashCode());
        Object $disposeRemark = getDisposeRemark();
        int result13 = (result12 * 59) + ($disposeRemark == null ? 43 : $disposeRemark.hashCode());
        Object $createBy = getCreateBy();
        int result14 = (result13 * 59) + ($createBy == null ? 43 : $createBy.hashCode());
        Object $createTime = getCreateTime();
        int result15 = (result14 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $updateTime = getUpdateTime();
        int result16 = (result15 * 59) + ($updateTime == null ? 43 : $updateTime.hashCode());
        Object $updateBy = getUpdateBy();
        return (result16 * 59) + ($updateBy == null ? 43 : $updateBy.hashCode());
    }
    public Long getId() {
        return this.id;
    }
    public Long getLampsId() {
        return this.lampsId;
    }
    public String getAlarmStatus() {
        return this.alarmStatus;
    }
    public LocalDateTime getAcceptTime() {
        return this.acceptTime;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public Integer getLayerHeight() {
        return this.layerHeight;
    }
    public BigDecimal getLongitude() {
        return this.longitude;
    }
    public BigDecimal getLatitude() {
        return this.latitude;
    }
    public String getAlarmType() {
        return this.alarmType;
    }
    public String getAlarmTypeName() {
        return this.alarmTypeName;
    }
    public String getDisposeBy() {
        return this.disposeBy;
    }
    public LocalDateTime getDisposeTime() {
        return this.disposeTime;
    }
    public String getDisposeRemark() {
        return this.disposeRemark;
    }
    public String getCreateBy() {
        return this.createBy;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public LocalDateTime getUpdateTime() {
        return this.updateTime;
    }
    public String getUpdateBy() {
        return this.updateBy;
    }
}
