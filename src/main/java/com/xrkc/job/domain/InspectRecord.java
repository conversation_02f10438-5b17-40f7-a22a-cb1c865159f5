package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/InspectRecord.class */
public class InspectRecord implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId("record_id")
    private Long recordId;
    private Long roadId;
    private String roadName;
    private Long roadLeaderId;
    private String roadLeader;
    private String frequencyType;
    private String frequencyVal;
    private LocalTime validBeginTime;
    private LocalTime validEndTime;
    private LocalDateTime inspectBeginTime;
    private LocalDateTime inspectEndTime;
    private String recordStatus;
    private Integer roadLocationCount;
    private Integer finishLocationCount;
    private String inspectStatus;
    private Long typeId;
    private String typeName;
    private String way;
    private String content;
    private String inspectPersonSignature;
    private Long teamId;
    private String teamName;
    private String teamLeader;
    private Integer abnormalLocationCount;
    public Long getRecordId() {
        return this.recordId;
    }
    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }
    public Long getRoadId() {
        return this.roadId;
    }
    public void setRoadId(Long roadId) {
        this.roadId = roadId;
    }
    public String getRoadName() {
        return this.roadName;
    }
    public void setRoadName(String roadName) {
        this.roadName = roadName;
    }
    public String getFrequencyType() {
        return this.frequencyType;
    }
    public void setFrequencyType(String frequencyType) {
        this.frequencyType = frequencyType;
    }
    public String getFrequencyVal() {
        return this.frequencyVal;
    }
    public void setFrequencyVal(String frequencyVal) {
        this.frequencyVal = frequencyVal;
    }
    public Long getTypeId() {
        return this.typeId;
    }
    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }
    public String getTypeName() {
        return this.typeName;
    }
    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }
    public LocalDateTime getInspectBeginTime() {
        return this.inspectBeginTime;
    }
    public void setInspectBeginTime(LocalDateTime inspectBeginTime) {
        this.inspectBeginTime = inspectBeginTime;
    }
    public LocalDateTime getInspectEndTime() {
        return this.inspectEndTime;
    }
    public void setInspectEndTime(LocalDateTime inspectEndTime) {
        this.inspectEndTime = inspectEndTime;
    }
    public String getRecordStatus() {
        return this.recordStatus;
    }
    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
    }
    public Integer getRoadLocationCount() {
        return this.roadLocationCount;
    }
    public void setRoadLocationCount(Integer roadLocationCount) {
        this.roadLocationCount = roadLocationCount;
    }
    public String getWay() {
        return this.way;
    }
    public void setWay(String way) {
        this.way = way;
    }
    public String getContent() {
        return this.content;
    }
    public void setContent(String content) {
        this.content = content;
    }
    public Long getTeamId() {
        return this.teamId;
    }
    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }
    public String getTeamName() {
        return this.teamName;
    }
    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }
    public String getTeamLeader() {
        return this.teamLeader;
    }
    public void setTeamLeader(String teamLeader) {
        this.teamLeader = teamLeader;
    }
    public String getRoadLeader() {
        return this.roadLeader;
    }
    public void setRoadLeader(String roadLeader) {
        this.roadLeader = roadLeader;
    }
    public Integer getFinishLocationCount() {
        return this.finishLocationCount;
    }
    public void setFinishLocationCount(Integer finishLocationCount) {
        this.finishLocationCount = finishLocationCount;
    }
    public LocalTime getValidBeginTime() {
        return this.validBeginTime;
    }
    public void setValidBeginTime(LocalTime validBeginTime) {
        this.validBeginTime = validBeginTime;
    }
    public LocalTime getValidEndTime() {
        return this.validEndTime;
    }
    public void setValidEndTime(LocalTime validEndTime) {
        this.validEndTime = validEndTime;
    }
    public String getInspectStatus() {
        return this.inspectStatus;
    }
    public void setInspectStatus(String inspectStatus) {
        this.inspectStatus = inspectStatus;
    }
    public Long getRoadLeaderId() {
        return this.roadLeaderId;
    }
    public void setRoadLeaderId(Long roadLeaderId) {
        this.roadLeaderId = roadLeaderId;
    }
    public Integer getAbnormalLocationCount() {
        return this.abnormalLocationCount;
    }
    public void setAbnormalLocationCount(Integer abnormalLocationCount) {
        this.abnormalLocationCount = abnormalLocationCount;
    }
    public String getInspectPersonSignature() {
        return this.inspectPersonSignature;
    }
    public void setInspectPersonSignature(String inspectPersonSignature) {
        this.inspectPersonSignature = inspectPersonSignature;
    }
}
