package com.xrkc.job.mapper;
import com.xrkc.job.domain.FacilityAccessRecord;
import com.xrkc.job.domain.StatisticsFacilityStay;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Param;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/StatisticsFacilityStayMapper.class */
public interface StatisticsFacilityStayMapper {
    int insert(StatisticsFacilityStay statisticsFacilityStay);
    List<FacilityAccessRecord> selectList(@Param("minusMinutes") LocalDateTime localDateTime);
    int updateDisposeStatus(@Param("personId") Long l, @Param("acceptTime") LocalDateTime localDateTime, @Param("facilityId") Long l2);
    Long findDelMaxId(@Param("minusMinutes") LocalDateTime localDateTime);
    int delByMaxId(@Param("maxId") Long l, @Param("delRow") int i);
    int delByDisposeStatus();
}
