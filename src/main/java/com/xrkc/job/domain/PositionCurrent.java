package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
@TableName("position_current")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/PositionCurrent.class */
public class PositionCurrent implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId
    private Long id;
    @TableField(exist = false)
    private Long positionId;
    private LocalDateTime acceptTime;
    private Integer beaconId;
    private Double distance;
    @TableField(exist = false)
    private Double centralPointDistance;
    private Long cardId;
    private String cardType;
    @TableField(exist = false)
    private String cardTypeName;
    @TableField(exist = false)
    private Integer cardPower;
    private BigDecimal longitude;
    private BigDecimal latitude;
    private String layerId;
    private Integer layerHeight;
    @TableField(exist = false)
    private String layerName;
    @TableField(exist = false)
    private Double layerMoveSpeed;
    private LocalDateTime createTime;
    private Long personId;
    private String personType;
    @TableField(exist = false)
    private String personTypeName;
    @TableField(exist = false)
    private String personCategory;
    private String personAttribute;
    private String staffType;
    private String realName;
    @TableField(exist = false)
    private String sex;
    private Long deptId;
    private Long postId;
    @TableField(exist = false)
    private Integer undisposedAlarmCount;
    private String personPhoto;
    @TableField(exist = false)
    private String idNumber;
    private String deptName;
    @TableField(exist = false)
    private String cardStatus;
    private String postName;
    @TableField(exist = false)
    private String phone;
    private String jobNumber;
    private Long contractorId;
    private String contractorName;
    private Integer stillStatus;
    @TableField(exist = false)
    private Boolean inRailScope;
    private Long pressure;
    @TableField(exist = false)
    private String visitReason;
    @TableField(exist = false)
    private String visitLocation;
    @TableField(exist = false)
    private String receiveLeader;
    @TableField(exist = false)
    private String companyName;
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/PositionCurrent$PositionCurrentBuilder.class */
    public static class PositionCurrentBuilder {
        private Long id;
        private Long positionId;
        private LocalDateTime acceptTime;
        private Integer beaconId;
        private Double distance;
        private Double centralPointDistance;
        private Long cardId;
        private String cardType;
        private String cardTypeName;
        private Integer cardPower;
        private BigDecimal longitude;
        private BigDecimal latitude;
        private String layerId;
        private Integer layerHeight;
        private String layerName;
        private Double layerMoveSpeed;
        private LocalDateTime createTime;
        private Long personId;
        private String personType;
        private String personTypeName;
        private String personCategory;
        private String personAttribute;
        private String staffType;
        private String realName;
        private String sex;
        private Long deptId;
        private Long postId;
        private Integer undisposedAlarmCount;
        private String personPhoto;
        private String idNumber;
        private String deptName;
        private String cardStatus;
        private String postName;
        private String phone;
        private String jobNumber;
        private Long contractorId;
        private String contractorName;
        private Integer stillStatus;
        private Boolean inRailScope;
        private Long pressure;
        private String visitReason;
        private String visitLocation;
        private String receiveLeader;
        private String companyName;
        PositionCurrentBuilder() {
        }
        public PositionCurrentBuilder id(Long id) {
            this.id = id;
            return this;
        }
        public PositionCurrentBuilder positionId(Long positionId) {
            this.positionId = positionId;
            return this;
        }
        public PositionCurrentBuilder acceptTime(LocalDateTime acceptTime) {
            this.acceptTime = acceptTime;
            return this;
        }
        public PositionCurrentBuilder beaconId(Integer beaconId) {
            this.beaconId = beaconId;
            return this;
        }
        public PositionCurrentBuilder distance(Double distance) {
            this.distance = distance;
            return this;
        }
        public PositionCurrentBuilder centralPointDistance(Double centralPointDistance) {
            this.centralPointDistance = centralPointDistance;
            return this;
        }
        public PositionCurrentBuilder cardId(Long cardId) {
            this.cardId = cardId;
            return this;
        }
        public PositionCurrentBuilder cardType(String cardType) {
            this.cardType = cardType;
            return this;
        }
        public PositionCurrentBuilder cardTypeName(String cardTypeName) {
            this.cardTypeName = cardTypeName;
            return this;
        }
        public PositionCurrentBuilder cardPower(Integer cardPower) {
            this.cardPower = cardPower;
            return this;
        }
        public PositionCurrentBuilder longitude(BigDecimal longitude) {
            this.longitude = longitude;
            return this;
        }
        public PositionCurrentBuilder latitude(BigDecimal latitude) {
            this.latitude = latitude;
            return this;
        }
        public PositionCurrentBuilder layerId(String layerId) {
            this.layerId = layerId;
            return this;
        }
        public PositionCurrentBuilder layerHeight(Integer layerHeight) {
            this.layerHeight = layerHeight;
            return this;
        }
        public PositionCurrentBuilder layerName(String layerName) {
            this.layerName = layerName;
            return this;
        }
        public PositionCurrentBuilder layerMoveSpeed(Double layerMoveSpeed) {
            this.layerMoveSpeed = layerMoveSpeed;
            return this;
        }
        public PositionCurrentBuilder createTime(LocalDateTime createTime) {
            this.createTime = createTime;
            return this;
        }
        public PositionCurrentBuilder personId(Long personId) {
            this.personId = personId;
            return this;
        }
        public PositionCurrentBuilder personType(String personType) {
            this.personType = personType;
            return this;
        }
        public PositionCurrentBuilder personTypeName(String personTypeName) {
            this.personTypeName = personTypeName;
            return this;
        }
        public PositionCurrentBuilder personCategory(String personCategory) {
            this.personCategory = personCategory;
            return this;
        }
        public PositionCurrentBuilder personAttribute(String personAttribute) {
            this.personAttribute = personAttribute;
            return this;
        }
        public PositionCurrentBuilder staffType(String staffType) {
            this.staffType = staffType;
            return this;
        }
        public PositionCurrentBuilder realName(String realName) {
            this.realName = realName;
            return this;
        }
        public PositionCurrentBuilder sex(String sex) {
            this.sex = sex;
            return this;
        }
        public PositionCurrentBuilder deptId(Long deptId) {
            this.deptId = deptId;
            return this;
        }
        public PositionCurrentBuilder postId(Long postId) {
            this.postId = postId;
            return this;
        }
        public PositionCurrentBuilder undisposedAlarmCount(Integer undisposedAlarmCount) {
            this.undisposedAlarmCount = undisposedAlarmCount;
            return this;
        }
        public PositionCurrentBuilder personPhoto(String personPhoto) {
            this.personPhoto = personPhoto;
            return this;
        }
        public PositionCurrentBuilder idNumber(String idNumber) {
            this.idNumber = idNumber;
            return this;
        }
        public PositionCurrentBuilder deptName(String deptName) {
            this.deptName = deptName;
            return this;
        }
        public PositionCurrentBuilder cardStatus(String cardStatus) {
            this.cardStatus = cardStatus;
            return this;
        }
        public PositionCurrentBuilder postName(String postName) {
            this.postName = postName;
            return this;
        }
        public PositionCurrentBuilder phone(String phone) {
            this.phone = phone;
            return this;
        }
        public PositionCurrentBuilder jobNumber(String jobNumber) {
            this.jobNumber = jobNumber;
            return this;
        }
        public PositionCurrentBuilder contractorId(Long contractorId) {
            this.contractorId = contractorId;
            return this;
        }
        public PositionCurrentBuilder contractorName(String contractorName) {
            this.contractorName = contractorName;
            return this;
        }
        public PositionCurrentBuilder stillStatus(Integer stillStatus) {
            this.stillStatus = stillStatus;
            return this;
        }
        public PositionCurrentBuilder inRailScope(Boolean inRailScope) {
            this.inRailScope = inRailScope;
            return this;
        }
        public PositionCurrentBuilder pressure(Long pressure) {
            this.pressure = pressure;
            return this;
        }
        public PositionCurrentBuilder visitReason(String visitReason) {
            this.visitReason = visitReason;
            return this;
        }
        public PositionCurrentBuilder visitLocation(String visitLocation) {
            this.visitLocation = visitLocation;
            return this;
        }
        public PositionCurrentBuilder receiveLeader(String receiveLeader) {
            this.receiveLeader = receiveLeader;
            return this;
        }
        public PositionCurrentBuilder companyName(String companyName) {
            this.companyName = companyName;
            return this;
        }
        public PositionCurrent build() {
            return new PositionCurrent(this.id, this.positionId, this.acceptTime, this.beaconId, this.distance, this.centralPointDistance, this.cardId, this.cardType, this.cardTypeName, this.cardPower, this.longitude, this.latitude, this.layerId, this.layerHeight, this.layerName, this.layerMoveSpeed, this.createTime, this.personId, this.personType, this.personTypeName, this.personCategory, this.personAttribute, this.staffType, this.realName, this.sex, this.deptId, this.postId, this.undisposedAlarmCount, this.personPhoto, this.idNumber, this.deptName, this.cardStatus, this.postName, this.phone, this.jobNumber, this.contractorId, this.contractorName, this.stillStatus, this.inRailScope, this.pressure, this.visitReason, this.visitLocation, this.receiveLeader, this.companyName);
        }
        public String toString() {
            return "PositionCurrent.PositionCurrentBuilder(id=" + this.id + ", positionId=" + this.positionId + ", acceptTime=" + this.acceptTime + ", beaconId=" + this.beaconId + ", distance=" + this.distance + ", centralPointDistance=" + this.centralPointDistance + ", cardId=" + this.cardId + ", cardType=" + this.cardType + ", cardTypeName=" + this.cardTypeName + ", cardPower=" + this.cardPower + ", longitude=" + this.longitude + ", latitude=" + this.latitude + ", layerId=" + this.layerId + ", layerHeight=" + this.layerHeight + ", layerName=" + this.layerName + ", layerMoveSpeed=" + this.layerMoveSpeed + ", createTime=" + this.createTime + ", personId=" + this.personId + ", personType=" + this.personType + ", personTypeName=" + this.personTypeName + ", personCategory=" + this.personCategory + ", personAttribute=" + this.personAttribute + ", staffType=" + this.staffType + ", realName=" + this.realName + ", sex=" + this.sex + ", deptId=" + this.deptId + ", postId=" + this.postId + ", undisposedAlarmCount=" + this.undisposedAlarmCount + ", personPhoto=" + this.personPhoto + ", idNumber=" + this.idNumber + ", deptName=" + this.deptName + ", cardStatus=" + this.cardStatus + ", postName=" + this.postName + ", phone=" + this.phone + ", jobNumber=" + this.jobNumber + ", contractorId=" + this.contractorId + ", contractorName=" + this.contractorName + ", stillStatus=" + this.stillStatus + ", inRailScope=" + this.inRailScope + ", pressure=" + this.pressure + ", visitReason=" + this.visitReason + ", visitLocation=" + this.visitLocation + ", receiveLeader=" + this.receiveLeader + ", companyName=" + this.companyName + StringPool.RIGHT_BRACKET;
        }
    }
    public void setId(Long id) {
        this.id = id;
    }
    public void setPositionId(Long positionId) {
        this.positionId = positionId;
    }
    public void setAcceptTime(LocalDateTime acceptTime) {
        this.acceptTime = acceptTime;
    }
    public void setBeaconId(Integer beaconId) {
        this.beaconId = beaconId;
    }
    public void setDistance(Double distance) {
        this.distance = distance;
    }
    public void setCentralPointDistance(Double centralPointDistance) {
        this.centralPointDistance = centralPointDistance;
    }
    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }
    public void setCardType(String cardType) {
        this.cardType = cardType;
    }
    public void setCardTypeName(String cardTypeName) {
        this.cardTypeName = cardTypeName;
    }
    public void setCardPower(Integer cardPower) {
        this.cardPower = cardPower;
    }
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
    public void setLayerHeight(Integer layerHeight) {
        this.layerHeight = layerHeight;
    }
    public void setLayerName(String layerName) {
        this.layerName = layerName;
    }
    public void setLayerMoveSpeed(Double layerMoveSpeed) {
        this.layerMoveSpeed = layerMoveSpeed;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public void setPersonId(Long personId) {
        this.personId = personId;
    }
    public void setPersonType(String personType) {
        this.personType = personType;
    }
    public void setPersonTypeName(String personTypeName) {
        this.personTypeName = personTypeName;
    }
    public void setPersonCategory(String personCategory) {
        this.personCategory = personCategory;
    }
    public void setPersonAttribute(String personAttribute) {
        this.personAttribute = personAttribute;
    }
    public void setStaffType(String staffType) {
        this.staffType = staffType;
    }
    public void setRealName(String realName) {
        this.realName = realName;
    }
    public void setSex(String sex) {
        this.sex = sex;
    }
    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
    public void setPostId(Long postId) {
        this.postId = postId;
    }
    public void setUndisposedAlarmCount(Integer undisposedAlarmCount) {
        this.undisposedAlarmCount = undisposedAlarmCount;
    }
    public void setPersonPhoto(String personPhoto) {
        this.personPhoto = personPhoto;
    }
    public void setIdNumber(String idNumber) {
        this.idNumber = idNumber;
    }
    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    public void setCardStatus(String cardStatus) {
        this.cardStatus = cardStatus;
    }
    public void setPostName(String postName) {
        this.postName = postName;
    }
    public void setPhone(String phone) {
        this.phone = phone;
    }
    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }
    public void setContractorId(Long contractorId) {
        this.contractorId = contractorId;
    }
    public void setContractorName(String contractorName) {
        this.contractorName = contractorName;
    }
    public void setStillStatus(Integer stillStatus) {
        this.stillStatus = stillStatus;
    }
    public void setInRailScope(Boolean inRailScope) {
        this.inRailScope = inRailScope;
    }
    public void setPressure(Long pressure) {
        this.pressure = pressure;
    }
    public void setVisitReason(String visitReason) {
        this.visitReason = visitReason;
    }
    public void setVisitLocation(String visitLocation) {
        this.visitLocation = visitLocation;
    }
    public void setReceiveLeader(String receiveLeader) {
        this.receiveLeader = receiveLeader;
    }
    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof PositionCurrent)) {
            return false;
        }
        PositionCurrent other = (PositionCurrent) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$id = getId();
        Object other$id = other.getId();
        if (this$id == null) {
            if (other$id != null) {
                return false;
            }
        } else if (!this$id.equals(other$id)) {
            return false;
        }
        Object this$positionId = getPositionId();
        Object other$positionId = other.getPositionId();
        if (this$positionId == null) {
            if (other$positionId != null) {
                return false;
            }
        } else if (!this$positionId.equals(other$positionId)) {
            return false;
        }
        Object this$beaconId = getBeaconId();
        Object other$beaconId = other.getBeaconId();
        if (this$beaconId == null) {
            if (other$beaconId != null) {
                return false;
            }
        } else if (!this$beaconId.equals(other$beaconId)) {
            return false;
        }
        Object this$distance = getDistance();
        Object other$distance = other.getDistance();
        if (this$distance == null) {
            if (other$distance != null) {
                return false;
            }
        } else if (!this$distance.equals(other$distance)) {
            return false;
        }
        Object this$centralPointDistance = getCentralPointDistance();
        Object other$centralPointDistance = other.getCentralPointDistance();
        if (this$centralPointDistance == null) {
            if (other$centralPointDistance != null) {
                return false;
            }
        } else if (!this$centralPointDistance.equals(other$centralPointDistance)) {
            return false;
        }
        Object this$cardId = getCardId();
        Object other$cardId = other.getCardId();
        if (this$cardId == null) {
            if (other$cardId != null) {
                return false;
            }
        } else if (!this$cardId.equals(other$cardId)) {
            return false;
        }
        Object this$cardPower = getCardPower();
        Object other$cardPower = other.getCardPower();
        if (this$cardPower == null) {
            if (other$cardPower != null) {
                return false;
            }
        } else if (!this$cardPower.equals(other$cardPower)) {
            return false;
        }
        Object this$layerHeight = getLayerHeight();
        Object other$layerHeight = other.getLayerHeight();
        if (this$layerHeight == null) {
            if (other$layerHeight != null) {
                return false;
            }
        } else if (!this$layerHeight.equals(other$layerHeight)) {
            return false;
        }
        Object this$layerMoveSpeed = getLayerMoveSpeed();
        Object other$layerMoveSpeed = other.getLayerMoveSpeed();
        if (this$layerMoveSpeed == null) {
            if (other$layerMoveSpeed != null) {
                return false;
            }
        } else if (!this$layerMoveSpeed.equals(other$layerMoveSpeed)) {
            return false;
        }
        Object this$personId = getPersonId();
        Object other$personId = other.getPersonId();
        if (this$personId == null) {
            if (other$personId != null) {
                return false;
            }
        } else if (!this$personId.equals(other$personId)) {
            return false;
        }
        Object this$deptId = getDeptId();
        Object other$deptId = other.getDeptId();
        if (this$deptId == null) {
            if (other$deptId != null) {
                return false;
            }
        } else if (!this$deptId.equals(other$deptId)) {
            return false;
        }
        Object this$postId = getPostId();
        Object other$postId = other.getPostId();
        if (this$postId == null) {
            if (other$postId != null) {
                return false;
            }
        } else if (!this$postId.equals(other$postId)) {
            return false;
        }
        Object this$undisposedAlarmCount = getUndisposedAlarmCount();
        Object other$undisposedAlarmCount = other.getUndisposedAlarmCount();
        if (this$undisposedAlarmCount == null) {
            if (other$undisposedAlarmCount != null) {
                return false;
            }
        } else if (!this$undisposedAlarmCount.equals(other$undisposedAlarmCount)) {
            return false;
        }
        Object this$contractorId = getContractorId();
        Object other$contractorId = other.getContractorId();
        if (this$contractorId == null) {
            if (other$contractorId != null) {
                return false;
            }
        } else if (!this$contractorId.equals(other$contractorId)) {
            return false;
        }
        Object this$stillStatus = getStillStatus();
        Object other$stillStatus = other.getStillStatus();
        if (this$stillStatus == null) {
            if (other$stillStatus != null) {
                return false;
            }
        } else if (!this$stillStatus.equals(other$stillStatus)) {
            return false;
        }
        Object this$inRailScope = getInRailScope();
        Object other$inRailScope = other.getInRailScope();
        if (this$inRailScope == null) {
            if (other$inRailScope != null) {
                return false;
            }
        } else if (!this$inRailScope.equals(other$inRailScope)) {
            return false;
        }
        Object this$pressure = getPressure();
        Object other$pressure = other.getPressure();
        if (this$pressure == null) {
            if (other$pressure != null) {
                return false;
            }
        } else if (!this$pressure.equals(other$pressure)) {
            return false;
        }
        Object this$acceptTime = getAcceptTime();
        Object other$acceptTime = other.getAcceptTime();
        if (this$acceptTime == null) {
            if (other$acceptTime != null) {
                return false;
            }
        } else if (!this$acceptTime.equals(other$acceptTime)) {
            return false;
        }
        Object this$cardType = getCardType();
        Object other$cardType = other.getCardType();
        if (this$cardType == null) {
            if (other$cardType != null) {
                return false;
            }
        } else if (!this$cardType.equals(other$cardType)) {
            return false;
        }
        Object this$cardTypeName = getCardTypeName();
        Object other$cardTypeName = other.getCardTypeName();
        if (this$cardTypeName == null) {
            if (other$cardTypeName != null) {
                return false;
            }
        } else if (!this$cardTypeName.equals(other$cardTypeName)) {
            return false;
        }
        Object this$longitude = getLongitude();
        Object other$longitude = other.getLongitude();
        if (this$longitude == null) {
            if (other$longitude != null) {
                return false;
            }
        } else if (!this$longitude.equals(other$longitude)) {
            return false;
        }
        Object this$latitude = getLatitude();
        Object other$latitude = other.getLatitude();
        if (this$latitude == null) {
            if (other$latitude != null) {
                return false;
            }
        } else if (!this$latitude.equals(other$latitude)) {
            return false;
        }
        Object this$layerId = getLayerId();
        Object other$layerId = other.getLayerId();
        if (this$layerId == null) {
            if (other$layerId != null) {
                return false;
            }
        } else if (!this$layerId.equals(other$layerId)) {
            return false;
        }
        Object this$layerName = getLayerName();
        Object other$layerName = other.getLayerName();
        if (this$layerName == null) {
            if (other$layerName != null) {
                return false;
            }
        } else if (!this$layerName.equals(other$layerName)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        if (this$createTime == null) {
            if (other$createTime != null) {
                return false;
            }
        } else if (!this$createTime.equals(other$createTime)) {
            return false;
        }
        Object this$personType = getPersonType();
        Object other$personType = other.getPersonType();
        if (this$personType == null) {
            if (other$personType != null) {
                return false;
            }
        } else if (!this$personType.equals(other$personType)) {
            return false;
        }
        Object this$personTypeName = getPersonTypeName();
        Object other$personTypeName = other.getPersonTypeName();
        if (this$personTypeName == null) {
            if (other$personTypeName != null) {
                return false;
            }
        } else if (!this$personTypeName.equals(other$personTypeName)) {
            return false;
        }
        Object this$personCategory = getPersonCategory();
        Object other$personCategory = other.getPersonCategory();
        if (this$personCategory == null) {
            if (other$personCategory != null) {
                return false;
            }
        } else if (!this$personCategory.equals(other$personCategory)) {
            return false;
        }
        Object this$personAttribute = getPersonAttribute();
        Object other$personAttribute = other.getPersonAttribute();
        if (this$personAttribute == null) {
            if (other$personAttribute != null) {
                return false;
            }
        } else if (!this$personAttribute.equals(other$personAttribute)) {
            return false;
        }
        Object this$staffType = getStaffType();
        Object other$staffType = other.getStaffType();
        if (this$staffType == null) {
            if (other$staffType != null) {
                return false;
            }
        } else if (!this$staffType.equals(other$staffType)) {
            return false;
        }
        Object this$realName = getRealName();
        Object other$realName = other.getRealName();
        if (this$realName == null) {
            if (other$realName != null) {
                return false;
            }
        } else if (!this$realName.equals(other$realName)) {
            return false;
        }
        Object this$sex = getSex();
        Object other$sex = other.getSex();
        if (this$sex == null) {
            if (other$sex != null) {
                return false;
            }
        } else if (!this$sex.equals(other$sex)) {
            return false;
        }
        Object this$personPhoto = getPersonPhoto();
        Object other$personPhoto = other.getPersonPhoto();
        if (this$personPhoto == null) {
            if (other$personPhoto != null) {
                return false;
            }
        } else if (!this$personPhoto.equals(other$personPhoto)) {
            return false;
        }
        Object this$idNumber = getIdNumber();
        Object other$idNumber = other.getIdNumber();
        if (this$idNumber == null) {
            if (other$idNumber != null) {
                return false;
            }
        } else if (!this$idNumber.equals(other$idNumber)) {
            return false;
        }
        Object this$deptName = getDeptName();
        Object other$deptName = other.getDeptName();
        if (this$deptName == null) {
            if (other$deptName != null) {
                return false;
            }
        } else if (!this$deptName.equals(other$deptName)) {
            return false;
        }
        Object this$cardStatus = getCardStatus();
        Object other$cardStatus = other.getCardStatus();
        if (this$cardStatus == null) {
            if (other$cardStatus != null) {
                return false;
            }
        } else if (!this$cardStatus.equals(other$cardStatus)) {
            return false;
        }
        Object this$postName = getPostName();
        Object other$postName = other.getPostName();
        if (this$postName == null) {
            if (other$postName != null) {
                return false;
            }
        } else if (!this$postName.equals(other$postName)) {
            return false;
        }
        Object this$phone = getPhone();
        Object other$phone = other.getPhone();
        if (this$phone == null) {
            if (other$phone != null) {
                return false;
            }
        } else if (!this$phone.equals(other$phone)) {
            return false;
        }
        Object this$jobNumber = getJobNumber();
        Object other$jobNumber = other.getJobNumber();
        if (this$jobNumber == null) {
            if (other$jobNumber != null) {
                return false;
            }
        } else if (!this$jobNumber.equals(other$jobNumber)) {
            return false;
        }
        Object this$contractorName = getContractorName();
        Object other$contractorName = other.getContractorName();
        if (this$contractorName == null) {
            if (other$contractorName != null) {
                return false;
            }
        } else if (!this$contractorName.equals(other$contractorName)) {
            return false;
        }
        Object this$visitReason = getVisitReason();
        Object other$visitReason = other.getVisitReason();
        if (this$visitReason == null) {
            if (other$visitReason != null) {
                return false;
            }
        } else if (!this$visitReason.equals(other$visitReason)) {
            return false;
        }
        Object this$visitLocation = getVisitLocation();
        Object other$visitLocation = other.getVisitLocation();
        if (this$visitLocation == null) {
            if (other$visitLocation != null) {
                return false;
            }
        } else if (!this$visitLocation.equals(other$visitLocation)) {
            return false;
        }
        Object this$receiveLeader = getReceiveLeader();
        Object other$receiveLeader = other.getReceiveLeader();
        if (this$receiveLeader == null) {
            if (other$receiveLeader != null) {
                return false;
            }
        } else if (!this$receiveLeader.equals(other$receiveLeader)) {
            return false;
        }
        Object this$companyName = getCompanyName();
        Object other$companyName = other.getCompanyName();
        return this$companyName == null ? other$companyName == null : this$companyName.equals(other$companyName);
    }
    protected boolean canEqual(Object other) {
        return other instanceof PositionCurrent;
    }
    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $positionId = getPositionId();
        int result2 = (result * 59) + ($positionId == null ? 43 : $positionId.hashCode());
        Object $beaconId = getBeaconId();
        int result3 = (result2 * 59) + ($beaconId == null ? 43 : $beaconId.hashCode());
        Object $distance = getDistance();
        int result4 = (result3 * 59) + ($distance == null ? 43 : $distance.hashCode());
        Object $centralPointDistance = getCentralPointDistance();
        int result5 = (result4 * 59) + ($centralPointDistance == null ? 43 : $centralPointDistance.hashCode());
        Object $cardId = getCardId();
        int result6 = (result5 * 59) + ($cardId == null ? 43 : $cardId.hashCode());
        Object $cardPower = getCardPower();
        int result7 = (result6 * 59) + ($cardPower == null ? 43 : $cardPower.hashCode());
        Object $layerHeight = getLayerHeight();
        int result8 = (result7 * 59) + ($layerHeight == null ? 43 : $layerHeight.hashCode());
        Object $layerMoveSpeed = getLayerMoveSpeed();
        int result9 = (result8 * 59) + ($layerMoveSpeed == null ? 43 : $layerMoveSpeed.hashCode());
        Object $personId = getPersonId();
        int result10 = (result9 * 59) + ($personId == null ? 43 : $personId.hashCode());
        Object $deptId = getDeptId();
        int result11 = (result10 * 59) + ($deptId == null ? 43 : $deptId.hashCode());
        Object $postId = getPostId();
        int result12 = (result11 * 59) + ($postId == null ? 43 : $postId.hashCode());
        Object $undisposedAlarmCount = getUndisposedAlarmCount();
        int result13 = (result12 * 59) + ($undisposedAlarmCount == null ? 43 : $undisposedAlarmCount.hashCode());
        Object $contractorId = getContractorId();
        int result14 = (result13 * 59) + ($contractorId == null ? 43 : $contractorId.hashCode());
        Object $stillStatus = getStillStatus();
        int result15 = (result14 * 59) + ($stillStatus == null ? 43 : $stillStatus.hashCode());
        Object $inRailScope = getInRailScope();
        int result16 = (result15 * 59) + ($inRailScope == null ? 43 : $inRailScope.hashCode());
        Object $pressure = getPressure();
        int result17 = (result16 * 59) + ($pressure == null ? 43 : $pressure.hashCode());
        Object $acceptTime = getAcceptTime();
        int result18 = (result17 * 59) + ($acceptTime == null ? 43 : $acceptTime.hashCode());
        Object $cardType = getCardType();
        int result19 = (result18 * 59) + ($cardType == null ? 43 : $cardType.hashCode());
        Object $cardTypeName = getCardTypeName();
        int result20 = (result19 * 59) + ($cardTypeName == null ? 43 : $cardTypeName.hashCode());
        Object $longitude = getLongitude();
        int result21 = (result20 * 59) + ($longitude == null ? 43 : $longitude.hashCode());
        Object $latitude = getLatitude();
        int result22 = (result21 * 59) + ($latitude == null ? 43 : $latitude.hashCode());
        Object $layerId = getLayerId();
        int result23 = (result22 * 59) + ($layerId == null ? 43 : $layerId.hashCode());
        Object $layerName = getLayerName();
        int result24 = (result23 * 59) + ($layerName == null ? 43 : $layerName.hashCode());
        Object $createTime = getCreateTime();
        int result25 = (result24 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $personType = getPersonType();
        int result26 = (result25 * 59) + ($personType == null ? 43 : $personType.hashCode());
        Object $personTypeName = getPersonTypeName();
        int result27 = (result26 * 59) + ($personTypeName == null ? 43 : $personTypeName.hashCode());
        Object $personCategory = getPersonCategory();
        int result28 = (result27 * 59) + ($personCategory == null ? 43 : $personCategory.hashCode());
        Object $personAttribute = getPersonAttribute();
        int result29 = (result28 * 59) + ($personAttribute == null ? 43 : $personAttribute.hashCode());
        Object $staffType = getStaffType();
        int result30 = (result29 * 59) + ($staffType == null ? 43 : $staffType.hashCode());
        Object $realName = getRealName();
        int result31 = (result30 * 59) + ($realName == null ? 43 : $realName.hashCode());
        Object $sex = getSex();
        int result32 = (result31 * 59) + ($sex == null ? 43 : $sex.hashCode());
        Object $personPhoto = getPersonPhoto();
        int result33 = (result32 * 59) + ($personPhoto == null ? 43 : $personPhoto.hashCode());
        Object $idNumber = getIdNumber();
        int result34 = (result33 * 59) + ($idNumber == null ? 43 : $idNumber.hashCode());
        Object $deptName = getDeptName();
        int result35 = (result34 * 59) + ($deptName == null ? 43 : $deptName.hashCode());
        Object $cardStatus = getCardStatus();
        int result36 = (result35 * 59) + ($cardStatus == null ? 43 : $cardStatus.hashCode());
        Object $postName = getPostName();
        int result37 = (result36 * 59) + ($postName == null ? 43 : $postName.hashCode());
        Object $phone = getPhone();
        int result38 = (result37 * 59) + ($phone == null ? 43 : $phone.hashCode());
        Object $jobNumber = getJobNumber();
        int result39 = (result38 * 59) + ($jobNumber == null ? 43 : $jobNumber.hashCode());
        Object $contractorName = getContractorName();
        int result40 = (result39 * 59) + ($contractorName == null ? 43 : $contractorName.hashCode());
        Object $visitReason = getVisitReason();
        int result41 = (result40 * 59) + ($visitReason == null ? 43 : $visitReason.hashCode());
        Object $visitLocation = getVisitLocation();
        int result42 = (result41 * 59) + ($visitLocation == null ? 43 : $visitLocation.hashCode());
        Object $receiveLeader = getReceiveLeader();
        int result43 = (result42 * 59) + ($receiveLeader == null ? 43 : $receiveLeader.hashCode());
        Object $companyName = getCompanyName();
        return (result43 * 59) + ($companyName == null ? 43 : $companyName.hashCode());
    }
    public String toString() {
        return "PositionCurrent(id=" + getId() + ", positionId=" + getPositionId() + ", acceptTime=" + getAcceptTime() + ", beaconId=" + getBeaconId() + ", distance=" + getDistance() + ", centralPointDistance=" + getCentralPointDistance() + ", cardId=" + getCardId() + ", cardType=" + getCardType() + ", cardTypeName=" + getCardTypeName() + ", cardPower=" + getCardPower() + ", longitude=" + getLongitude() + ", latitude=" + getLatitude() + ", layerId=" + getLayerId() + ", layerHeight=" + getLayerHeight() + ", layerName=" + getLayerName() + ", layerMoveSpeed=" + getLayerMoveSpeed() + ", createTime=" + getCreateTime() + ", personId=" + getPersonId() + ", personType=" + getPersonType() + ", personTypeName=" + getPersonTypeName() + ", personCategory=" + getPersonCategory() + ", personAttribute=" + getPersonAttribute() + ", staffType=" + getStaffType() + ", realName=" + getRealName() + ", sex=" + getSex() + ", deptId=" + getDeptId() + ", postId=" + getPostId() + ", undisposedAlarmCount=" + getUndisposedAlarmCount() + ", personPhoto=" + getPersonPhoto() + ", idNumber=" + getIdNumber() + ", deptName=" + getDeptName() + ", cardStatus=" + getCardStatus() + ", postName=" + getPostName() + ", phone=" + getPhone() + ", jobNumber=" + getJobNumber() + ", contractorId=" + getContractorId() + ", contractorName=" + getContractorName() + ", stillStatus=" + getStillStatus() + ", inRailScope=" + getInRailScope() + ", pressure=" + getPressure() + ", visitReason=" + getVisitReason() + ", visitLocation=" + getVisitLocation() + ", receiveLeader=" + getReceiveLeader() + ", companyName=" + getCompanyName() + StringPool.RIGHT_BRACKET;
    }
    public static PositionCurrentBuilder builder() {
        return new PositionCurrentBuilder();
    }
    public PositionCurrent() {
    }
    public PositionCurrent(Long id, Long positionId, LocalDateTime acceptTime, Integer beaconId, Double distance, Double centralPointDistance, Long cardId, String cardType, String cardTypeName, Integer cardPower, BigDecimal longitude, BigDecimal latitude, String layerId, Integer layerHeight, String layerName, Double layerMoveSpeed, LocalDateTime createTime, Long personId, String personType, String personTypeName, String personCategory, String personAttribute, String staffType, String realName, String sex, Long deptId, Long postId, Integer undisposedAlarmCount, String personPhoto, String idNumber, String deptName, String cardStatus, String postName, String phone, String jobNumber, Long contractorId, String contractorName, Integer stillStatus, Boolean inRailScope, Long pressure, String visitReason, String visitLocation, String receiveLeader, String companyName) {
        this.id = id;
        this.positionId = positionId;
        this.acceptTime = acceptTime;
        this.beaconId = beaconId;
        this.distance = distance;
        this.centralPointDistance = centralPointDistance;
        this.cardId = cardId;
        this.cardType = cardType;
        this.cardTypeName = cardTypeName;
        this.cardPower = cardPower;
        this.longitude = longitude;
        this.latitude = latitude;
        this.layerId = layerId;
        this.layerHeight = layerHeight;
        this.layerName = layerName;
        this.layerMoveSpeed = layerMoveSpeed;
        this.createTime = createTime;
        this.personId = personId;
        this.personType = personType;
        this.personTypeName = personTypeName;
        this.personCategory = personCategory;
        this.personAttribute = personAttribute;
        this.staffType = staffType;
        this.realName = realName;
        this.sex = sex;
        this.deptId = deptId;
        this.postId = postId;
        this.undisposedAlarmCount = undisposedAlarmCount;
        this.personPhoto = personPhoto;
        this.idNumber = idNumber;
        this.deptName = deptName;
        this.cardStatus = cardStatus;
        this.postName = postName;
        this.phone = phone;
        this.jobNumber = jobNumber;
        this.contractorId = contractorId;
        this.contractorName = contractorName;
        this.stillStatus = stillStatus;
        this.inRailScope = inRailScope;
        this.pressure = pressure;
        this.visitReason = visitReason;
        this.visitLocation = visitLocation;
        this.receiveLeader = receiveLeader;
        this.companyName = companyName;
    }
    public Long getId() {
        return this.id;
    }
    public Long getPositionId() {
        return this.positionId;
    }
    public LocalDateTime getAcceptTime() {
        return this.acceptTime;
    }
    public Integer getBeaconId() {
        return this.beaconId;
    }
    public Double getDistance() {
        return this.distance;
    }
    public Double getCentralPointDistance() {
        return this.centralPointDistance;
    }
    public Long getCardId() {
        return this.cardId;
    }
    public String getCardType() {
        return this.cardType;
    }
    public String getCardTypeName() {
        return this.cardTypeName;
    }
    public Integer getCardPower() {
        return this.cardPower;
    }
    public BigDecimal getLongitude() {
        return this.longitude;
    }
    public BigDecimal getLatitude() {
        return this.latitude;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public Integer getLayerHeight() {
        return this.layerHeight;
    }
    public String getLayerName() {
        return this.layerName;
    }
    public Double getLayerMoveSpeed() {
        return this.layerMoveSpeed;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public Long getPersonId() {
        return this.personId;
    }
    public String getPersonType() {
        return this.personType;
    }
    public String getPersonTypeName() {
        return this.personTypeName;
    }
    public String getPersonCategory() {
        return this.personCategory;
    }
    public String getPersonAttribute() {
        return this.personAttribute;
    }
    public String getStaffType() {
        return this.staffType;
    }
    public String getRealName() {
        return this.realName;
    }
    public String getSex() {
        return this.sex;
    }
    public Long getDeptId() {
        return this.deptId;
    }
    public Long getPostId() {
        return this.postId;
    }
    public Integer getUndisposedAlarmCount() {
        return this.undisposedAlarmCount;
    }
    public String getPersonPhoto() {
        return this.personPhoto;
    }
    public String getIdNumber() {
        return this.idNumber;
    }
    public String getDeptName() {
        return this.deptName;
    }
    public String getCardStatus() {
        return this.cardStatus;
    }
    public String getPostName() {
        return this.postName;
    }
    public String getPhone() {
        return this.phone;
    }
    public String getJobNumber() {
        return this.jobNumber;
    }
    public Long getContractorId() {
        return this.contractorId;
    }
    public String getContractorName() {
        return this.contractorName;
    }
    public Integer getStillStatus() {
        return this.stillStatus;
    }
    public Boolean getInRailScope() {
        return this.inRailScope;
    }
    public Long getPressure() {
        return this.pressure;
    }
    public String getVisitReason() {
        return this.visitReason;
    }
    public String getVisitLocation() {
        return this.visitLocation;
    }
    public String getReceiveLeader() {
        return this.receiveLeader;
    }
    public String getCompanyName() {
        return this.companyName;
    }
}
