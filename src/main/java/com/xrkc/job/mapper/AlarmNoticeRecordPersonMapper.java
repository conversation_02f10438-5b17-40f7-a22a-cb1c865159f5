package com.xrkc.job.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xrkc.job.domain.AlarmNoticeRecordPerson;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/AlarmNoticeRecordPersonMapper.class */
public interface AlarmNoticeRecordPersonMapper extends BaseMapper<AlarmNoticeRecordPerson> {
    int updateByRecordId(AlarmNoticeRecordPerson alarmNoticeRecordPerson);
    int updateFinishByRecordId(@Param("recordId") Long l, @Param("noticeResult") String str);
}
