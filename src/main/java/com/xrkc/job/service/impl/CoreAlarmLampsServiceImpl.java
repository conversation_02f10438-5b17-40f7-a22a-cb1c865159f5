package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.job.domain.CoreAlarmLamps;
import com.xrkc.job.mapper.CoreAlarmLampsMapper;
import com.xrkc.job.service.ICoreAlarmLampsService;
import java.time.LocalDateTime;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/CoreAlarmLampsServiceImpl.class */
public class CoreAlarmLampsServiceImpl extends ServiceImpl<CoreAlarmLampsMapper, CoreAlarmLamps> implements ICoreAlarmLampsService {
    @Autowired
    private CoreAlarmLampsMapper coreAlarmLampsMapper;
    @Override // com.xrkc.job.service.ICoreAlarmLampsService
    public boolean existAlarmByParams(Map<String, Object> map) {
        Integer alarmInterval = (Integer) map.get("alarmInterval");
        LocalDateTime minusMinutes = LocalDateTime.now().minusMinutes(alarmInterval.intValue());
        map.put("minusMinutes", minusMinutes);
        return this.coreAlarmLampsMapper.existAlarmByParams(map) > 0;
    }
}
