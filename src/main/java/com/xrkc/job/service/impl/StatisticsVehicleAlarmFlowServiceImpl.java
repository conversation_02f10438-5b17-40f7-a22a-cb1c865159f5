package com.xrkc.job.service.impl;
import com.xrkc.job.domain.StatisticsVehicleAlarmFlow;
import com.xrkc.job.mapper.StatisticsVehicleAlarmFlowMapper;
import com.xrkc.job.service.IStatisticsVehicleAlarmFlowService;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/StatisticsVehicleAlarmFlowServiceImpl.class */
public class StatisticsVehicleAlarmFlowServiceImpl implements IStatisticsVehicleAlarmFlowService {
    @Autowired
    private StatisticsVehicleAlarmFlowMapper statisticsVehicleAlarmFlowMapper;
    @Override // com.xrkc.job.service.IStatisticsVehicleAlarmFlowService
    public int batchReplace(List<StatisticsVehicleAlarmFlow> list) {
        LocalDate statisticsDate = null;
        int result = 0;
        Set<String> set = new HashSet<>();
        Iterator<StatisticsVehicleAlarmFlow> it = list.iterator();
        while (it.hasNext()) {
            statisticsDate = it.next().getStatisticsDate();
        }
        List<StatisticsVehicleAlarmFlow> oldList = this.statisticsVehicleAlarmFlowMapper.selectByStatisticsDate(statisticsDate);
        if (CollectionUtils.isEmpty(oldList)) {
            Iterator<StatisticsVehicleAlarmFlow> it2 = list.iterator();
            while (it2.hasNext()) {
                result += this.statisticsVehicleAlarmFlowMapper.insert(it2.next());
            }
            return result;
        }
        Iterator<StatisticsVehicleAlarmFlow> it3 = oldList.iterator();
        while (it3.hasNext()) {
            set.add(it3.next().getAlarmType());
        }
        for (StatisticsVehicleAlarmFlow flow : list) {
            if (set.contains(flow.getAlarmType())) {
                result += this.statisticsVehicleAlarmFlowMapper.update(flow);
            } else {
                result += this.statisticsVehicleAlarmFlowMapper.insert(flow);
            }
        }
        return result;
    }
}
