package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.job.domain.StatisticsInspectFlowDanger;
import com.xrkc.job.mapper.StatisticsInspectFlowDangerMapper;
import com.xrkc.job.service.IStatisticsInspectFlowDangerService;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/StatisticsInspectFlowDangerServiceImpl.class */
public class StatisticsInspectFlowDangerServiceImpl extends ServiceImpl<StatisticsInspectFlowDangerMapper, StatisticsInspectFlowDanger> implements IStatisticsInspectFlowDangerService {
    @Autowired
    private StatisticsInspectFlowDangerMapper statisticsInspectFlowDangerMapper;
    @Override // com.xrkc.job.service.IStatisticsInspectFlowDangerService
    public int batchReplace(List<StatisticsInspectFlowDanger> statisticsInspectFlowDangers) {
        List<StatisticsInspectFlowDanger> list = new ArrayList<>();
        Set<String> set = new HashSet<>();
        if (CollectionUtils.isEmpty(statisticsInspectFlowDangers)) {
            return list.size();
        }
        LocalDate statisticsDate = statisticsInspectFlowDangers.get(0).getStatisticsDate();
        List<StatisticsInspectFlowDanger> oldList = this.statisticsInspectFlowDangerMapper.selectByStatisticsDate(statisticsDate);
        if (CollectionUtils.isEmpty(oldList)) {
            list.addAll(statisticsInspectFlowDangers);
        } else {
            StringBuilder buf = new StringBuilder();
            for (StatisticsInspectFlowDanger danger : oldList) {
                buf.append(danger.getStatisticsDate());
                buf.append(danger.getDangerType());
                set.add(buf.toString());
                buf.setLength(0);
            }
            for (StatisticsInspectFlowDanger danger2 : statisticsInspectFlowDangers) {
                buf.append(danger2.getStatisticsDate());
                buf.append(danger2.getDangerType());
                if (set.contains(buf.toString())) {
                    LambdaUpdateWrapper<StatisticsInspectFlowDanger> wrapper = new LambdaUpdateWrapper<>();
                    wrapper.eq((v0) -> {
                        return v0.getDangerType();
                    }, danger2.getDangerType());
                    wrapper.eq((v0) -> {
                        return v0.getStatisticsDate();
                    }, danger2.getStatisticsDate());
                    this.statisticsInspectFlowDangerMapper.update(danger2, wrapper);
                } else {
                    list.add(danger2);
                }
                buf.setLength(0);
            }
        }
        if (!CollectionUtils.isEmpty(list)) {
            this.statisticsInspectFlowDangerMapper.batchReplace(list);
        }
        return list.size();
    }
}
