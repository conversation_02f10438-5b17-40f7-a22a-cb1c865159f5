package com.xrkc.job.util;
import java.util.regex.Pattern;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/RegexUtils.class */
public class RegexUtils {
    public static final String REGEX_USERNAME = "^[a-zA-Z]\\w{5,20}$";
    public static final String REGEX_PASSWORD = "^[a-zA-Z0-9]{6,20}$";
    public static final String REGEX_MOBILE = "^1\\d{10}$";
    public static final String REGEX_TELEPHONE = "[0-9-()（）]{7,18}";
    public static final String REGEX_MOBILE_TELEPHONE = "[0-9-()（）]{7,18}|^1\\d{10}$";
    public static final String REGEX_EMAIL = "^([a-z0-9A-Z]+[-|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";
    public static final String REGEX_CHINESE = "^[一-龥],{0,}$";
    public static final String REGEX_ID_CARD = "(^\\d{18}$)|(^\\d{15}$)";
    public static final String REGEX_URL = "http(s)?://([\\w-]+\\.)+[\\w-]+(/[\\w- ./?%&=]*)?";
    public static final String REGEX_IP_ADDR = "(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)";
    public static final String REGEX_LOGISTICS_DAYS = "^[0-9]{1,2}$";
    public static final String REGEX_POSSIVE_INTEGER = "^[1-9]\\d*$";
    public static final String REGEX_POSITIVE_NUM = "^[+]?\\d+(.\\d+)?$";
    public static final String REGEX_DECIMAL_NUM = "^[1-9]\\d*$|^[1-9]\\d*\\.\\d\\d?$|^0\\.\\d\\d?$";
    public static final String REGEX_PURE_NUM = "^[0-9]*$";
    public static final String REGEX_PRIODUCT_ARRIVAL = "([0-8]?[0-9]|90)";
    public static final String REGEX_TAXPAYER_IDENTIFICATION_NUMBER = "^[a-zA-Z0-9]{18}$";
    public static final String REGEX_JPG_JPEG_PNG = "^.+(\\.jpg|\\.JPG|\\.jpeg|\\.JPEG|\\.png|\\.PNG)$";
    public static final String REGEX_AGREEMENT_ENCLOSURE = "^.+(\\.pdf|\\.doc|\\.docx|\\.jpg|\\.jpeg|\\.png|\\.bmp|\\.gif|\\.rar|\\.zip|\\.xlsx|\\.xls|\\.txt|\\.PDF|\\.DOC|\\.DOCX|\\.JPG|\\.JPEG|\\.PNG|\\.BMP|\\.GIF|\\.RAR|\\.ZIP|\\.XLSX|\\.XLS|\\.TXT)$";
    public static final String REGEX_CHINESE_ENGLISH_NUMBER = "^[a-z0-9A-Z一-龥]+$";
    public static final String REGEX_ENGLISH_NUMBER = "^[a-z0-9A-Z]+$";
    private static final String REGEX_JPG_JPEG_PNG_PDF = "^.+(\\.jpg|\\.JPG|\\.jpeg|\\.JPEG|\\.png|\\.PNG|\\.pdf|\\.PDF)$";
    public static final String STRING_LENGTH_ONE = "^.{1,64}$";
    public static final String STRING_LENGTH_TWO = "^.{1,20}$";
    public static final String REGEX_TWO_DECIMAL = "^(((0|[1-9]\\d{0,9})(\\.\\d{1,2}))|(0|[1-9]\\d{0,9}))$";
    public static boolean isChiEngNum(String name) {
        return Pattern.matches("^[a-z0-9A-Z一-龥]+$", name);
    }
    public static boolean isEngNum(String englishName) {
        return Pattern.matches("^[a-z0-9A-Z]+$", englishName);
    }
    public static boolean isDecimalNum(String num) {
        return Pattern.matches("^[1-9]\\d*$|^[1-9]\\d*\\.\\d\\d?$|^0\\.\\d\\d?$", num);
    }
    public static boolean isUsername(String username) {
        return Pattern.matches("^[a-zA-Z]\\w{5,20}$", username);
    }
    public static boolean isPassword(String password) {
        return Pattern.matches("^[a-zA-Z0-9]{6,20}$", password);
    }
    public static boolean isEmail(String email) {
        if (org.apache.commons.lang3.StringUtils.isBlank(email)) {
            return false;
        }
        return Pattern.matches("^([a-z0-9A-Z]+[-|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$", email);
    }
    public static boolean isChinese(String chinese) {
        return Pattern.matches("^[一-龥],{0,}$", chinese);
    }
    public static boolean isIDCard(String idCard) {
        return Pattern.matches("(^\\d{18}$)|(^\\d{15}$)", idCard);
    }
    public static boolean isUrl(String url) {
        return Pattern.matches("http(s)?://([\\w-]+\\.)+[\\w-]+(/[\\w- ./?%&=]*)?", url);
    }
    public static boolean isIPAddr(String ipAddr) {
        return Pattern.matches("(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)", ipAddr);
    }
    public static boolean isLogisticsDays(String logisticsNum) {
        return Pattern.matches("^[0-9]{1,2}$", logisticsNum);
    }
    public static boolean isTelephone(String logisticsNum) {
        return Pattern.matches("[0-9-()（）]{7,18}", logisticsNum);
    }
    public static boolean isMobile(String number) {
        if (org.apache.commons.lang3.StringUtils.isBlank(number)) {
            return false;
        }
        return Pattern.matches("^1\\d{10}$", number);
    }
    public static boolean isTelephoneOrMobile(String logisticsNum) {
        return Pattern.matches("[0-9-()（）]{7,18}|^1\\d{10}$", logisticsNum);
    }
    public static boolean isPositiveNum(String num) {
        return Pattern.matches("^[+]?\\d+(.\\d+)?$", num);
    }
    public static boolean isNum(String num) {
        return Pattern.matches("^[0-9]*$", num);
    }
    public static boolean isPositiveInteger(String num) {
        return Pattern.matches("^[1-9]\\d*$", num);
    }
    public static boolean isArrival(String days) {
        return Pattern.matches("([0-8]?[0-9]|90)", days);
    }
    public static boolean isTaxpayerIdentificationNumber(String taxpayerIdentificationNumber) {
        return Pattern.matches("^[a-zA-Z0-9]{18}$", taxpayerIdentificationNumber);
    }
    public static boolean isAgreementEnclosure(String agreementEnclosure) {
        return Pattern.matches("^.+(\\.pdf|\\.doc|\\.docx|\\.jpg|\\.jpeg|\\.png|\\.bmp|\\.gif|\\.rar|\\.zip|\\.xlsx|\\.xls|\\.txt|\\.PDF|\\.DOC|\\.DOCX|\\.JPG|\\.JPEG|\\.PNG|\\.BMP|\\.GIF|\\.RAR|\\.ZIP|\\.XLSX|\\.XLS|\\.TXT)$", agreementEnclosure);
    }
    public static boolean isPayScreenshotItem(String payScreenshotItem) {
        return Pattern.matches("^.+(\\.jpg|\\.JPG|\\.jpeg|\\.JPEG|\\.png|\\.PNG)$", payScreenshotItem);
    }
    public static boolean isJpgPngPdf(String url) {
        return Pattern.matches(REGEX_JPG_JPEG_PNG_PDF, url);
    }
    public static boolean REGEX_TWO_DECIMAL(String url) {
        return Pattern.matches("^(((0|[1-9]\\d{0,9})(\\.\\d{1,2}))|(0|[1-9]\\d{0,9}))$", url);
    }
}
