package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.job.domain.SystemUserDataScopeVO;
import com.xrkc.job.domain.SystemUserRole;
import com.xrkc.job.mapper.SystemUserRoleMapper;
import com.xrkc.job.service.ISystemUserRoleService;
import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/SystemUserRoleServiceImpl.class */
public class SystemUserRoleServiceImpl extends ServiceImpl<SystemUserRoleMapper, SystemUserRole> implements ISystemUserRoleService {
    @Autowired
    private SystemUserRoleMapper systemUserRoleMapper;
    @Override // com.xrkc.job.service.ISystemUserRoleService
    public List<SystemUserRole> getUserRoleList() {
        return this.systemUserRoleMapper.getUserRoleList();
    }
    @Override // com.xrkc.job.service.ISystemUserRoleService
    public List<SystemUserDataScopeVO> findDataScopeByUserIds(Set<Long> pushUserIds) {
        return this.systemUserRoleMapper.findDataScopeByUserIds(pushUserIds);
    }
}
