package com.xrkc.job.util;
import ch.qos.logback.core.joran.util.beans.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.domain.basic.JsonResult;
import com.xrkc.core.domain.person.Person;
import com.xrkc.core.domain.system.SystemApi;
import com.xrkc.core.utils.PersonUtils;
import com.xrkc.job.config.PersonSynchronizerConstants;
import com.xrkc.job.domain.AccessRecordParmVO;
import com.xrkc.job.domain.ExecutorPerson;
import com.xrkc.job.domain.Face;
import com.xrkc.job.domain.HikvisionSyncPerson;
import com.xrkc.job.domain.UpdatePesonHK;
import com.xrkc.redis.service.RedisService;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/PersonSynchronizerUtil.class */
public class PersonSynchronizerUtil {
    private static final int POOL_SIZE = 1;
    private static final String personPhotoKeyPrefix = "hkPerson";
    private static final Logger log = LogManager.getLogger((Class<?>) PersonSynchronizerUtil.class);
    public static String successCode = "0";
    public static String errorCode = "1";
    private static final ThreadPoolExecutor EXECUTOR = new ThreadPoolExecutor(1, 1, 0, TimeUnit.MICROSECONDS, new ArrayBlockingQueue(500), new ThreadPoolExecutor.CallerRunsPolicy());
    public static JSONObject orgAdd() {
        JSONObject res = new JSONObject();
        String msg = "添加人员到组失败";
        Map<String, String> pathMap = new HashMap<>();
        pathMap.put("https://", PersonSynchronizerConstants.ORG_ADD);
        JSONObject queryParams = new JSONObject();
        queryParams.put("orgName", PersonSynchronizerConstants.ORG_NAME);
        queryParams.put("parentIndexCode", "0");
        try {
            String result = ArtemisHttpUtil.doPostStringArtemis(pathMap, queryParams.toJSONString(new JSONWriter.Feature[0]), null, null, "application/json");
            log.info("添加组结果:{}", result);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(result)) {
                JSONObject json = JSONObject.parseObject(result);
                if (Objects.nonNull(json.get("code"))) {
                    String obj = JSONObject.toJSONString(json.get("code"), new JSONWriter.Feature[0]);
                    if (successCode.equals(obj)) {
                        JSONObject data = JSONObject.parseObject(JSONObject.toJSONString(json.get("data"), new JSONWriter.Feature[0]));
                        res.put("code", successCode);
                        res.put("orgIndexCode", JSONObject.toJSONString(data.get("orgIndexCode"), new JSONWriter.Feature[0]));
                        return res;
                    }
                    msg = JSONObject.toJSONString(json.get("msg"), new JSONWriter.Feature[0]);
                }
            }
        } catch (Exception e) {
            log.error("添加组请求失败，url:{}，msg:{}", PersonSynchronizerConstants.ORG_ADD, e.getMessage());
        }
        res.put("code", errorCode);
        res.put("msg", msg);
        return res;
    }
    public static UpdatePesonHK addPersonToOrg(List<Person> personList, String orgIndexCode) throws ExecutionException, InterruptedException {
        UpdatePesonHK updatePesonHK = new UpdatePesonHK();
        List<Person> updatePersionList = new ArrayList<>();
        boolean isUpdateTime = true;
        List<Future<ExecutorPerson>> futureList = new ArrayList<>();
        List<String> temporaryAllowList = new ArrayList<>();
        List<String> deleteAllowList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(personList)) {
            personList.forEach(person -> {
                log.info("开始处理处理人员:{}", person.getPersonId());
                futureList.add(EXECUTOR.submit(() -> {
                    ExecutorPerson executorPerson = new ExecutorPerson();
                    executorPerson.setPersonId(person.getPersonId());
                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(person.getPersonCode())) {
                        boolean r = updatePerson(person, orgIndexCode, executorPerson);
                        if (r) {
                            executorPerson.setUpdateFail("1");
                        } else {
                            executorPerson.setUpdateFail("2");
                        }
                    } else {
                        boolean rr = addPerson(person, orgIndexCode, executorPerson);
                        if (rr) {
                            executorPerson.setUpdateFail("1");
                        } else {
                            executorPerson.setUpdateFail("2");
                        }
                    }
                    return executorPerson;
                }));
            });
        }
        if (!CollectionUtils.isEmpty(futureList)) {
            for (Future<ExecutorPerson> ss : futureList) {
                try {
                    ExecutorPerson executorPerson = ss.get();
                    if (Objects.nonNull(executorPerson) && !PersonSynchronizerConstants.notupdate.equals(executorPerson.getNotUpdate())) {
                        Person person2 = new Person();
                        person2.setPersonId(executorPerson.getPersonId());
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(executorPerson.getHikvisionPersonIdId())) {
                            person2.setPersonCode(executorPerson.getHikvisionPersonIdId());
                        }
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(executorPerson.getUpdateFail())) {
                            person2.setUpdateFail(executorPerson.getUpdateFail());
                        }
                        updatePersionList.add(person2);
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(executorPerson.getDeleteId())) {
                            deleteAllowList.add(executorPerson.getDeleteId());
                        }
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(executorPerson.getTemporaryAllowId())) {
                            temporaryAllowList.add(executorPerson.getTemporaryAllowId());
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        if (!CollectionUtils.isEmpty(deleteAllowList)) {
            boolean b = deletePesonsPrivilege(deleteAllowList);
            if (b) {
                isUpdateTime = false;
            }
        }
        if (!CollectionUtils.isEmpty(temporaryAllowList)) {
            boolean b2 = addPesonsPrivilege(temporaryAllowList);
            if (b2) {
                isUpdateTime = false;
            }
        }
        if (CollUtil.isNotEmpty((Collection<?>) updatePersionList)) {
            authReapplication();
        }
        updatePesonHK.setUpdatePersionList(updatePersionList);
        updatePesonHK.setUpdateTime(Boolean.valueOf(isUpdateTime));
        return updatePesonHK;
    }
    public static UpdatePesonHK addPersonToOrg1(List<Person> personList, String orgIndexCode) {
        UpdatePesonHK updatePesonHK = new UpdatePesonHK();
        List<Person> updatePersionList = new ArrayList<>();
        boolean isUpdateTime = true;
        List<ExecutorPerson> futureList = new ArrayList<>();
        List<String> temporaryAllowList = new ArrayList<>();
        List<String> deleteAllowList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(personList)) {
            personList.forEach(person -> {
                log.info("开始处理处理人员:{}", person.getPersonId());
                ExecutorPerson executorPerson = new ExecutorPerson();
                executorPerson.setPersonId(person.getPersonId());
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(person.getPersonCode())) {
                    executorPerson.setDeleteId(person.getPersonCode());
                    boolean r = updatePerson(person, orgIndexCode, executorPerson);
                    if (r) {
                        executorPerson.setUpdateFail("1");
                    } else {
                        executorPerson.setUpdateFail("2");
                    }
                } else {
                    boolean rr = addPerson(person, orgIndexCode, executorPerson);
                    if (rr) {
                        executorPerson.setUpdateFail("1");
                    } else {
                        executorPerson.setUpdateFail("2");
                    }
                }
                futureList.add(executorPerson);
            });
        }
        if (!CollectionUtils.isEmpty(futureList)) {
            for (ExecutorPerson executorPerson : futureList) {
                try {
                    Person person2 = new Person();
                    person2.setPersonId(executorPerson.getPersonId());
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(executorPerson.getHikvisionPersonIdId())) {
                        person2.setPersonCode(executorPerson.getHikvisionPersonIdId());
                    }
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(executorPerson.getUpdateFail())) {
                        person2.setUpdateFail(executorPerson.getUpdateFail());
                    }
                    updatePersionList.add(person2);
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(executorPerson.getDeleteId())) {
                        deleteAllowList.add(executorPerson.getDeleteId());
                    }
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(executorPerson.getTemporaryAllowId())) {
                        temporaryAllowList.add(executorPerson.getTemporaryAllowId());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        if (!CollectionUtils.isEmpty(deleteAllowList)) {
            boolean b = deletePesonsPrivilege(deleteAllowList);
            if (b) {
                isUpdateTime = false;
            }
        }
        if (!CollectionUtils.isEmpty(temporaryAllowList)) {
            boolean b2 = addPesonsPrivilege(temporaryAllowList);
            if (b2) {
                isUpdateTime = false;
            }
        }
        authReapplication();
        updatePesonHK.setUpdatePersionList(updatePersionList);
        updatePesonHK.setUpdateTime(Boolean.valueOf(isUpdateTime));
        return updatePesonHK;
    }
    public static int checkGender(String sex) {
        int res = 0;
        if ("男".equals(sex)) {
            res = 1;
        } else if ("女".equals(sex)) {
            res = 2;
        }
        return res;
    }
    public static HikvisionSyncPerson assemblerInformation(Person person, String imgBase64, String orgIndexCode, String type) {
        HikvisionSyncPerson p = new HikvisionSyncPerson();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(person.getPersonCode())) {
            p.setPersonId(person.getPersonCode());
        }
        p.setPersonGivenName(person.getRealName());
        p.setOrgIndexCode(orgIndexCode);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(person.getSex())) {
            p.setGender(Integer.valueOf(checkGender(person.getSex())));
        }
        if (BeanUtil.PREFIX_ADDER.equals(type)) {
            List<Face> faceList = new ArrayList<>();
            p.setPersonCode(person.getIdNumber());
            Face face = new Face();
            face.setFaceData(imgBase64);
            faceList.add(face);
            p.setFaces(faceList);
        }
        return p;
    }
    public static boolean checkIdNumberValidity(Person person) {
        boolean res = false;
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(person.getIdNumber()) && person.getIdNumber().length() <= 16) {
            res = true;
        } else {
            log.info("当前人员id:{},姓名:{},身份证号:{}.因身份证号为空或超过长度16不符合规范,过滤处理", person.getPersonId(), person.getRealName(), person.getIdNumber());
        }
        return res;
    }
    public static boolean addPerson(Person person, String orgIndexCode, ExecutorPerson executorPerson) throws IOException {
        boolean success = false;
        log.info("开始新增人员:{}", person.getPersonId());
        Map<String, String> pathMap = new HashMap<>();
        pathMap.put("https://", PersonSynchronizerConstants.PERSON_TO_ORG_ADD);
        String imgBase64 = imgPathToBase64(person.getPersonId(), person.getPersonPhoto(), person.getRealName());
        try {
            String result = null;
            if (checkIdNumberValidity(person)) {
                HikvisionSyncPerson p = assemblerInformation(person, imgBase64, orgIndexCode, BeanUtil.PREFIX_ADDER);
                log.info("添加人员到组参数");
                result = ArtemisHttpUtil.doPostStringArtemis(pathMap, JSONObject.toJSONString(p, new JSONWriter.Feature[0]), null, null, "application/json");
                if (Objects.isNull(result)) {
                    result = ArtemisHttpUtil.doPostStringArtemis(pathMap, JSONObject.toJSONString(p, new JSONWriter.Feature[0]), null, null, "application/json");
                }
                log.info("添加人员id:{}到组,返回结果:{}", person.getPersonId(), result);
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(result)) {
                JSONObject json = JSONObject.parseObject(result);
                if (Objects.nonNull(json.get("code")) && successCode.equals(json.get("code").toString())) {
                    saveRedisInformation(person);
                    String hikvisionPersonId = json.get("data").toString();
                    if (Objects.nonNull(json.get("data"))) {
                        executorPerson.setHikvisionPersonIdId(hikvisionPersonId);
                        executorPerson.setPersonId(person.getPersonId());
                    }
                    if ("Y".equals(person.getGateThrough())) {
                        String res = faceCheck(imgBase64, PersonSynchronizerConstants.acsDevIndexCode, person.getPersonId());
                        if (successCode.equals(res)) {
                            executorPerson.setTemporaryAllowId(hikvisionPersonId);
                            success = true;
                        }
                    } else {
                        success = true;
                    }
                }
            }
        } catch (Exception e) {
            log.error("添加人员到组请求失败，url:{}，msg:{}", PersonSynchronizerConstants.PERSON_TO_ORG_ADD, e.getMessage());
        }
        return success;
    }
    public static String imgPathToBase64(Long personId, String personPhoto, String realName) throws IOException {
        String key = PersonUtils.getPersonPhotoKey(personId, personPhoto);
        JSONObject jsonCache = (JSONObject) ((RedisService) com.xrkc.core.utils.SpringUtils.getBean(RedisService.class)).getCacheObject(key);
        if (Objects.nonNull(jsonCache)) {
            String b = jsonCache.getString("base64");
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(b)) {
                return b;
            }
        }
        String base64 = FileUtil.imgPathToBase64(personPhoto, "【Hikvision】" + personId + "_" + realName);
        return base64;
    }
    public static boolean updatePerson(Person person, String orgIndexCode, ExecutorPerson executorPerson) throws IOException {
        boolean success = false;
        log.info("开始修改人员:{},海康威视的id:{}", person.getPersonId(), person.getPersonCode());
        Map<String, String> pathMap = new HashMap<>();
        pathMap.put("https://", PersonSynchronizerConstants.PERSON_TO_ORG_update);
        String imgBase64 = imgPathToBase64(person.getPersonId(), person.getPersonPhoto(), person.getRealName());
        if (modifyInformation(person).booleanValue()) {
            log.info("当前人员:{},海康人员id:{} 信息未发生改变,不做更新", person.getPersonId(), person.getPersonCode());
            executorPerson.setNotUpdate(PersonSynchronizerConstants.notupdate);
            return true;
        }
        executorPerson.setDeleteId(person.getPersonCode());
        HikvisionSyncPerson p = assemblerInformation(person, imgBase64, orgIndexCode, "update");
        try {
            long ll1 = System.currentTimeMillis();
            log.info("修改人员到组参数:{}", JSONObject.toJSONString(p, new JSONWriter.Feature[0]));
            String result = ArtemisHttpUtil.doPostStringArtemis(pathMap, JSONObject.toJSONString(p, new JSONWriter.Feature[0]), null, null, "application/json");
            if (Objects.isNull(result)) {
                result = ArtemisHttpUtil.doPostStringArtemis(pathMap, JSONObject.toJSONString(p, new JSONWriter.Feature[0]), null, null, "application/json");
            }
            long ll2 = System.currentTimeMillis();
            log.info("修改人员id:{}到组,返回结果:{},耗时:{}", person.getPersonId(), result, Long.valueOf(ll2 - ll1));
            Map<String, String> updateFaceData = new HashMap<>();
            updateFaceData.put("https://", PersonSynchronizerConstants.updateFaceData);
            JSONObject queryParams = new JSONObject();
            queryParams.put("personId", person.getPersonCode());
            queryParams.put("faceData", imgBase64);
            long l1 = System.currentTimeMillis();
            String resultFace = ArtemisHttpUtil.doPostStringArtemis(updateFaceData, queryParams.toJSONString(new JSONWriter.Feature[0]), null, null, "application/json");
            long l2 = System.currentTimeMillis();
            if (Objects.isNull(resultFace)) {
                resultFace = ArtemisHttpUtil.doPostStringArtemis(updateFaceData, queryParams.toJSONString(new JSONWriter.Feature[0]), null, null, "application/json");
            }
            log.info("修改人员人脸:{},返回结果:{},耗时:{}", person.getPersonId(), resultFace, Long.valueOf(l2 - l1));
            if (org.apache.commons.lang3.StringUtils.isNotBlank(result) && org.apache.commons.lang3.StringUtils.isNotBlank(resultFace)) {
                JSONObject json = JSONObject.parseObject(result);
                JSONObject resultFaceJson = JSONObject.parseObject(resultFace);
                if (Objects.nonNull(json.get("code")) && Objects.nonNull(resultFaceJson.get("code")) && successCode.equals(json.get("code").toString()) && successCode.equals(resultFaceJson.get("code").toString())) {
                    if ("Y".equals(person.getGateThrough())) {
                        String res = faceCheck(imgBase64, PersonSynchronizerConstants.acsDevIndexCode, person.getPersonId());
                        if (successCode.equals(res)) {
                            executorPerson.setTemporaryAllowId(person.getPersonCode());
                            success = true;
                        }
                    } else {
                        success = true;
                    }
                }
            }
        } catch (Exception e) {
            log.error("修改人员到组请求失败，url:{}，msg:{}", PersonSynchronizerConstants.PERSON_TO_ORG_update, e.getMessage());
        }
        return success;
    }
    public static Boolean modifyInformation(Person person) {
        String currentInfo = getValue(person);
        String cacheObject = getOldValue(person);
        if (currentInfo.equals(cacheObject)) {
            return true;
        }
        saveRedisInformation(person);
        return false;
    }
    public static String getKey(Long personId) {
        StringJoiner sj = new StringJoiner(":");
        sj.add(personPhotoKeyPrefix);
        sj.add(personId.toString());
        return sj.toString();
    }
    public static String getOldValue(Person person) {
        String oldValue = String.valueOf(((RedisService) com.xrkc.core.utils.SpringUtils.getBean(RedisService.class)).get(getKey(person.getPersonId())));
        return oldValue;
    }
    public static String getValue(Person person) {
        StringJoiner sj = new StringJoiner("_");
        sj.add(person.getIdNumber());
        sj.add(person.getGateThrough());
        sj.add(person.getPersonPhoto());
        return sj.toString();
    }
    public static void saveRedisInformation(Person person) {
        ((RedisService) com.xrkc.core.utils.SpringUtils.getBean(RedisService.class)).set(getKey(person.getPersonId()), getValue(person));
    }
    public static String faceCheckOld(String faceData, String acsDevIndexCode, Long pesonId) {
        log.info("开始处理faceCheck(),acsDevIndexCode值:{},人员id:{}", acsDevIndexCode, pesonId);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(acsDevIndexCode)) {
            Map<String, String> pathMap = new HashMap<>();
            pathMap.put("https://", PersonSynchronizerConstants.FACE_CHECK);
            JSONObject queryParams = new JSONObject();
            queryParams.put("faceData", faceData);
            queryParams.put("acsDevIndexCode", acsDevIndexCode);
            log.info("acsDevIndexCode:{}", acsDevIndexCode);
            try {
                String result = ArtemisHttpUtil.doPostStringArtemis(pathMap, queryParams.toJSONString(new JSONWriter.Feature[0]), null, null, "application/json");
                if (Objects.isNull(result)) {
                    result = ArtemisHttpUtil.doPostStringArtemis(pathMap, queryParams.toJSONString(new JSONWriter.Feature[0]), null, null, "application/json");
                }
                log.info("校验人脸图片参数返回结果:{},人员id:{}", result, pesonId);
                if (org.apache.commons.lang3.StringUtils.isNotBlank(result)) {
                    JSONObject json = JSONObject.parseObject(result);
                    if (Objects.nonNull(json.get("code"))) {
                        if (successCode.equals(json.get("code").toString())) {
                            return successCode;
                        }
                        log.info("校验人脸失败，msg:{},人员id:{}", JSONObject.toJSONString(json.get("msg"), new JSONWriter.Feature[0]), pesonId);
                    }
                }
            } catch (Exception e) {
                log.error("校验人脸图片有效性请求失败，url:{}，msg:{},人员id:{}", PersonSynchronizerConstants.FACE_CHECK, e.getMessage(), pesonId);
            }
        }
        return errorCode;
    }
    public static String faceCheck(String faceData, String acsDevIndexCode, Long pesonId) {
        return successCode;
    }
    public static boolean addPesonsPrivilege(List<String> personIds) {
        boolean success = true;
        log.info("开始处理将人员添加到访问权限组");
        if (!CollectionUtils.isEmpty(personIds)) {
            Map<String, String> pathMap = new HashMap<>();
            pathMap.put("https://", PersonSynchronizerConstants.ADD_PESONS_PRIVILEGE);
            JSONObject queryParams = new JSONObject();
            queryParams.put("privilegeGroupId", "1");
            queryParams.put("type", 1);
            List<Map<String, String>> list = new ArrayList<>();
            personIds.stream().forEach(s -> {
                HashMap map = new HashMap();
                map.put("id", s);
                list.add(map);
            });
            queryParams.put("list", list);
            try {
                String result = ArtemisHttpUtil.doPostStringArtemis(pathMap, queryParams.toJSONString(new JSONWriter.Feature[0]), null, null, "application/json");
                if (Objects.isNull(result)) {
                    result = ArtemisHttpUtil.doPostStringArtemis(pathMap, queryParams.toJSONString(new JSONWriter.Feature[0]), null, null, "application/json");
                }
                log.info("将人员添加到访问权限组参数{},结果:{}", queryParams.toJSONString(new JSONWriter.Feature[0]), result);
                if (org.apache.commons.lang3.StringUtils.isNotBlank(result)) {
                    JSONObject json = JSONObject.parseObject(result);
                    if (Objects.nonNull(json.get("code"))) {
                        if (successCode.equals(json.get("code").toString())) {
                            success = false;
                        }
                    }
                }
            } catch (Exception e) {
                log.error("将人员添加到访问权限组请求失败，url:{}，msg:{}", PersonSynchronizerConstants.ADD_PESONS_PRIVILEGE, e.getMessage());
            }
        }
        return success;
    }
    public static boolean deletePesonsPrivilege(List<String> personIds) {
        boolean success = true;
        log.info("开始处理删除人员权限");
        if (!CollectionUtils.isEmpty(personIds)) {
            Map<String, String> pathMap = new HashMap<>();
            pathMap.put("https://", PersonSynchronizerConstants.DELETE_PESONS_PRIVILEGE);
            JSONObject queryParams = new JSONObject();
            queryParams.put("privilegeGroupId", "1");
            queryParams.put("type", 1);
            List<Map<String, String>> list = new ArrayList<>();
            personIds.stream().forEach(s -> {
                HashMap map = new HashMap();
                map.put("id", s);
                list.add(map);
            });
            queryParams.put("list", list);
            try {
                String result = ArtemisHttpUtil.doPostStringArtemis(pathMap, queryParams.toJSONString(new JSONWriter.Feature[0]), null, null, "application/json");
                if (Objects.isNull(result)) {
                    result = ArtemisHttpUtil.doPostStringArtemis(pathMap, queryParams.toJSONString(new JSONWriter.Feature[0]), null, null, "application/json");
                }
                log.info("删除权限组参数{},结果:{}", queryParams.toJSONString(new JSONWriter.Feature[0]), result);
                if (org.apache.commons.lang3.StringUtils.isNotBlank(result)) {
                    JSONObject json = JSONObject.parseObject(result);
                    if (Objects.nonNull(json.get("code"))) {
                        if (successCode.equals(json.get("code").toString())) {
                            success = false;
                        }
                    }
                }
            } catch (Exception e) {
                log.error("删除权限组请求失败，url:{}，msg:{}", PersonSynchronizerConstants.DELETE_PESONS_PRIVILEGE, e.getMessage());
            }
        }
        return success;
    }
    public static boolean authReapplication() {
        boolean success = true;
        Map<String, String> pathMap = new HashMap<>();
        pathMap.put("https://", PersonSynchronizerConstants.SYNC_PERMISSIONS);
        JSONObject queryParams = new JSONObject();
        try {
            String result = ArtemisHttpUtil.doPostStringArtemis(pathMap, queryParams.toJSONString(new JSONWriter.Feature[0]), null, null, "application/json");
            if (Objects.isNull(result)) {
                result = ArtemisHttpUtil.doPostStringArtemis(pathMap, queryParams.toJSONString(new JSONWriter.Feature[0]), null, null, "application/json");
            }
            log.info("同步访问控制设备的权限结果:{}", result);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(result)) {
                JSONObject json = JSONObject.parseObject(result);
                if (Objects.nonNull(json.get("code"))) {
                    if (successCode.equals(json.get("code").toString())) {
                        success = false;
                    } else {
                        log.info("失败同步访问控制设备的权限");
                    }
                }
            }
        } catch (Exception e) {
            log.error("同步访问控制设备的权限请求失败，url:{}，msg:{}", PersonSynchronizerConstants.SYNC_PERMISSIONS, e.getMessage());
        }
        return success;
    }
    public static List<List<Person>> getListInBatches(List<Person> list) {
        List<List<Person>> resultList = new ArrayList<>();
        int listSize = list.size();
        int index = 500;
        for (int i = 0; i < list.size(); i += 500) {
            if (i + 500 > listSize) {
                index = listSize - i;
            }
            List newList = list.subList(i, i + index);
            resultList.add(newList);
        }
        return resultList;
    }
    public static void separatingEmployeeList(List<Person> list) {
        List<String> separatingEmployee = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            list.stream().forEach(p -> {
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(p.getPersonCode())) {
                    separatingEmployee.add(p.getPersonCode());
                }
            });
        }
        if (!CollectionUtils.isEmpty(separatingEmployee)) {
            deletePesonsPrivilege(separatingEmployee);
            authReapplication();
        }
    }
    public static List<AccessRecordParmVO> addHistoricalAccessRecord(SystemApi historySystemApi, LocalDateTime currentTime) {
        List<AccessRecordParmVO> list = new ArrayList<>();
        JSONObject queryParams = accessRecordParm(historySystemApi, currentTime, 1);
        if (getTakerecords(list, queryParams, 1).booleanValue()) {
            return list;
        }
        return null;
    }
    public static Boolean getTakerecords(List<AccessRecordParmVO> list, JSONObject queryParams, Integer pageNo) throws NumberFormatException {
        log.info("获取第:{}页数据", pageNo);
        Boolean success = false;
        queryParams.put("pageNo", pageNo);
        Map<String, String> pathMap = new HashMap<>();
        pathMap.put("https://", PersonSynchronizerConstants.historicalAccessRecord);
        try {
            String result = ArtemisHttpUtil.doPostStringArtemis(pathMap, queryParams.toJSONString(new JSONWriter.Feature[0]), null, null, "application/json");
            if (Objects.isNull(result)) {
                result = ArtemisHttpUtil.doPostStringArtemis(pathMap, queryParams.toJSONString(new JSONWriter.Feature[0]), null, null, "application/json");
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(result)) {
                JSONObject json = JSONObject.parseObject(result);
                if (Objects.nonNull(json.get("code"))) {
                    if (successCode.equals(json.get("code").toString())) {
                        success = true;
                        JSONObject data = JSONObject.parseObject(json.get("data").toString());
                        if (Objects.nonNull(data)) {
                            int total = Integer.parseInt(data.get(JsonResult.TOTAL).toString());
                            JSONArray jsonArray = data.getJSONArray("list");
                            if (CollUtil.isNotEmpty((Collection<?>) jsonArray)) {
                                jsonArray.forEach(s -> {
                                    JSONObject ss = (JSONObject) s;
                                    AccessRecordParmVO res = new AccessRecordParmVO();
                                    res.setReaderIndexCode(String.valueOf(ss.get("readerIndexCode ")));
                                    res.setDoorName(String.valueOf(ss.get("doorName")));
                                    res.setPersonName(String.valueOf(ss.get("personName")));
                                    res.setPersonId(String.valueOf(ss.get("personId")));
                                    LocalDateTime deviceTime = com.xrkc.core.utils.DateUtils.getDateStrFromISO8601(String.valueOf(ss.get("deviceTime")));
                                    res.setDeviceTime(deviceTime);
                                    res.setReaderName(String.valueOf(ss.get("readerName ")));
                                    res.setUniqueValue(com.xrkc.core.utils.DateUtils.toStringTime(deviceTime) + "-" + res.getPersonId());
                                    list.add(res);
                                });
                                if (total == PersonSynchronizerConstants.pageSize.intValue()) {
                                    getTakerecords(list, queryParams, Integer.valueOf(pageNo.intValue() + 1));
                                }
                            }
                        }
                    } else {
                        log.info("失败获取进出记录:{}", json.get("msg"));
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取历史的进出记录请求失败，url:{}，msg:{}", PersonSynchronizerConstants.historicalAccessRecord, e.getMessage());
        }
        return success;
    }
    public static JSONObject accessRecordParm(SystemApi api, LocalDateTime currentTime, Integer pageNo) {
        JSONObject queryParams = new JSONObject();
        queryParams.put("endTime", com.xrkc.core.utils.DateUtils.getISO8601TimestampFromDateStr(currentTime));
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(api.getHost())) {
            queryParams.put("doorIndexCodes", Arrays.stream(api.getHost().split(",")).collect(Collectors.toSet()));
        } else {
            queryParams.put("doorIndexCodes", Boolean.valueOf(new HashSet().add("258")));
        }
        queryParams.put("pageNo", pageNo);
        queryParams.put("pageSize", PersonSynchronizerConstants.pageSize);
        queryParams.put("eventType", ConfigValue.event_type);
        LocalDateTime beforeTime = currentTime.minus(31L, (TemporalUnit) ChronoUnit.DAYS);
        if (api.getUpdateTime().isBefore(beforeTime)) {
            api.setUpdateTime(beforeTime);
        }
        queryParams.put("startTime", com.xrkc.core.utils.DateUtils.getISO8601TimestampFromDateStr(api.getUpdateTime()));
        return queryParams;
    }
}
