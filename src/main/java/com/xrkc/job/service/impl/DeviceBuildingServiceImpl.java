package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.job.domain.DeviceBuilding;
import com.xrkc.job.mapper.DeviceBuildingMapper;
import com.xrkc.job.service.DeviceBuildingService;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/DeviceBuildingServiceImpl.class */
public class DeviceBuildingServiceImpl extends ServiceImpl<DeviceBuildingMapper, DeviceBuilding> implements DeviceBuildingService {
    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.xrkc.job.service.DeviceBuildingService
    public DeviceBuilding queryOneExist(String buildingName) {
        LambdaQueryWrapper<DeviceBuilding> wrapper = (LambdaQueryWrapper) Wrappers.lambdaQuery(DeviceBuilding.class).eq((v0) -> {
            return v0.getBuildingName();
        }, buildingName);
        return getOne(wrapper);
    }
}
