package com.xrkc.job.service;
import com.xrkc.job.domain.PositionDataCalc;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IPositionHistoryService.class */
public interface IPositionHistoryService {
    boolean existTable(String str);
    int createTable(String str);
    int batchBakHistory(String str, List<PositionDataCalc> list);
    int updateOrInsert(String str, List<PositionDataCalc> list, int i);
    List<String> selectDeleteHistoryTable();
    int deleteTable(String str);
}
