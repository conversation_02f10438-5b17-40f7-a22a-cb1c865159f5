package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.job.domain.RehearsalPlan;
import com.xrkc.job.mapper.RehearsalPlanMapper;
import com.xrkc.job.service.IRehearsalPlanService;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/RehearsalPlanServiceImpl.class */
public class RehearsalPlanServiceImpl extends ServiceImpl<RehearsalPlanMapper, RehearsalPlan> implements IRehearsalPlanService {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) RehearsalPlanServiceImpl.class);
    @Autowired
    private RehearsalPlanMapper rehearsalPlanMapper;
    @Override // com.xrkc.job.service.IRehearsalPlanService
    public List<RehearsalPlan> selectRehearsalPlanList() {
        return this.rehearsalPlanMapper.selectRehearsalPlanList();
    }
    @Override // com.xrkc.job.service.IRehearsalPlanService
    public int batchUpdateStatus(String rehearsalStatus, List<Long> rehearsalIds) {
        if (StringUtils.isNotBlank(rehearsalStatus) && !CollectionUtils.isEmpty(rehearsalIds)) {
            return this.rehearsalPlanMapper.batchUpdateStatus(rehearsalStatus, rehearsalIds);
        }
        return 0;
    }
}
