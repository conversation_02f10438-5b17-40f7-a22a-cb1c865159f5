package com.xrkc.job.task;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.xrkc.core.constant.Constants;
import com.xrkc.core.domain.vehicle.VehicleAlarm;
import com.xrkc.job.controller.WebSocketServer;
import com.xrkc.job.module.vehicle.service.IVehicleAlarmService;
import com.xrkc.job.util.MQTTPublishClient;
import com.xrkc.job.vo.PushVehicleAlarmVO;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import net.jodah.expiringmap.ExpiringMap;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
@Component("vehiclePushAlarmTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/VehiclePushAlarmTask.class */
public class VehiclePushAlarmTask {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) PushAlarmTask.class);
    @Autowired
    private MQTTPublishClient mqttPublishClient;
    @Autowired
    private IVehicleAlarmService vehicleAlarmService;
    @Autowired
    private WebSocketServer webSocketServer;
    ExpiringMap<String, LocalDateTime> mqttVehiclePushAlarmMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    ExpiringMap<String, LocalDateTime> mqttVehiclePushAlarmMapNew = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    ExpiringMap<Long, LocalDateTime> wsVehiclePushAlarmMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    @Deprecated
    public void vehicleAlarmMqttPush() {
        if (this.mqttPublishClient.getClient() != null && this.mqttPublishClient.getClient().isConnected()) {
            String topic = "/vehicleAlarm";
            List<VehicleAlarm> list = this.vehicleAlarmService.selectCurrentUnDisposeList();
            list.forEach(t -> {
                String key = "alarmVehicle" + t.getId();
                if (!this.mqttVehiclePushAlarmMap.containsKey(key) || this.mqttVehiclePushAlarmMap.get(key).isBefore(t.getAlarmTime())) {
                    PushVehicleAlarmVO vo = PushVehicleAlarmVO.builder().id(t.getId()).alarmId(t.getId()).alarmStatus("15").alarmTime(t.getAlarmTime()).alarmType(t.getAlarmType()).alarmTypeName(t.getAlarmTypeName()).carNo(t.getLicensePlateNumber()).longitude(t.getLongitude()).latitude(t.getLatitude()).dir(t.getDirection()).speed(t.getSpeed()).remark(t.getRemark()).build();
                    JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(vo));
                    String msg = JSON.toJSONString(jsonObject, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible);
                    log.info("准备推送车辆报警数据：{}", msg);
                    if (this.mqttPublishClient.publish(null, topic, msg).booleanValue()) {
                        this.mqttVehiclePushAlarmMap.put(key, t.getAlarmTime());
                    }
                }
            });
        }
    }
    /* JADX WARN: Multi-variable type inference failed */
    public void vehicleAlarmMqttPushNew() {
        if (this.mqttPublishClient.getClient() != null && this.mqttPublishClient.getClient().isConnected()) {
            String topic = "/vehicle/alarm";
            List<Object> list = ((LambdaQueryChainWrapper) ((LambdaQueryChainWrapper) this.vehicleAlarmService.lambdaSelectList().eq((v0) -> {
                return v0.getAlarmStatus();
            }, "15")).ge((v0) -> {
                return v0.getAlarmTime();
            }, LocalDateTime.now().minusMinutes(1L))).list();
            Collection list2 = ((LambdaQueryChainWrapper) ((LambdaQueryChainWrapper) this.vehicleAlarmService.lambdaSelectList().eq((v0) -> {
                return v0.getAlarmStatus();
            }, "20")).ge((v0) -> {
                return v0.getDisposeTime();
            }, LocalDateTime.now().minusMinutes(1L))).list();
            if (!CollectionUtils.isEmpty((Collection<?>) list2)) {
                list.addAll(list2);
            }
            list.forEach(alarm -> {
                String key = "alarmVehicle_" + alarm.getId() + "_" + alarm.getAlarmStatus();
                if (!this.mqttVehiclePushAlarmMapNew.containsKey(key) || this.mqttVehiclePushAlarmMapNew.get(key).isBefore(alarm.getAlarmTime())) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("uniqueId", UUID.randomUUID().toString());
                    jsonObject.put("id", alarm.getId());
                    jsonObject.put("vehicleId", alarm.getVehicleId());
                    jsonObject.put("cardId", alarm.getCardId());
                    jsonObject.put("licensePlateNumber", alarm.getLicensePlateNumber());
                    jsonObject.put("driverName", alarm.getDriverName());
                    jsonObject.put("driverTel", alarm.getDriverTel());
                    jsonObject.put("alarmTime", alarm.getAlarmTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    jsonObject.put("companyName", alarm.getCompanyName());
                    jsonObject.put("remark", alarm.getRemark());
                    jsonObject.put("alarmStatus", alarm.getAlarmStatus());
                    jsonObject.put("alarmType", alarm.getAlarmType());
                    jsonObject.put("alarmTypeName", alarm.getAlarmTypeName());
                    jsonObject.put("latitude", alarm.getLatitude());
                    jsonObject.put("longitude", alarm.getLongitude());
                    jsonObject.put("speed", alarm.getSpeed());
                    if ("20".equals(alarm.getAlarmStatus())) {
                        jsonObject.put("disposeBy", alarm.getDisposeBy());
                        jsonObject.put("disposeTime", alarm.getDisposeTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        jsonObject.put("disposeFeedback", alarm.getDisposeFeedback());
                    }
                    String msg = JSON.toJSONString(jsonObject, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible);
                    log.info("准备推送车辆报警数据：{}", msg);
                    if (this.mqttPublishClient.publish(null, topic, msg).booleanValue()) {
                        this.mqttVehiclePushAlarmMapNew.put(key, alarm.getAlarmTime());
                    }
                }
            });
        }
    }
    public void pushUnDisposedVehicleAlarm() {
        if (CollectionUtils.isEmpty(WebSocketServer.systemSessionMap)) {
            return;
        }
        Long count = this.vehicleAlarmService.countUnDisposed();
        JSONObject countJson = new JSONObject();
        countJson.put("msgType", "vehicleAlarmCount");
        countJson.put("data", count);
        this.webSocketServer.sendMessageForXR(null, countJson.toJSONString(new JSONWriter.Feature[0]));
        List<VehicleAlarm> list = this.vehicleAlarmService.selectCurrentUnDisposeList();
        if (!CollectionUtils.isEmpty(list)) {
            list.stream().forEach(t -> {
                String alarmName;
                if (!this.wsVehiclePushAlarmMap.containsKey(t.getId())) {
                    JSONObject alarmJson = new JSONObject();
                    JSONObject data = new JSONObject();
                    data.put("alarmId", t.getId());
                    data.put("driverTel", t.getDriverTel());
                    data.put("acceptTime", t.getAlarmTime().format(Constants.DATE_TIME_FORMATTER));
                    if ("3".equals(t.getAlarmType())) {
                        alarmName = "车辆定位卡低电提醒，请及时充电";
                    } else {
                        StringBuilder str = new StringBuilder(VehicleMessageTask.source);
                        str.append(StringUtils.isNotBlank(t.getVehicleName()) ? t.getVehicleName() : t.getCardId());
                        if (Objects.nonNull(t.getCardId())) {
                            str.append(StringPool.LEFT_BRACKET);
                            str.append(t.getCardId());
                            str.append(StringPool.RIGHT_BRACKET);
                        }
                        str.append("触发");
                        str.append(t.getAlarmTypeName());
                        alarmName = str.toString();
                    }
                    data.put("alarmName", alarmName);
                    alarmJson.put("msgType", "vehicleAlarm");
                    alarmJson.put("data", data);
                    log.debug("websocket推送未处理车辆报警: {}", alarmJson.toJSONString(new JSONWriter.Feature[0]));
                    this.webSocketServer.sendMessageForXR(null, alarmJson.toJSONString(new JSONWriter.Feature[0]));
                    this.wsVehiclePushAlarmMap.put(t.getId(), t.getAlarmTime());
                }
            });
        }
    }
}
