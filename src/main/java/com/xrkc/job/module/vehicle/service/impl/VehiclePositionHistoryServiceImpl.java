package com.xrkc.job.module.vehicle.service.impl;
import com.xrkc.core.constant.CacheConstants;
import com.xrkc.core.domain.vehicle.VehiclePositionHistory;
import com.xrkc.datascope.config.RequestDynamicTableNameHelper;
import com.xrkc.job.module.vehicle.mapper.VehiclePositionHistoryMapper;
import com.xrkc.job.module.vehicle.service.IVehiclePositionHistoryService;
import com.xrkc.job.service.IPositionHistoryService;
import com.xrkc.redis.service.RedisService;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/module/vehicle/service/impl/VehiclePositionHistoryServiceImpl.class */
public class VehiclePositionHistoryServiceImpl implements IVehiclePositionHistoryService {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) VehiclePositionHistoryServiceImpl.class);
    @Autowired
    private VehiclePositionHistoryMapper mapper;
    @Autowired
    private IPositionHistoryService positionHistoryService;
    @Autowired
    private RedisService redisService;
    @Value("${spring.datasource.driver-class-name}")
    private String driverClassName;
    @Override // com.xrkc.job.module.vehicle.service.IVehiclePositionHistoryService
    public int batchBakHistory(String tableName, List<VehiclePositionHistory> list) {
        if (StringUtils.isBlank(tableName) || CollectionUtils.isEmpty(list)) {
            return 0;
        }
        list.forEach(t -> {
            t.setId(null);
            t.setCreateTime(LocalDateTime.now());
        });
        String datasourceId = "master";
        List<VehiclePositionHistory> insertList = (List) list.stream().filter(f -> {
            return !this.redisService.hasKey(getPositionHistoryKey(datasourceId, f.getCardId(), f.getAcceptTime()));
        }).collect(Collectors.toList());
        int rows = 0;
        if (!CollectionUtils.isEmpty(insertList)) {
            LocalDate yesterday = LocalDate.now().minusDays(1L);
            ArrayList arrayList = new ArrayList();
            ArrayList arrayList2 = new ArrayList();
            insertList.forEach(f2 -> {
                if (yesterday.equals(f2.getAcceptTime().toLocalDate())) {
                    arrayList.add(f2);
                } else {
                    arrayList2.add(f2);
                }
            });
            if (!CollectionUtils.isEmpty(arrayList)) {
                String baseTableName = this.driverClassName.contains("dm") ? "VEHICLE_POSITION_HISTORY_" : "vehicle_position_history_";
                String yesterdayTableName = baseTableName + yesterday.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                if (!this.positionHistoryService.existTable(yesterdayTableName)) {
                    createTable(yesterdayTableName);
                }
                try {
                    RequestDynamicTableNameHelper.setRequestData(yesterdayTableName);
                    this.mapper.insertBatch(arrayList, 1000);
                } catch (Exception e) {
                    log.error("切换动态表名批量保存车辆轨迹错误：{}，msg:{}", tableName, e.getMessage());
                } finally {
                }
                arrayList.forEach(f3 -> {
                    this.redisService.set(getPositionHistoryKey(datasourceId, ((VehiclePositionHistory)f3).getCardId(), ((VehiclePositionHistory)f3).getAcceptTime()), ((VehiclePositionHistory)f3).getVehicleId(), 420L);
                });
                rows = 0 + arrayList.size();
            }
            if (!CollectionUtils.isEmpty(arrayList2)) {
                if (!this.positionHistoryService.existTable(tableName)) {
                    createTable(tableName);
                }
                try {
                    RequestDynamicTableNameHelper.setRequestData(tableName);
                    this.mapper.insertBatch(arrayList2, 1000);
                } catch (Exception e2) {
                    log.error("切换动态表名批量保存车辆轨迹错误：{}，msg:{}", tableName, e2.getMessage());
                } finally {
                }
                arrayList2.forEach(f4 -> {
                    this.redisService.set(getPositionHistoryKey(datasourceId, ((VehiclePositionHistory)f4).getCardId(), ((VehiclePositionHistory)f4).getAcceptTime()), ((VehiclePositionHistory)f4).getVehicleId(), 420L);
                });
            }
            rows += arrayList2.size();
        }
        return rows;
    }
    private static String getPositionHistoryKey(String datasourceId, Long cardId, LocalDateTime acceptTime) {
        return CacheConstants.vehicleTrackKeyPrefix + datasourceId + ":" + cardId + "_" + acceptTime.toString().replaceAll(":", "-").replaceAll("\\.", "-");
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehiclePositionHistoryService
    public void createTable(String tableName) {
        this.mapper.createTable(tableName);
    }
}
