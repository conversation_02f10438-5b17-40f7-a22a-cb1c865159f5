package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xrkc.core.annotation.Excel;
import com.xrkc.core.domain.basic.BasicEntity;
import java.time.LocalDateTime;
@TableName(value = "device_card_sender_vehicle_log", autoResultMap = true)
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/DeviceCardSenderVehicleLog.class */
public class DeviceCardSenderVehicleLog extends BasicEntity {
    @TableId("id")
    private Long id;
    @Excel(name = "车辆类别", readConverterExp = "internal=内部,visitor=访客")
    private String vehicleCategory;
    @Excel(name = "车辆名称")
    private String vehicleName;
    @Excel(name = "车辆类型", readConverterExp = "50=汽车,51=货车,52=叉车,53=消防车")
    private String vehicleType;
    @Excel(name = "车载卡号")
    private Long cardId;
    private Long vehicleId;
    @Excel(name = "车牌号")
    private String licensePlateNumber;
    @Excel(name = "司机姓名")
    private String driverName;
    @Excel(name = "身份证号")
    private String idNumber;
    @Excel(name = "司机手机号")
    private String driverTel;
    @Excel(name = "单位名称")
    private String companyName;
    @Excel(name = "发卡机SN")
    private String deviceSn;
    @Excel(name = "柜组")
    private Integer deviceAims;
    @Excel(name = "柜号")
    private Integer deviceNum;
    @Excel(name = "类型", readConverterExp = "0=还卡,1=发卡,3=发卡中")
    private Integer cardSenderType;
    @Excel(name = "结果")
    private String result;
    @Excel(name = "认证方式", readConverterExp = "face=人脸,qr=扫码,id=身份证,ic=刷卡")
    private String identifyType;
    @Excel(name = "发卡方式")
    private String rentType;
    @Excel(name = "备注")
    private String remark;
    @Excel(name = "记录时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    private String deviceName;
    private LocalDateTime commandTime;
    private LocalDateTime closedTime;
    private LocalDateTime updateTime;
    @TableField(exist = false)
    private String createBy;
    @TableField(exist = false)
    private String updateBy;
    @TableField("notify_status")
    private String notifyStatus;
    public void setId(Long id) {
        this.id = id;
    }
    public void setVehicleCategory(String vehicleCategory) {
        this.vehicleCategory = vehicleCategory;
    }
    public void setVehicleName(String vehicleName) {
        this.vehicleName = vehicleName;
    }
    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }
    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }
    public void setVehicleId(Long vehicleId) {
        this.vehicleId = vehicleId;
    }
    public void setLicensePlateNumber(String licensePlateNumber) {
        this.licensePlateNumber = licensePlateNumber;
    }
    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }
    public void setIdNumber(String idNumber) {
        this.idNumber = idNumber;
    }
    public void setDriverTel(String driverTel) {
        this.driverTel = driverTel;
    }
    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
    public void setDeviceSn(String deviceSn) {
        this.deviceSn = deviceSn;
    }
    public void setDeviceAims(Integer deviceAims) {
        this.deviceAims = deviceAims;
    }
    public void setDeviceNum(Integer deviceNum) {
        this.deviceNum = deviceNum;
    }
    public void setCardSenderType(Integer cardSenderType) {
        this.cardSenderType = cardSenderType;
    }
    public void setResult(String result) {
        this.result = result;
    }
    public void setIdentifyType(String identifyType) {
        this.identifyType = identifyType;
    }
    public void setRentType(String rentType) {
        this.rentType = rentType;
    }
    @Override // com.xrkc.core.domain.basic.BasicEntity
    public void setRemark(String remark) {
        this.remark = remark;
    }
    @Override // com.xrkc.core.domain.basic.BasicEntity
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }
    public void setCommandTime(LocalDateTime commandTime) {
        this.commandTime = commandTime;
    }
    public void setClosedTime(LocalDateTime closedTime) {
        this.closedTime = closedTime;
    }
    @Override // com.xrkc.core.domain.basic.BasicEntity
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    @Override // com.xrkc.core.domain.basic.BasicEntity
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    @Override // com.xrkc.core.domain.basic.BasicEntity
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public void setNotifyStatus(String notifyStatus) {
        this.notifyStatus = notifyStatus;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof DeviceCardSenderVehicleLog)) {
            return false;
        }
        DeviceCardSenderVehicleLog other = (DeviceCardSenderVehicleLog) o;
        if (!other.canEqual(this) || !super.equals(o)) {
            return false;
        }
        Object this$id = getId();
        Object other$id = other.getId();
        if (this$id == null) {
            if (other$id != null) {
                return false;
            }
        } else if (!this$id.equals(other$id)) {
            return false;
        }
        Object this$cardId = getCardId();
        Object other$cardId = other.getCardId();
        if (this$cardId == null) {
            if (other$cardId != null) {
                return false;
            }
        } else if (!this$cardId.equals(other$cardId)) {
            return false;
        }
        Object this$vehicleId = getVehicleId();
        Object other$vehicleId = other.getVehicleId();
        if (this$vehicleId == null) {
            if (other$vehicleId != null) {
                return false;
            }
        } else if (!this$vehicleId.equals(other$vehicleId)) {
            return false;
        }
        Object this$deviceAims = getDeviceAims();
        Object other$deviceAims = other.getDeviceAims();
        if (this$deviceAims == null) {
            if (other$deviceAims != null) {
                return false;
            }
        } else if (!this$deviceAims.equals(other$deviceAims)) {
            return false;
        }
        Object this$deviceNum = getDeviceNum();
        Object other$deviceNum = other.getDeviceNum();
        if (this$deviceNum == null) {
            if (other$deviceNum != null) {
                return false;
            }
        } else if (!this$deviceNum.equals(other$deviceNum)) {
            return false;
        }
        Object this$cardSenderType = getCardSenderType();
        Object other$cardSenderType = other.getCardSenderType();
        if (this$cardSenderType == null) {
            if (other$cardSenderType != null) {
                return false;
            }
        } else if (!this$cardSenderType.equals(other$cardSenderType)) {
            return false;
        }
        Object this$vehicleCategory = getVehicleCategory();
        Object other$vehicleCategory = other.getVehicleCategory();
        if (this$vehicleCategory == null) {
            if (other$vehicleCategory != null) {
                return false;
            }
        } else if (!this$vehicleCategory.equals(other$vehicleCategory)) {
            return false;
        }
        Object this$vehicleName = getVehicleName();
        Object other$vehicleName = other.getVehicleName();
        if (this$vehicleName == null) {
            if (other$vehicleName != null) {
                return false;
            }
        } else if (!this$vehicleName.equals(other$vehicleName)) {
            return false;
        }
        Object this$vehicleType = getVehicleType();
        Object other$vehicleType = other.getVehicleType();
        if (this$vehicleType == null) {
            if (other$vehicleType != null) {
                return false;
            }
        } else if (!this$vehicleType.equals(other$vehicleType)) {
            return false;
        }
        Object this$licensePlateNumber = getLicensePlateNumber();
        Object other$licensePlateNumber = other.getLicensePlateNumber();
        if (this$licensePlateNumber == null) {
            if (other$licensePlateNumber != null) {
                return false;
            }
        } else if (!this$licensePlateNumber.equals(other$licensePlateNumber)) {
            return false;
        }
        Object this$driverName = getDriverName();
        Object other$driverName = other.getDriverName();
        if (this$driverName == null) {
            if (other$driverName != null) {
                return false;
            }
        } else if (!this$driverName.equals(other$driverName)) {
            return false;
        }
        Object this$idNumber = getIdNumber();
        Object other$idNumber = other.getIdNumber();
        if (this$idNumber == null) {
            if (other$idNumber != null) {
                return false;
            }
        } else if (!this$idNumber.equals(other$idNumber)) {
            return false;
        }
        Object this$driverTel = getDriverTel();
        Object other$driverTel = other.getDriverTel();
        if (this$driverTel == null) {
            if (other$driverTel != null) {
                return false;
            }
        } else if (!this$driverTel.equals(other$driverTel)) {
            return false;
        }
        Object this$companyName = getCompanyName();
        Object other$companyName = other.getCompanyName();
        if (this$companyName == null) {
            if (other$companyName != null) {
                return false;
            }
        } else if (!this$companyName.equals(other$companyName)) {
            return false;
        }
        Object this$deviceSn = getDeviceSn();
        Object other$deviceSn = other.getDeviceSn();
        if (this$deviceSn == null) {
            if (other$deviceSn != null) {
                return false;
            }
        } else if (!this$deviceSn.equals(other$deviceSn)) {
            return false;
        }
        Object this$result = getResult();
        Object other$result = other.getResult();
        if (this$result == null) {
            if (other$result != null) {
                return false;
            }
        } else if (!this$result.equals(other$result)) {
            return false;
        }
        Object this$identifyType = getIdentifyType();
        Object other$identifyType = other.getIdentifyType();
        if (this$identifyType == null) {
            if (other$identifyType != null) {
                return false;
            }
        } else if (!this$identifyType.equals(other$identifyType)) {
            return false;
        }
        Object this$rentType = getRentType();
        Object other$rentType = other.getRentType();
        if (this$rentType == null) {
            if (other$rentType != null) {
                return false;
            }
        } else if (!this$rentType.equals(other$rentType)) {
            return false;
        }
        Object this$remark = getRemark();
        Object other$remark = other.getRemark();
        if (this$remark == null) {
            if (other$remark != null) {
                return false;
            }
        } else if (!this$remark.equals(other$remark)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        if (this$createTime == null) {
            if (other$createTime != null) {
                return false;
            }
        } else if (!this$createTime.equals(other$createTime)) {
            return false;
        }
        Object this$deviceName = getDeviceName();
        Object other$deviceName = other.getDeviceName();
        if (this$deviceName == null) {
            if (other$deviceName != null) {
                return false;
            }
        } else if (!this$deviceName.equals(other$deviceName)) {
            return false;
        }
        Object this$commandTime = getCommandTime();
        Object other$commandTime = other.getCommandTime();
        if (this$commandTime == null) {
            if (other$commandTime != null) {
                return false;
            }
        } else if (!this$commandTime.equals(other$commandTime)) {
            return false;
        }
        Object this$closedTime = getClosedTime();
        Object other$closedTime = other.getClosedTime();
        if (this$closedTime == null) {
            if (other$closedTime != null) {
                return false;
            }
        } else if (!this$closedTime.equals(other$closedTime)) {
            return false;
        }
        Object this$updateTime = getUpdateTime();
        Object other$updateTime = other.getUpdateTime();
        if (this$updateTime == null) {
            if (other$updateTime != null) {
                return false;
            }
        } else if (!this$updateTime.equals(other$updateTime)) {
            return false;
        }
        Object this$createBy = getCreateBy();
        Object other$createBy = other.getCreateBy();
        if (this$createBy == null) {
            if (other$createBy != null) {
                return false;
            }
        } else if (!this$createBy.equals(other$createBy)) {
            return false;
        }
        Object this$updateBy = getUpdateBy();
        Object other$updateBy = other.getUpdateBy();
        if (this$updateBy == null) {
            if (other$updateBy != null) {
                return false;
            }
        } else if (!this$updateBy.equals(other$updateBy)) {
            return false;
        }
        Object this$notifyStatus = getNotifyStatus();
        Object other$notifyStatus = other.getNotifyStatus();
        return this$notifyStatus == null ? other$notifyStatus == null : this$notifyStatus.equals(other$notifyStatus);
    }
    protected boolean canEqual(Object other) {
        return other instanceof DeviceCardSenderVehicleLog;
    }
    public int hashCode() {
        int result = super.hashCode();
        Object $id = getId();
        int result2 = (result * 59) + ($id == null ? 43 : $id.hashCode());
        Object $cardId = getCardId();
        int result3 = (result2 * 59) + ($cardId == null ? 43 : $cardId.hashCode());
        Object $vehicleId = getVehicleId();
        int result4 = (result3 * 59) + ($vehicleId == null ? 43 : $vehicleId.hashCode());
        Object $deviceAims = getDeviceAims();
        int result5 = (result4 * 59) + ($deviceAims == null ? 43 : $deviceAims.hashCode());
        Object $deviceNum = getDeviceNum();
        int result6 = (result5 * 59) + ($deviceNum == null ? 43 : $deviceNum.hashCode());
        Object $cardSenderType = getCardSenderType();
        int result7 = (result6 * 59) + ($cardSenderType == null ? 43 : $cardSenderType.hashCode());
        Object $vehicleCategory = getVehicleCategory();
        int result8 = (result7 * 59) + ($vehicleCategory == null ? 43 : $vehicleCategory.hashCode());
        Object $vehicleName = getVehicleName();
        int result9 = (result8 * 59) + ($vehicleName == null ? 43 : $vehicleName.hashCode());
        Object $vehicleType = getVehicleType();
        int result10 = (result9 * 59) + ($vehicleType == null ? 43 : $vehicleType.hashCode());
        Object $licensePlateNumber = getLicensePlateNumber();
        int result11 = (result10 * 59) + ($licensePlateNumber == null ? 43 : $licensePlateNumber.hashCode());
        Object $driverName = getDriverName();
        int result12 = (result11 * 59) + ($driverName == null ? 43 : $driverName.hashCode());
        Object $idNumber = getIdNumber();
        int result13 = (result12 * 59) + ($idNumber == null ? 43 : $idNumber.hashCode());
        Object $driverTel = getDriverTel();
        int result14 = (result13 * 59) + ($driverTel == null ? 43 : $driverTel.hashCode());
        Object $companyName = getCompanyName();
        int result15 = (result14 * 59) + ($companyName == null ? 43 : $companyName.hashCode());
        Object $deviceSn = getDeviceSn();
        int result16 = (result15 * 59) + ($deviceSn == null ? 43 : $deviceSn.hashCode());
        Object $result = getResult();
        int result17 = (result16 * 59) + ($result == null ? 43 : $result.hashCode());
        Object $identifyType = getIdentifyType();
        int result18 = (result17 * 59) + ($identifyType == null ? 43 : $identifyType.hashCode());
        Object $rentType = getRentType();
        int result19 = (result18 * 59) + ($rentType == null ? 43 : $rentType.hashCode());
        Object $remark = getRemark();
        int result20 = (result19 * 59) + ($remark == null ? 43 : $remark.hashCode());
        Object $createTime = getCreateTime();
        int result21 = (result20 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $deviceName = getDeviceName();
        int result22 = (result21 * 59) + ($deviceName == null ? 43 : $deviceName.hashCode());
        Object $commandTime = getCommandTime();
        int result23 = (result22 * 59) + ($commandTime == null ? 43 : $commandTime.hashCode());
        Object $closedTime = getClosedTime();
        int result24 = (result23 * 59) + ($closedTime == null ? 43 : $closedTime.hashCode());
        Object $updateTime = getUpdateTime();
        int result25 = (result24 * 59) + ($updateTime == null ? 43 : $updateTime.hashCode());
        Object $createBy = getCreateBy();
        int result26 = (result25 * 59) + ($createBy == null ? 43 : $createBy.hashCode());
        Object $updateBy = getUpdateBy();
        int result27 = (result26 * 59) + ($updateBy == null ? 43 : $updateBy.hashCode());
        Object $notifyStatus = getNotifyStatus();
        return (result27 * 59) + ($notifyStatus == null ? 43 : $notifyStatus.hashCode());
    }
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/DeviceCardSenderVehicleLog$DeviceCardSenderVehicleLogBuilder.class */
    public static class DeviceCardSenderVehicleLogBuilder {
        private Long id;
        private String vehicleCategory;
        private String vehicleName;
        private String vehicleType;
        private Long cardId;
        private Long vehicleId;
        private String licensePlateNumber;
        private String driverName;
        private String idNumber;
        private String driverTel;
        private String companyName;
        private String deviceSn;
        private Integer deviceAims;
        private Integer deviceNum;
        private Integer cardSenderType;
        private String result;
        private String identifyType;
        private String rentType;
        private String remark;
        private LocalDateTime createTime;
        private String deviceName;
        private LocalDateTime commandTime;
        private LocalDateTime closedTime;
        private LocalDateTime updateTime;
        private String createBy;
        private String updateBy;
        private String notifyStatus;
        DeviceCardSenderVehicleLogBuilder() {
        }
        public DeviceCardSenderVehicleLogBuilder id(Long id) {
            this.id = id;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder vehicleCategory(String vehicleCategory) {
            this.vehicleCategory = vehicleCategory;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder vehicleName(String vehicleName) {
            this.vehicleName = vehicleName;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder vehicleType(String vehicleType) {
            this.vehicleType = vehicleType;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder cardId(Long cardId) {
            this.cardId = cardId;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder vehicleId(Long vehicleId) {
            this.vehicleId = vehicleId;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder licensePlateNumber(String licensePlateNumber) {
            this.licensePlateNumber = licensePlateNumber;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder driverName(String driverName) {
            this.driverName = driverName;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder idNumber(String idNumber) {
            this.idNumber = idNumber;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder driverTel(String driverTel) {
            this.driverTel = driverTel;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder companyName(String companyName) {
            this.companyName = companyName;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder deviceSn(String deviceSn) {
            this.deviceSn = deviceSn;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder deviceAims(Integer deviceAims) {
            this.deviceAims = deviceAims;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder deviceNum(Integer deviceNum) {
            this.deviceNum = deviceNum;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder cardSenderType(Integer cardSenderType) {
            this.cardSenderType = cardSenderType;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder result(String result) {
            this.result = result;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder identifyType(String identifyType) {
            this.identifyType = identifyType;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder rentType(String rentType) {
            this.rentType = rentType;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder remark(String remark) {
            this.remark = remark;
            return this;
        }
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        public DeviceCardSenderVehicleLogBuilder createTime(LocalDateTime createTime) {
            this.createTime = createTime;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder deviceName(String deviceName) {
            this.deviceName = deviceName;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder commandTime(LocalDateTime commandTime) {
            this.commandTime = commandTime;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder closedTime(LocalDateTime closedTime) {
            this.closedTime = closedTime;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder updateTime(LocalDateTime updateTime) {
            this.updateTime = updateTime;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder createBy(String createBy) {
            this.createBy = createBy;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder updateBy(String updateBy) {
            this.updateBy = updateBy;
            return this;
        }
        public DeviceCardSenderVehicleLogBuilder notifyStatus(String notifyStatus) {
            this.notifyStatus = notifyStatus;
            return this;
        }
        public DeviceCardSenderVehicleLog build() {
            return new DeviceCardSenderVehicleLog(this.id, this.vehicleCategory, this.vehicleName, this.vehicleType, this.cardId, this.vehicleId, this.licensePlateNumber, this.driverName, this.idNumber, this.driverTel, this.companyName, this.deviceSn, this.deviceAims, this.deviceNum, this.cardSenderType, this.result, this.identifyType, this.rentType, this.remark, this.createTime, this.deviceName, this.commandTime, this.closedTime, this.updateTime, this.createBy, this.updateBy, this.notifyStatus);
        }
        public String toString() {
            return "DeviceCardSenderVehicleLog.DeviceCardSenderVehicleLogBuilder(id=" + this.id + ", vehicleCategory=" + this.vehicleCategory + ", vehicleName=" + this.vehicleName + ", vehicleType=" + this.vehicleType + ", cardId=" + this.cardId + ", vehicleId=" + this.vehicleId + ", licensePlateNumber=" + this.licensePlateNumber + ", driverName=" + this.driverName + ", idNumber=" + this.idNumber + ", driverTel=" + this.driverTel + ", companyName=" + this.companyName + ", deviceSn=" + this.deviceSn + ", deviceAims=" + this.deviceAims + ", deviceNum=" + this.deviceNum + ", cardSenderType=" + this.cardSenderType + ", result=" + this.result + ", identifyType=" + this.identifyType + ", rentType=" + this.rentType + ", remark=" + this.remark + ", createTime=" + this.createTime + ", deviceName=" + this.deviceName + ", commandTime=" + this.commandTime + ", closedTime=" + this.closedTime + ", updateTime=" + this.updateTime + ", createBy=" + this.createBy + ", updateBy=" + this.updateBy + ", notifyStatus=" + this.notifyStatus + StringPool.RIGHT_BRACKET;
        }
    }
    public String toString() {
        return "DeviceCardSenderVehicleLog(super=" + super.toString() + ", id=" + getId() + ", vehicleCategory=" + getVehicleCategory() + ", vehicleName=" + getVehicleName() + ", vehicleType=" + getVehicleType() + ", cardId=" + getCardId() + ", vehicleId=" + getVehicleId() + ", licensePlateNumber=" + getLicensePlateNumber() + ", driverName=" + getDriverName() + ", idNumber=" + getIdNumber() + ", driverTel=" + getDriverTel() + ", companyName=" + getCompanyName() + ", deviceSn=" + getDeviceSn() + ", deviceAims=" + getDeviceAims() + ", deviceNum=" + getDeviceNum() + ", cardSenderType=" + getCardSenderType() + ", result=" + getResult() + ", identifyType=" + getIdentifyType() + ", rentType=" + getRentType() + ", remark=" + getRemark() + ", createTime=" + getCreateTime() + ", deviceName=" + getDeviceName() + ", commandTime=" + getCommandTime() + ", closedTime=" + getClosedTime() + ", updateTime=" + getUpdateTime() + ", createBy=" + getCreateBy() + ", updateBy=" + getUpdateBy() + ", notifyStatus=" + getNotifyStatus() + StringPool.RIGHT_BRACKET;
    }
    public static DeviceCardSenderVehicleLogBuilder builder() {
        return new DeviceCardSenderVehicleLogBuilder();
    }
    public DeviceCardSenderVehicleLog(Long id, String vehicleCategory, String vehicleName, String vehicleType, Long cardId, Long vehicleId, String licensePlateNumber, String driverName, String idNumber, String driverTel, String companyName, String deviceSn, Integer deviceAims, Integer deviceNum, Integer cardSenderType, String result, String identifyType, String rentType, String remark, LocalDateTime createTime, String deviceName, LocalDateTime commandTime, LocalDateTime closedTime, LocalDateTime updateTime, String createBy, String updateBy, String notifyStatus) {
        this.id = id;
        this.vehicleCategory = vehicleCategory;
        this.vehicleName = vehicleName;
        this.vehicleType = vehicleType;
        this.cardId = cardId;
        this.vehicleId = vehicleId;
        this.licensePlateNumber = licensePlateNumber;
        this.driverName = driverName;
        this.idNumber = idNumber;
        this.driverTel = driverTel;
        this.companyName = companyName;
        this.deviceSn = deviceSn;
        this.deviceAims = deviceAims;
        this.deviceNum = deviceNum;
        this.cardSenderType = cardSenderType;
        this.result = result;
        this.identifyType = identifyType;
        this.rentType = rentType;
        this.remark = remark;
        this.createTime = createTime;
        this.deviceName = deviceName;
        this.commandTime = commandTime;
        this.closedTime = closedTime;
        this.updateTime = updateTime;
        this.createBy = createBy;
        this.updateBy = updateBy;
        this.notifyStatus = notifyStatus;
    }
    public DeviceCardSenderVehicleLog() {
    }
    public Long getId() {
        return this.id;
    }
    public String getVehicleCategory() {
        return this.vehicleCategory;
    }
    public String getVehicleName() {
        return this.vehicleName;
    }
    public String getVehicleType() {
        return this.vehicleType;
    }
    public Long getCardId() {
        return this.cardId;
    }
    public Long getVehicleId() {
        return this.vehicleId;
    }
    public String getLicensePlateNumber() {
        return this.licensePlateNumber;
    }
    public String getDriverName() {
        return this.driverName;
    }
    public String getIdNumber() {
        return this.idNumber;
    }
    public String getDriverTel() {
        return this.driverTel;
    }
    public String getCompanyName() {
        return this.companyName;
    }
    public String getDeviceSn() {
        return this.deviceSn;
    }
    public Integer getDeviceAims() {
        return this.deviceAims;
    }
    public Integer getDeviceNum() {
        return this.deviceNum;
    }
    public Integer getCardSenderType() {
        return this.cardSenderType;
    }
    public String getResult() {
        return this.result;
    }
    public String getIdentifyType() {
        return this.identifyType;
    }
    public String getRentType() {
        return this.rentType;
    }
    @Override // com.xrkc.core.domain.basic.BasicEntity
    public String getRemark() {
        return this.remark;
    }
    @Override // com.xrkc.core.domain.basic.BasicEntity
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public String getDeviceName() {
        return this.deviceName;
    }
    public LocalDateTime getCommandTime() {
        return this.commandTime;
    }
    public LocalDateTime getClosedTime() {
        return this.closedTime;
    }
    @Override // com.xrkc.core.domain.basic.BasicEntity
    public LocalDateTime getUpdateTime() {
        return this.updateTime;
    }
    @Override // com.xrkc.core.domain.basic.BasicEntity
    public String getCreateBy() {
        return this.createBy;
    }
    @Override // com.xrkc.core.domain.basic.BasicEntity
    public String getUpdateBy() {
        return this.updateBy;
    }
    public String getNotifyStatus() {
        return this.notifyStatus;
    }
}
