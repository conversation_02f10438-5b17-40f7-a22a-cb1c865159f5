package com.xrkc.job.module.vehicle.mapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xrkc.core.domain.vehicle.VehicleInfo;
import com.xrkc.core.mybatisplus.BaseMapperX;
import java.lang.invoke.SerializedLambda;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/module/vehicle/mapper/VehicleInfoMapper.class */
public interface VehicleInfoMapper extends BaseMapperX<VehicleInfo> {
    /* JADX WARN: Multi-variable type inference failed */
    default List<VehicleInfo> selectNotNullCardIdList() {
        return selectList((Wrapper) new LambdaQueryWrapper<VehicleInfo>().isNotNull((v0) -> {
            return v0.getCardId();
        }));
    }
}
