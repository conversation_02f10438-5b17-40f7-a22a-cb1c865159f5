package com.xrkc.job.domain;
import com.xrkc.core.domain.person.Person;
import java.io.Serializable;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/UpdatePesonHK.class */
public class UpdatePesonHK implements Serializable {
    private static final long serialVersionUID = 1;
    private List<Person> updatePersionList;
    private Boolean isUpdateTime;
    public List<Person> getUpdatePersionList() {
        return this.updatePersionList;
    }
    public void setUpdatePersionList(List<Person> updatePersionList) {
        this.updatePersionList = updatePersionList;
    }
    public Boolean getUpdateTime() {
        return this.isUpdateTime;
    }
    public void setUpdateTime(Boolean updateTime) {
        this.isUpdateTime = updateTime;
    }
}
