package com.xrkc.job.service.impl;
import com.xrkc.job.domain.StatisticsAlarmAreaFlow;
import com.xrkc.job.mapper.StatisticsAreaAlarmFlowMapper;
import com.xrkc.job.service.IStatisticsAreaAlarmFlowService;
import java.time.LocalDate;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/StatisticsAreaAlarmFlowServiceImpl.class */
public class StatisticsAreaAlarmFlowServiceImpl implements IStatisticsAreaAlarmFlowService {
    @Autowired
    private StatisticsAreaAlarmFlowMapper statisticsAreaAlarmFlowMapper;
    @Override // com.xrkc.job.service.IStatisticsAreaAlarmFlowService
    public int batchReplace(LocalDate statisticsDate, List<StatisticsAlarmAreaFlow> list) {
        int result = 0;
        List<StatisticsAlarmAreaFlow> oldList = this.statisticsAreaAlarmFlowMapper.selectList((v0) -> {
            return v0.getStatisticsDate();
        }, statisticsDate);
        if (CollectionUtils.isEmpty(oldList)) {
            Iterator<StatisticsAlarmAreaFlow> it = list.iterator();
            while (it.hasNext()) {
                result += this.statisticsAreaAlarmFlowMapper.insert((StatisticsAreaAlarmFlowMapper) it.next());
            }
            return result;
        }
        Map<Long, Long> map = (Map) oldList.stream().collect(Collectors.toMap((v0) -> {
            return v0.getAreaId();
        }, (v0) -> {
            return v0.getStatisticsId();
        }));
        for (StatisticsAlarmAreaFlow flow : list) {
            if (map.containsKey(flow.getAreaId())) {
                flow.setStatisticsId(map.get(flow.getAreaId()));
                result += this.statisticsAreaAlarmFlowMapper.updateById((StatisticsAreaAlarmFlowMapper) flow);
            } else {
                result += this.statisticsAreaAlarmFlowMapper.insert((StatisticsAreaAlarmFlowMapper) flow);
            }
        }
        return result;
    }
}
