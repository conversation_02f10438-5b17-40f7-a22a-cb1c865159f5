package com.xrkc.job.task;
import com.xrkc.job.domain.InspectRecord;
import com.xrkc.job.domain.InspectRecordItem;
import com.xrkc.job.domain.InspectRecordItemParam;
import com.xrkc.job.domain.InspectRecordLocation;
import com.xrkc.job.service.IInspectRecordService;
import com.xrkc.job.util.DateUtil2;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.chrono.ChronoLocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
@Component("inspectTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/InspectTask.class */
public class InspectTask {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) InspectTask.class);
    @Autowired
    private IInspectRecordService inspectRecordService;
    @Transactional
    public void generateInspectRecord() {
        List<InspectRecord> everydayRoadList = this.inspectRecordService.selectEverydayInspectTaskList("日检");
        List<InspectRecord> notEverydayRoadList = this.inspectRecordService.selectNotEverydayInspectTaskList("日检");
        if (CollectionUtils.isEmpty(everydayRoadList) && CollectionUtils.isEmpty(notEverydayRoadList)) {
            log.debug("没有巡检路线");
            return;
        }
        List<InspectRecord> inspectRecordList = new ArrayList<>();
        LocalDate today = LocalDate.now();
        everydayRoadList.stream().forEach(road -> {
            road.setInspectBeginTime(LocalDateTime.of(today, road.getValidBeginTime()));
            road.setInspectEndTime(LocalDateTime.of(today, road.getValidEndTime()));
            setRecordStatus(road);
        });
        notEverydayRoadList.stream().forEach(road2 -> {
            List<Integer> timeListInt = (List) Arrays.stream(road2.getFrequencyVal().split(",")).mapToInt(Integer::parseInt).boxed().sorted().collect(Collectors.toList());
            Boolean exist = false;
            if ("周检".equals(road2.getFrequencyType())) {
                if (timeListInt.contains(Integer.valueOf(today.getDayOfWeek().getValue()))) {
                    exist = true;
                    road2.setInspectBeginTime(today.atStartOfDay());
                    road2.setInspectEndTime(today.atStartOfDay().plusDays(1L).minusSeconds(1L));
                }
            } else if ("周检一次".equals(road2.getFrequencyType())) {
                int todayWeek = today.getDayOfWeek().getValue();
                Integer beginTime = timeListInt.get(0);
                Integer endTime = timeListInt.get(timeListInt.size() - 1);
                if (beginTime.intValue() <= todayWeek && todayWeek <= endTime.intValue()) {
                    exist = true;
                    road2.setInspectBeginTime(today.atStartOfDay());
                    road2.setInspectEndTime(today.atStartOfDay().plusDays((endTime.intValue() - todayWeek) + 1).minusSeconds(1L));
                }
            } else if ("月检".equals(road2.getFrequencyType())) {
                if (timeListInt.contains(Integer.valueOf(today.getDayOfMonth()))) {
                    exist = true;
                    road2.setInspectBeginTime(today.atStartOfDay());
                    road2.setInspectEndTime(today.atStartOfDay().plusDays(1L).minusSeconds(1L));
                }
            } else if ("月检一次".equals(road2.getFrequencyType())) {
                int todayRi = today.getDayOfMonth();
                Integer curMonthLastDays = DateUtil2.getCurMonthLastDays();
                Integer beginTime2 = timeListInt.get(0);
                Integer endTime2 = timeListInt.get(timeListInt.size() - 1);
                if (beginTime2.intValue() > curMonthLastDays.intValue()) {
                    beginTime2 = Integer.valueOf(curMonthLastDays.intValue() - 1);
                }
                if (endTime2.intValue() > curMonthLastDays.intValue()) {
                    endTime2 = curMonthLastDays;
                }
                if (beginTime2.intValue() <= todayRi && todayRi <= endTime2.intValue()) {
                    exist = true;
                    road2.setInspectBeginTime(today.atStartOfDay());
                    road2.setInspectEndTime(today.atStartOfDay().plusDays((endTime2.intValue() - todayRi) + 1).minusSeconds(1L));
                }
            } else if ("年检".equals(road2.getFrequencyType())) {
                if (timeListInt.contains(Integer.valueOf(today.getMonthValue()))) {
                    exist = true;
                    road2.setInspectBeginTime(today.atStartOfDay().withDayOfMonth(1));
                    road2.setInspectEndTime(today.atStartOfDay().plusDays(1L).minusSeconds(1L).withDayOfMonth(today.lengthOfMonth()));
                }
            } else if ("年检一次".equals(road2.getFrequencyType())) {
                int todayMouth = today.getMonthValue();
                Integer beginTime3 = timeListInt.get(0);
                Integer endTime3 = timeListInt.get(timeListInt.size() - 1);
                if (beginTime3.intValue() <= todayMouth && todayMouth <= endTime3.intValue()) {
                    exist = true;
                    road2.setInspectBeginTime(today.atStartOfDay().withDayOfMonth(1));
                    road2.setInspectEndTime(today.atStartOfDay().plusMonths(endTime3.intValue() - todayMouth).plusDays(1L).minusSeconds(1L).withDayOfMonth(today.lengthOfMonth()));
                }
            }
            if (exist.booleanValue()) {
                setRecordStatus(road2);
                inspectRecordList.add(road2);
            }
        });
        inspectRecordList.addAll(everydayRoadList);
        inspectRecordList.removeIf(r -> {
            return "3".equals(r.getRecordStatus());
        });
        if (CollectionUtils.isEmpty(inspectRecordList)) {
            log.debug("验证后没有巡检路线");
            return;
        }
        List<Long> roadIds = (List) inspectRecordList.stream().map((v0) -> {
            return v0.getRoadId();
        }).distinct().collect(Collectors.toList());
        List<InspectRecordLocation> inspectRecordLocationList = this.inspectRecordService.selectInspectLocationList(roadIds);
        List<Long> locationIds = (List) inspectRecordLocationList.stream().map((v0) -> {
            return v0.getLocationId();
        }).distinct().collect(Collectors.toList());
        List<InspectRecordItem> inspectRecordItemList = this.inspectRecordService.selectInspectItemList(locationIds);
        inspectRecordList.stream().forEach(road3 -> {
            int exist = this.inspectRecordService.existInspectTask(road3);
            if (exist == 0) {
                List<InspectRecordLocation> insertLocationList = (List) inspectRecordLocationList.stream().filter(f -> {
                    return f.getRoadId().compareTo(road3.getRoadId()) == 0;
                }).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(insertLocationList)) {
                    log.info("新增失败，没有关联巡检点");
                    return;
                }
                List<Long> insertLocationIds = (List) insertLocationList.stream().map((v0) -> {
                    return v0.getLocationId();
                }).distinct().collect(Collectors.toList());
                List<InspectRecordItem> insertItemList = (List) inspectRecordItemList.stream().filter(f2 -> {
                    return insertLocationIds.contains(f2.getLocationId());
                }).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(insertItemList)) {
                    log.info("新增失败，没有关联巡检项");
                    return;
                }
                road3.setRoadLocationCount(Integer.valueOf(insertLocationList.size()));
                this.inspectRecordService.insertInspectRecord(road3);
                insertLocationList.stream().forEach(e -> {
                    e.setRecordId(road3.getRecordId());
                    e.setLocationStatus("N");
                    e.setInspectBeginTime(road3.getInspectBeginTime());
                });
                this.inspectRecordService.batchInsertLocation(insertLocationList);
                locationIds.forEach(locationId -> {
                    List<InspectRecordItem> items = this.inspectRecordService.selectInspectItem(locationId);
                    if (!CollectionUtils.isEmpty(items)) {
                        items.forEach(t -> {
                            t.setRecordId(road3.getRecordId());
                            t.setLocationId(locationId);
                        });
                    }
                    this.inspectRecordService.batchInsertItem(items);
                    items.forEach(item -> {
                        List<InspectRecordItemParam> itemParams = this.inspectRecordService.selectInspectItemParam(item.getItemId());
                        if (!CollectionUtils.isEmpty(itemParams)) {
                            itemParams.forEach(t2 -> {
                                t2.setRecordId(road3.getRecordId());
                                t2.setLocationId(locationId);
                            });
                            this.inspectRecordService.batchInsertItemParam(itemParams);
                        }
                    });
                });
            }
        });
    }
    private void setRecordStatus(InspectRecord t) {
        LocalDateTime now = LocalDateTime.now();
        if (t.getInspectBeginTime().compareTo((ChronoLocalDateTime<?>) now) > 0) {
            t.setRecordStatus("1");
            return;
        }
        if (t.getInspectBeginTime().compareTo((ChronoLocalDateTime<?>) now) < 1 && t.getInspectEndTime().compareTo((ChronoLocalDateTime<?>) now) > 0) {
            t.setRecordStatus("2");
        } else if (t.getInspectEndTime().compareTo((ChronoLocalDateTime<?>) now) < 1) {
            t.setRecordStatus("3");
        }
    }
    public void updateRecordStatus() {
        List<InspectRecord> recordList = this.inspectRecordService.selectInspectRecord();
        recordList.stream().forEach(t -> {
            setRecordStatus(t);
            if ("3".equals(t.getRecordStatus()) && "2".equals(t.getInspectStatus()) && StringUtils.isBlank(t.getInspectPersonSignature()) && t.getRoadLocationCount().compareTo(t.getFinishLocationCount()) == 0) {
                t.setInspectStatus("4");
            }
            if ("3".equals(t.getRecordStatus()) && t.getRoadLocationCount().compareTo(t.getFinishLocationCount()) != 0) {
                t.setInspectStatus("4");
            }
            this.inspectRecordService.updateInspectRecord(t);
        });
    }
}
