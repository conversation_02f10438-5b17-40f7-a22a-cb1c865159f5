package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.job.domain.DeviceCardSenderVehicleLog;
import com.xrkc.job.mapper.DeviceCardSenderVehicleLogMapper;
import com.xrkc.job.service.IDeviceCardSenderVehicleLogService;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/DeviceCardSenderVehicleLogServiceImpl.class */
public class DeviceCardSenderVehicleLogServiceImpl extends ServiceImpl<DeviceCardSenderVehicleLogMapper, DeviceCardSenderVehicleLog> implements IDeviceCardSenderVehicleLogService {
    @Override // com.xrkc.job.service.IDeviceCardSenderVehicleLogService
    public void batchInsert(List<DeviceCardSenderVehicleLog> list) {
        list.forEach(t -> {
            t.setId(null);
            t.setCreateTime(LocalDateTime.now());
        });
        saveBatch(list, 1000);
    }
}
