package com.xrkc.job.domain;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/InspectRecordItem.class */
public class InspectRecordItem implements Serializable {
    private static final long serialVersionUID = 1;
    private Long recordId;
    private Long locationId;
    private Long itemId;
    private String itemName;
    private String feedbackTag;
    @JsonSerialize(using = ToStringSerializer.class)
    public Long getItemId() {
        return this.itemId;
    }
    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }
    public Long getLocationId() {
        return this.locationId;
    }
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }
    public Long getRecordId() {
        return this.recordId;
    }
    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }
    public String getItemName() {
        return this.itemName;
    }
    public void setItemName(String itemName) {
        this.itemName = itemName;
    }
    public String getFeedbackTag() {
        return this.feedbackTag;
    }
    public void setFeedbackTag(String feedbackTag) {
        this.feedbackTag = feedbackTag;
    }
}
