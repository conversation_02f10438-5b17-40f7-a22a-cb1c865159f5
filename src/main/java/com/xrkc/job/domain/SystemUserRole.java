package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
@TableName("system_user_role")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/SystemUserRole.class */
public class SystemUserRole implements Serializable {
    @TableField("user_id")
    private Long userId;
    @TableField("role_id")
    private Long roleId;
    @TableField(exist = false)
    private String dataScope;
    @TableField(exist = false)
    private Long facilityId;
    public Long getFacilityId() {
        return this.facilityId;
    }
    public void setFacilityId(Long facilityId) {
        this.facilityId = facilityId;
    }
    public String getDataScope() {
        return this.dataScope;
    }
    public void setDataScope(String dataScope) {
        this.dataScope = dataScope;
    }
    @JsonSerialize(using = ToStringSerializer.class)
    public Long getUserId() {
        return this.userId;
    }
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    @JsonSerialize(using = ToStringSerializer.class)
    public Long getRoleId() {
        return this.roleId;
    }
    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("userId", getUserId()).append("roleId", getRoleId()).toString();
    }
}
