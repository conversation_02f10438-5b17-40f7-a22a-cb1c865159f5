package com.xrkc.job.service.impl;
import com.xrkc.job.domain.SystemLicense;
import com.xrkc.job.mapper.SystemLicenseMapper;
import com.xrkc.job.service.ISystemLicenseService;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/SystemLicenseServiceImpl.class */
public class SystemLicenseServiceImpl implements ISystemLicenseService {
    @Autowired
    private SystemLicenseMapper systemLicenseMapper;
    @Override // com.xrkc.job.service.ISystemLicenseService
    public Map<String, LocalDateTime> getConfig() {
        List<SystemLicense> all = this.systemLicenseMapper.configList();
        Map<String, LocalDateTime> config = new HashMap<>();
        for (SystemLicense license : all) {
            config.put(license.getLicenseKey().toString(), license.getExpireTime());
        }
        return config;
    }
}
