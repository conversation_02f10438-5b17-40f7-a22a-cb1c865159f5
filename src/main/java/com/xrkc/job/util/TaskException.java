package com.xrkc.job.util;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/TaskException.class */
public class TaskException extends RuntimeException {
    private static final long serialVersionUID = 1;
    private Integer code;
    private String message;
    public TaskException(String message) {
        this.message = message;
    }
    public TaskException(String message, Integer code) {
        this.message = message;
        this.code = code;
    }
    public TaskException(String message, Throwable e) {
        super(message, e);
        this.message = message;
    }
    @Override // java.lang.Throwable
    public String getMessage() {
        return this.message;
    }
    public Integer getCode() {
        return this.code;
    }
}
