package com.xrkc.job.util;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/MD5Utils.class */
public class MD5Utils {
    public static String string2MD5(String inStr) throws NoSuchAlgorithmException {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            char[] charArray = inStr.toCharArray();
            byte[] byteArray = new byte[charArray.length];
            for (int i = 0; i < charArray.length; i++) {
                byteArray[i] = (byte) charArray[i];
            }
            byte[] md5Bytes = md5.digest(byteArray);
            StringBuffer hexValue = new StringBuffer();
            for (byte b : md5Bytes) {
                int val = b & 255;
                if (val < 16) {
                    hexValue.append("0");
                }
                hexValue.append(Integer.toHexString(val));
            }
            return hexValue.toString();
        } catch (Exception e) {
            System.out.println(e.toString());
            e.printStackTrace();
            return "";
        }
    }
    public static String convertMD5(String inStr) {
        char[] a = inStr.toCharArray();
        for (int i = 0; i < a.length; i++) {
            a[i] = (char) (a[i] ^ 't');
        }
        String s = new String(a);
        return s;
    }
    public static void main(String[] args) throws NoSuchAlgorithmException {
        System.out.println("原始：xrkc_test");
        System.out.println("MD5后：" + string2MD5("xrkc_test"));
        System.out.println("加密的：" + convertMD5("xrkc_test"));
        System.out.println("解密的：" + convertMD5(convertMD5("xrkc_test")));
        System.out.println("解密的：" + convertMD5(convertMD5("a8761791a5f3e39dc27a0d7cd127d230")));
        System.out.println("解密的：" + convertMD5("\f\u0006\u001f\u0017+ \u0011\u0007 "));
    }
}
