package com.xrkc.job.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xrkc.core.domain.device.DeviceBeaconHistory;
import java.time.LocalDateTime;
import org.apache.ibatis.annotations.Param;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/DeviceBeaconHistoryMapper.class */
public interface DeviceBeaconHistoryMapper extends BaseMapper<DeviceBeaconHistory> {
    Long findDelMaxId(@Param("minusDays") LocalDateTime localDateTime);
    int delByMaxId(@Param("maxId") Long l, @Param("delRow") int i);
}
