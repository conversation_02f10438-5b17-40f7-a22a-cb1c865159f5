package com.xrkc.job.util;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/DateTimeUtil.class */
public class DateTimeUtil {
    public static LocalDateTime parseLocalDateTime(String dateTime, String format) {
        return LocalDateTime.parse(dateTime, DateTimeFormatter.ofPattern(format));
    }
    public static String formatLocalDateTime(LocalDateTime dateTime, String format) {
        return DateTimeFormatter.ofPattern(format).format(dateTime);
    }
}
