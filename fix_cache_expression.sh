#!/bin/bash

# 批量修复 CacheOperationExpressionEvaluator.RESULT_VARIABLE 访问权限问题
# 将 CacheOperationExpressionEvaluator.RESULT_VARIABLE 替换为字符串字面量 "result"

echo "开始批量修复 CacheOperationExpressionEvaluator.RESULT_VARIABLE 访问权限问题..."

# 查找所有包含 CacheOperationExpressionEvaluator.RESULT_VARIABLE 的 Java 文件
files=$(find src/main/java -name "*.java" -exec grep -l "CacheOperationExpressionEvaluator\.RESULT_VARIABLE" {} \;)

if [ -z "$files" ]; then
    echo "没有找到包含 CacheOperationExpressionEvaluator.RESULT_VARIABLE 的文件"
    exit 0
fi

echo "找到以下文件需要修复："
echo "$files"
echo ""

# 统计修复的文件数量
count=0

# 遍历每个文件进行修复
for file in $files; do
    echo "正在修复文件: $file"
    
    # 备份原文件
    cp "$file" "$file.backup"
    
    # 替换 CacheOperationExpressionEvaluator.RESULT_VARIABLE 为 "result"
    sed -i '' 's/CacheOperationExpressionEvaluator\.RESULT_VARIABLE/"result"/g' "$file"
    
    # 检查是否还有残留的 CacheOperationExpressionEvaluator 导入
    if grep -q "import.*CacheOperationExpressionEvaluator" "$file"; then
        # 删除 CacheOperationExpressionEvaluator 的导入语句
        sed -i '' '/import.*CacheOperationExpressionEvaluator/d' "$file"
        echo "  - 删除了 CacheOperationExpressionEvaluator 的导入语句"
    fi
    
    count=$((count + 1))
    echo "  - 修复完成"
done

echo ""
echo "批量修复完成！"
echo "总共修复了 $count 个文件"
echo ""
echo "修复内容："
echo "1. 将 CacheOperationExpressionEvaluator.RESULT_VARIABLE 替换为 \"result\""
echo "2. 删除了 CacheOperationExpressionEvaluator 的导入语句"
echo ""
echo "注意：原文件已备份为 .backup 文件，如需恢复请手动操作" 