package com.xrkc.job.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xrkc.job.domain.StatisticsInspectFlowDanger;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IStatisticsInspectFlowDangerService.class */
public interface IStatisticsInspectFlowDangerService extends IService<StatisticsInspectFlowDanger> {
    int batchReplace(List<StatisticsInspectFlowDanger> list);
}
