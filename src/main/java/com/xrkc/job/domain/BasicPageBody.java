package com.xrkc.job.domain;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/BasicPageBody.class */
public class BasicPageBody implements Serializable {
    private static final long serialVersionUID = 1;
    private String searchValue;
    private String createBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    private String updateBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    private String remark;
    private String pageFlag;
    private Integer pageNum;
    private Integer pageSize;
    private String sortBy;
    private String sortType;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime beginDate;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime endDate;
    private Map<String, Object> params;
    @JsonIgnore
    public String getSearchValue() {
        return this.searchValue;
    }
    public void setSearchValue(String searchValue) {
        this.searchValue = searchValue;
    }
    @JsonIgnore
    public String getCreateBy() {
        return this.createBy;
    }
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    @JsonIgnore
    public String getUpdateBy() {
        return this.updateBy;
    }
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public String getRemark() {
        return this.remark;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }
    public String getSortBy() {
        return this.sortBy;
    }
    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }
    public String getSortType() {
        return this.sortType;
    }
    public void setSortType(String sortType) {
        this.sortType = sortType;
    }
    public Integer getPageNum() {
        return this.pageNum;
    }
    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }
    public Integer getPageSize() {
        return this.pageSize;
    }
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
    @JsonIgnore
    public Map<String, Object> getParams() {
        return this.params;
    }
    public void setParams(Map<String, Object> params) {
        this.params = params;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getUpdateTime() {
        return this.updateTime;
    }
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    public LocalDateTime getBeginTime() {
        return this.beginTime;
    }
    public void setBeginTime(LocalDateTime beginTime) {
        this.beginTime = beginTime;
    }
    public LocalDateTime getEndTime() {
        return this.endTime;
    }
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    public LocalDateTime getBeginDate() {
        return this.beginDate;
    }
    public void setBeginDate(LocalDateTime beginDate) {
        this.beginDate = beginDate;
    }
    public LocalDateTime getEndDate() {
        return this.endDate;
    }
    public void setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
    }
    public String getPageFlag() {
        return this.pageFlag;
    }
    public void setPageFlag(String pageFlag) {
        this.pageFlag = pageFlag;
    }
}
