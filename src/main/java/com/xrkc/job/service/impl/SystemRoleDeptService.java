package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.core.domain.system.SystemRoleDept;
import com.xrkc.job.mapper.SystemRoleDeptMapper;
import com.xrkc.job.service.ISystemRoleDeptService;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/SystemRoleDeptService.class */
public class SystemRoleDeptService extends ServiceImpl<SystemRoleDeptMapper, SystemRoleDept> implements ISystemRoleDeptService {
    @Autowired
    private SystemRoleDeptMapper systemRoleDeptMapper;
    @Override // com.xrkc.job.service.ISystemRoleDeptService
    public List<Long> getDeptIdsByRoleId(Long roleId) {
        return this.systemRoleDeptMapper.getDeptIdsByRoleId(roleId);
    }
    @Override // com.xrkc.job.service.ISystemRoleDeptService
    public List<Long> getDeptIdsSubdivision(Long userId) {
        List<Long> deptId = this.systemRoleDeptMapper.getDeptIdCadre(userId);
        if (CollectionUtils.isEmpty(deptId)) {
            return deptId;
        }
        List<Long> deptIds = getTreeDeptIds(new ArrayList(), deptId);
        return deptIds;
    }
    @Nullable
    private List<Long> getTreeDeptIds(List<Long> deptIds, List<Long> parentDeptIds) {
        List<Long> deptParentIds = this.systemRoleDeptMapper.getDeptIdsSubdivision(parentDeptIds);
        if (CollectionUtils.isEmpty(deptParentIds)) {
            deptIds.addAll(parentDeptIds);
            return (List) deptIds.stream().distinct().collect(Collectors.toList());
        }
        deptIds.addAll(parentDeptIds);
        return getTreeDeptIds(deptIds, deptParentIds);
    }
    @Override // com.xrkc.job.service.ISystemRoleDeptService
    public List<Long> getDeptIdCadre(Long userId) {
        return this.systemRoleDeptMapper.getDeptIdCadre(userId);
    }
}
