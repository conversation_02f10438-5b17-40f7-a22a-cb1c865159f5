#!/bin/bash

# 删除项目中所有Mapper的updateById和insert方法中对参数的强制转换
# 例如：updateById((SomeMapper) entity) -> updateById(entity)
#      insert((SomeMapper) entity) -> insert(entity)

echo "开始删除项目中所有Mapper方法的强制转换..."

# 统计修复的文件数量
total_files=0
total_changes=0

# 查找所有包含强制转换的Java文件
echo "1. 查找包含强制转换的文件..."

# 查找updateById方法中的强制转换
updateById_files=$(find src/main/java -name "*.java" -exec grep -l "updateById.*Mapper.*)" {} \; 2>/dev/null)

# 查找insert方法中的强制转换  
insert_files=$(find src/main/java -name "*.java" -exec grep -l "insert.*Mapper.*)" {} \; 2>/dev/null)

# 合并并去重文件列表
all_files=$(echo -e "$updateById_files\n$insert_files" | sort -u | grep -v "^$")

if [ -z "$all_files" ]; then
    echo "未找到包含强制转换的文件"
    exit 0
fi

echo "找到以下包含强制转换的文件："
echo "$all_files"
echo ""

# 处理每个文件
for file in $all_files; do
    if [ -f "$file" ]; then
        echo "处理文件: $file"
        
        # 备份原文件
        cp "$file" "$file.backup"
        
        # 统计该文件中的变更数量
        file_changes=0
        
        # 删除updateById方法中的强制转换
        # 匹配模式：updateById((SomeMapper) variable) -> updateById(variable)
        updateById_count=$(grep -c "updateById.*Mapper.*)" "$file" 2>/dev/null || echo "0")
        if [ "$updateById_count" -gt 0 ]; then
            echo "  - 发现 $updateById_count 个updateById强制转换"
            # 使用sed删除强制转换，保留变量名
            # 处理格式：updateById((MapperType) variable)
            sed -i '' 's/updateById((\([^)]*Mapper\)) \([^)]*\))/updateById(\2)/g' "$file"
            # 处理格式：updateById((MapperType)variable) - 没有空格的情况
            sed -i '' 's/updateById((\([^)]*Mapper\))\([^)]*\))/updateById(\2)/g' "$file"
            file_changes=$((file_changes + updateById_count))
        fi

        # 删除insert方法中的强制转换
        # 匹配模式：insert((SomeMapper) variable) -> insert(variable)
        insert_count=$(grep -c "insert.*Mapper.*)" "$file" 2>/dev/null || echo "0")
        if [ "$insert_count" -gt 0 ]; then
            echo "  - 发现 $insert_count 个insert强制转换"
            # 使用sed删除强制转换，保留变量名
            # 处理格式：insert((MapperType) variable)
            sed -i '' 's/insert((\([^)]*Mapper\)) \([^)]*\))/insert(\2)/g' "$file"
            # 处理格式：insert((MapperType)variable) - 没有空格的情况
            sed -i '' 's/insert((\([^)]*Mapper\))\([^)]*\))/insert(\2)/g' "$file"
            file_changes=$((file_changes + insert_count))
        fi

        # 验证修复结果
        remaining_updateById=$(grep -c "updateById.*Mapper.*)" "$file" 2>/dev/null || echo "0")
        remaining_insert=$(grep -c "insert.*Mapper.*)" "$file" 2>/dev/null || echo "0")
        
        if [ "$remaining_updateById" -eq 0 ] && [ "$remaining_insert" -eq 0 ]; then
            echo "  ✓ 修复完成，已删除所有强制转换"
        else
            echo "  ⚠ 警告：仍有 $remaining_updateById 个updateById和 $remaining_insert 个insert强制转换未处理"
        fi
        
        total_files=$((total_files + 1))
        total_changes=$((total_changes + file_changes))
        echo ""
    fi
done

echo "=========================================="
echo "修复完成！"
echo "处理文件数量: $total_files"
echo "删除强制转换数量: $total_changes"
echo ""
echo "修复的文件类型："
echo "- updateById方法中的强制转换"
echo "- insert方法中的强制转换"
echo ""
echo "注意："
echo "1. 原文件已备份为 .backup 后缀"
echo "2. 请检查修复结果并测试代码"
echo "3. 如有问题可以从备份文件恢复"
echo "=========================================="
