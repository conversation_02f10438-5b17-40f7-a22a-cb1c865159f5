package com.xrkc.job.service.impl;
import com.xrkc.job.domain.StatisticsFacilityCurrentPerson;
import com.xrkc.job.mapper.StatisticsFacilityCurrentPersonMapper;
import com.xrkc.job.service.IStatisticsFacilityCurrentPersonService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/StatisticsFacilityCurrentPersonServiceImpl.class */
public class StatisticsFacilityCurrentPersonServiceImpl implements IStatisticsFacilityCurrentPersonService {
    @Autowired
    private StatisticsFacilityCurrentPersonMapper statisticsFacilityCurrentPersonMapper;
    @Override // com.xrkc.job.service.IStatisticsFacilityCurrentPersonService
    @Transactional(rollbackFor = {Exception.class})
    public int batchInsert(List<StatisticsFacilityCurrentPerson> list) {
        truncateTable();
        return this.statisticsFacilityCurrentPersonMapper.batchInsert(list);
    }
    @Override // com.xrkc.job.service.IStatisticsFacilityCurrentPersonService
    public int truncateTable() {
        return this.statisticsFacilityCurrentPersonMapper.truncateTable();
    }
}
