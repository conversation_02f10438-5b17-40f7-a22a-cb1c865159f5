package com.xrkc.job.domain;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/StatisticsFacilityStay.class */
public class StatisticsFacilityStay implements Serializable {
    private static final long serialVersionUID = 1;
    private Long statisticsId;
    private LocalDateTime acceptTime;
    private Long personId;
    private Long cardId;
    private Integer beaconId;
    private BigDecimal longitude;
    private BigDecimal latitude;
    private String layerId;
    private Integer layerHeight;
    private Long facilityId;
    private String facilityName;
    private String statusId;
    private String disposeStatus;
    private LocalDateTime createTime;
    public Long getStatisticsId() {
        return this.statisticsId;
    }
    public void setStatisticsId(Long statisticsId) {
        this.statisticsId = statisticsId;
    }
    public LocalDateTime getAcceptTime() {
        return this.acceptTime;
    }
    public void setAcceptTime(LocalDateTime acceptTime) {
        this.acceptTime = acceptTime;
    }
    public Long getCardId() {
        return this.cardId;
    }
    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }
    public BigDecimal getLongitude() {
        return this.longitude;
    }
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    public BigDecimal getLatitude() {
        return this.latitude;
    }
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
    public Long getFacilityId() {
        return this.facilityId;
    }
    public void setFacilityId(Long facilityId) {
        this.facilityId = facilityId;
    }
    public String getFacilityName() {
        return this.facilityName;
    }
    public void setFacilityName(String facilityName) {
        this.facilityName = facilityName;
    }
    public String getDisposeStatus() {
        return this.disposeStatus;
    }
    public void setDisposeStatus(String disposeStatus) {
        this.disposeStatus = disposeStatus;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public String getStatusId() {
        return this.statusId;
    }
    public void setStatusId(String statusId) {
        this.statusId = statusId;
    }
    public Integer getBeaconId() {
        return this.beaconId;
    }
    public void setBeaconId(Integer beaconId) {
        this.beaconId = beaconId;
    }
    public Integer getLayerHeight() {
        return this.layerHeight;
    }
    public void setLayerHeight(Integer layerHeight) {
        this.layerHeight = layerHeight;
    }
    public Long getPersonId() {
        return this.personId;
    }
    public void setPersonId(Long personId) {
        this.personId = personId;
    }
}
