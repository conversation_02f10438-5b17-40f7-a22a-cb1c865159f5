package com.xrkc.job.mapper;
import com.xrkc.core.mybatisplus.BaseMapperX;
import com.xrkc.job.domain.LampsTimeControl;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/LampsTimeControlMapper.class */
public interface LampsTimeControlMapper extends BaseMapperX<LampsTimeControl> {
    @Select({"SELECT lamps_group_id FROM lamps_time_control_group WHERE lamps_time_id= #{lampsTimeId} "})
    List<Long> findGroupIdByTimeId(@Param("lampsTimeId") Long l);
}
