package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.xrkc.core.domain.alarm.CoreAlarmPeople;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/GatherAlarmVo.class */
public class GatherAlarmVo extends Alarm {
    @TableField(exist = false)
    private LocalDateTime gatherStartTime;
    @TableField(exist = false)
    private LocalDateTime gatherEndTime;
    @TableField(exist = false)
    private Map<Long, CoreAlarmPeople> alarmPeopleMap;
    @TableField(exist = false)
    private List<CoreAlarmPeople> alarmedPeopleList;
    @TableField(exist = false)
    private Set<Long> lastGatherPeopleSet;
    @TableField(exist = false)
    private Double radius;
    @TableField(exist = false)
    private int maxGatherPerson;
    public void setGatherStartTime(LocalDateTime gatherStartTime) {
        this.gatherStartTime = gatherStartTime;
    }
    public void setGatherEndTime(LocalDateTime gatherEndTime) {
        this.gatherEndTime = gatherEndTime;
    }
    public void setAlarmPeopleMap(Map<Long, CoreAlarmPeople> alarmPeopleMap) {
        this.alarmPeopleMap = alarmPeopleMap;
    }
    public void setAlarmedPeopleList(List<CoreAlarmPeople> alarmedPeopleList) {
        this.alarmedPeopleList = alarmedPeopleList;
    }
    public void setLastGatherPeopleSet(Set<Long> lastGatherPeopleSet) {
        this.lastGatherPeopleSet = lastGatherPeopleSet;
    }
    public void setRadius(Double radius) {
        this.radius = radius;
    }
    public void setMaxGatherPerson(int maxGatherPerson) {
        this.maxGatherPerson = maxGatherPerson;
    }
    @Override // com.xrkc.job.domain.Alarm
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof GatherAlarmVo)) {
            return false;
        }
        GatherAlarmVo other = (GatherAlarmVo) o;
        if (!other.canEqual(this) || getMaxGatherPerson() != other.getMaxGatherPerson()) {
            return false;
        }
        Object this$radius = getRadius();
        Object other$radius = other.getRadius();
        if (this$radius == null) {
            if (other$radius != null) {
                return false;
            }
        } else if (!this$radius.equals(other$radius)) {
            return false;
        }
        Object this$gatherStartTime = getGatherStartTime();
        Object other$gatherStartTime = other.getGatherStartTime();
        if (this$gatherStartTime == null) {
            if (other$gatherStartTime != null) {
                return false;
            }
        } else if (!this$gatherStartTime.equals(other$gatherStartTime)) {
            return false;
        }
        Object this$gatherEndTime = getGatherEndTime();
        Object other$gatherEndTime = other.getGatherEndTime();
        if (this$gatherEndTime == null) {
            if (other$gatherEndTime != null) {
                return false;
            }
        } else if (!this$gatherEndTime.equals(other$gatherEndTime)) {
            return false;
        }
        Object this$alarmPeopleMap = getAlarmPeopleMap();
        Object other$alarmPeopleMap = other.getAlarmPeopleMap();
        if (this$alarmPeopleMap == null) {
            if (other$alarmPeopleMap != null) {
                return false;
            }
        } else if (!this$alarmPeopleMap.equals(other$alarmPeopleMap)) {
            return false;
        }
        Object this$alarmedPeopleList = getAlarmedPeopleList();
        Object other$alarmedPeopleList = other.getAlarmedPeopleList();
        if (this$alarmedPeopleList == null) {
            if (other$alarmedPeopleList != null) {
                return false;
            }
        } else if (!this$alarmedPeopleList.equals(other$alarmedPeopleList)) {
            return false;
        }
        Object this$lastGatherPeopleSet = getLastGatherPeopleSet();
        Object other$lastGatherPeopleSet = other.getLastGatherPeopleSet();
        return this$lastGatherPeopleSet == null ? other$lastGatherPeopleSet == null : this$lastGatherPeopleSet.equals(other$lastGatherPeopleSet);
    }
    @Override // com.xrkc.job.domain.Alarm
    protected boolean canEqual(Object other) {
        return other instanceof GatherAlarmVo;
    }
    @Override // com.xrkc.job.domain.Alarm
    public int hashCode() {
        int result = (1 * 59) + getMaxGatherPerson();
        Object $radius = getRadius();
        int result2 = (result * 59) + ($radius == null ? 43 : $radius.hashCode());
        Object $gatherStartTime = getGatherStartTime();
        int result3 = (result2 * 59) + ($gatherStartTime == null ? 43 : $gatherStartTime.hashCode());
        Object $gatherEndTime = getGatherEndTime();
        int result4 = (result3 * 59) + ($gatherEndTime == null ? 43 : $gatherEndTime.hashCode());
        Object $alarmPeopleMap = getAlarmPeopleMap();
        int result5 = (result4 * 59) + ($alarmPeopleMap == null ? 43 : $alarmPeopleMap.hashCode());
        Object $alarmedPeopleList = getAlarmedPeopleList();
        int result6 = (result5 * 59) + ($alarmedPeopleList == null ? 43 : $alarmedPeopleList.hashCode());
        Object $lastGatherPeopleSet = getLastGatherPeopleSet();
        return (result6 * 59) + ($lastGatherPeopleSet == null ? 43 : $lastGatherPeopleSet.hashCode());
    }
    @Override // com.xrkc.job.domain.Alarm
    public String toString() {
        return "GatherAlarmVo(gatherStartTime=" + getGatherStartTime() + ", gatherEndTime=" + getGatherEndTime() + ", alarmPeopleMap=" + getAlarmPeopleMap() + ", alarmedPeopleList=" + getAlarmedPeopleList() + ", lastGatherPeopleSet=" + getLastGatherPeopleSet() + ", radius=" + getRadius() + ", maxGatherPerson=" + getMaxGatherPerson() + StringPool.RIGHT_BRACKET;
    }
    public LocalDateTime getGatherStartTime() {
        return this.gatherStartTime;
    }
    public LocalDateTime getGatherEndTime() {
        return this.gatherEndTime;
    }
    public Map<Long, CoreAlarmPeople> getAlarmPeopleMap() {
        return this.alarmPeopleMap;
    }
    public List<CoreAlarmPeople> getAlarmedPeopleList() {
        return this.alarmedPeopleList;
    }
    public Set<Long> getLastGatherPeopleSet() {
        return this.lastGatherPeopleSet;
    }
    public Double getRadius() {
        return this.radius;
    }
    public int getMaxGatherPerson() {
        return this.maxGatherPerson;
    }
}
