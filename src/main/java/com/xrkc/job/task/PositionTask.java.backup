package com.xrkc.job.task;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.domain.cardDispenser.CardDispenserPerson;
import com.xrkc.core.domain.vehicle.PositionHistoryCalendar;
import com.xrkc.job.domain.Constants;
import com.xrkc.job.domain.PositionDataCalc;
import com.xrkc.job.domain.PositionVO;
import com.xrkc.job.module.vehicle.mapper.PositionHistoryCalendarMapper;
import com.xrkc.job.service.IDeviceCardSenderPersonService;
import com.xrkc.job.service.IPositionCurrentService;
import com.xrkc.job.service.IPositionDataCalcService;
import com.xrkc.job.service.IPositionHistoryService;
import com.xrkc.job.service.IRssiService;
import com.xrkc.job.service.IStatisticsFacilityStayService;
import com.xrkc.job.service.ISystemConfigService;
import com.xrkc.job.util.CommonUtil;
import com.xrkc.redis.utils.RedisCacheUtils;
import java.lang.invoke.SerializedLambda;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.chrono.ChronoLocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import net.jodah.expiringmap.ExpiringMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
@Component("positionTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/PositionTask.class */
public class PositionTask {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) PositionTask.class);
    @Autowired
    private IPositionHistoryService positionHistoryService;
    @Autowired
    private IPositionCurrentService positionCurrentService;
    @Autowired
    private IPositionDataCalcService positionDataCalcService;
    @Autowired
    private ISystemConfigService systemConfigService;
    @Autowired
    private IRssiService rssiService;
    @Autowired
    private IStatisticsFacilityStayService statisticsFacilityStayService;
    @Autowired
    private PositionHistoryCalendarMapper positionHistoryCalendarMapper;
    @Autowired
    private IDeviceCardSenderPersonService deviceCardSenderPersonService;
    @Value("${spring.datasource.driver-class-name}")
    private String driverClassName;
    private Map<Long, PositionDataCalc> cardLatestPositionHisMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    private Map<Long, PositionVO> lastPositionMap = ExpiringMap.builder().expiration(3, TimeUnit.DAYS).build();

    /* JADX WARN: Multi-variable type inference failed */
    public void savePosition() {
        String tableName;
        Map<String, String> configMap = RedisCacheUtils.getConfigRedisCache();
        String historyOptimizationFlag = configMap.getOrDefault(Constants.CONIFG_KEY_POSITION_HISTORY_OPTIMIZATION, "N");
        String offlineBeacons = configMap.getOrDefault(ConfigValue.CONFIG_KEY_OFFLINE_BEACONS, "");
        List<PositionDataCalc> positionDataList = this.positionDataCalcService.selectList(240);
        if (StringUtils.isNotBlank(offlineBeacons)) {
            List<String> offlineBeaconList = Arrays.asList(offlineBeacons.split(","));
            positionDataList.removeIf(r -> {
                return Objects.nonNull(r.getBeaconId()) && r.getBeaconId().intValue() > 0 && offlineBeaconList.contains(r.getBeaconId().toString());
            });
        }
        LocalDateTime now = LocalDateTime.now();
        if (StrUtil.equalsIgnoreCase(historyOptimizationFlag, "Y")) {
            Map<Long, List<PositionDataCalc>> positionListMap = (Map) positionDataList.stream().collect(Collectors.groupingBy((v0) -> {
                return v0.getCardId();
            }));
            positionListMap.values().forEach(positionList -> {
                CollectionUtil.sort(positionList, (first, sencond) -> {
                    return first.getAcceptTime().isBefore(sencond.getAcceptTime()) ? -1 : 1;
                });
                for (int i = 0; i < positionList.size(); i++) {
                    PositionDataCalc currentPosition = (PositionDataCalc) positionList.get(i);
                    if (currentPosition.getPositionType() == null) {
                        return;
                    }
                    PositionDataCalc lastPosition = this.cardLatestPositionHisMap.get(currentPosition.getCardId());
                    String lastLayerId = "";
                    if (lastPosition != null) {
                        if (StrUtil.isNotEmpty(lastPosition.getOrgLayerId())) {
                            lastLayerId = lastPosition.getOrgLayerId();
                        } else {
                            lastLayerId = lastPosition.getLayerId();
                        }
                    }
                    if (lastPosition != null && !currentPosition.getAcceptTime().isAfter(lastPosition.getAcceptTime())) {
                        positionDataList.remove(currentPosition);
                    } else if (currentPosition.getAcceptTime().plusSeconds(30L).isAfter(now)) {
                        positionDataList.remove(currentPosition);
                    } else if (lastPosition != null && lastPosition.getAcceptTime().plusSeconds(60L).isAfter(currentPosition.getAcceptTime())) {
                        String lastPositionType = lastPosition.getPositionType();
                        String currentPositionType = currentPosition.getPositionType();
                        if (lastPositionType.equals("1") && !currentPositionType.equals("1")) {
                            boolean stopExecute = false;
                            int j = i + 1;
                            while (true) {
                                if (j >= positionList.size()) {
                                    break;
                                }
                                PositionDataCalc nextPosition = (PositionDataCalc) positionList.get(j);
                                if (currentPosition.getAcceptTime().plusSeconds(30L).isBefore(nextPosition.getAcceptTime()) || j > i + 3) {
                                    break;
                                }
                                if (!nextPosition.getPositionType().equals(lastPositionType)) {
                                    j++;
                                } else {
                                    positionDataList.remove(currentPosition);
                                    stopExecute = true;
                                    break;
                                }
                            }
                            if (stopExecute) {
                            }
                        } else if (!lastPositionType.equals("1") && currentPositionType.equals("1")) {
                            boolean stopExecute2 = false;
                            int j2 = i + 1;
                            while (true) {
                                if (j2 >= positionList.size()) {
                                    break;
                                }
                                PositionDataCalc nextPosition2 = (PositionDataCalc) positionList.get(j2);
                                if (currentPosition.getAcceptTime().plusSeconds(30L).isBefore(nextPosition2.getAcceptTime())) {
                                    break;
                                }
                                if (currentPosition.getLayerId().equals(lastLayerId) || !nextPosition2.getLayerId().equals(lastLayerId)) {
                                    j2++;
                                } else {
                                    positionDataList.remove(currentPosition);
                                    stopExecute2 = true;
                                    break;
                                }
                            }
                            if (stopExecute2) {
                            }
                        } else if (!lastPositionType.equals("1") && !currentPositionType.equals("1") && !lastLayerId.equals(currentPosition.getLayerId())) {
                            boolean stopExecute3 = false;
                            int repeatIndoorPositionCnt = 0;
                            int j3 = i + 1;
                            while (true) {
                                if (j3 >= positionList.size()) {
                                    break;
                                }
                                PositionDataCalc nextPosition3 = (PositionDataCalc) positionList.get(j3);
                                if (nextPosition3.getBeaconId().equals(currentPosition.getBeaconId()) && nextPosition3.getDistance().equals(currentPosition.getDistance())) {
                                    repeatIndoorPositionCnt++;
                                }
                                if (currentPosition.getAcceptTime().plusSeconds(30L).isBefore(nextPosition3.getAcceptTime()) || j3 > i + 3 + repeatIndoorPositionCnt) {
                                    break;
                                }
                                if (!nextPosition3.getLayerId().equals(lastLayerId)) {
                                    j3++;
                                } else {
                                    positionDataList.remove(currentPosition);
                                    stopExecute3 = true;
                                    break;
                                }
                            }
                            if (stopExecute3) {
                            }
                        }
                    } else {
                        PositionDataCalc changeLayerPosition = null;
                        PositionDataCalc tempLastPosition = null;
                        int currentPositionCnt = 0;
                        double longitude = 0.0d;
                        double latitude = 0.0d;
                        if (lastPosition != null) {
                            if (LocalDateTimeUtil.between(lastPosition.getAcceptTime(), currentPosition.getAcceptTime(), ChronoUnit.SECONDS) <= 30) {
                                longitude = lastPosition.getLongitude().doubleValue();
                                latitude = lastPosition.getLatitude().doubleValue();
                                currentPositionCnt = 0 + 1;
                                tempLastPosition = lastPosition;
                            }
                            if (!lastLayerId.equals(currentPosition.getLayerId())) {
                                changeLayerPosition = lastPosition;
                            }
                        }
                        int sampleCnt = "0".equals(currentPosition.getPositionType()) ? 3 : 2;
                        for (int j4 = i; j4 < positionList.size() && currentPositionCnt < sampleCnt; j4++) {
                            PositionDataCalc item = (PositionDataCalc) positionList.get(j4);
                            if (tempLastPosition == null || !tempLastPosition.getBeaconId().equals(item.getBeaconId()) || !tempLastPosition.getDistance().equals(item.getDistance())) {
                                if (LocalDateTimeUtil.between(currentPosition.getAcceptTime(), item.getAcceptTime(), ChronoUnit.SECONDS) > 30) {
                                    break;
                                }
                                if (StrUtil.equals(currentPosition.getLayerId(), item.getLayerId()) || StrUtil.equals(currentPosition.getLayerId(), item.getOrgLayerId())) {
                                    longitude += item.getLongitude().doubleValue();
                                    latitude += item.getLatitude().doubleValue();
                                    currentPositionCnt++;
                                    tempLastPosition = item;
                                }
                            }
                        }
                        if (!"1".equals(currentPosition.getPositionType())) {
                            currentPosition.setLongitude(BigDecimal.valueOf(longitude / currentPositionCnt));
                            currentPosition.setLatitude(BigDecimal.valueOf(latitude / currentPositionCnt));
                        }
                        if (changeLayerPosition != null) {
                            if (changeLayerPosition.getLayerHeight().intValue() > currentPosition.getLayerHeight().intValue()) {
                                currentPosition.setLongitude(changeLayerPosition.getLongitude());
                                currentPosition.setLatitude(changeLayerPosition.getLatitude());
                            } else if (changeLayerPosition.getLayerHeight().intValue() < currentPosition.getLayerHeight().intValue()) {
                                currentPosition.setOrgLayerId(currentPosition.getLayerId());
                                currentPosition.setLayerId(changeLayerPosition.getLayerId());
                                currentPosition.setLayerHeight(changeLayerPosition.getLayerHeight());
                            }
                        }
                        this.cardLatestPositionHisMap.put(currentPosition.getCardId(), currentPosition);
                    }
                }
            });
        }
        if (!CollectionUtils.isEmpty(positionDataList)) {
            LocalDate today = LocalDate.now();
            if (this.driverClassName.contains("dm")) {
                tableName = "POSITION_HISTORY_" + today.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            } else {
                tableName = "position_history_" + today.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            }
            if (!this.positionHistoryService.existTable(tableName)) {
                this.positionHistoryService.createTable(tableName);
            }
            this.positionHistoryService.updateOrInsert(tableName, positionDataList, 1000);
            List<PositionHistoryCalendar> calendarList = this.positionHistoryCalendarMapper.selectList((Wrapper) new LambdaQueryWrapper<PositionHistoryCalendar>().eq((v0) -> {
                return v0.getDate();
            }, today));
            List<Long> personIds = (List) calendarList.stream().map((v0) -> {
                return v0.getPersonId();
            }).collect(Collectors.toList());
            positionDataList.removeIf(r2 -> {
                return personIds.contains(r2.getPersonId());
            });
            if (!CollectionUtils.isEmpty(positionDataList)) {
                Map<Long, PositionDataCalc> groupedItems = (Map) positionDataList.stream().collect(Collectors.groupingBy((v0) -> {
                    return v0.getPersonId();
                }, Collectors.collectingAndThen(Collectors.toList(), value -> {
                    return (PositionDataCalc) value.get(0);
                })));
                ArrayList arrayList = new ArrayList();
                groupedItems.forEach((personId, item) -> {
                    PositionHistoryCalendar p = PositionHistoryCalendar.builder().day(today).createTime(now).personId(personId).personType(item.getPersonType()).realName(item.getRealName()).deptId(item.getDeptId()).postId(item.getPostId()).contractorId(item.getContractorId()).build();
                    arrayList.add(p);
                });
                this.positionHistoryCalendarMapper.insertBatch(arrayList, 1000);
            }
        }
    }
    public void disposeOfflineBeacons() {
        Map<String, String> configMap = this.systemConfigService.getConfig();
        String offlineBeacons = configMap.get(ConfigValue.CONFIG_KEY_OFFLINE_BEACONS);
        if (com.xrkc.job.util.StringUtils.isNotBlank(offlineBeacons)) {
            this.positionCurrentService.deleteByOfflineBeacons(offlineBeacons);
            this.positionCurrentService.delDataByOfflineBeacons(offlineBeacons);
        }
    }
    public void delRssi() {
        Map<String, String> configMap = this.systemConfigService.getConfig();
        String v_rssi_save_second = configMap.get(ConfigValue.CONFIG_KEY_RSSI_SAVE_SECOND);
        Long rssiSaveSecond = Long.valueOf(com.xrkc.job.util.StringUtils.isNotBlank(v_rssi_save_second) ? Long.valueOf(v_rssi_save_second).longValue() : 30L);
        this.rssiService.batchDelete(rssiSaveSecond);
        this.statisticsFacilityStayService.delByDisposeStatus();
    }
    public void calcRssi() {
        Map<String, String> configMap = this.systemConfigService.getConfig();
        String v_distance = configMap.get(ConfigValue.CONFIG_KEY_SINGLE_VALID_DISTANCE);
        Object validDistance = Integer.valueOf(com.xrkc.job.util.StringUtils.isNotBlank(v_distance) ? Integer.valueOf(v_distance).intValue() : 11);
        String v_rssi_min = configMap.get(ConfigValue.CONFIG_KEY_RSSI_MIN);
        Object rssiMin = Integer.valueOf(com.xrkc.job.util.StringUtils.isNotBlank(v_rssi_min) ? Integer.valueOf(v_rssi_min).intValue() : -100);
        Map<String, Object> map = new HashMap<>();
        map.put("validDistance", validDistance);
        map.put("rssiMin", rssiMin);
        map.put("validSecond", 3);
        this.rssiService.batchReplace(map);
    }
    public void calcPosition_mod0() {
        List<Long> cardIdList = this.rssiService.selectCardIdListByMod(0);
        if (!CollectionUtils.isEmpty(cardIdList)) {
            calcPosition(cardIdList);
        }
    }
    public void calcPosition_mod1() {
        List<Long> cardIdList = this.rssiService.selectCardIdListByMod(1);
        if (!CollectionUtils.isEmpty(cardIdList)) {
            calcPosition(cardIdList);
        }
    }
    public void calcPosition_mod2() {
        List<Long> cardIdList = this.rssiService.selectCardIdListByMod(2);
        if (!CollectionUtils.isEmpty(cardIdList)) {
            calcPosition(cardIdList);
        }
    }
    public void calcPosition_mod3() {
        List<Long> cardIdList = this.rssiService.selectCardIdListByMod(3);
        if (!CollectionUtils.isEmpty(cardIdList)) {
            calcPosition(cardIdList);
        }
    }
    public void calcPosition_mod4() {
        List<Long> cardIdList = this.rssiService.selectCardIdListByMod(4);
        if (!CollectionUtils.isEmpty(cardIdList)) {
            calcPosition(cardIdList);
        }
    }
    private void calcPosition(List<Long> cardIdList) {
        Map<String, String> configMap = this.systemConfigService.getConfig();
        String v_samplingSecond = configMap.get(ConfigValue.CONFIG_KEY_SAMPLING_SECOND);
        Integer samplingSecond = Integer.valueOf(com.xrkc.job.util.StringUtils.isNotBlank(v_samplingSecond) ? Integer.valueOf(v_samplingSecond).intValue() : 6);
        String v_moveDistance = configMap.get(ConfigValue.CONFIG_KEY_MOVE_DISTANCE);
        Double moveDistance = com.xrkc.job.util.StringUtils.isNotBlank(v_moveDistance) ? Double.valueOf(v_moveDistance) : ConfigValue.DEFAULT_MOVE_DISTANCE;
        String v_notMoveDistance = configMap.get(ConfigValue.CONFIG_KEY_NOT_MOVE_DISTANCE);
        Double notMoveDistance = com.xrkc.job.util.StringUtils.isNotBlank(v_notMoveDistance) ? Double.valueOf(v_notMoveDistance) : ConfigValue.DEFAULT_NOT_MOVE_DISTANCE;
        String v_changeLayerSecond = configMap.get(ConfigValue.CONFIG_KEY_CHANGE_LAYER_SECOND);
        long changeLayerSecond = com.xrkc.job.util.StringUtils.isNotBlank(v_changeLayerSecond) ? Long.valueOf(v_changeLayerSecond).longValue() : 30L;
        cardIdList.stream().forEach(cardId -> {
            Map<String, Object> map = new HashMap<>();
            map.put("samplingSecond", samplingSecond);
            map.put("cardId", cardId);
            List<PositionVO> rssiInfoList = this.rssiService.selectRssiList(map);
            if (!CollectionUtils.isEmpty(rssiInfoList)) {
                PositionVO lastPosition = this.lastPositionMap.get(cardId);
                PositionVO minDistancePosition = (PositionVO) ((Optional) rssiInfoList.stream().collect(Collectors.minBy(Comparator.comparing((v0) -> {
                    return v0.getDistance();
                })))).get();
                if (Objects.nonNull(lastPosition)) {
                    if (Objects.nonNull(lastPosition.getStillStatus()) && lastPosition.getStillStatus().intValue() == 1 && minDistancePosition.getStillStatus().intValue() == 1) {
                        notMovePosition(minDistancePosition, lastPosition);
                        return;
                    }
                    Double minDistance = minDistancePosition.getDistance();
                    Boolean changeLayerMove = true;
                    if (!lastPosition.getLayerId().equals(minDistancePosition.getLayerId())) {
                        long interval = Duration.between(lastPosition.getAcceptTime(), minDistancePosition.getAcceptTime()).getSeconds();
                        if (interval < changeLayerSecond) {
                            changeLayerMove = false;
                        }
                    }
                    if (Objects.nonNull(minDistance) && minDistance.compareTo(moveDistance) < 0 && changeLayerMove.booleanValue()) {
                        updatePosition(minDistancePosition, lastPosition);
                        return;
                    }
                    double intervalDistance = CommonUtil.getDistance(lastPosition.getLongitude().doubleValue(), lastPosition.getLatitude().doubleValue(), minDistancePosition.getLongitude().doubleValue(), minDistancePosition.getLatitude().doubleValue());
                    log.debug("卡号：{}，上次：{}，本次：{}，距离：{}", minDistancePosition.getCardId(), lastPosition.getBeaconId(), minDistancePosition.getBeaconId(), Double.valueOf(intervalDistance));
                    if (intervalDistance > 30.0d) {
                        log.info("注意卡号：{}，上次：{}，本次：{}，距离：{}", minDistancePosition.getCardId(), lastPosition.getBeaconId(), minDistancePosition.getBeaconId(), Double.valueOf(intervalDistance));
                    }
                    if (intervalDistance < notMoveDistance.doubleValue()) {
                        notMovePosition(minDistancePosition, lastPosition);
                        return;
                    } else {
                        if (changeLayerMove.booleanValue()) {
                            updatePosition(minDistancePosition, lastPosition);
                            return;
                        }
                        return;
                    }
                }
                updatePosition(minDistancePosition, lastPosition);
            }
        });
    }
    private synchronized void updatePosition(PositionVO t, PositionVO lastPosition) {
        boolean execute = true;
        if (Objects.nonNull(lastPosition) && lastPosition.getAcceptTime().compareTo((ChronoLocalDateTime<?>) t.getAcceptTime()) == 0) {
            execute = false;
        }
        if (execute) {
            this.lastPositionMap.put(t.getCardId(), t);
            this.positionCurrentService.replaceVO(t);
            this.positionDataCalcService.replaceVO(t);
        }
    }
    private void notMovePosition(PositionVO t, PositionVO lastPosition) {
        boolean execute = true;
        if (Objects.nonNull(lastPosition) && lastPosition.getAcceptTime().compareTo((ChronoLocalDateTime<?>) t.getAcceptTime()) == 0) {
            execute = false;
        }
        if (execute) {
            t.setBeaconId(lastPosition.getBeaconId());
            t.setLongitude(lastPosition.getLongitude());
            t.setLatitude(lastPosition.getLatitude());
            this.lastPositionMap.put(t.getCardId(), t);
            this.positionCurrentService.notMovePosition(t);
            this.positionDataCalcService.insertByCurrent(t);
        }
    }
    /* JADX WARN: Multi-variable type inference failed */
    public void noEffectivePositioning() {
        Long sendedMinute = Long.valueOf(Long.parseLong(RedisCacheUtils.getConfigRedisCache().getOrDefault(ConfigValue.NO_EFFECTIVE_POSITION, "15")));
        List<CardDispenserPerson> list = (( (this.deviceCardSenderPersonService.lambdaSelect().ne((v0) -> {
            return v0.getCardSenderType();
        }, "1")).ne((v0) -> {
            return v0.getOnline();
        }, "2")).ge((v0) -> {
            return v0.getCommandTime();
        }, LocalDateTime.now().minusDays(1L))).list();
        list.forEach(cardDispenserPerson -> {
            cardDispenserPerson.setOnline("2");
        });
        this.deviceCardSenderPersonService.batchUpdate(list);
        List<CardDispenserPerson> list2 = ( (this.deviceCardSenderPersonService.lambdaSelect().eq((v0) -> {
            return v0.getCardSenderType();
        }, "1")).eq((v0) -> {
            return v0.getResult();
        }, "成功")).list();
        if (CollUtil.isNotEmpty((Collection<?>) list2)) {
            List<Long> sendSucOfflinePersonIdList = (List) list2.stream().map((v0) -> {
                return v0.getPersonId();
            }).collect(Collectors.toList());
            List<Long> onlinePersonIdList = this.positionCurrentService.selectAllOnlinePersonId(sendSucOfflinePersonIdList);
            List<Long> updateOnlinePersonIdList = new ArrayList<>();
            List<Long> updateOfflinePersonIdList = new ArrayList<>();
            list2.forEach(cardDispenserPerson2 -> {
                if (onlinePersonIdList.contains(cardDispenserPerson2.getPersonId())) {
                    if (!StrUtil.equals(cardDispenserPerson2.getOnline(), "1")) {
                        updateOnlinePersonIdList.add(cardDispenserPerson2.getPersonId());
                    }
                } else if (cardDispenserPerson2.getCommandTime().isBefore(LocalDateTime.now().minusMinutes(sendedMinute.longValue())) && !StrUtil.equals(cardDispenserPerson2.getOnline(), "0")) {
                    updateOfflinePersonIdList.add(cardDispenserPerson2.getPersonId());
                }
            });
            if (!updateOnlinePersonIdList.isEmpty()) {
                this.deviceCardSenderPersonService.updateOnlineStatus(updateOnlinePersonIdList);
            }
            if (!updateOfflinePersonIdList.isEmpty()) {
                this.deviceCardSenderPersonService.updateOfflineStatus(updateOfflinePersonIdList);
            }
        }
    }
}
