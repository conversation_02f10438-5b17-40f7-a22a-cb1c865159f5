package com.xrkc.job.domain;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/LocationContrastBo.class */
public class LocationContrastBo {
    private Integer locationCount;
    private Integer locationAllCount;
    public void setLocationCount(Integer locationCount) {
        this.locationCount = locationCount;
    }
    public void setLocationAllCount(Integer locationAllCount) {
        this.locationAllCount = locationAllCount;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof LocationContrastBo)) {
            return false;
        }
        LocationContrastBo other = (LocationContrastBo) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$locationCount = getLocationCount();
        Object other$locationCount = other.getLocationCount();
        if (this$locationCount == null) {
            if (other$locationCount != null) {
                return false;
            }
        } else if (!this$locationCount.equals(other$locationCount)) {
            return false;
        }
        Object this$locationAllCount = getLocationAllCount();
        Object other$locationAllCount = other.getLocationAllCount();
        return this$locationAllCount == null ? other$locationAllCount == null : this$locationAllCount.equals(other$locationAllCount);
    }
    protected boolean canEqual(Object other) {
        return other instanceof LocationContrastBo;
    }
    public int hashCode() {
        Object $locationCount = getLocationCount();
        int result = (1 * 59) + ($locationCount == null ? 43 : $locationCount.hashCode());
        Object $locationAllCount = getLocationAllCount();
        return (result * 59) + ($locationAllCount == null ? 43 : $locationAllCount.hashCode());
    }
    public String toString() {
        return "LocationContrastBo(locationCount=" + getLocationCount() + ", locationAllCount=" + getLocationAllCount() + StringPool.RIGHT_BRACKET;
    }
    public Integer getLocationCount() {
        return this.locationCount;
    }
    public Integer getLocationAllCount() {
        return this.locationAllCount;
    }
}
