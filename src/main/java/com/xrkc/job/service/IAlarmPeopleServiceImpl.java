package com.xrkc.job.service;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.core.domain.alarm.CoreAlarmPeople;
import com.xrkc.core.utils.bean.BeanUtils;
import com.xrkc.job.domain.PositionCurrent;
import com.xrkc.job.mapper.AlarmPeopleMapper;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IAlarmPeopleServiceImpl.class */
public class IAlarmPeopleServiceImpl extends ServiceImpl<AlarmPeopleMapper, CoreAlarmPeople> implements IAlarmPeopleService {
    private static final String ALARM_TYPE_40 = "40";
    @Autowired
    AlarmPeopleMapper mapper;
    @Override // com.xrkc.job.service.IAlarmPeopleService
    public void saveAlarmPeople(Map<Long, List<PositionCurrent>> map, String alarmType) {
        List<CoreAlarmPeople> pesList = new ArrayList<>();
        List<CoreAlarmPeople> updatePesList = new ArrayList<>();
        map.forEach((coreAlarmId, list) -> {
            if (CollUtil.isNotEmpty((Collection<?>) list)) {
                list.forEach(s -> {
                    CoreAlarmPeople coreAlarmPeople = new CoreAlarmPeople();
                    BeanUtils.copyProperties(s, coreAlarmPeople);
                    coreAlarmPeople.setId(null);
                    coreAlarmPeople.setCoreAlarmId(coreAlarmId);
                    coreAlarmPeople.setCreateTime(LocalDateTime.now());
                    if ("40".equals(alarmType)) {
                        coreAlarmPeople.setAcceptTime(LocalDateTime.now());
                    }
                    if (Objects.isNull(coreAlarmPeople.getAcceptTime())) {
                        coreAlarmPeople.setAcceptTime(LocalDateTime.now());
                    }
                    List<CoreAlarmPeople> coreAlarmPeopleList = this.mapper.selectAlarmPeopleId(coreAlarmId);
                    if (CollUtil.isNotEmpty((Collection<?>) coreAlarmPeopleList)) {
                        Map<Long, CoreAlarmPeople> coreAlarmPeopleMap = (Map) coreAlarmPeopleList.stream().collect(Collectors.toMap((v0) -> {
                            return v0.getPersonId();
                        }, t -> {
                            return t;
                        }));
                        if (coreAlarmPeopleMap.containsKey(s.getPersonId())) {
                            CoreAlarmPeople oldCoreAlarmPeople = coreAlarmPeopleMap.get(s.getPersonId());
                            if (Objects.nonNull(oldCoreAlarmPeople) && Objects.nonNull(oldCoreAlarmPeople.getEndTime())) {
                                oldCoreAlarmPeople.setEndTime(null);
                                oldCoreAlarmPeople.setAcceptTime(s.getAcceptTime());
                                updatePesList.add(oldCoreAlarmPeople);
                                return;
                            }
                            return;
                        }
                        pesList.add(coreAlarmPeople);
                        return;
                    }
                    pesList.add(coreAlarmPeople);
                });
            }
        });
        if (CollUtil.isNotEmpty((Collection<?>) pesList)) {
            for (CoreAlarmPeople coreAlarmPeople : pesList) {
                this.mapper.insert(coreAlarmPeople);
            }
        }
        if (CollUtil.isNotEmpty((Collection<?>) updatePesList)) {
            for (CoreAlarmPeople coreAlarmPeople2 : updatePesList) {
                this.mapper.updatePeople(coreAlarmPeople2);
            }
        }
    }
    @Override // com.xrkc.job.service.IAlarmPeopleService
    public void updatepeopleAlarm(Map<Long, Set<Long>> map, String alarmType, LocalDateTime time) {
        if (CollUtil.isNotEmpty(map)) {
            map.forEach((areaId, list) -> {
                if (CollUtil.isNotEmpty((Collection<?>) list)) {
                    List<Long> alarmIds = this.mapper.selectAlarmId(areaId, alarmType);
                    if (CollUtil.isNotEmpty((Collection<?>) alarmIds)) {
                        this.mapper.updatepeopleAlarm(alarmIds, list, time);
                    }
                }
            });
        }
    }
    @Override // com.xrkc.job.service.IAlarmPeopleService
    public void update50AlarmpeopleAlarm(Map<Long, Set<Long>> map, String alarmType, LocalDateTime time) {
        if (CollUtil.isNotEmpty(map)) {
            map.forEach((areaId, list) -> {
                if (CollUtil.isNotEmpty((Collection<?>) list)) {
                    List<Long> alarmIds = this.mapper.selectAlarmId(areaId, alarmType);
                    if (CollUtil.isNotEmpty((Collection<?>) alarmIds)) {
                        this.mapper.updateONLeavepeopleAlarm(alarmIds, list, time);
                    }
                }
            });
        }
    }
    @Override // com.xrkc.job.service.IAlarmPeopleService
    public List<CoreAlarmPeople> selectPersonLowPowerCardList(int cardPower) {
        return this.mapper.selectPersonLowPowerCardList(cardPower);
    }
    @Override // com.xrkc.job.service.IAlarmPeopleService
    public int batchInsert(List<CoreAlarmPeople> lowPowerList) {
        this.mapper.insertBatch(lowPowerList, 1000);
        return lowPowerList.size();
    }
}
