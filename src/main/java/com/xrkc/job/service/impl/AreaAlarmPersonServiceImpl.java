package com.xrkc.job.service.impl;
import com.xrkc.job.domain.AreaAlarmPerson;
import com.xrkc.job.mapper.AreaAlarmPersonMapper;
import com.xrkc.job.service.IAreaAlarmPersonService;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/AreaAlarmPersonServiceImpl.class */
public class AreaAlarmPersonServiceImpl implements IAreaAlarmPersonService {
    @Autowired
    private AreaAlarmPersonMapper mapper;
    @Override // com.xrkc.job.service.IAreaAlarmPersonService
    public List<Long> findAlarmPersonIdsByAreaId(Long areaId) {
        List<AreaAlarmPerson> list = this.mapper.selectList((v0) -> {
            return v0.getAreaId();
        }, areaId);
        return (List) list.stream().map((v0) -> {
            return v0.getPersonId();
        }).collect(Collectors.toList());
    }
}
