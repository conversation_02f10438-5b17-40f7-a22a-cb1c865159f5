package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.core.domain.system.SystemRoleFacility;
import com.xrkc.job.domain.SystemRoleFacilityVO;
import com.xrkc.job.mapper.SystemRoleFacilityMapper;
import com.xrkc.job.service.ISystemRoleFacilityService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/SystemRoleFacilityServiceImpl.class */
public class SystemRoleFacilityServiceImpl extends ServiceImpl<SystemRoleFacilityMapper, SystemRoleFacility> implements ISystemRoleFacilityService {
    @Autowired
    private SystemRoleFacilityMapper systemRoleFacilityMapper;
    @Override // com.xrkc.job.service.ISystemRoleFacilityService
    public List<SystemRoleFacilityVO> findByRoleIds(List<Long> roleIds) {
        return this.systemRoleFacilityMapper.findByRoleIds(roleIds);
    }
}
