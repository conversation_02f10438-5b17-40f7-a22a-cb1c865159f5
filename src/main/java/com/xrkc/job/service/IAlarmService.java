package com.xrkc.job.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xrkc.job.domain.Alarm;
import java.util.List;
import java.util.Map;
import java.util.Set;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IAlarmService.class */
public interface IAlarmService extends IService<Alarm> {
    int insertAlarm(Alarm alarm);
    int insertAreaAlarm(Alarm alarm);
    void insert50AreaAlarm(Alarm alarm);
    Alarm selectOne(Long l);
    List<Alarm> selectUnDisposedAlarm(Integer num);
    List<Alarm> statisticsByAlarm(Map<String, Object> map);
    List<Alarm> statisticsAlarmArea(Map<String, Object> map);
    List<Alarm> selectUnDisposedAlarmCard(List<Long> list);
    boolean existAlarmByParams(Map<String, Object> map);
    List<Alarm> selectList(Map<String, Object> map);
    List<Alarm> selectUnDisposedAlarmRail();
    List<Alarm> selectUnDisposedAlarmSOS();
    List<Long> selectDisposedAlarmId(List<Long> list);
    void closeAlarmEndTime(String str, Map<Long, Set<Long>> map, Set<Long> set, Set<Long> set2);
    Set<Long> selectNoClose81AlarmEndTime();
}
