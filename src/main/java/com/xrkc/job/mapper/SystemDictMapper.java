package com.xrkc.job.mapper;
import com.xrkc.core.mybatisplus.BaseMapperX;
import com.xrkc.job.domain.DeptStatisticsVo;
import com.xrkc.job.domain.SystemDict;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/SystemDictMapper.class */
public interface SystemDictMapper extends BaseMapperX<SystemDict> {
    List<SystemDict> findDictType(String str);
    List<DeptStatisticsVo> selectCountDeptList();
}
