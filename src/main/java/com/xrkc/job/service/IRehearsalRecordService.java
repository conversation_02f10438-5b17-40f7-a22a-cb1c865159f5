package com.xrkc.job.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xrkc.job.domain.RehearsalRecord;
import java.util.List;
import java.util.Map;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IRehearsalRecordService.class */
public interface IRehearsalRecordService extends IService<RehearsalRecord> {
    List<RehearsalRecord> selectUnCompletePlan();
    List<Long> findRehearsalCard(RehearsalRecord rehearsalRecord);
    int batchInsert(List<RehearsalRecord> list);
    List<RehearsalRecord> selectList(Map<String, Object> map);
    Integer batchUpdate(List<RehearsalRecord> list);
}
