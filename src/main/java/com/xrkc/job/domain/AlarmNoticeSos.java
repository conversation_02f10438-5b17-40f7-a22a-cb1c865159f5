package com.xrkc.job.domain;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xrkc.job.util.LongArrayToListStringSerialize;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/AlarmNoticeSos.class */
public class AlarmNoticeSos {
    private Long alarmNoticeSosId;
    private String alarmNoticeEnable;
    private String noticeType;
    private String firstPersonId;
    private String firstRealName;
    private String secondPersonId;
    private String secondRealName;
    private Long secondIntervalSecond;
    private String thirdPersonId;
    private String thirdRealName;
    private Long thirdIntervalSecond;
    private List<Long> firstPersonIds;
    private List<PersonVO> firstPersonList;
    private List<Long> secondPersonIds;
    private List<PersonVO> secondPersonList;
    private List<Long> thirdPersonIds;
    private List<PersonVO> thirdPersonList;
    private String searchValue;
    private String createBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    private String updateBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    private String remark;
    private Map<String, Object> params;
    @JsonSerialize(using = ToStringSerializer.class)
    public Long getAlarmNoticeSosId() {
        return this.alarmNoticeSosId;
    }
    public void setAlarmNoticeSosId(Long alarmNoticeSosId) {
        this.alarmNoticeSosId = alarmNoticeSosId;
    }
    public String getNoticeType() {
        return this.noticeType;
    }
    public void setNoticeType(String noticeType) {
        this.noticeType = noticeType;
    }
    public String getFirstPersonId() {
        return this.firstPersonId;
    }
    public void setFirstPersonId(String firstPersonId) {
        this.firstPersonId = firstPersonId;
    }
    public String getFirstRealName() {
        return this.firstRealName;
    }
    public void setFirstRealName(String firstRealName) {
        this.firstRealName = firstRealName;
    }
    public String getSecondPersonId() {
        return this.secondPersonId;
    }
    public void setSecondPersonId(String secondPersonId) {
        this.secondPersonId = secondPersonId;
    }
    public String getSecondRealName() {
        return this.secondRealName;
    }
    public void setSecondRealName(String secondRealName) {
        this.secondRealName = secondRealName;
    }
    public String getThirdPersonId() {
        return this.thirdPersonId;
    }
    public void setThirdPersonId(String thirdPersonId) {
        this.thirdPersonId = thirdPersonId;
    }
    public String getThirdRealName() {
        return this.thirdRealName;
    }
    public void setThirdRealName(String thirdRealName) {
        this.thirdRealName = thirdRealName;
    }
    public String getAlarmNoticeEnable() {
        return this.alarmNoticeEnable;
    }
    public void setAlarmNoticeEnable(String alarmNoticeEnable) {
        this.alarmNoticeEnable = alarmNoticeEnable;
    }
    public Long getSecondIntervalSecond() {
        return this.secondIntervalSecond;
    }
    public void setSecondIntervalSecond(Long secondIntervalSecond) {
        this.secondIntervalSecond = secondIntervalSecond;
    }
    public Long getThirdIntervalSecond() {
        return this.thirdIntervalSecond;
    }
    public void setThirdIntervalSecond(Long thirdIntervalSecond) {
        this.thirdIntervalSecond = thirdIntervalSecond;
    }
    @JsonSerialize(using = LongArrayToListStringSerialize.class)
    public List<Long> getFirstPersonIds() {
        return this.firstPersonIds;
    }
    public void setFirstPersonIds(List<Long> firstPersonIds) {
        this.firstPersonIds = firstPersonIds;
    }
    @JsonSerialize(using = LongArrayToListStringSerialize.class)
    public List<Long> getSecondPersonIds() {
        return this.secondPersonIds;
    }
    public void setSecondPersonIds(List<Long> secondPersonIds) {
        this.secondPersonIds = secondPersonIds;
    }
    @JsonSerialize(using = LongArrayToListStringSerialize.class)
    public List<Long> getThirdPersonIds() {
        return this.thirdPersonIds;
    }
    public void setThirdPersonIds(List<Long> thirdPersonIds) {
        this.thirdPersonIds = thirdPersonIds;
    }
    public List<PersonVO> getFirstPersonList() {
        return this.firstPersonList;
    }
    public void setFirstPersonList(List<PersonVO> firstPersonList) {
        this.firstPersonList = firstPersonList;
    }
    public List<PersonVO> getSecondPersonList() {
        return this.secondPersonList;
    }
    public void setSecondPersonList(List<PersonVO> secondPersonList) {
        this.secondPersonList = secondPersonList;
    }
    public List<PersonVO> getThirdPersonList() {
        return this.thirdPersonList;
    }
    public void setThirdPersonList(List<PersonVO> thirdPersonList) {
        this.thirdPersonList = thirdPersonList;
    }
    public String getSearchValue() {
        return this.searchValue;
    }
    public void setSearchValue(String searchValue) {
        this.searchValue = searchValue;
    }
    public String getCreateBy() {
        return this.createBy;
    }
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public String getUpdateBy() {
        return this.updateBy;
    }
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public LocalDateTime getUpdateTime() {
        return this.updateTime;
    }
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    public String getRemark() {
        return this.remark;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }
    public Map<String, Object> getParams() {
        return this.params;
    }
    public void setParams(Map<String, Object> params) {
        this.params = params;
    }
}
