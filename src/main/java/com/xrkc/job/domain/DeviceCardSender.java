package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
@TableName("device_card_sender")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/DeviceCardSender.class */
public class DeviceCardSender implements Serializable {
    private static final long serialVersionUID = 1;
    @JsonSerialize(using = ToStringSerializer.class)
    @TableId("card_sender_id")
    private Long cardSenderId;
    @TableField("device_sn")
    private String deviceSn;
    @TableField("device_name")
    private String deviceName;
    private Integer deviceBtnRent;
    @TableField("longitude")
    private BigDecimal longitude;
    @TableField("latitude")
    private BigDecimal latitude;
    @TableField("layer_id")
    private String layerId;
    @TableField("layer_height")
    private Integer layerHeight;
    @TableField("user_name")
    private String userName;
    @TableField("password")
    private String password;
    @TableField("device_enable")
    private String deviceEnable;
    @TableField("face_url")
    private String faceUrl;
    @TableField("face_flag")
    private String faceFlag;
    @TableField("face_mac_addr")
    private String faceMacAddr;
    private String faceSn;
    @TableField("face_heart_time")
    private LocalDateTime faceHeartTime;
    @TableField("device_heart_time")
    private LocalDateTime deviceHeartTime;
    public void setCardSenderId(Long cardSenderId) {
        this.cardSenderId = cardSenderId;
    }
    public void setDeviceSn(String deviceSn) {
        this.deviceSn = deviceSn;
    }
    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }
    public void setDeviceBtnRent(Integer deviceBtnRent) {
        this.deviceBtnRent = deviceBtnRent;
    }
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
    public void setLayerHeight(Integer layerHeight) {
        this.layerHeight = layerHeight;
    }
    public void setUserName(String userName) {
        this.userName = userName;
    }
    public void setPassword(String password) {
        this.password = password;
    }
    public void setDeviceEnable(String deviceEnable) {
        this.deviceEnable = deviceEnable;
    }
    public void setFaceUrl(String faceUrl) {
        this.faceUrl = faceUrl;
    }
    public void setFaceFlag(String faceFlag) {
        this.faceFlag = faceFlag;
    }
    public void setFaceMacAddr(String faceMacAddr) {
        this.faceMacAddr = faceMacAddr;
    }
    public void setFaceSn(String faceSn) {
        this.faceSn = faceSn;
    }
    public void setFaceHeartTime(LocalDateTime faceHeartTime) {
        this.faceHeartTime = faceHeartTime;
    }
    public void setDeviceHeartTime(LocalDateTime deviceHeartTime) {
        this.deviceHeartTime = deviceHeartTime;
    }
    public String toString() {
        return "DeviceCardSender(cardSenderId=" + getCardSenderId() + ", deviceSn=" + getDeviceSn() + ", deviceName=" + getDeviceName() + ", deviceBtnRent=" + getDeviceBtnRent() + ", longitude=" + getLongitude() + ", latitude=" + getLatitude() + ", layerId=" + getLayerId() + ", layerHeight=" + getLayerHeight() + ", userName=" + getUserName() + ", password=" + getPassword() + ", deviceEnable=" + getDeviceEnable() + ", faceUrl=" + getFaceUrl() + ", faceFlag=" + getFaceFlag() + ", faceMacAddr=" + getFaceMacAddr() + ", faceSn=" + getFaceSn() + ", faceHeartTime=" + getFaceHeartTime() + ", deviceHeartTime=" + getDeviceHeartTime() + StringPool.RIGHT_BRACKET;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof DeviceCardSender)) {
            return false;
        }
        DeviceCardSender other = (DeviceCardSender) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$cardSenderId = getCardSenderId();
        Object other$cardSenderId = other.getCardSenderId();
        if (this$cardSenderId == null) {
            if (other$cardSenderId != null) {
                return false;
            }
        } else if (!this$cardSenderId.equals(other$cardSenderId)) {
            return false;
        }
        Object this$deviceBtnRent = getDeviceBtnRent();
        Object other$deviceBtnRent = other.getDeviceBtnRent();
        if (this$deviceBtnRent == null) {
            if (other$deviceBtnRent != null) {
                return false;
            }
        } else if (!this$deviceBtnRent.equals(other$deviceBtnRent)) {
            return false;
        }
        Object this$layerHeight = getLayerHeight();
        Object other$layerHeight = other.getLayerHeight();
        if (this$layerHeight == null) {
            if (other$layerHeight != null) {
                return false;
            }
        } else if (!this$layerHeight.equals(other$layerHeight)) {
            return false;
        }
        Object this$deviceSn = getDeviceSn();
        Object other$deviceSn = other.getDeviceSn();
        if (this$deviceSn == null) {
            if (other$deviceSn != null) {
                return false;
            }
        } else if (!this$deviceSn.equals(other$deviceSn)) {
            return false;
        }
        Object this$deviceName = getDeviceName();
        Object other$deviceName = other.getDeviceName();
        if (this$deviceName == null) {
            if (other$deviceName != null) {
                return false;
            }
        } else if (!this$deviceName.equals(other$deviceName)) {
            return false;
        }
        Object this$longitude = getLongitude();
        Object other$longitude = other.getLongitude();
        if (this$longitude == null) {
            if (other$longitude != null) {
                return false;
            }
        } else if (!this$longitude.equals(other$longitude)) {
            return false;
        }
        Object this$latitude = getLatitude();
        Object other$latitude = other.getLatitude();
        if (this$latitude == null) {
            if (other$latitude != null) {
                return false;
            }
        } else if (!this$latitude.equals(other$latitude)) {
            return false;
        }
        Object this$layerId = getLayerId();
        Object other$layerId = other.getLayerId();
        if (this$layerId == null) {
            if (other$layerId != null) {
                return false;
            }
        } else if (!this$layerId.equals(other$layerId)) {
            return false;
        }
        Object this$userName = getUserName();
        Object other$userName = other.getUserName();
        if (this$userName == null) {
            if (other$userName != null) {
                return false;
            }
        } else if (!this$userName.equals(other$userName)) {
            return false;
        }
        Object this$password = getPassword();
        Object other$password = other.getPassword();
        if (this$password == null) {
            if (other$password != null) {
                return false;
            }
        } else if (!this$password.equals(other$password)) {
            return false;
        }
        Object this$deviceEnable = getDeviceEnable();
        Object other$deviceEnable = other.getDeviceEnable();
        if (this$deviceEnable == null) {
            if (other$deviceEnable != null) {
                return false;
            }
        } else if (!this$deviceEnable.equals(other$deviceEnable)) {
            return false;
        }
        Object this$faceUrl = getFaceUrl();
        Object other$faceUrl = other.getFaceUrl();
        if (this$faceUrl == null) {
            if (other$faceUrl != null) {
                return false;
            }
        } else if (!this$faceUrl.equals(other$faceUrl)) {
            return false;
        }
        Object this$faceFlag = getFaceFlag();
        Object other$faceFlag = other.getFaceFlag();
        if (this$faceFlag == null) {
            if (other$faceFlag != null) {
                return false;
            }
        } else if (!this$faceFlag.equals(other$faceFlag)) {
            return false;
        }
        Object this$faceMacAddr = getFaceMacAddr();
        Object other$faceMacAddr = other.getFaceMacAddr();
        if (this$faceMacAddr == null) {
            if (other$faceMacAddr != null) {
                return false;
            }
        } else if (!this$faceMacAddr.equals(other$faceMacAddr)) {
            return false;
        }
        Object this$faceSn = getFaceSn();
        Object other$faceSn = other.getFaceSn();
        if (this$faceSn == null) {
            if (other$faceSn != null) {
                return false;
            }
        } else if (!this$faceSn.equals(other$faceSn)) {
            return false;
        }
        Object this$faceHeartTime = getFaceHeartTime();
        Object other$faceHeartTime = other.getFaceHeartTime();
        if (this$faceHeartTime == null) {
            if (other$faceHeartTime != null) {
                return false;
            }
        } else if (!this$faceHeartTime.equals(other$faceHeartTime)) {
            return false;
        }
        Object this$deviceHeartTime = getDeviceHeartTime();
        Object other$deviceHeartTime = other.getDeviceHeartTime();
        return this$deviceHeartTime == null ? other$deviceHeartTime == null : this$deviceHeartTime.equals(other$deviceHeartTime);
    }
    protected boolean canEqual(Object other) {
        return other instanceof DeviceCardSender;
    }
    public int hashCode() {
        Object $cardSenderId = getCardSenderId();
        int result = (1 * 59) + ($cardSenderId == null ? 43 : $cardSenderId.hashCode());
        Object $deviceBtnRent = getDeviceBtnRent();
        int result2 = (result * 59) + ($deviceBtnRent == null ? 43 : $deviceBtnRent.hashCode());
        Object $layerHeight = getLayerHeight();
        int result3 = (result2 * 59) + ($layerHeight == null ? 43 : $layerHeight.hashCode());
        Object $deviceSn = getDeviceSn();
        int result4 = (result3 * 59) + ($deviceSn == null ? 43 : $deviceSn.hashCode());
        Object $deviceName = getDeviceName();
        int result5 = (result4 * 59) + ($deviceName == null ? 43 : $deviceName.hashCode());
        Object $longitude = getLongitude();
        int result6 = (result5 * 59) + ($longitude == null ? 43 : $longitude.hashCode());
        Object $latitude = getLatitude();
        int result7 = (result6 * 59) + ($latitude == null ? 43 : $latitude.hashCode());
        Object $layerId = getLayerId();
        int result8 = (result7 * 59) + ($layerId == null ? 43 : $layerId.hashCode());
        Object $userName = getUserName();
        int result9 = (result8 * 59) + ($userName == null ? 43 : $userName.hashCode());
        Object $password = getPassword();
        int result10 = (result9 * 59) + ($password == null ? 43 : $password.hashCode());
        Object $deviceEnable = getDeviceEnable();
        int result11 = (result10 * 59) + ($deviceEnable == null ? 43 : $deviceEnable.hashCode());
        Object $faceUrl = getFaceUrl();
        int result12 = (result11 * 59) + ($faceUrl == null ? 43 : $faceUrl.hashCode());
        Object $faceFlag = getFaceFlag();
        int result13 = (result12 * 59) + ($faceFlag == null ? 43 : $faceFlag.hashCode());
        Object $faceMacAddr = getFaceMacAddr();
        int result14 = (result13 * 59) + ($faceMacAddr == null ? 43 : $faceMacAddr.hashCode());
        Object $faceSn = getFaceSn();
        int result15 = (result14 * 59) + ($faceSn == null ? 43 : $faceSn.hashCode());
        Object $faceHeartTime = getFaceHeartTime();
        int result16 = (result15 * 59) + ($faceHeartTime == null ? 43 : $faceHeartTime.hashCode());
        Object $deviceHeartTime = getDeviceHeartTime();
        return (result16 * 59) + ($deviceHeartTime == null ? 43 : $deviceHeartTime.hashCode());
    }
    public Long getCardSenderId() {
        return this.cardSenderId;
    }
    public String getDeviceSn() {
        return this.deviceSn;
    }
    public String getDeviceName() {
        return this.deviceName;
    }
    public Integer getDeviceBtnRent() {
        return this.deviceBtnRent;
    }
    public BigDecimal getLongitude() {
        return this.longitude;
    }
    public BigDecimal getLatitude() {
        return this.latitude;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public Integer getLayerHeight() {
        return this.layerHeight;
    }
    public String getUserName() {
        return this.userName;
    }
    public String getPassword() {
        return this.password;
    }
    public String getDeviceEnable() {
        return this.deviceEnable;
    }
    public String getFaceUrl() {
        return this.faceUrl;
    }
    public String getFaceFlag() {
        return this.faceFlag;
    }
    public String getFaceMacAddr() {
        return this.faceMacAddr;
    }
    public String getFaceSn() {
        return this.faceSn;
    }
    public LocalDateTime getFaceHeartTime() {
        return this.faceHeartTime;
    }
    public LocalDateTime getDeviceHeartTime() {
        return this.deviceHeartTime;
    }
}
