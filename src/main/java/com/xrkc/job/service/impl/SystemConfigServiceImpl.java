package com.xrkc.job.service.impl;
import com.xrkc.core.domain.system.SystemConfig;
import com.xrkc.job.mapper.SysConfigMapper;
import com.xrkc.job.service.ISystemConfigService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/SystemConfigServiceImpl.class */
public class SystemConfigServiceImpl implements ISystemConfigService {
    @Autowired
    private SysConfigMapper sysConfigMapper;
    @Override // com.xrkc.job.service.ISystemConfigService
    public Map<String, String> getConfig() {
        List<SystemConfig> all = this.sysConfigMapper.configList();
        Map<String, String> config = new HashMap<>();
        all.forEach(f -> {
        });
        return config;
    }
    @Override // com.xrkc.job.service.ISystemConfigService
    public String findConfigVal(String configKey) {
        if (StringUtils.isNotBlank(configKey)) {
            return this.sysConfigMapper.findConfigVal(configKey);
        }
        return null;
    }
    @Override // com.xrkc.job.service.ISystemConfigService
    public String selectRemarkByKey(String configKey) {
        if (StringUtils.isNotBlank(configKey)) {
            return this.sysConfigMapper.selectRemarkByKey(configKey);
        }
        return null;
    }
    @Override // com.xrkc.job.service.ISystemConfigService
    public void updateRemarkByKey(String configKey, String today) {
        this.sysConfigMapper.updateRemarkByKey(configKey, today);
    }
}
