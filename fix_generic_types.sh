#!/bin/bash

# 批量修复项目中各种泛型类型问题

echo "开始批量修复项目中的泛型类型问题..."

# 1. 修复 new LambdaQueryWrapperX() 的使用
echo "1. 修复 new LambdaQueryWrapperX() 的使用..."
files=$(find src/main/java -name "*.java" -exec grep -l "new LambdaQueryWrapperX()" {} \; 2>/dev/null)
if [ ! -z "$files" ]; then
    for file in $files; do
        echo "  - 修复文件: $file"
        # 获取实体类名称
        entity_class=$(grep -o "extends BaseMapperX<[^>]*>" "$file" | head -1 | sed 's/.*<\([^>]*\)>.*/\1/')
        if [ ! -z "$entity_class" ]; then
            sed -i '' "s/new LambdaQueryWrapperX()/new LambdaQueryWrapperX<$entity_class>()/g" "$file"
            echo "    - 已添加泛型类型: $entity_class"
        fi
    done
fi

# 2. 修复 new LambdaUpdateWrapper() 的使用
echo "2. 修复 new LambdaUpdateWrapper() 的使用..."
files=$(find src/main/java -name "*.java" -exec grep -l "new LambdaUpdateWrapper()" {} \; 2>/dev/null)
if [ ! -z "$files" ]; then
    for file in $files; do
        echo "  - 修复文件: $file"
        # 获取实体类名称
        entity_class=$(grep -o "extends BaseMapperX<[^>]*>" "$file" | head -1 | sed 's/.*<\([^>]*\)>.*/\1/')
        if [ ! -z "$entity_class" ]; then
            sed -i '' "s/new LambdaUpdateWrapper()/new LambdaUpdateWrapper<$entity_class>()/g" "$file"
            echo "    - 已添加泛型类型: $entity_class"
        fi
    done
fi

# 3. 修复 new QueryWrapper() 的使用
echo "3. 修复 new QueryWrapper() 的使用..."
files=$(find src/main/java -name "*.java" -exec grep -l "new QueryWrapper()" {} \; 2>/dev/null)
if [ ! -z "$files" ]; then
    for file in $files; do
        echo "  - 修复文件: $file"
        # 获取实体类名称
        entity_class=$(grep -o "extends BaseMapperX<[^>]*>" "$file" | head -1 | sed 's/.*<\([^>]*\)>.*/\1/')
        if [ ! -z "$entity_class" ]; then
            sed -i '' "s/new QueryWrapper()/new QueryWrapper<$entity_class>()/g" "$file"
            echo "    - 已添加泛型类型: $entity_class"
        fi
    done
fi

# 4. 修复 new UpdateWrapper() 的使用
echo "4. 修复 new UpdateWrapper() 的使用..."
files=$(find src/main/java -name "*.java" -exec grep -l "new UpdateWrapper()" {} \; 2>/dev/null)
if [ ! -z "$files" ]; then
    for file in $files; do
        echo "  - 修复文件: $file"
        # 获取实体类名称
        entity_class=$(grep -o "extends BaseMapperX<[^>]*>" "$file" | head -1 | sed 's/.*<\([^>]*\)>.*/\1/')
        if [ ! -z "$entity_class" ]; then
            sed -i '' "s/new UpdateWrapper()/new UpdateWrapper<$entity_class>()/g" "$file"
            echo "    - 已添加泛型类型: $entity_class"
        fi
    done
fi

# 5. 修复 Wrappers.lambdaQuery() 的使用
echo "5. 修复 Wrappers.lambdaQuery() 的使用..."
files=$(find src/main/java -name "*.java" -exec grep -l "Wrappers\.lambdaQuery()" {} \; 2>/dev/null)
if [ ! -z "$files" ]; then
    for file in $files; do
        echo "  - 修复文件: $file"
        # 获取实体类名称
        entity_class=$(grep -o "extends BaseMapperX<[^>]*>" "$file" | head -1 | sed 's/.*<\([^>]*\)>.*/\1/')
        if [ ! -z "$entity_class" ]; then
            sed -i '' "s/Wrappers\.lambdaQuery()/Wrappers.lambdaQuery($entity_class.class)/g" "$file"
            echo "    - 已添加泛型类型: $entity_class"
        fi
    done
fi

# 6. 修复 Wrappers.lambdaUpdate() 的使用
echo "6. 修复 Wrappers.lambdaUpdate() 的使用..."
files=$(find src/main/java -name "*.java" -exec grep -l "Wrappers\.lambdaUpdate()" {} \; 2>/dev/null)
if [ ! -z "$files" ]; then
    for file in $files; do
        echo "  - 修复文件: $file"
        # 获取实体类名称
        entity_class=$(grep -o "extends BaseMapperX<[^>]*>" "$file" | head -1 | sed 's/.*<\([^>]*\)>.*/\1/')
        if [ ! -z "$entity_class" ]; then
            sed -i '' "s/Wrappers\.lambdaUpdate()/Wrappers.lambdaUpdate($entity_class.class)/g" "$file"
            echo "    - 已添加泛型类型: $entity_class"
        fi
    done
fi

echo ""
echo "批量修复完成！"
echo ""
echo "修复内容："
echo "1. 修复了 new LambdaQueryWrapperX() 的使用"
echo "2. 修复了 new LambdaUpdateWrapper() 的使用"
echo "3. 修复了 new QueryWrapper() 的使用"
echo "4. 修复了 new UpdateWrapper() 的使用"
echo "5. 修复了 Wrappers.lambdaQuery() 的使用"
echo "6. 修复了 Wrappers.lambdaUpdate() 的使用"
echo ""
echo "注意：所有修复都基于 Mapper 接口的泛型参数自动推断实体类" 