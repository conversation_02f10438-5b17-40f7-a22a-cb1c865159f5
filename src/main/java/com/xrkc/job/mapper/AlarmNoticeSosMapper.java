package com.xrkc.job.mapper;
import com.xrkc.job.domain.AlarmNotice;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/AlarmNoticeSosMapper.class */
public interface AlarmNoticeSosMapper {
    AlarmNotice findByAlarmNoticeSosId(Long l);
    AlarmNotice findByAlarmNoticeId(Long l);
    List<AlarmNotice> findByAlarmNoticeIds(@Param("list") Set<Long> set);
}
