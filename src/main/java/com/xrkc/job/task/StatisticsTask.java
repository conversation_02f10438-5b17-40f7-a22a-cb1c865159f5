package com.xrkc.job.task;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.domain.statistics.StatisticsAlarmDispose;
import com.xrkc.core.domain.statistics.StatisticsBuildingAlarm;
import com.xrkc.core.domain.statistics.StatisticsDeptOnlineLog;
import com.xrkc.core.domain.statistics.StatisticsFacilityCurrentPersonList;
import com.xrkc.core.domain.vehicle.PositionHistoryCalendar;
import com.xrkc.core.domain.vehicle.VehicleAlarm;
import com.xrkc.job.domain.Alarm;
import com.xrkc.job.domain.Constants;
import com.xrkc.job.domain.Facility;
import com.xrkc.job.domain.FacilityAccessRecord;
import com.xrkc.job.domain.FacilityPersonVo;
import com.xrkc.job.domain.FacilityRail;
import com.xrkc.job.domain.PositionCurrent;
import com.xrkc.job.domain.Rail;
import com.xrkc.job.domain.StatisticsAlarmAreaFlow;
import com.xrkc.job.domain.StatisticsAlarmFlow;
import com.xrkc.job.domain.StatisticsFacilityCurrentPerson;
import com.xrkc.job.domain.StatisticsPersonFlow;
import com.xrkc.job.domain.StatisticsRailCurrentPerson;
import com.xrkc.job.domain.StatisticsVehicleAlarmFlow;
import com.xrkc.job.domain.SystemDict;
import com.xrkc.job.mapper.StatisticsAlarmDisposeMapper;
import com.xrkc.job.mapper.StatisticsBuildingAlarmMapper;
import com.xrkc.job.module.vehicle.mapper.PositionHistoryCalendarMapper;
import com.xrkc.job.module.vehicle.service.IVehicleAlarmService;
import com.xrkc.job.service.IAlarmService;
import com.xrkc.job.service.IFacilityAccessRecordService;
import com.xrkc.job.service.IFacilityService;
import com.xrkc.job.service.IPersonService;
import com.xrkc.job.service.IPositionCurrentService;
import com.xrkc.job.service.IRailService;
import com.xrkc.job.service.IStatisticsAlarmFlowService;
import com.xrkc.job.service.IStatisticsAreaAlarmFlowService;
import com.xrkc.job.service.IStatisticsDeptOnlineLogService;
import com.xrkc.job.service.IStatisticsFacilityCurrentPersonListService;
import com.xrkc.job.service.IStatisticsFacilityCurrentPersonService;
import com.xrkc.job.service.IStatisticsPersonFlowService;
import com.xrkc.job.service.IStatisticsRailCurrentPersonService;
import com.xrkc.job.service.IStatisticsVehicleAlarmFlowService;
import com.xrkc.job.service.ISystemDictService;
import com.xrkc.job.util.CommonUtil;
import com.xrkc.redis.utils.RedisCacheUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalUnit;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;
import net.jodah.expiringmap.ExpirationPolicy;
import net.jodah.expiringmap.ExpiringMap;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
@Component("statisticsTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/StatisticsTask.class */
public class StatisticsTask {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) StatisticsTask.class);
    @Autowired
    private IStatisticsPersonFlowService statisticsPersonFlowService;
    @Autowired
    private IStatisticsAlarmFlowService statisticsAlarmFlowService;
    @Autowired
    private IStatisticsAreaAlarmFlowService statisticsAreaAlarmFlowService;
    @Autowired
    private ISystemDictService systemDictService;
    @Autowired
    private IAlarmService alarmService;
    @Autowired
    private IStatisticsVehicleAlarmFlowService statisticsVehicleAlarmFlowService;
    @Autowired
    private IRailService railService;
    @Autowired
    private IPositionCurrentService positionCurrentService;
    @Autowired
    private IStatisticsRailCurrentPersonService statisticsRailCurrentPersonService;
    @Autowired
    private IFacilityService facilityService;
    @Autowired
    private IStatisticsFacilityCurrentPersonService statisticsFacilityCurrentPersonService;
    @Autowired
    private IFacilityAccessRecordService facilityAccessRecordService;
    @Autowired
    private IStatisticsDeptOnlineLogService statisticsDeptOnlineLogService;
    @Autowired
    private IPersonService personService;
    @Autowired
    private IVehicleAlarmService vehicleAlarmService;
    @Autowired
    private IStatisticsFacilityCurrentPersonListService statisticsFacilityPersonListService;
    @Autowired
    private PositionHistoryCalendarMapper positionHistoryCalendarMapper;
    @Autowired
    private StatisticsAlarmDisposeMapper statisticsAlarmDisposeMapper;
    @Autowired
    private StatisticsBuildingAlarmMapper statisticsBuildingAlarmMapper;
    ExpiringMap<Long, Map<Long, FacilityPersonVo>> facilityPersonList = ExpiringMap.builder().expirationPolicy(ExpirationPolicy.ACCESSED).expiration(30, TimeUnit.DAYS).build();
    private boolean facilityPersonListIsInit = false;
    private static final long minute = 30;
    private static final int newScale = 2;

    public void calibrationStatisticsYesterday() {
        LocalDateTime nowTime = LocalDateTime.now();
        LocalDate now = LocalDate.now();
        LocalDate yesterday = now.minus(1L, (TemporalUnit) ChronoUnit.DAYS);
        statisticsPerson(yesterday, nowTime);
        statisticsAlarmType(yesterday, nowTime);
        statisticsAlarmArea(yesterday, nowTime);
        statisticsVehicleAlarmType(yesterday, nowTime);
    }
    public void statisticsTodayFlow() {
        LocalDateTime nowTime = LocalDateTime.now();
        LocalDate now = LocalDate.now();
        statisticsPerson(now, nowTime);
        statisticsAlarmType(now, nowTime);
        statisticsAlarmArea(now, nowTime);
        statisticsVehicleAlarmType(now, nowTime);
    }
    /* JADX WARN: Multi-variable type inference failed */
    private void statisticsPerson(LocalDate statisticsDate, LocalDateTime nowTime) {
        List<SystemDict> personTypeList = this.systemDictService.findDictType("person_type");
        List<StatisticsPersonFlow> statisticsPersonFlowList = new ArrayList<>();
        List<PositionHistoryCalendar> calendarList = this.positionHistoryCalendarMapper.selectList((Wrapper) new LambdaQueryWrapper<PositionHistoryCalendar>().eq((v0) -> {
            return v0.getDay();
        }, statisticsDate));
        Map<String, Long> countMap = (Map) calendarList.stream().filter(f -> {
            return StringUtils.isNotBlank(f.getPersonType());
        }).collect(Collectors.groupingBy((v0) -> {
            return v0.getPersonType();
        }, Collectors.counting()));
        personTypeList.forEach(dict -> {
            StatisticsPersonFlow entity = new StatisticsPersonFlow();
            entity.setStatisticsDate(statisticsDate);
            entity.setPersonType(dict.getDictValue());
            entity.setPersonTypeName(dict.getDictLabel());
            entity.setCreateTime(nowTime);
            entity.setPersonCount(Long.valueOf(CollectionUtils.isEmpty(calendarList) ? 0L : ((Long) countMap.getOrDefault(dict.getDictValue(), 0L)).longValue()));
            statisticsPersonFlowList.add(entity);
        });
        if (!CollectionUtils.isEmpty(statisticsPersonFlowList)) {
            this.statisticsPersonFlowService.batchReplace(statisticsPersonFlowList);
        }
    }
    private void statisticsAlarmType(LocalDate statisticsDate, LocalDateTime nowTime) {
        Map<String, Object> map = new HashMap<>();
        map.put("alarmDate", statisticsDate);
        List<Alarm> alarmList = this.alarmService.statisticsByAlarm(map);
        List<SystemDict> alarmTypeList = this.systemDictService.findDictType(VehicleAlarm.ALARM_TYPE);
        List<StatisticsAlarmFlow> result = new ArrayList<>();
        alarmTypeList.stream().forEach(dict -> {
            StatisticsAlarmFlow entity = new StatisticsAlarmFlow();
            entity.setStatisticsDate(statisticsDate);
            entity.setAlarmType(dict.getDictValue());
            entity.setAlarmTypeName(dict.getDictLabel());
            entity.setCreateTime(nowTime);
            entity.setAlarmCount(0);
            alarmList.stream().filter(f -> {
                return dict.getDictValue().equals(f.getAlarmType());
            }).findFirst().ifPresent(alarm -> {
                entity.setAlarmCount(alarm.getAlarmCount());
            });
            result.add(entity);
        });
        if (!CollectionUtils.isEmpty(result)) {
            this.statisticsAlarmFlowService.batchReplace(statisticsDate, result);
        }
    }
    private void statisticsAlarmArea(LocalDate statisticsDate, LocalDateTime nowTime) {
        Map<String, Object> map = new HashMap<>();
        map.put("alarmDate", statisticsDate);
        List<Alarm> alarmList = this.alarmService.statisticsAlarmArea(map);
        List<StatisticsAlarmAreaFlow> result = new ArrayList<>();
        alarmList.stream().forEach(alarm -> {
            StatisticsAlarmAreaFlow entity = new StatisticsAlarmAreaFlow();
            entity.setStatisticsDate(statisticsDate);
            entity.setAreaId(alarm.getAreaId());
            entity.setAreaName(alarm.getAreaName());
            entity.setAlarmCount(alarm.getAlarmCount());
            entity.setCreateTime(nowTime);
            result.add(entity);
        });
        if (!CollectionUtils.isEmpty(result)) {
            this.statisticsAreaAlarmFlowService.batchReplace(statisticsDate, result);
        }
    }
    private void statisticsVehicleAlarmType(LocalDate statisticsDate, LocalDateTime nowTime) {
        Map<String, Object> map = new HashMap<>();
        map.put("alarmDate", statisticsDate);
        Map<String, Long> alarmTypeMap = this.vehicleAlarmService.statisticsAlarmTypeMap(statisticsDate);
        List<SystemDict> vehicleAlarmTypeList = this.systemDictService.findDictType("vehicle_alarm_type");
        List<StatisticsVehicleAlarmFlow> result = new ArrayList<>();
        vehicleAlarmTypeList.stream().forEach(dict -> {
            StatisticsVehicleAlarmFlow entity = new StatisticsVehicleAlarmFlow();
            entity.setStatisticsDate(statisticsDate);
            entity.setAlarmType(dict.getDictValue());
            entity.setAlarmTypeName(dict.getDictLabel());
            entity.setCreateTime(nowTime);
            Long count = alarmTypeMap.containsKey(dict.getDictValue()) ? (Long) alarmTypeMap.get(dict.getDictValue()) : 0L;
            entity.setAlarmCount(count);
            result.add(entity);
        });
        if (!CollectionUtils.isEmpty(result)) {
            this.statisticsVehicleAlarmFlowService.batchReplace(result);
        }
    }
    public void statisticsRailCurrentPerson() {
        LocalDateTime nowTime = LocalDateTime.now();
        List<SystemDict> personTypeList = this.systemDictService.findDictType("person_type");
        Map<String, Object> railQuery = new HashMap<>();
        railQuery.put("railType", "region");
        List<Rail> railList = this.railService.selectList(railQuery);
        List<StatisticsRailCurrentPerson> statisticsRailCurrentPersonList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(railList)) {
            railList.stream().filter(f -> {
                return StringUtils.isNotBlank(f.getRailScope());
            }).forEach(rail -> {
                List<String> personVoList = this.positionCurrentService.countCurrentPerson(rail.getLayerId(), rail.getRailScope(), rail.getDrawType());
                personTypeList.stream().forEach(dict -> {
                    StatisticsRailCurrentPerson entity = new StatisticsRailCurrentPerson();
                    entity.setRailId(rail.getRailId());
                    entity.setRailName(rail.getRailName());
                    entity.setRailType(rail.getRailType());
                    entity.setRailScope(rail.getRailScope());
                    entity.setLayerId(rail.getLayerId());
                    entity.setLayerHeight(rail.getLayerHeight());
                    entity.setStatisticsTime(nowTime);
                    entity.setPersonType(dict.getDictValue());
                    entity.setPersonTypeName(dict.getDictLabel());
                    entity.setPersonCount(Long.valueOf(personVoList.stream().filter(f2 -> {
                        return dict.getDictValue().equals(f2);
                    }).count()));
                    statisticsRailCurrentPersonList.add(entity);
                });
            });
        }
        this.statisticsRailCurrentPersonService.batchReplace("region", statisticsRailCurrentPersonList);
        Map<String, Object> railQuery2 = new HashMap<>();
        railQuery2.put("railType", "risk");
        List<Rail> riskRailList = this.railService.selectList(railQuery2);
        List<StatisticsRailCurrentPerson> statisticsRiskRailCurrentPersonList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(riskRailList)) {
            riskRailList.stream().filter(f2 -> {
                return StringUtils.isNotBlank(f2.getRailScope());
            }).forEach(rail2 -> {
                List<String> personVoList = this.positionCurrentService.countCurrentPerson(null, rail2.getRailScope(), rail2.getDrawType());
                personTypeList.stream().forEach(dict -> {
                    StatisticsRailCurrentPerson entity = new StatisticsRailCurrentPerson();
                    entity.setRailId(rail2.getRailId());
                    entity.setRailName(rail2.getRailName());
                    entity.setRailType(rail2.getRailType());
                    entity.setRailScope(rail2.getRailScope());
                    entity.setLayerId(rail2.getLayerId());
                    entity.setLayerHeight(rail2.getLayerHeight());
                    entity.setStatisticsTime(nowTime);
                    entity.setPersonType(dict.getDictValue());
                    entity.setPersonTypeName(dict.getDictLabel());
                    entity.setPersonCount(Long.valueOf(personVoList.stream().filter(f3 -> {
                        return dict.getDictValue().equals(f3);
                    }).count()));
                    statisticsRiskRailCurrentPersonList.add(entity);
                });
            });
        }
        this.statisticsRailCurrentPersonService.batchReplace("risk", statisticsRiskRailCurrentPersonList);
        List<FacilityRail> facilityRailList = this.facilityService.selectFacilityRailList();
        if (CollectionUtils.isEmpty(facilityRailList)) {
            this.statisticsFacilityCurrentPersonService.truncateTable();
            this.statisticsFacilityPersonListService.deleteAll();
            return;
        }
        Map<String, Long> map = new HashMap<>();
        facilityRailList.forEach(s -> {
            map.put(s.getFacilityName(), s.getFacilityId());
            if (inNeedLayId(s)) {
                s.setLayerId(null);
            }
        });
        List<StatisticsFacilityCurrentPerson> statisticsFacilityCurrentPersonList = new ArrayList<>();
        List<StatisticsFacilityCurrentPersonList> statisticsFacilityCurrentPersonLists = new ArrayList<>();
        facilityRailList.stream().collect(Collectors.groupingBy((v0) -> {
            return v0.getFacilityName();
        })).forEach((name, facilityRailListChild) -> {
            StatisticsFacilityCurrentPerson statisticsFacilityCurrentPerson = new StatisticsFacilityCurrentPerson();
            statisticsFacilityCurrentPerson.setPersonCount(0);
            statisticsFacilityCurrentPerson.setFacilityName(name);
            statisticsFacilityCurrentPerson.setFacilityId((Long) map.get(name));
            facilityRailListChild.removeIf(r -> {
                return StringUtils.isBlank(r.getRailScope());
            });
            if (!CollectionUtils.isEmpty(facilityRailListChild)) {
                List<StatisticsFacilityCurrentPersonList> personList = new ArrayList<>();
                (facilityRailListChild).stream().forEach(rail3 -> {
                    personList.addAll(this.positionCurrentService.getCurrentPersonListByRails(rail3.getLayerId(), rail3.getRailScope(), rail3.getDrawType()));
                });
                List<StatisticsFacilityCurrentPersonList> personIds = (List) personList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> {
                    return new TreeSet<>(Comparator.comparing((v0) -> {
                        return (v0).getPersonId();
                    }));
                }), (v1) -> {
                    return new ArrayList(v1);
                }));
                statisticsFacilityCurrentPerson.setPersonCount(Integer.valueOf(personIds.size()));
                personIds.stream().forEach(s2 -> {
                    s2.setFacilityName(name);
                    s2.setFacilityId((Long) map.get(name));
                    s2.setCreateTime(nowTime);
                });
                statisticsFacilityCurrentPersonLists.addAll(personIds);
            }
            statisticsFacilityCurrentPersonList.add(statisticsFacilityCurrentPerson);
        });
        this.statisticsFacilityCurrentPersonService.batchInsert(statisticsFacilityCurrentPersonList);
        this.statisticsFacilityPersonListService.batchInsert(statisticsFacilityCurrentPersonLists);
    }
    /* JADX WARN: Multi-variable type inference failed */
    public void generateFacilityRecord() throws NumberFormatException {
        if (!this.facilityPersonListIsInit) {
            ((LambdaQueryChainWrapper) this.facilityAccessRecordService.lambdaQuery().eq((v0) -> {
                return v0.getStatusId();
            }, ConfigValue.STATUS_ID_98)).list().forEach(facilityAccessRecord -> {
                Long facilityId = ((FacilityAccessRecord)facilityAccessRecord).getFacilityId();
                if (!this.facilityPersonList.containsKey(facilityId)) {
                    this.facilityPersonList.put(facilityId, new HashMap());
                }
                FacilityPersonVo facilityPersonVo = FacilityPersonVo.builder().recordId(((FacilityAccessRecord)facilityAccessRecord).getRecordId()).enterTime(((FacilityAccessRecord)facilityAccessRecord).getAcceptTime()).personId(((FacilityAccessRecord)facilityAccessRecord).getPersonId()).build();
                this.facilityPersonList.get(facilityId).put(facilityPersonVo.getPersonId(), facilityPersonVo);
            });
            this.facilityPersonListIsInit = true;
        }
        List<Facility> facilityList = this.facilityService.selectFacilityAndRailList();
        if (CollectionUtils.isEmpty(facilityList)) {
            return;
        }
        List<PositionCurrent> positionCurrentList = this.positionCurrentService.selectList();
        Map<String, String> config = RedisCacheUtils.getConfigRedisCache();
        long duration = Long.parseLong(config.getOrDefault(Constants.CONIFG_KEY_AREA_INOUT_DURATION_TIME, "10"));
        facilityList.stream().forEach(facility -> {
            Long facilityId = facility.getFacilityId();
            if (!this.facilityPersonList.containsKey(facilityId)) {
                this.facilityPersonList.put(facilityId, new HashMap());
            }
            Map<Long, FacilityPersonVo> personVoMap = this.facilityPersonList.get(facilityId);
            Set<Long> noLocationPersonSet = (Set) personVoMap.values().stream().filter(person -> {
                return person.getRecordId() != null;
            }).map((v0) -> {
                return (v0).getPersonId();
            }).collect(Collectors.toSet());
            positionCurrentList.stream().forEach(position -> {
                Long personId = position.getPersonId();
                noLocationPersonSet.remove(personId);
                if (inRailScope(position, facility)) {
                    if (!personVoMap.containsKey(personId)) {
                        personVoMap.put(personId, FacilityPersonVo.builder().personId(personId).enterTime(position.getAcceptTime()).build());
                        return;
                    }
                    FacilityPersonVo facilityPersonVo = (FacilityPersonVo) personVoMap.get(personId);
                    facilityPersonVo.setOutTime(null);
                    if (facilityPersonVo.getRecordId() == null && LocalDateTimeUtil.between(facilityPersonVo.getEnterTime(), position.getAcceptTime(), ChronoUnit.SECONDS) > duration) {
                        this.personService.getPersonById(personId);
                        FacilityAccessRecord facilityAccessRecord2 = new FacilityAccessRecord();
                        facilityAccessRecord2.setFacilityId(facilityId);
                        facilityAccessRecord2.setFacilityName(facility.getFacilityName());
                        facilityAccessRecord2.setAcceptTime(facilityPersonVo.getEnterTime());
                        facilityAccessRecord2.setCreateTime(LocalDateTime.now());
                        facilityAccessRecord2.setBeaconId(position.getBeaconId());
                        facilityAccessRecord2.setCardId(position.getCardId());
                        facilityAccessRecord2.setContractorId(position.getContractorId());
                        facilityAccessRecord2.setContractorName(position.getContractorName());
                        facilityAccessRecord2.setDeptId(position.getDeptId());
                        facilityAccessRecord2.setDeptName(position.getDeptName());
                        facilityAccessRecord2.setJobNumber(position.getJobNumber());
                        facilityAccessRecord2.setLongitude(position.getLongitude());
                        facilityAccessRecord2.setLatitude(position.getLatitude());
                        facilityAccessRecord2.setLayerId(position.getLayerId());
                        facilityAccessRecord2.setLayerHeight(position.getLayerHeight());
                        facilityAccessRecord2.setPersonId(position.getPersonId());
                        facilityAccessRecord2.setRealName(position.getRealName());
                        facilityAccessRecord2.setPersonType(position.getPersonType());
                        facilityAccessRecord2.setPersonTypeName(position.getPersonTypeName());
                        facilityAccessRecord2.setPostId(position.getPostId());
                        facilityAccessRecord2.setPostName(position.getPostName());
                        facilityAccessRecord2.setStaffType(position.getStaffType());
                        facilityAccessRecord2.setStaffTypeName(position.getPersonCategory());
                        facilityAccessRecord2.setContractorName(position.getContractorName());
                        facilityAccessRecord2.setStatusId(ConfigValue.STATUS_ID_98);
                        this.facilityAccessRecordService.save(facilityAccessRecord2);
                        facilityPersonVo.setRecordId(facilityAccessRecord2.getRecordId());
                        return;
                    }
                    return;
                }
                if (personVoMap.containsKey(personId)) {
                    FacilityPersonVo facilityPersonVo2 = (FacilityPersonVo) personVoMap.get(personId);
                    if (facilityPersonVo2.getRecordId() == null) {
                        personVoMap.remove(personId);
                        return;
                    }
                    if (facilityPersonVo2.getOutTime() == null) {
                        facilityPersonVo2.setOutTime(position.getAcceptTime());
                        return;
                    }
                    if (LocalDateTimeUtil.between(facilityPersonVo2.getOutTime(), position.getAcceptTime(), ChronoUnit.SECONDS) > duration) {
                        long stay = Duration.between(facilityPersonVo2.getEnterTime(), facilityPersonVo2.getOutTime()).toMillis() / 1000;
                        FacilityAccessRecord facilityAccessRecord3 = new FacilityAccessRecord();
                        facilityAccessRecord3.setRecordId(facilityPersonVo2.getRecordId());
                        facilityAccessRecord3.setLeaveTime(facilityPersonVo2.getOutTime());
                        facilityAccessRecord3.setStatusId(ConfigValue.STATUS_ID_99);
                        facilityAccessRecord3.setStaySecond(Long.valueOf(stay));
                        facilityAccessRecord3.setUpdateTime(LocalDateTime.now());
                        this.facilityAccessRecordService.updateById(facilityAccessRecord3);
                        personVoMap.remove(personId);
                    }
                }
            });
            noLocationPersonSet.forEach(personId -> {
                FacilityPersonVo facilityPersonVo = (FacilityPersonVo) personVoMap.get(personId);
                if (facilityPersonVo.getOutTime() == null) {
                    facilityPersonVo.setOutTime(LocalDateTime.now());
                    return;
                }
                if (LocalDateTimeUtil.between(facilityPersonVo.getOutTime(), LocalDateTime.now(), ChronoUnit.SECONDS) > duration) {
                    long stay = Duration.between(facilityPersonVo.getEnterTime(), facilityPersonVo.getOutTime()).toMillis() / 1000;
                    FacilityAccessRecord facilityAccessRecord2 = new FacilityAccessRecord();
                    facilityAccessRecord2.setRecordId(facilityPersonVo.getRecordId());
                    facilityAccessRecord2.setLeaveTime(facilityPersonVo.getOutTime());
                    facilityAccessRecord2.setStatusId(ConfigValue.STATUS_ID_99);
                    facilityAccessRecord2.setStaySecond(Long.valueOf(stay));
                    facilityAccessRecord2.setUpdateTime(LocalDateTime.now());
                    this.facilityAccessRecordService.updateById(facilityAccessRecord2);
                    personVoMap.remove(personId);
                }
            });
        });
    }
    private boolean inRailScope(PositionCurrent position, Facility facility) {
        AtomicBoolean r = new AtomicBoolean(false);
        Iterator<FacilityRail> it = facility.getFacilityRailList().iterator();
        while (true) {
            if (!it.hasNext()) {
                break;
            }
            FacilityRail rail = it.next();
            String railScope = rail.getRailScope();
            if (railScope.contains("POLYGON ((")) {
                rail.setRailScope(getNewRailScope(railScope));
            }
            if ("1".equals(facility.getStatisticType()) || rail.getLayerId().equals(position.getLayerId())) {
                if (CommonUtil.containsRailScope(position.getLongitude(), position.getLatitude(), rail.getRailScope(), rail.getDrawType())) {
                    r.set(true);
                    break;
                }
            }
        }
        return r.get();
    }
    private static String getNewRailScope(String railScope) {
        StringBuilder sb = new StringBuilder();
        sb.append("POLYGON((");
        String[] scopeArray = railScope.replaceAll("POLYGON \\(\\(", "").replaceAll("\\)\\)", "").split(",");
        sb.append(scopeArray[0] + ",");
        for (int i = 1; i < scopeArray.length; i++) {
            if (i == scopeArray.length - 1) {
                String s = scopeArray[i].trim();
                sb.append(s + "))");
            } else {
                String s2 = scopeArray[i].trim();
                sb.append(s2 + ",");
            }
        }
        return sb.toString();
    }
    private boolean inNeedLayId(FacilityRail facility) {
        AtomicBoolean r = new AtomicBoolean(false);
        if ("1".equals(facility.getStatisticType())) {
            r.set(true);
        }
        return r.get();
    }
    public void statisticDeptCurrent() {
        List<PositionCurrent> positionCurrentList = this.positionCurrentService.selectList();
        if (CollectionUtils.isEmpty(positionCurrentList)) {
            return;
        }
        LocalDate statisticsTime = LocalDate.now();
        List<StatisticsDeptOnlineLog> statisticsDeptOnlineLogList = this.personService.findDeptPersonTotal();
        Map<Long, Long> deptGroup = (Map) positionCurrentList.stream().filter(f -> {
            return Objects.nonNull(f.getDeptId());
        }).collect(Collectors.groupingBy((v0) -> {
            return v0.getDeptId();
        }, Collectors.counting()));
        statisticsDeptOnlineLogList.forEach(dept -> {
            Long count = (Long) deptGroup.getOrDefault(dept.getDeptId(), 0L);
            dept.setCount(Integer.valueOf(Integer.parseInt(count.toString())));
            dept.setStatisticsTime(statisticsTime);
        });
        List<StatisticsDeptOnlineLog> list = this.statisticsDeptOnlineLogService.selectListByStatisticsTime(statisticsTime);
        Map<Long, StatisticsDeptOnlineLog> historyMap = (Map) list.stream().collect(Collectors.toMap((v0) -> {
            return v0.getDeptId();
        }, Function.identity()));
        statisticsDeptOnlineLogList.forEach(t -> {
            t.setOnlineRate(BigDecimal.valueOf(t.getCount().intValue()).divide(BigDecimal.valueOf(t.getDeptTotal().intValue()), 4, 4).multiply(BigDecimal.valueOf(100L)).setScale(2));
            StatisticsDeptOnlineLog oldEntity = (StatisticsDeptOnlineLog) historyMap.get(t.getDeptId());
            if (Objects.nonNull(oldEntity)) {
                Integer oldCount = oldEntity.getCount();
                if (Objects.isNull(oldCount) || t.getCount().intValue() > oldCount.intValue()) {
                    t.setStatisticsId(oldEntity.getStatisticsId());
                    this.statisticsDeptOnlineLogService.updateById(t);
                    return;
                }
                return;
            }
            this.statisticsDeptOnlineLogService.insert(t);
        });
    }
    public void initDeptCurrent() {
    }
    /* JADX WARN: Multi-variable type inference failed */
    public void statisticsAlarmDispose() {
        BigDecimal disposeRate;
        BigDecimal disposeTimelyRate;
        LocalDateTime nowTime = LocalDateTime.now();
        LocalDate today = LocalDate.now();
        LocalDateTime beginTime = today.atTime(LocalTime.MIN);
        LocalDateTime endTime = today.atTime(LocalTime.MAX);
        List<Alarm> list = this.alarmService.list( (new LambdaQueryWrapper<Alarm>().ge((v0) -> {
            return v0.getAcceptTime();
        }, beginTime)).le((v0) -> {
            return v0.getAcceptTime();
        }, endTime));
        long alarmCount = list.size();
        long disposeAlarmCount = 0;
        long disposeAlarmTimeCount = 0;
        if (alarmCount > 0) {
            disposeAlarmCount = list.stream().filter(f -> {
                return "20".equals(f.getAlarmStatus());
            }).count();
            disposeAlarmTimeCount = list.stream().filter(f2 -> {
                return "20".equals(f2.getAlarmStatus()) && Objects.nonNull(f2.getDisposeTime()) && timeDifference(f2.getAcceptTime(), f2.getDisposeTime(), 30L);
            }).count();
            double l = disposeAlarmCount / alarmCount;
            disposeRate = BigDecimal.valueOf(l);
            if (disposeAlarmCount == 0) {
                disposeTimelyRate = BigDecimal.ZERO;
            } else {
                double h = disposeAlarmTimeCount / disposeAlarmCount;
                disposeTimelyRate = BigDecimal.valueOf(h);
            }
        } else {
            disposeRate = BigDecimal.valueOf(1L);
            disposeTimelyRate = BigDecimal.valueOf(1L);
        }
        StatisticsAlarmDispose entity = StatisticsAlarmDispose.builder().statisticsDate(today).alarmCount(Long.valueOf(alarmCount)).disposeAlarmCount(Long.valueOf(disposeAlarmCount)).disposeAlarmTimeCount(Long.valueOf(disposeAlarmTimeCount)).disposeRate(disposeRate.multiply(BigDecimal.valueOf(100L)).setScale(2, RoundingMode.HALF_UP)).disposeTimelyRate(disposeTimelyRate.multiply(BigDecimal.valueOf(100L)).setScale(2, RoundingMode.HALF_UP)).createTime(nowTime).build();
        StatisticsAlarmDispose oldEntity = this.statisticsAlarmDisposeMapper.selectOne((SFunction<StatisticsAlarmDispose, ?>) (v0) -> {
            return v0.getStatisticsDate();
        }, today);
        if (Objects.nonNull(oldEntity)) {
            entity.setStatisticsId(oldEntity.getStatisticsId());
            this.statisticsAlarmDisposeMapper.updateById(entity);
        } else {
            this.statisticsAlarmDisposeMapper.insert(entity);
        }
    }
    private static boolean timeDifference(LocalDateTime minTime, LocalDateTime maxTime, long minute2) {
        Duration duration = Duration.between(minTime, maxTime);
        long totalMinutes = duration.toMinutes();
        return totalMinutes <= minute2;
    }
    /* JADX WARN: Multi-variable type inference failed */
    public void statisticsBuildingAlarm() {
        LocalDateTime nowTime = LocalDateTime.now();
        LocalDate today = LocalDate.now();
        LocalDateTime beginTime = today.atTime(LocalTime.MIN);
        LocalDateTime endTime = today.atTime(LocalTime.MAX);
        List<Alarm> list = this.alarmService.list( ( (new LambdaQueryWrapper<Alarm>().isNotNull((v0) -> {
            return v0.getBuildingId();
        })).ge((v0) -> {
            return v0.getAcceptTime();
        }, beginTime)).le((v0) -> {
            return v0.getAcceptTime();
        }, endTime));
        Map<Long, List<Alarm>> buildingMap = (Map) list.stream().collect(Collectors.groupingBy((v0) -> {
            return v0.getBuildingId();
        }));
        buildingMap.forEach((buildingId, alarmList) -> {
            Map<String, Long> alarmCountMap = (Map) alarmList.stream().collect(Collectors.groupingBy((v0) -> {
                return v0.getAlarmType();
            }, Collectors.counting()));
            Alarm alarm = (Alarm) alarmList.stream().findFirst().get();
            alarmCountMap.forEach((alarmType, count) -> {
                StatisticsBuildingAlarm entity = StatisticsBuildingAlarm.builder().createTime(nowTime).statisticsDate(today).alarmType(alarmType).alarmTypeName(alarm.getAlarmTypeName()).alarmCount(count).buildingId(buildingId).buildingName(alarm.getBuildingName()).build();
                StatisticsBuildingAlarm oldEntity = this.statisticsBuildingAlarmMapper.selectOne((v0) -> {
                    return v0.getStatisticsDate();
                }, today, (v0) -> {
                    return v0.getBuildingId();
                }, buildingId, (v0) -> {
                    return v0.getAlarmType();
                }, alarmType);
                if (Objects.nonNull(oldEntity)) {
                    entity.setStatisticsId(oldEntity.getStatisticsId());
                    this.statisticsBuildingAlarmMapper.updateById(entity);
                } else {
                    this.statisticsBuildingAlarmMapper.insert(entity);
                }
            });
        });
    }
}
