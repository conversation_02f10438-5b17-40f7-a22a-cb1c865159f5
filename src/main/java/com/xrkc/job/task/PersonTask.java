package com.xrkc.job.task;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.domain.basic.JsonResult;
import com.xrkc.core.domain.person.Person;
import com.xrkc.core.domain.system.SystemApi;
import com.xrkc.core.domain.system.SystemDept;
import com.xrkc.core.utils.DateUtils;
import com.xrkc.core.utils.PersonUtils;
import com.xrkc.job.config.PersonSynchronizerConstants;
import com.xrkc.job.domain.SystemUser;
import com.xrkc.job.domain.SystemUserRole;
import com.xrkc.job.domain.UpdatePesonHK;
import com.xrkc.job.service.IGatePeopleService;
import com.xrkc.job.service.IPersonService;
import com.xrkc.job.service.ISystemDeptService;
import com.xrkc.job.service.ISystemUserRoleService;
import com.xrkc.job.service.ISystemUserService;
import com.xrkc.job.service.SystemApiService;
import com.xrkc.job.service.impl.HttpClientService;
import com.xrkc.job.util.MD5Utils;
import com.xrkc.job.util.PersonSynchronizerUtil;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.endpoint.SanitizableData;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
@Component("personTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/PersonTask.class */
public class PersonTask {
    @Autowired
    private ISystemDeptService systemDeptService;
    @Autowired
    private IPersonService personService;
    @Autowired
    private ISystemUserService systemUserService;
    @Autowired
    private ISystemUserRoleService systemUserRoleService;
    @Autowired
    private HttpClientService httpClientService;
    @Autowired
    private SystemApiService systemApiService;
    @Autowired
    IGatePeopleService gatePeopleService;
    private static final String org_api = "/artemis/api/resource/v1/org/orgList";
    private static final String person_api = "/artemis/api/resource/v2/person/advance/personList";
    private static final int pageSize = 500;
    private static final int batchSyncSize = 5;
    private static final Logger log = LoggerFactory.getLogger((Class<?>) PersonTask.class);
    private static Set<String> orgIndexCodeList = new HashSet();
    private static String orgIndexCode = "1";
    private static LocalDateTime lastUpdateDate = null;
    private static LocalDateTime lastCreateDate = null;
    private static final TrustManager[] trustAllCerts = {new X509TrustManager() { // from class: com.xrkc.job.task.PersonTask.1
        void AnonymousClass1() {
        }
        @Override // javax.net.ssl.X509TrustManager
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[0];
        }
        @Override // javax.net.ssl.X509TrustManager
        public void checkClientTrusted(X509Certificate[] chain, String authType) {
        }
        @Override // javax.net.ssl.X509TrustManager
        public void checkServerTrusted(X509Certificate[] chain, String authType) {
        }
    }};
    private static final HostnameVerifier DO_NOT_VERIFY = (hostname, session) -> {
        return true;
    };
    public void syncHikvisionOrg() {
        SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_HIKVISION);
        if (Objects.isNull(systemApi) || StringUtils.isAnyBlank(systemApi.getUserName(), systemApi.getPassword())) {
            return;
        }
        ArtemisConfig.host = systemApi.getHost();
        ArtemisConfig.appKey = systemApi.getUserName();
        ArtemisConfig.appSecret = systemApi.getPassword();
        Map<String, String> pathMap = new HashMap<>();
        pathMap.put("https://", org_api);
        boolean flag = true;
        int pageNo = 1;
        int count = 0;
        Map<String, Long> deptCodeMap = (Map) this.systemDeptService.selectList().stream().filter(systemDept -> {
            return "task".equals(systemDept.getCreateBy()) && StringUtils.isNotBlank(systemDept.getDeptCode());
        }).collect(Collectors.toMap((v0) -> {
            return v0.getDeptCode();
        }, (v0) -> {
            return v0.getDeptId();
        }));
        Set<String> syncDeptCodeSet = new HashSet<>();
        synchronized (this) {
            while (flag) {
                JSONObject queryParams = new JSONObject();
                queryParams.put("pageNo", Integer.valueOf(pageNo));
                queryParams.put("pageSize", 500);
                String result = ArtemisHttpUtil.doPostStringArtemis(pathMap, queryParams.toJSONString(new JSONWriter.Feature[0]), null, null, "application/json", null);
                log.info("同步Hikvision组织接口响应：{}", result);
                count += 500;
                try {
                    JSONObject resultJson = JSONObject.parseObject(result);
                    String code = resultJson.getString("code");
                    if ("0".equals(code)) {
                        JSONObject data = resultJson.getJSONObject("data");
                        int total = data.getInteger(JsonResult.TOTAL).intValue();
                        int amount = total - count;
                        JSONArray list = data.getJSONArray("list");
                        for (int i = 0; i < list.size(); i++) {
                            String orgIndexCode2 = list.getJSONObject(i).getString("orgIndexCode");
                            orgIndexCodeList.add(orgIndexCode2);
                            String deptName = list.getJSONObject(i).getString("orgName");
                            if (StringUtils.isBlank(deptName)) {
                                return;
                            }
                            Long parenId = 0L;
                            String parentDeptCode = list.getJSONObject(i).getString("parentOrgIndexCode");
                            if (deptCodeMap.containsKey(parentDeptCode)) {
                                parenId = deptCodeMap.get(parentDeptCode);
                            } else {
                                String parenName = list.getJSONObject(i).getString("parentOrgName");
                                if (StringUtils.isNotBlank(parenName)) {
                                    SystemDept parentDept = new SystemDept();
                                    parentDept.setParentId(0L);
                                    parentDept.setDeptCode(parentDeptCode);
                                    parentDept.setDeptName(parenName);
                                    parentDept.setCreateBy("task");
                                    this.systemDeptService.insertEntity(parentDept);
                                    deptCodeMap.put(parentDeptCode, parentDept.getDeptId());
                                    parenId = parentDept.getDeptId();
                                }
                            }
                            Long deptId = deptCodeMap.get(orgIndexCode2);
                            SystemDept systemDept2 = new SystemDept();
                            systemDept2.setDeptName(deptName);
                            systemDept2.setDeptCode(orgIndexCode2);
                            systemDept2.setParentId(parenId);
                            if (Objects.isNull(deptId)) {
                                systemDept2.setCreateBy("task");
                                this.systemDeptService.insertEntity(systemDept2);
                            } else {
                                systemDept2.setDeptId(deptId);
                                systemDept2.setUpdateBy("task");
                                this.systemDeptService.updateEntity(systemDept2);
                            }
                            syncDeptCodeSet.add(orgIndexCode2);
                            deptCodeMap.put(orgIndexCode2, systemDept2.getDeptId());
                        }
                        if (amount > 0) {
                            pageNo++;
                            flag = true;
                        } else {
                            flag = false;
                        }
                        log.info("【组织结果】{}，{}", Boolean.valueOf(flag), Integer.valueOf(amount));
                    } else {
                        log.error("【Hikvision同步组织失败】code:{},msg:{}", code, resultJson.getString("msg"));
                        return;
                    }
                } catch (Exception e) {
                    log.error("【Hikvision请求组织失败】，请检查通信是否正常");
                    return;
                }
            }
            deptCodeMap.forEach((deptCode, deptId2) -> {
                if (!syncDeptCodeSet.contains(deptCode)) {
                    this.systemDeptService.deleteEntity(deptId2);
                }
            });
        }
    }
    /* renamed from: com.xrkc.job.task.PersonTask$1 */
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/PersonTask$1.class */
    static class AnonymousClass1 implements X509TrustManager {
        AnonymousClass1() {
        }
        @Override // javax.net.ssl.X509TrustManager
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[0];
        }
        @Override // javax.net.ssl.X509TrustManager
        public void checkClientTrusted(X509Certificate[] chain, String authType) {
        }
        @Override // javax.net.ssl.X509TrustManager
        public void checkServerTrusted(X509Certificate[] chain, String authType) {
        }
    }
    private static SSLSocketFactory trustAllHosts(HttpsURLConnection connection) throws NoSuchAlgorithmException, KeyManagementException {
        SSLSocketFactory oldFactory = connection.getSSLSocketFactory();
        try {
            SSLContext sc = SSLContext.getInstance("TLS");
            sc.init(null, trustAllCerts, new SecureRandom());
            SSLSocketFactory newFactory = sc.getSocketFactory();
            connection.setSSLSocketFactory(newFactory);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return oldFactory;
    }
    private String getPersonPhoto(JSONObject personPhotoJson) {
        SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_HIKVISION_PIC_URI);
        if (Objects.isNull(systemApi) || StringUtils.isBlank(systemApi.getHost()) || StringUtils.isBlank(systemApi.getBasicTopic())) {
            log.info("未配置图片请求地址");
            return null;
        }
        String destUrl = systemApi.getHost() + personPhotoJson.getString("picUri");
        log.info("personPhotoJson:{},destUrl:{}", personPhotoJson, destUrl);
        String personPhotoIndexCode = personPhotoJson.getString("personPhotoIndexCode");
        String imgName = personPhotoIndexCode + ".png";
        String dir = systemApi.getBasicTopic() + "\\hikvisionPerson\\";
        String personPhoto = "/hikvisionPerson/" + imgName;
        String filePath = dir + imgName;
        try {
            HttpsURLConnection connection = (HttpsURLConnection) new URL(destUrl).openConnection();
            trustAllHosts(connection);
            connection.setHostnameVerifier(DO_NOT_VERIFY);
            connection.setDoInput(true);
            connection.connect();
            File imgFile = inputStreamToFile(connection.getInputStream(), dir, filePath);
            connection.disconnect();
            if (imgFile != null) {
                return personPhoto;
            }
            return null;
        } catch (Exception e) {
            log.error(e.getMessage(), (Throwable) e);
            return null;
        }
    }
    private String imgToBase64(String url) throws IOException {
        SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_HIKVISION_PIC_URI);
        if (Objects.isNull(systemApi) || StringUtils.isBlank(systemApi.getHost()) || StringUtils.isBlank(systemApi.getBasicTopic())) {
            log.info("未配置图片请求地址");
            return null;
        }
        if (StringUtils.isNotBlank(url)) {
            String filePath = systemApi.getBasicTopic() + url;
            File file = new File(filePath);
            if (file.exists()) {
                byte[] refereeFileOriginalBytes = FileUtils.readFileToByteArray(file);
                byte[] refereeFileBase64Bytes = Base64.encodeBase64(refereeFileOriginalBytes);
                String str = new String(refereeFileBase64Bytes, "UTF-8");
                return str;
            }
            return "";
        }
        return "";
    }
    public static File inputStreamToFile(InputStream ins, String dir, String filePath) throws IOException {
        File fileDir = new File(dir);
        if (!fileDir.exists()) {
            fileDir.mkdirs();
        }
        File file = new File(filePath);
        if (file.exists()) {
            return file;
        }
        OutputStream os = null;
        try {
            try {
                os = new FileOutputStream(file);
                byte[] buffer = new byte[8192];
                while (true) {
                    int bytesRead = ins.read(buffer, 0, 8192);
                    if (bytesRead == -1) {
                        break;
                    }
                    os.write(buffer, 0, bytesRead);
                }
                if (null != os) {
                    try {
                        os.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                if (null != ins) {
                    ins.close();
                }
                return file;
            } catch (Exception e2) {
                log.error(e2.getMessage(), (Throwable) e2);
                file.delete();
                if (null != os) {
                    try {
                        os.close();
                    } catch (Exception e3) {
                        e3.printStackTrace();
                        return null;
                    }
                }
                if (null != ins) {
                    ins.close();
                }
                return null;
            }
        } catch (Throwable th) {
            if (null != os) {
                try {
                    os.close();
                } catch (Exception e4) {
                    e4.printStackTrace();
                    throw th;
                }
            }
            if (null != ins) {
                ins.close();
            }
            throw th;
        }
    }
    public void syncHikvisionPerson() {
        SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_HIKVISION);
        if (Objects.isNull(systemApi) || StringUtils.isAnyBlank(systemApi.getUserName(), systemApi.getPassword())) {
            return;
        }
        ArtemisConfig.host = systemApi.getHost();
        ArtemisConfig.appKey = systemApi.getUserName();
        ArtemisConfig.appSecret = systemApi.getPassword();
        Map<String, String> pathMap = new HashMap<>();
        pathMap.put("https://", person_api);
        if (CollectionUtils.isEmpty(orgIndexCodeList)) {
            log.info("组织架构集合id为空，本次无法同步人员");
            return;
        }
        log.info("同步Hikvision人员中……");
        Map<String, Long> personIdNumberMap = (Map) this.personService.selectList().stream().filter(f -> {
            return StringUtils.isNotBlank(f.getIdNumber());
        }).collect(Collectors.toMap((v0) -> {
            return v0.getIdNumber();
        }, (v0) -> {
            return v0.getPersonId();
        }));
        Set<String> hikvisionPersonIdNumber = new HashSet<>();
        Map<String, Long> deptCodeMap = (Map) this.systemDeptService.selectList().stream().filter(f2 -> {
            return StringUtils.isNotBlank(f2.getDeptCode());
        }).collect(Collectors.toMap((v0) -> {
            return v0.getDeptCode();
        }, (v0) -> {
            return v0.getDeptId();
        }));
        synchronized (this) {
            List<String> orgList = (List) orgIndexCodeList.stream().collect(Collectors.toList());
            AtomicInteger currentBatch = new AtomicInteger();
            int totalBatch = (int) Math.ceil(orgList.size() / 5.0d);
            Map<String, Integer> map = new HashMap<>();
            map.put("allTotal", 0);
            map.put("allNoIdNumberCount", 0);
            while (!orgList.isEmpty()) {
                currentBatch.getAndIncrement();
                int totalSize = orgList.size();
                List<String> batchList = orgList.subList(0, Math.min(5, totalSize));
                boolean syncResult = syncHikvisionPersonInBatches(pathMap, personIdNumberMap, hikvisionPersonIdNumber, deptCodeMap, batchList, currentBatch.get(), totalBatch, map);
                if (!syncResult) {
                    return;
                } else {
                    orgList.subList(0, Math.min(5, totalSize)).clear();
                }
            }
            Integer allTotal = map.get("allTotal");
            Integer allNoIdNumberCount = map.get("allNoIdNumberCount");
            log.info("【Hikvision同步人员】,同步完成,总条数:{},allNoIdNumberCount:{}", allTotal, allNoIdNumberCount);
            if (allTotal.intValue() >= 0 && allTotal.intValue() - allNoIdNumberCount.intValue() == hikvisionPersonIdNumber.size()) {
                log.info("【Hikvision同步人员】,开始删除没有的人员");
                hikvisionPersonIdNumber.forEach(idNumber -> {
                    personIdNumberMap.remove(idNumber);
                });
                personIdNumberMap.values().forEach(personId -> {
                    Person person = this.personService.getPersonById(personId);
                    if (person != null && person.getCardId() != null) {
                        this.personService.unbindingCard(personId, person.getPersonType(), person.getCardId());
                    }
                    this.personService.deleteByPersonId(personId);
                });
            }
        }
    }
    public boolean syncHikvisionPersonInBatches(Map<String, String> pathMap, Map<String, Long> personIdNumberMap, Set<String> hikvisionPersonIdNumber, Map<String, Long> deptCodeMap, List<String> batchList, int batch, int totalBatch, Map<String, Integer> map) {
        Integer allTotal;
        Integer allNoIdNumberCount;
        boolean z;
        boolean flag = true;
        int pageNo = 1;
        int count = 0;
        int total = 0;
        int noIdNumberCount = 0;
        while (flag) {
            JSONObject queryParams = new JSONObject();
            queryParams.put("pageNo", Integer.valueOf(pageNo));
            queryParams.put("pageSize", 500);
            String orgIndexCodes = (String) batchList.stream().collect(Collectors.joining(","));
            queryParams.put("orgIndexCodes", orgIndexCodes);
            String result = ArtemisHttpUtil.doPostStringArtemis(pathMap, queryParams.toJSONString(new JSONWriter.Feature[0]), null, null, "application/json", null);
            log.info("同步Hikvision人员接口响应：{}", result);
            count += 500;
            try {
                JSONObject resultJson = JSONObject.parseObject(result);
                String code = resultJson.getString("code");
                if ("0".equals(code)) {
                    JSONObject data = resultJson.getJSONObject("data");
                    total = data.getInteger(JsonResult.TOTAL).intValue();
                    int amount = total - count;
                    JSONArray list = data.getJSONArray("list");
                    for (int i = 0; i < list.size(); i++) {
                        String certificateNo = list.getJSONObject(i).getString("certificateNo");
                        if (StringUtils.isNotBlank(certificateNo)) {
                            hikvisionPersonIdNumber.add(certificateNo);
                            String personName = list.getJSONObject(i).getString("personName");
                            int gender = list.getJSONObject(i).getInteger("gender").intValue();
                            String phoneNo = list.getJSONObject(i).getString("phoneNo");
                            String jobNo = list.getJSONObject(i).getString("jobNo");
                            String orgIndexCode2 = list.getJSONObject(i).getString("orgIndexCode");
                            Long deptId = deptCodeMap.get(orgIndexCode2);
                            Person person = new Person();
                            if (StringUtils.isNotBlank(phoneNo)) {
                                String phoneNo2 = phoneNo.trim();
                                person.setPhone(phoneNo2.length() > 11 ? null : phoneNo2);
                            } else {
                                person.setPhone(null);
                            }
                            JSONArray personPhotoArray = list.getJSONObject(i).getJSONArray(PersonUtils.personPhotoKeyPrefix);
                            if (Objects.nonNull(personPhotoArray) && personPhotoArray.size() > 0) {
                                for (int j = 0; j < personPhotoArray.size(); j++) {
                                    try {
                                        Person oldPerson = null;
                                        if (personIdNumberMap.get(certificateNo) != null) {
                                            oldPerson = this.personService.getPersonById(personIdNumberMap.get(certificateNo));
                                        }
                                        if (oldPerson == null || !StrUtil.startWith(oldPerson.getPersonPhoto(), "/xrkc")) {
                                            String p = getPersonPhoto(personPhotoArray.getJSONObject(j));
                                            if (StringUtils.isNotBlank(p)) {
                                                person.setPersonPhoto(p);
                                            }
                                            if (oldPerson == null || !StrUtil.equals(oldPerson.getPersonPhoto(), p)) {
                                                String imgStr = imgToBase64(p);
                                                person.setPhotoSign(DigestUtil.md5Hex(imgStr));
                                            }
                                        }
                                    } catch (Exception e) {
                                        log.error(e.getMessage(), (Throwable) e);
                                        log.error("请求图片失败：{}", personPhotoArray.getJSONObject(j).toJSONString(new JSONWriter.Feature[0]));
                                    }
                                }
                            } else {
                                person.setPersonPhoto(null);
                            }
                            person.setRealName(personName.length() > 10 ? personName.substring(0, 10) : personName);
                            person.setSex(Objects.nonNull(Integer.valueOf(gender)) ? gender == 1 ? "男" : gender == 2 ? "女" : "" : "");
                            person.setJobNumber(jobNo);
                            person.setIdType("居民身份证");
                            person.setIdNumber(certificateNo);
                            person.setDeptId(deptId);
                            person.setPersonSource("海康威视");
                            if (!CollectionUtils.isEmpty(personIdNumberMap) && Objects.nonNull(personIdNumberMap.get(certificateNo))) {
                                person.setPersonId(personIdNumberMap.get(certificateNo));
                                person.setUpdateBy("task");
                                this.personService.updateEntity(person);
                                log.info("更新部门:{}，deptId：{}", deptId, person.getRealName());
                                personIdNumberMap.put(certificateNo, person.getPersonId());
                            } else {
                                person.setPersonType("staff");
                                person.setStaffType("staff");
                                person.setCreateBy("task");
                                person.setJobStatus("Y");
                                Long personId = this.personService.insertEntity(person);
                                personIdNumberMap.put(certificateNo, personId);
                                log.info("新增部门:{}，deptId：{}", deptId, person.getRealName());
                            }
                        } else {
                            noIdNumberCount++;
                        }
                    }
                    String info = String.format("当前机构列表:%s，本次请求共：%d 条，当前执行第:%d 页，批次：%d/%d", orgIndexCodes, Integer.valueOf(total), Integer.valueOf(pageNo), Integer.valueOf(batch), Integer.valueOf(totalBatch));
                    log.info("【Hikvision同步人员】,{}", info);
                    if (amount > 0) {
                        log.info("分页拉取 {},{}", Integer.valueOf(amount), Boolean.valueOf(amount > 0));
                        pageNo++;
                        z = true;
                    } else {
                        z = false;
                    }
                    flag = z;
                    log.info("【Hikvision同步人员】,{}", info);
                } else {
                    log.error("【Hikvision同步人员失败】code:{},msg:{}", code, resultJson.getString("msg"));
                    return false;
                }
            } catch (Exception e2) {
                log.error(e2.getMessage(), (Throwable) e2);
                return false;
            }
           // log.error(e2.getMessage(), (Throwable) e2);
            return false;
        }
        Integer allTotal2 = map.get("allTotal");
        if (Objects.isNull(allTotal2)) {
            allTotal = Integer.valueOf(total);
        } else {
            allTotal = Integer.valueOf(allTotal2.intValue() + total);
        }
        Integer allNoIdNumberCount2 = map.get("allNoIdNumberCount");
        if (Objects.isNull(allNoIdNumberCount2)) {
            allNoIdNumberCount = Integer.valueOf(noIdNumberCount);
        } else {
            allNoIdNumberCount = Integer.valueOf(allNoIdNumberCount2.intValue() + noIdNumberCount);
        }
        map.put("allTotal", allTotal);
        map.put("allNoIdNumberCount", allNoIdNumberCount);
        return true;
    }
    public void syncHbshUser() throws NoSuchAlgorithmException {
        SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_HUABEISHIHUA);
        if (Objects.isNull(systemApi) || StringUtils.isAnyBlank(systemApi.getUserName(), systemApi.getPassword())) {
            return;
        }
        String sign = MD5Utils.string2MD5(systemApi.getUserName() + systemApi.getPassword());
        String url = systemApi.getHost() + "?Appkey=" + systemApi.getUserName() + "&Sign=" + sign + "&IsUpdate=false";
        JSONObject jsonObject = sendLinkGet(url, null);
        if (Objects.isNull(jsonObject)) {
            return;
        }
        boolean success = jsonObject.getBoolean("success").booleanValue();
        if (!success) {
            log.error("{}同步失败,errCode:{}", ConfigValue.API_KEY_HUABEISHIHUA, jsonObject.getString("errCode"));
            return;
        }
        JSONObject dataJson = jsonObject.getJSONObject("data");
        if (dataJson.getIntValue(JsonResult.TOTAL) > 0) {
            JSONArray data = dataJson.getJSONArray("data");
            ArrayList<SystemUser> arrayList = new ArrayList();
            LocalDateTime now = LocalDateTime.now();
            for (int i = 0; i < data.size(); i++) {
                SystemUser t = new SystemUser();
                t.setUserType("10");
                t.setUserEnable("Y");
                t.setPassword(SanitizableData.SANITIZED_VALUE);
                t.setUserName(data.getJSONObject(i).getString("UserName"));
                t.setCreateBy("task");
                t.setCreateTime(now);
                arrayList.add(t);
            }
            if (!CollectionUtils.isEmpty(arrayList)) {
                List<String> username = this.systemUserService.findAllUserName();
                arrayList.removeIf(r -> {
                    return username.contains(r.getUserName());
                });
                if (!CollectionUtils.isEmpty(arrayList) && this.systemUserService.saveBatch(arrayList)) {
                    ArrayList arrayList2 = new ArrayList();
                    arrayList.stream().forEach(user -> {
                        SystemUserRole ur = new SystemUserRole();
                        ur.setUserId(user.getUserId());
                        ur.setRoleId(99L);
                        arrayList2.add(ur);
                    });
                    this.systemUserRoleService.saveBatch(arrayList2);
                }
            }
        }
    }
    public void syncTaihuaUser() throws NoSuchAlgorithmException {
        SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.API_KEY_TAIHUA);
        if (Objects.isNull(systemApi) || StringUtils.isAnyBlank(systemApi.getUserName(), systemApi.getPassword())) {
            return;
        }
        long timestamp = System.currentTimeMillis() / 1000;
        String sign = MD5Utils.string2MD5(systemApi.getUserName() + timestamp + systemApi.getPassword()).toLowerCase();
        String url = systemApi.getHost() + "?appkey=" + systemApi.getUserName() + "&timestamp=" + timestamp + "&sign=" + sign + "&is_update=0";
        JSONObject jsonObject = sendLinkGet(url, null);
        if (Objects.isNull(jsonObject)) {
            return;
        }
        int code = jsonObject.getIntValue("code");
        if (code != 0) {
            log.error("{}同步失败,errCode:{}", ConfigValue.API_KEY_HUABEISHIHUA, jsonObject.getString("message"));
            return;
        }
        JSONArray data = jsonObject.getJSONArray("data");
        ArrayList<SystemUser> arrayList = new ArrayList();
        LocalDateTime now = LocalDateTime.now();
        for (int i = 0; i < data.size(); i++) {
            SystemUser t = new SystemUser();
            t.setUserType("10");
            t.setUserEnable("Y");
            t.setPassword(SanitizableData.SANITIZED_VALUE);
            t.setUserName(data.getJSONObject(i).getString("no"));
            t.setCreateBy("task");
            t.setCreateTime(now);
            arrayList.add(t);
        }
        if (!CollectionUtils.isEmpty(arrayList)) {
            List<String> username = this.systemUserService.findAllUserName();
            arrayList.removeIf(r -> {
                return username.contains(r.getUserName());
            });
            if (!CollectionUtils.isEmpty(arrayList) && this.systemUserService.saveBatch(arrayList)) {
                ArrayList arrayList2 = new ArrayList();
                arrayList.stream().forEach(user -> {
                    SystemUserRole ur = new SystemUserRole();
                    ur.setUserId(user.getUserId());
                    ur.setRoleId(99L);
                    arrayList2.add(ur);
                });
                this.systemUserRoleService.saveBatch(arrayList2);
            }
        }
    }
    private JSONObject sendLinkGet(String url, Map<String, String> headerParam) {
        JSONObject json;
        try {
            json = JSONObject.parseObject(this.httpClientService.doGetIgnoreSSL(url, headerParam, null));
        } catch (Exception e) {
            try {
                String result = this.httpClientService.doGetIgnoreSSL(url, headerParam, null);
                log.info("请求结果：{}", result);
                json = JSONObject.parseObject(result);
            } catch (Exception e2) {
                log.info("2次请求失败,url:{}，msg:{}", url, e2.getMessage());
                return null;
            }
        }
        return json;
    }
    @Transactional
    public void syncHikvisionWhiteList() throws ExecutionException, InterruptedException {
        long l1 = System.currentTimeMillis();
        SystemApi historySystemApi = this.systemApiService.getByApiKey(ConfigValue.HIKVISION_SYNC_PERSON_HISTORY);
        if (Objects.isNull(historySystemApi)) {
            log.info("请配置(同步白名单到海康)最新的同步时间");
            return;
        }
        checkHistory(historySystemApi);
        Map<String, Object> map = new HashMap<>();
        map.put("jobStatus", "Y");
        if (lastCreateDate != null && StringUtils.isNotEmpty(orgIndexCode)) {
            map.put("lastCreateDate", lastCreateDate);
            if (lastUpdateDate != null) {
                map.put("lastUpdateDate", lastUpdateDate);
            }
        }
        boolean isUpdate = true;
        List<Person> list = this.personService.selectPersonList(map);
        if (!CollectionUtils.isEmpty(list)) {
            SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.HIKVISION_SYNC_PERSON);
            if (Objects.isNull(systemApi)) {
                log.info("请配置海康威视SystemApi");
                return;
            }
            if (StringUtils.isAnyBlank(systemApi.getUserName(), systemApi.getPassword())) {
                return;
            }
            if (StringUtils.isEmpty(historySystemApi.getPassword())) {
                log.info("请SystemApi的设备号");
                return;
            }
            String[] split = historySystemApi.getPassword().split(",");
            PersonSynchronizerConstants.acsDevIndexCode = split[0];
            assignmentArtemisConfig(systemApi);
            if (StringUtils.isEmpty(orgIndexCode)) {
                JSONObject obj = PersonSynchronizerUtil.orgAdd();
                if (PersonSynchronizerUtil.successCode.equals(JSONObject.toJSONString(obj.get("code"), new JSONWriter.Feature[0]))) {
                    String orgCode = JSONObject.toJSONString(obj.get("orgIndexCode"), new JSONWriter.Feature[0]);
                    orgIndexCode = orgCode;
                } else {
                    log.info("机构新增失败");
                    return;
                }
            }
            List<List<Person>> perList = PersonSynchronizerUtil.getListInBatches(list);
            List<Person> updatePersionList = new ArrayList<>();
            Iterator<List<Person>> it = perList.iterator();
            while (true) {
                if (!it.hasNext()) {
                    break;
                }
                List<Person> s = it.next();
                UpdatePesonHK updatePesonHK = PersonSynchronizerUtil.addPersonToOrg(s, orgIndexCode);
                if (!CollectionUtils.isEmpty(updatePesonHK.getUpdatePersionList())) {
                    updatePersionList.addAll(updatePesonHK.getUpdatePersionList());
                }
                if (Objects.nonNull(updatePesonHK.getUpdateTime()) && !updatePesonHK.getUpdateTime().booleanValue()) {
                    isUpdate = false;
                    break;
                }
            }
            if (!CollectionUtils.isEmpty(updatePersionList)) {
                this.personService.updateHikvisionPerson(updatePersionList);
            }
            if (isUpdate) {
                lastCreateDate = list.get(0).getLastCreateDate();
                lastUpdateDate = list.get(0).getLastUpdateDate();
                updateHistory(historySystemApi);
                log.info("同步时间已更新");
            } else {
                log.info("删除权限或同步权限失败,下次重新按当前时间同步");
            }
        } else {
            log.info("暂无人员信息向海康同步");
        }
        map.put("jobStatus", "N");
        List<Person> separatingEmployeeList = this.personService.selectPersonList(map);
        if (!CollectionUtils.isEmpty(separatingEmployeeList) && Objects.nonNull(lastCreateDate)) {
            PersonSynchronizerUtil.separatingEmployeeList(separatingEmployeeList);
        } else {
            log.info("暂无离职人员信息向海康同步");
        }
        long l2 = System.currentTimeMillis();
        log.info("同步人员用时:{}", Long.valueOf(l2 - l1));
    }
    public void checkEquipmentNumber() {
        SystemApi historySystemApi = this.systemApiService.getByApiKey(ConfigValue.HIKVISION_SYNC_PERSON_HISTORY);
        SystemApi updateSystemApi = new SystemApi();
        if (Objects.isNull(historySystemApi)) {
            log.info("请配置(同步白名单到海康)最新的同步时间");
            return;
        }
        if (StringUtils.isNotEmpty(historySystemApi.getPassword())) {
            if (historySystemApi.getPassword().equals(historySystemApi.getBasicTopic())) {
                log.info("设备正常");
            } else {
                log.info("设备已修改,正在执行同步设备");
                updateSystemApi.setPassword(historySystemApi.getBasicTopic());
                updateSystemApi.setApiId(historySystemApi.getApiId());
                updateEquipmentNumber(updateSystemApi);
            }
        }
        if (StringUtils.isEmpty(ArtemisConfig.host)) {
            SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.HIKVISION_SYNC_PERSON);
            if (Objects.isNull(systemApi)) {
                log.info("请配置海康威视SystemApi");
                return;
            } else {
                if (StringUtils.isAnyBlank(systemApi.getUserName(), systemApi.getPassword())) {
                    return;
                }
                if (StringUtils.isEmpty(historySystemApi.getPassword())) {
                    log.info("请SystemApi的设备号");
                    return;
                }
                assignmentArtemisConfig(systemApi);
            }
        }
        PersonSynchronizerUtil.authReapplication();
    }
    public void replacementPersonDeparture() {
        log.info("开始执行重置二道门人员离开");
        LocalDateTime initializationTime = LocalDateTime.now();
        this.gatePeopleService.replacementPersonDeparture(initializationTime);
    }
    @Transactional
    public void getHistoricalAccessRecord() {
        LocalDateTime currentTime = LocalDateTime.now();
        SystemApi systemApi = this.systemApiService.getByApiKey(ConfigValue.HIKVISION_SYNC_PERSON);
        if (Objects.isNull(systemApi)) {
            log.info("请配置海康威视SystemApi");
            return;
        }
        assignmentArtemisConfig(systemApi);
        log.info("开始执行同步二道门历史记录,当前执行时间是:{}", DateUtils.toStringTime(currentTime));
        SystemApi historySystemApi = this.systemApiService.getByApiKey(ConfigValue.HISTORICAL_ACCESS_RECORD);
        if (Objects.isNull(historySystemApi)) {
            log.info("请配置同步二道门历史记录SystemApi");
            return;
        }
        SystemApi updateSys = new SystemApi();
        updateSys.setApiId(historySystemApi.getApiId());
        updateSys.setUpdateTime(currentTime);
        if (this.gatePeopleService.historicalAccessRecord(historySystemApi, currentTime).booleanValue()) {
            this.systemApiService.updateTimeByApiKey(currentTime, historySystemApi.getApiId());
        }
    }
    public void assignmentArtemisConfig(SystemApi systemApi) {
        ArtemisConfig.host = systemApi.getHost();
        ArtemisConfig.appKey = systemApi.getUserName();
        ArtemisConfig.appSecret = systemApi.getPassword();
    }
    public void checkHistory(SystemApi historySystemApi) {
        LocalDateTime historyLastCreateDate = historySystemApi.getCreateTime();
        LocalDateTime historyLastUpdateDate = historySystemApi.getUpdateTime();
        String historyOrgIndexCode = historySystemApi.getUserName();
        if (Objects.isNull(lastCreateDate) && Objects.isNull(lastUpdateDate) && StringUtils.isNotEmpty(historyOrgIndexCode) && Objects.nonNull(historyLastCreateDate)) {
            log.info("服务已重启,正在初始化之前的更新时间");
            lastCreateDate = historyLastCreateDate;
            lastUpdateDate = historyLastUpdateDate;
            orgIndexCode = historyOrgIndexCode;
        }
    }
    public void updateHistory(SystemApi historySystemApi) {
        if ((Objects.nonNull(lastCreateDate) && !lastCreateDate.equals(historySystemApi.getCreateTime())) || (Objects.nonNull(lastUpdateDate) && !lastUpdateDate.equals(historySystemApi.getUpdateTime()))) {
            this.systemApiService.updateByApiKey(lastCreateDate, lastUpdateDate, orgIndexCode, historySystemApi.getApiId());
            log.info("记录最新的创建时间:{},最新的修改时间:{}", lastCreateDate, lastUpdateDate);
        }
    }
    public void updateEquipmentNumber(SystemApi historySystemApi) {
        this.systemApiService.updateEquipmentNumber(historySystemApi.getPassword(), historySystemApi.getApiId());
    }
}
