package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
@TableName(value = "statistics_alarm_flow", autoResultMap = true)
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/StatisticsAlarmAreaFlow.class */
public class StatisticsAlarmAreaFlow implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId
    private Long statisticsId;
    private LocalDate statisticsDate;
    private Long areaId;
    private String areaName;
    private Integer alarmCount;
    private LocalDateTime createTime;
    public void setStatisticsId(Long statisticsId) {
        this.statisticsId = statisticsId;
    }
    public void setStatisticsDate(LocalDate statisticsDate) {
        this.statisticsDate = statisticsDate;
    }
    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }
    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }
    public void setAlarmCount(Integer alarmCount) {
        this.alarmCount = alarmCount;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof StatisticsAlarmAreaFlow)) {
            return false;
        }
        StatisticsAlarmAreaFlow other = (StatisticsAlarmAreaFlow) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$statisticsId = getStatisticsId();
        Object other$statisticsId = other.getStatisticsId();
        if (this$statisticsId == null) {
            if (other$statisticsId != null) {
                return false;
            }
        } else if (!this$statisticsId.equals(other$statisticsId)) {
            return false;
        }
        Object this$areaId = getAreaId();
        Object other$areaId = other.getAreaId();
        if (this$areaId == null) {
            if (other$areaId != null) {
                return false;
            }
        } else if (!this$areaId.equals(other$areaId)) {
            return false;
        }
        Object this$alarmCount = getAlarmCount();
        Object other$alarmCount = other.getAlarmCount();
        if (this$alarmCount == null) {
            if (other$alarmCount != null) {
                return false;
            }
        } else if (!this$alarmCount.equals(other$alarmCount)) {
            return false;
        }
        Object this$statisticsDate = getStatisticsDate();
        Object other$statisticsDate = other.getStatisticsDate();
        if (this$statisticsDate == null) {
            if (other$statisticsDate != null) {
                return false;
            }
        } else if (!this$statisticsDate.equals(other$statisticsDate)) {
            return false;
        }
        Object this$areaName = getAreaName();
        Object other$areaName = other.getAreaName();
        if (this$areaName == null) {
            if (other$areaName != null) {
                return false;
            }
        } else if (!this$areaName.equals(other$areaName)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        return this$createTime == null ? other$createTime == null : this$createTime.equals(other$createTime);
    }
    protected boolean canEqual(Object other) {
        return other instanceof StatisticsAlarmAreaFlow;
    }
    public int hashCode() {
        Object $statisticsId = getStatisticsId();
        int result = (1 * 59) + ($statisticsId == null ? 43 : $statisticsId.hashCode());
        Object $areaId = getAreaId();
        int result2 = (result * 59) + ($areaId == null ? 43 : $areaId.hashCode());
        Object $alarmCount = getAlarmCount();
        int result3 = (result2 * 59) + ($alarmCount == null ? 43 : $alarmCount.hashCode());
        Object $statisticsDate = getStatisticsDate();
        int result4 = (result3 * 59) + ($statisticsDate == null ? 43 : $statisticsDate.hashCode());
        Object $areaName = getAreaName();
        int result5 = (result4 * 59) + ($areaName == null ? 43 : $areaName.hashCode());
        Object $createTime = getCreateTime();
        return (result5 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
    }
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/StatisticsAlarmAreaFlow$StatisticsAlarmAreaFlowBuilder.class */
    public static class StatisticsAlarmAreaFlowBuilder {
        private Long statisticsId;
        private LocalDate statisticsDate;
        private Long areaId;
        private String areaName;
        private Integer alarmCount;
        private LocalDateTime createTime;
        StatisticsAlarmAreaFlowBuilder() {
        }
        public StatisticsAlarmAreaFlowBuilder statisticsId(Long statisticsId) {
            this.statisticsId = statisticsId;
            return this;
        }
        public StatisticsAlarmAreaFlowBuilder statisticsDate(LocalDate statisticsDate) {
            this.statisticsDate = statisticsDate;
            return this;
        }
        public StatisticsAlarmAreaFlowBuilder areaId(Long areaId) {
            this.areaId = areaId;
            return this;
        }
        public StatisticsAlarmAreaFlowBuilder areaName(String areaName) {
            this.areaName = areaName;
            return this;
        }
        public StatisticsAlarmAreaFlowBuilder alarmCount(Integer alarmCount) {
            this.alarmCount = alarmCount;
            return this;
        }
        public StatisticsAlarmAreaFlowBuilder createTime(LocalDateTime createTime) {
            this.createTime = createTime;
            return this;
        }
        public StatisticsAlarmAreaFlow build() {
            return new StatisticsAlarmAreaFlow(this.statisticsId, this.statisticsDate, this.areaId, this.areaName, this.alarmCount, this.createTime);
        }
        public String toString() {
            return "StatisticsAlarmAreaFlow.StatisticsAlarmAreaFlowBuilder(statisticsId=" + this.statisticsId + ", statisticsDate=" + this.statisticsDate + ", areaId=" + this.areaId + ", areaName=" + this.areaName + ", alarmCount=" + this.alarmCount + ", createTime=" + this.createTime + StringPool.RIGHT_BRACKET;
        }
    }
    public String toString() {
        return "StatisticsAlarmAreaFlow(super=" + super.toString() + ", statisticsId=" + getStatisticsId() + ", statisticsDate=" + getStatisticsDate() + ", areaId=" + getAreaId() + ", areaName=" + getAreaName() + ", alarmCount=" + getAlarmCount() + ", createTime=" + getCreateTime() + StringPool.RIGHT_BRACKET;
    }
    public static StatisticsAlarmAreaFlowBuilder builder() {
        return new StatisticsAlarmAreaFlowBuilder();
    }
    public StatisticsAlarmAreaFlow(Long statisticsId, LocalDate statisticsDate, Long areaId, String areaName, Integer alarmCount, LocalDateTime createTime) {
        this.statisticsId = statisticsId;
        this.statisticsDate = statisticsDate;
        this.areaId = areaId;
        this.areaName = areaName;
        this.alarmCount = alarmCount;
        this.createTime = createTime;
    }
    public StatisticsAlarmAreaFlow() {
    }
    public Long getStatisticsId() {
        return this.statisticsId;
    }
    public LocalDate getStatisticsDate() {
        return this.statisticsDate;
    }
    public Long getAreaId() {
        return this.areaId;
    }
    public String getAreaName() {
        return this.areaName;
    }
    public Integer getAlarmCount() {
        return this.alarmCount;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
}
