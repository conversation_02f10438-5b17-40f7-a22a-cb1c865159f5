package com.xrkc.job.domain;
import java.io.Serializable;
import java.time.LocalDateTime;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/SystemLicense.class */
public class SystemLicense implements Serializable {
    private static final long serialVersionUID = 1;
    private Long licenseId;
    private String licenseKey;
    private String licenseValue;
    private LocalDateTime expireTime;
    public String getLicenseValue() {
        return this.licenseValue;
    }
    public void setLicenseValue(String licenseValue) {
        this.licenseValue = licenseValue;
    }
    public LocalDateTime getExpireTime() {
        return this.expireTime;
    }
    public void setExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
    }
    public Long getLicenseId() {
        return this.licenseId;
    }
    public void setLicenseId(Long licenseId) {
        this.licenseId = licenseId;
    }
    public String getLicenseKey() {
        return this.licenseKey;
    }
    public void setLicenseKey(String licenseKey) {
        this.licenseKey = licenseKey;
    }
}
