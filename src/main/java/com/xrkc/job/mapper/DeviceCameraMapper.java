package com.xrkc.job.mapper;
import com.xrkc.core.domain.device.DeviceCamera;
import java.util.List;
import org.apache.ibatis.annotations.Select;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/DeviceCameraMapper.class */
public interface DeviceCameraMapper {
    @Select({"select rstp,longitude,latitude from device_camera where layer_id=#{layerId};"})
    List<DeviceCamera> selectAll(String str);
}
