package com.xrkc.job.mapper;
import com.xrkc.job.domain.Facility;
import com.xrkc.job.domain.FacilityRail;
import com.xrkc.job.domain.FacilityRailVO;
import java.util.List;
import org.apache.ibatis.annotations.Param;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/FacilityMapper.class */
public interface FacilityMapper {
    List<FacilityRailVO> selectFacilityRailVOList();
    List<FacilityRail> selectFacilityRailList();
    List<Facility> selectFacilityList();
    List<Facility> selectFacilityAndRailList();
    List<FacilityRailVO> getRailScopes(@Param("facilityId") Long l);
}
