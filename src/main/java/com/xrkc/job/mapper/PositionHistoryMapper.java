package com.xrkc.job.mapper;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/PositionHistoryMapper.class */
public interface PositionHistoryMapper {
    int createTable(String str);
    int batchBakHistory(Map<String, Object> map);
    int deleteTable(String str);
    int existTableDm(String str);
    int existTableUxdb(String str);
    int existTableMysql(String str);
    List<String> selectDeleteHistoryTableByDM(LocalDateTime localDateTime);
    List<String> selectDeleteHistoryTableByMysql(LocalDateTime localDateTime);
}
