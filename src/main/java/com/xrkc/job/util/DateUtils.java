package com.xrkc.job.util;
import cn.hutool.core.date.DatePattern;
import com.alibaba.nacos.common.utils.DateFormatUtils;
import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Date;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/DateUtils.class */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {
    public static String YYYY = "yyyy";
    public static String YYYY_MM = "yyyy-MM";
    public static String YYYY_MM_DD = "yyyy-MM-dd";
    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    private static String[] parsePatterns = {"yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", DatePattern.NORM_DATETIME_MINUTE_PATTERN, "yyyy-MM", DateFormatUtils.YYYYMMDDSLASH, "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM", "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};
    public static Date getNowDate() {
        return new Date();
    }
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }
    public static final String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }
    public static final String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }
    public static final String dateTimeNow(String format) {
        return parseDateToStr(format, new Date());
    }
    public static final String dateTime(Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }
    public static final String parseDateToStr(String format, Date date) {
        return new SimpleDateFormat(format).format(date);
    }
    public static final Date dateTime(String format, String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }
    public static final String datePath() {
        Date now = new Date();
        return org.apache.commons.lang3.time.DateFormatUtils.format(now, DateFormatUtils.YYYYMMDDSLASH);
    }
    public static final String dateTime() {
        Date now = new Date();
        return org.apache.commons.lang3.time.DateFormatUtils.format(now, "yyyyMMdd");
    }
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }
    public static String getDatePoor(Date endDate, Date nowDate) {
        long diff = endDate.getTime() - nowDate.getTime();
        long day = diff / 86400000;
        long hour = (diff % 86400000) / 3600000;
        long min = ((diff % 86400000) % 3600000) / 60000;
        return day + "天" + hour + "小时" + min + "分钟";
    }
    public static final String parseLocalDateTimeToStr(String format, LocalDateTime date) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(format);
        return df.format(date);
    }
    public static Date localDateTimeToDate(LocalDateTime dt) {
        return Date.from(dt.toInstant(ZoneOffset.of("+8")));
    }
    public static LocalDateTime dateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atOffset(ZoneOffset.of("+8")).toLocalDateTime();
    }
}
