package com.xrkc.job.module.log.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.core.domain.log.LogOperate;
import com.xrkc.job.module.log.mapper.LogOperateMapper;
import com.xrkc.job.module.log.service.LogOperateService;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/module/log/service/impl/LogOperateServiceImpl.class */
public class LogOperateServiceImpl extends ServiceImpl<LogOperateMapper, LogOperate> implements LogOperateService {
}
