package com.xrkc.job.mapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xrkc.core.domain.system.SystemDept;
import com.xrkc.core.mybatisplus.BaseMapperX;
import java.util.List;
import org.apache.ibatis.annotations.Select;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/SystemDeptMapper.class */
public interface SystemDeptMapper extends BaseMapperX<SystemDept> {
    @Select({"select * from system_dept;"})
    List<SystemDept> selectAllList();
    default int updatePrimaryId(Long primaryId, List<Long> deptIdList) {
        LambdaUpdateWrapper<SystemDept> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set((v0) -> {
            return v0.getPrimaryId();
        }, primaryId);
        wrapper.in( (v0) -> {
            return v0.getDeptId();
        }, deptIdList);
        return update(null, wrapper);
    }
}
