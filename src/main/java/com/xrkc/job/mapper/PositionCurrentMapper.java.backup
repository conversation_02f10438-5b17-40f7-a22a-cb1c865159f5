package com.xrkc.job.mapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xrkc.core.mybatisplus.BaseMapperX;
import com.xrkc.job.domain.AreaAlarmTempVO;
import com.xrkc.job.domain.PositionCurrent;
import com.xrkc.job.domain.PositionDataCalc;
import com.xrkc.job.domain.PositionVO;
import java.lang.invoke.SerializedLambda;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/PositionCurrentMapper.class */
public interface PositionCurrentMapper extends BaseMapperX<PositionCurrent> {
    int deleteByOfflineBeacons(String str);
    int delDataByOfflineBeacons(String str);
    List<PositionCurrent> selectListByParams(Map<String, Object> map);
    List<PositionCurrent> selectAlarmPerson(AreaAlarmTempVO areaAlarmTempVO);
    List<PositionCurrent> selectStopAlarmPerson(AreaAlarmTempVO areaAlarmTempVO);
    List<PositionCurrent> selectRailAlarmPerson(AreaAlarmTempVO areaAlarmTempVO);
    int deleteByOnlineMinute(@Param("minusSeconds") LocalDateTime localDateTime);
    List<PositionCurrent> selectAlarmPersonPosition(AreaAlarmTempVO areaAlarmTempVO);
    void delCurrentPosition2(Map<String, Object> map);
    void delRssiPosition2(Map<String, Object> map);
    int delRssiTempPosition2(Map<String, Object> map);
    @Select({"select * from position_current where card_id=#{cardId};"})
    PositionCurrent selectByCardId(Long l);
    List<PositionCurrent> findByLayerId(@Param("layerId") String str, @Param("minusSeconds") LocalDateTime localDateTime);
    List<PositionCurrent> findPersonId(@Param("minusSeconds") LocalDateTime localDateTime);
    List<PositionDataCalc> selectId(AreaAlarmTempVO areaAlarmTempVO);
    List<PositionCurrent> getCurrentPersonList(@Param("layerId") String str, @Param("minusSeconds") LocalDateTime localDateTime);
    List<Long> selectAllOnlinePersonId(@Param("list") List<Long> list, @Param("time") LocalDateTime localDateTime);
    default int updateByParams(PositionVO vo) {
        LambdaUpdateWrapper<PositionCurrent> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set((v0) -> {
            return v0.getAcceptTime();
        }, vo.getAcceptTime());
        wrapper.set((v0) -> {
            return v0.getStillStatus();
        }, vo.getStillStatus());
        wrapper.set((v0) -> {
            return v0.getLayerId();
        }, vo.getLayerId());
        wrapper.set((v0) -> {
            return v0.getLayerHeight();
        }, vo.getLayerHeight());
        wrapper.set((v0) -> {
            return v0.getRealName();
        }, vo.getRealName());
        wrapper.set((v0) -> {
            return v0.getJobNumber();
        }, vo.getJobNumber());
        wrapper.set((v0) -> {
            return v0.getDeptId();
        }, vo.getDeptId());
        wrapper.set((v0) -> {
            return v0.getDeptName();
        }, vo.getDeptName());
        wrapper.set((v0) -> {
            return v0.getPostId();
        }, vo.getPostId());
        wrapper.set((v0) -> {
            return v0.getPostName();
        }, vo.getPostName());
        wrapper.set((v0) -> {
            return v0.getCreateTime();
        }, LocalDateTime.now());
        wrapper.eq((v0) -> {
            return v0.getCardId();
        }, vo.getCardId());
        return update(null, wrapper);
    }
}
