package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.job.domain.StatisticsInspectFlow;
import com.xrkc.job.mapper.StatisticsInspectFlowMapper;
import com.xrkc.job.service.IStatisticsInspectFlowService;
import java.lang.invoke.SerializedLambda;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/StatisticsInspectFlowServiceImpl.class */
public class StatisticsInspectFlowServiceImpl extends ServiceImpl<StatisticsInspectFlowMapper, StatisticsInspectFlow> implements IStatisticsInspectFlowService {
    @Autowired
    private StatisticsInspectFlowMapper statisticsInspectFlowMapper;
    @Override // com.xrkc.job.service.IStatisticsInspectFlowService
    public synchronized int batchReplace(List<StatisticsInspectFlow> statisticsInspectFlow) {
        if (CollectionUtils.isEmpty(statisticsInspectFlow)) {
            return 0;
        }
        LocalDate statisticsDate = statisticsInspectFlow.get(0).getStatisticsDate();
        Set<String> set = new HashSet<>();
        List<StatisticsInspectFlow> oldList = this.statisticsInspectFlowMapper.selectByStatisticsDate(statisticsDate);
        ArrayList arrayList = new ArrayList();
        if (CollectionUtils.isEmpty(oldList)) {
            arrayList.addAll(statisticsInspectFlow);
        } else {
            StringBuilder buf = new StringBuilder();
            for (StatisticsInspectFlow danger : oldList) {
                buf.append(danger.getStatisticsDate());
                buf.append(danger.getInspectType());
                set.add(buf.toString());
                buf.setLength(0);
            }
            for (StatisticsInspectFlow flow : statisticsInspectFlow) {
                buf.append(flow.getStatisticsDate());
                buf.append(flow.getInspectType());
                if (set.contains(buf.toString())) {
                    LambdaUpdateWrapper<StatisticsInspectFlow> wrapper = new LambdaUpdateWrapper<>();
                    wrapper.eq((v0) -> {
                        return v0.getInspectType();
                    }, flow.getInspectType());
                    wrapper.eq((v0) -> {
                        return v0.getStatisticsDate();
                    }, flow.getStatisticsDate());
                    this.statisticsInspectFlowMapper.update(flow, wrapper);
                } else {
                    arrayList.add(flow);
                }
                buf.setLength(0);
            }
        }
        if (!CollectionUtils.isEmpty(arrayList)) {
            this.statisticsInspectFlowMapper.insertBatch(arrayList, 1000);
        }
        return arrayList.size();
    }
}
