package com.xrkc.job.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xrkc.core.domain.system.SystemRoleDept;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/ISystemRoleDeptService.class */
public interface ISystemRoleDeptService extends IService<SystemRoleDept> {
    List<Long> getDeptIdsByRoleId(Long l);
    List<Long> getDeptIdsSubdivision(Long l);
    List<Long> getDeptIdCadre(Long l);
}
