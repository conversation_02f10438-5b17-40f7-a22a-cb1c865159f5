package com.xrkc.job.util;
import ch.qos.logback.core.spi.AbstractComponentTracker;
import com.xrkc.core.constant.Constants;
import com.xrkc.core.domain.Circle;
import com.xrkc.core.domain.person.Person;
import com.xrkc.core.domain.system.SystemDept;
import com.xrkc.core.enums.NoticeType;
import com.xrkc.job.domain.SystemRoleFacilityVO;
import java.awt.geom.GeneralPath;
import java.awt.geom.Point2D;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/CommonUtil.class */
public class CommonUtil {
    private static final double EARTH_RADIUS = 6378137.0d;
    private static final double RAD = 0.017453292519943295d;
    private static final List<Integer> VALID_PWD_CHARS = new ArrayList();
    public static List<String> areaAlarmType;
//todo
//    static {
//        IntStream intStreamRangeClosed = IntStream.rangeClosed(48, 57);
//        List<Integer> list = VALID_PWD_CHARS;
//        list.getClass();
//        intStreamRangeClosed.forEach((v1) -> {
//            r1.add(v1);
//        });
//        IntStream intStreamRangeClosed2 = IntStream.rangeClosed(97, 122);
//        List<Integer> list2 = VALID_PWD_CHARS;
//        list2.getClass();
//        intStreamRangeClosed2.forEach((v1) -> {
//            r1.add(v1);
//        });
//        areaAlarmType = Arrays.asList("40", "50", Constants.ALARM_TYPE_96);
//    }
    public static String GetRandomString(int passwordLength) {
        StringBuilder str = new StringBuilder();
        IntStream intStreamInts = new Random().ints(passwordLength, 0, VALID_PWD_CHARS.size());
        List<Integer> list = VALID_PWD_CHARS;
        list.getClass();
        intStreamInts.map(list::get).forEach(s -> {
            str.append(s);
        });
        System.out.println(str.toString());
        return str.toString();
    }
    public static double getDistance(double lng1, double lat1, double lng2, double lat2) {
        double radLat1 = lat1 * 0.017453292519943295d;
        double radLat2 = lat2 * 0.017453292519943295d;
        double a = radLat1 - radLat2;
        double b = (lng1 - lng2) * 0.017453292519943295d;
        double s = 2.0d * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2.0d), 2.0d) + (Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2.0d), 2.0d))));
        return Math.round((s * EARTH_RADIUS) * 10000.0d) / AbstractComponentTracker.LINGERING_TIMEOUT;
    }
    public static void main(String[] args) {
        Map<String, BigDecimal> map = getCenterPoint("POLYGON((114.392858592208 30.459921117668, 114.393851568842 30.4596238001003, 114.39385490091 30.4595978208686, 114.394178114958 30.4594823567521, 114.394537980849 30.4593697790387, 114.396950420045 30.4586278914767, 114.396570543069 30.4580852130026, 114.396553883315 30.4580650064576, 114.39652994197 30.4580452678371, 114.396489971385 30.4580279551636, 114.395866992865 30.4575402444429, 114.395474098189 30.4564875217248, 114.39542749412 30.4565048259273, 114.395364246619 30.4565365492917, 114.393773064849 30.4573757623026, 114.39291077938 30.4577535008502, 114.392168138932 30.4582785687433, 114.392151485831 30.4583304991496, 114.392138164925 30.4583824292175, 114.392527270649 30.4593102894754, 114.39285786779 30.459935870176, 114.392858592208 30.459921117668))");
        System.out.println("中心点" + map.get("lng") + "," + map.get("lat"));
    }
    public static Map<String, BigDecimal> getCenterPoint(String str) {
        String[] arr = str.replace("POLYGON((", "").replace("))", "").replace(", ", ",").split(",");
        int total = arr.length;
        double X = 0.0d;
        double Y = 0.0d;
        double Z = 0.0d;
        for (int i = 0; i < arr.length; i++) {
            double lon = (Double.parseDouble(arr[i].split(" ")[0]) * 3.141592653589793d) / 180.0d;
            double lat = (Double.parseDouble(arr[i].split(" ")[1]) * 3.141592653589793d) / 180.0d;
            double x = Math.cos(lat) * Math.cos(lon);
            double y = Math.cos(lat) * Math.sin(lon);
            double z = Math.sin(lat);
            X += x;
            Y += y;
            Z += z;
        }
        double X2 = X / total;
        double Y2 = Y / total;
        double Z2 = Z / total;
        double Lon = Math.atan2(Y2, X2);
        double Hyp = Math.sqrt((X2 * X2) + (Y2 * Y2));
        double Lat = Math.atan2(Z2, Hyp);
        Map<String, BigDecimal> map = new HashMap<>();
        map.put("lng", new BigDecimal((Lon * 180.0d) / 3.141592653589793d));
        map.put("lat", new BigDecimal((Lat * 180.0d) / 3.141592653589793d));
        return map;
    }
    public static Map<String, String> getCenterPoint2(String str) {
        String[] arr = str.split(";");
        int total = arr.length;
        double X = 0.0d;
        double Y = 0.0d;
        double Z = 0.0d;
        for (int i = 0; i < arr.length; i++) {
            double lon = (Double.parseDouble(arr[i].split(",")[0]) * 3.141592653589793d) / 180.0d;
            double lat = (Double.parseDouble(arr[i].split(",")[1]) * 3.141592653589793d) / 180.0d;
            double x = Math.cos(lat) * Math.cos(lon);
            double y = Math.cos(lat) * Math.sin(lon);
            double z = Math.sin(lat);
            X += x;
            Y += y;
            Z += z;
        }
        double X2 = X / total;
        double Y2 = Y / total;
        double Z2 = Z / total;
        double Lon = Math.atan2(Y2, X2);
        double Hyp = Math.sqrt((X2 * X2) + (Y2 * Y2));
        double Lat = Math.atan2(Z2, Hyp);
        DecimalFormat df = new DecimalFormat("#0.000000");
        Map<String, String> map = new HashMap<>();
        map.put("lng", df.format((Lon * 180.0d) / 3.141592653589793d));
        map.put("lat", df.format((Lat * 180.0d) / 3.141592653589793d));
        return map;
    }
    public static boolean containsRailScope(BigDecimal longitude, BigDecimal latitude, String mapRailScope, String drawType) {
        String mapRailScope2;
        if (Objects.nonNull(longitude) && Objects.nonNull(latitude) && org.apache.commons.lang3.StringUtils.isNotBlank(mapRailScope) && mapRailScope.length() > 18) {
            if ("2".equals(drawType)) {
                if (mapRailScope.contains("POLYGON ((")) {
                    mapRailScope2 = mapRailScope.replaceAll("POLYGON \\(\\(", "").replaceAll("\\)\\)", "");
                } else {
                    mapRailScope2 = mapRailScope.replaceAll("POLYGON\\(\\(", "").replaceAll("\\)\\)", "");
                }
                String[] scopeArray = mapRailScope2.split(",");
                ArrayList<Point2D.Double> list = new ArrayList<>();
                for (String str : scopeArray) {
                    String[] i = str.split(" ");
                    if (i.length == 3) {
                        Point2D.Double point = new Point2D.Double(Double.valueOf(i[1]).doubleValue(), Double.valueOf(i[2]).doubleValue());
                        list.add(point);
                    } else {
                        Point2D.Double point2 = new Point2D.Double(Double.valueOf(i[0]).doubleValue(), Double.valueOf(i[1]).doubleValue());
                        list.add(point2);
                    }
                }
                GeneralPath path = genGeneralPath(list);
                return path.contains(longitude.doubleValue(), latitude.doubleValue());
            }
            if ("0".equals(drawType)) {
                return containsCircleScope(longitude, latitude, mapRailScope).booleanValue();
            }
            return false;
        }
        return false;
    }
    public static boolean containsFacilityRailScope(String layerId, BigDecimal longitude, BigDecimal latitude, List<SystemRoleFacilityVO> facility) {
        AtomicBoolean r = new AtomicBoolean(false);
        Iterator<SystemRoleFacilityVO> it = facility.iterator();
        while (true) {
            if (!it.hasNext()) {
                break;
            }
            SystemRoleFacilityVO rail = it.next();
            if (rail.getLayerId().equals(layerId) && containsRailScope(longitude, latitude, rail.getRailScope(), "2")) {
                r.set(true);
                break;
            }
        }
        return r.get();
    }
    private static GeneralPath genGeneralPath(ArrayList<Point2D.Double> points) {
        GeneralPath path = new GeneralPath();
        if (points.size() < 3) {
            return null;
        }
        path.moveTo((float) points.get(0).getX(), (float) points.get(0).getY());
        Iterator<Point2D.Double> it = points.iterator();
        while (it.hasNext()) {
            Point2D.Double point = it.next();
            path.lineTo((float) point.getX(), (float) point.getY());
        }
        path.closePath();
        return path;
    }
    public static String getKeyByMaxValue(Map<String, Long> m) {
        Map<String, Integer> map = new HashMap<>();
        m.forEach((k, v) -> {
            map.put(k, Integer.valueOf(v.intValue()));
        });
        List<Map.Entry<String, Integer>> list = new ArrayList<>(map.entrySet());
        Collections.sort(list, (o1, o2) -> {
            return ((Integer) o1.getValue()).intValue() - ((Integer) o2.getValue()).intValue();
        });
        return list.get(list.size() - 1).getKey();
    }
    public static Set<Long> getChildDeptIds(Long deptId, List<SystemDept> allDeptList) {
        Set<Long> childDeptIds = new HashSet<>();
        if (Objects.nonNull(deptId)) {
            childDeptIds.add(deptId);
            List<SystemDept> allChildList = (List) allDeptList.stream().filter(f -> {
                return Objects.nonNull(f.getParentId()) && f.getParentId().compareTo(deptId) == 0;
            }).collect(Collectors.toList());
            for (SystemDept dept : allChildList) {
                childDeptIds.add(dept.getDeptId());
                recursiveTree2(dept, allDeptList, childDeptIds);
            }
        }
        return childDeptIds;
    }
    private static SystemDept recursiveTree2(SystemDept parent, List<SystemDept> allDeptList, Set<Long> childDeptIds) {
        for (SystemDept dept : allDeptList) {
            if (Objects.nonNull(dept.getParentId()) && parent.getDeptId().compareTo(dept.getParentId()) == 0) {
                childDeptIds.add(dept.getDeptId());
                recursiveTree2(dept, allDeptList, childDeptIds);
            }
        }
        return parent;
    }
    public static Boolean containsCircleScope(BigDecimal longitude, BigDecimal latitude, String circleScope) throws NumberFormatException {
        if (org.apache.commons.lang3.StringUtils.isEmpty(circleScope)) {
            return false;
        }
        String[] circleScopes = circleScope.split(",");
        if (circleScopes.length != 3) {
            return false;
        }
        Circle circle = paseCircleScope(circleScopes);
        if (Objects.isNull(circle)) {
            return false;
        }
        return distencePC(new Point2D.Double(longitude.doubleValue(), latitude.doubleValue()), circle);
    }
    public static Circle paseCircleScope(String[] circleScope) throws NumberFormatException {
        try {
            Circle circle = new Circle();
            double lon = Double.parseDouble(circleScope[0]);
            double lat = Double.parseDouble(circleScope[1]);
            double r = Double.parseDouble(circleScope[2]);
            circle.setCc(new Point2D.Double(lon, lat));
            circle.setR(r);
            return circle;
        } catch (Exception e) {
            return null;
        }
    }
    public static Boolean distencePC(Point2D p, Circle c) {
        AtomicBoolean res = new AtomicBoolean(true);
        double d2 = Math.hypot(p.getX() - c.getCc().getX(), p.getY() - c.getCc().getY());
        double r = c.getR();
        if (d2 > r) {
            res.set(false);
        }
        return Boolean.valueOf(res.get());
    }
    public static boolean sameLayerId(String railScopeLayerId, String currentPeopleLayerId) {
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(railScopeLayerId) && railScopeLayerId.equals(currentPeopleLayerId)) {
            return true;
        }
        return false;
    }
    public static boolean isValidNumberOrMail(Person p, String type) {
        boolean res = false;
        if (NoticeType.sms.getKey().equals(type)) {
            res = RegexUtils.isMobile(p.getPhone());
        } else if (NoticeType.email.getKey().equals(type)) {
            res = RegexUtils.isEmail(p.getEmail());
        }
        return res;
    }
    public static String setPersonPhone(Person p, String type) {
        String phone = null;
        if (NoticeType.sms.getKey().equals(type)) {
            phone = p.getPhone();
        } else if (NoticeType.email.getKey().equals(type)) {
            phone = p.getEmail();
        }
        return phone;
    }
}
