package com.xrkc.job.service;
import com.xrkc.job.domain.RehearsalPerson;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IRehearsalPersonService.class */
public interface IRehearsalPersonService {
    int batchInsert(List<RehearsalPerson> list);
    List<RehearsalPerson> findRehearsalPersonList(List<Long> list);
    List<RehearsalPerson> findUnCompletePersonByRehearsalId(Long l);
    int batchCompleteUpdate(List<Long> list);
}
