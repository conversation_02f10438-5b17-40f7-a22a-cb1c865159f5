package com.xrkc.job.service.impl;
import com.xrkc.job.domain.InspectRecord;
import com.xrkc.job.domain.InspectRecordItem;
import com.xrkc.job.domain.InspectRecordItemParam;
import com.xrkc.job.domain.InspectRecordLocation;
import com.xrkc.job.mapper.InspectRecordMapper;
import com.xrkc.job.service.IInspectRecordService;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/InspectRecordServiceImpl.class */
public class InspectRecordServiceImpl implements IInspectRecordService {
    @Autowired
    private InspectRecordMapper inspectRecordMapper;
    @Override // com.xrkc.job.service.IInspectRecordService
    public List<InspectRecord> selectEverydayInspectTaskList(String frequencyType) {
        return this.inspectRecordMapper.selectEverydayInspectTaskList(frequencyType);
    }
    @Override // com.xrkc.job.service.IInspectRecordService
    public List<InspectRecord> selectNotEverydayInspectTaskList(String frequencyType) {
        return this.inspectRecordMapper.selectNotEverydayInspectTaskList(frequencyType);
    }
    @Override // com.xrkc.job.service.IInspectRecordService
    public int existInspectTask(InspectRecord t) {
        return this.inspectRecordMapper.existInspectTask(t);
    }
    @Override // com.xrkc.job.service.IInspectRecordService
    public int insertInspectRecord(InspectRecord t) {
        return this.inspectRecordMapper.insertInspectRecord(t);
    }
    @Override // com.xrkc.job.service.IInspectRecordService
    public List<InspectRecordLocation> selectInspectLocationList(List<Long> roadIds) {
        return this.inspectRecordMapper.selectInspectLocationList(roadIds);
    }
    @Override // com.xrkc.job.service.IInspectRecordService
    public int batchInsertLocation(List<InspectRecordLocation> insertLocationList) {
        return this.inspectRecordMapper.batchInsertLocation(insertLocationList);
    }
    @Override // com.xrkc.job.service.IInspectRecordService
    public List<InspectRecordItem> selectInspectItemList(List<Long> locationIds) {
        return this.inspectRecordMapper.selectInspectItemList(locationIds);
    }
    @Override // com.xrkc.job.service.IInspectRecordService
    public int batchInsertItem(List<InspectRecordItem> insertItemList) {
        return this.inspectRecordMapper.batchInsertItem(insertItemList);
    }
    @Override // com.xrkc.job.service.IInspectRecordService
    public int batchInsertItemParam(List<InspectRecordItemParam> insertItemParamList) {
        return this.inspectRecordMapper.batchInsertItemParam(insertItemParamList);
    }
    @Override // com.xrkc.job.service.IInspectRecordService
    public List<InspectRecord> selectInspectRecord() {
        return this.inspectRecordMapper.selectInspectRecord();
    }
    @Override // com.xrkc.job.service.IInspectRecordService
    public int updateInspectRecord(InspectRecord t) {
        return this.inspectRecordMapper.updateInspectRecord(t);
    }
    @Override // com.xrkc.job.service.IInspectRecordService
    public List<InspectRecord> selectYesterdayRecord(LocalDate startTime, LocalDateTime endTime) {
        return this.inspectRecordMapper.selectYesterdayRecord(startTime, endTime);
    }
    @Override // com.xrkc.job.service.IInspectRecordService
    public List<InspectRecordItem> selectInspectItem(Long locationId) {
        return this.inspectRecordMapper.selectInspectItem(locationId);
    }
    @Override // com.xrkc.job.service.IInspectRecordService
    public List<InspectRecordItemParam> selectInspectItemParam(Long itemId) {
        return this.inspectRecordMapper.selectInspectItemParam(itemId);
    }
}
