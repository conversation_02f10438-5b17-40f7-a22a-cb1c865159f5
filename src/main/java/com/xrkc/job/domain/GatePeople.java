package com.xrkc.job.domain;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/GatePeople.class */
public class GatePeople implements Serializable {
    private Long personId;
    private String personCode;
    private String personPhoto;
    private Long cardId;
    private String realName;
    private String deptName;
    private String idNumber;
    private String personType;
    private String staffType;
    private Long deptId;
    private Long postId;
    private Long contractorId;
    private String contractorName;
    public void setPersonId(Long personId) {
        this.personId = personId;
    }
    public void setPersonCode(String personCode) {
        this.personCode = personCode;
    }
    public void setPersonPhoto(String personPhoto) {
        this.personPhoto = personPhoto;
    }
    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }
    public void setRealName(String realName) {
        this.realName = realName;
    }
    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    public void setIdNumber(String idNumber) {
        this.idNumber = idNumber;
    }
    public void setPersonType(String personType) {
        this.personType = personType;
    }
    public void setStaffType(String staffType) {
        this.staffType = staffType;
    }
    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
    public void setPostId(Long postId) {
        this.postId = postId;
    }
    public void setContractorId(Long contractorId) {
        this.contractorId = contractorId;
    }
    public void setContractorName(String contractorName) {
        this.contractorName = contractorName;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof GatePeople)) {
            return false;
        }
        GatePeople other = (GatePeople) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$personId = getPersonId();
        Object other$personId = other.getPersonId();
        if (this$personId == null) {
            if (other$personId != null) {
                return false;
            }
        } else if (!this$personId.equals(other$personId)) {
            return false;
        }
        Object this$cardId = getCardId();
        Object other$cardId = other.getCardId();
        if (this$cardId == null) {
            if (other$cardId != null) {
                return false;
            }
        } else if (!this$cardId.equals(other$cardId)) {
            return false;
        }
        Object this$deptId = getDeptId();
        Object other$deptId = other.getDeptId();
        if (this$deptId == null) {
            if (other$deptId != null) {
                return false;
            }
        } else if (!this$deptId.equals(other$deptId)) {
            return false;
        }
        Object this$postId = getPostId();
        Object other$postId = other.getPostId();
        if (this$postId == null) {
            if (other$postId != null) {
                return false;
            }
        } else if (!this$postId.equals(other$postId)) {
            return false;
        }
        Object this$contractorId = getContractorId();
        Object other$contractorId = other.getContractorId();
        if (this$contractorId == null) {
            if (other$contractorId != null) {
                return false;
            }
        } else if (!this$contractorId.equals(other$contractorId)) {
            return false;
        }
        Object this$personCode = getPersonCode();
        Object other$personCode = other.getPersonCode();
        if (this$personCode == null) {
            if (other$personCode != null) {
                return false;
            }
        } else if (!this$personCode.equals(other$personCode)) {
            return false;
        }
        Object this$personPhoto = getPersonPhoto();
        Object other$personPhoto = other.getPersonPhoto();
        if (this$personPhoto == null) {
            if (other$personPhoto != null) {
                return false;
            }
        } else if (!this$personPhoto.equals(other$personPhoto)) {
            return false;
        }
        Object this$realName = getRealName();
        Object other$realName = other.getRealName();
        if (this$realName == null) {
            if (other$realName != null) {
                return false;
            }
        } else if (!this$realName.equals(other$realName)) {
            return false;
        }
        Object this$deptName = getDeptName();
        Object other$deptName = other.getDeptName();
        if (this$deptName == null) {
            if (other$deptName != null) {
                return false;
            }
        } else if (!this$deptName.equals(other$deptName)) {
            return false;
        }
        Object this$idNumber = getIdNumber();
        Object other$idNumber = other.getIdNumber();
        if (this$idNumber == null) {
            if (other$idNumber != null) {
                return false;
            }
        } else if (!this$idNumber.equals(other$idNumber)) {
            return false;
        }
        Object this$personType = getPersonType();
        Object other$personType = other.getPersonType();
        if (this$personType == null) {
            if (other$personType != null) {
                return false;
            }
        } else if (!this$personType.equals(other$personType)) {
            return false;
        }
        Object this$staffType = getStaffType();
        Object other$staffType = other.getStaffType();
        if (this$staffType == null) {
            if (other$staffType != null) {
                return false;
            }
        } else if (!this$staffType.equals(other$staffType)) {
            return false;
        }
        Object this$contractorName = getContractorName();
        Object other$contractorName = other.getContractorName();
        return this$contractorName == null ? other$contractorName == null : this$contractorName.equals(other$contractorName);
    }
    protected boolean canEqual(Object other) {
        return other instanceof GatePeople;
    }
    public int hashCode() {
        Object $personId = getPersonId();
        int result = (1 * 59) + ($personId == null ? 43 : $personId.hashCode());
        Object $cardId = getCardId();
        int result2 = (result * 59) + ($cardId == null ? 43 : $cardId.hashCode());
        Object $deptId = getDeptId();
        int result3 = (result2 * 59) + ($deptId == null ? 43 : $deptId.hashCode());
        Object $postId = getPostId();
        int result4 = (result3 * 59) + ($postId == null ? 43 : $postId.hashCode());
        Object $contractorId = getContractorId();
        int result5 = (result4 * 59) + ($contractorId == null ? 43 : $contractorId.hashCode());
        Object $personCode = getPersonCode();
        int result6 = (result5 * 59) + ($personCode == null ? 43 : $personCode.hashCode());
        Object $personPhoto = getPersonPhoto();
        int result7 = (result6 * 59) + ($personPhoto == null ? 43 : $personPhoto.hashCode());
        Object $realName = getRealName();
        int result8 = (result7 * 59) + ($realName == null ? 43 : $realName.hashCode());
        Object $deptName = getDeptName();
        int result9 = (result8 * 59) + ($deptName == null ? 43 : $deptName.hashCode());
        Object $idNumber = getIdNumber();
        int result10 = (result9 * 59) + ($idNumber == null ? 43 : $idNumber.hashCode());
        Object $personType = getPersonType();
        int result11 = (result10 * 59) + ($personType == null ? 43 : $personType.hashCode());
        Object $staffType = getStaffType();
        int result12 = (result11 * 59) + ($staffType == null ? 43 : $staffType.hashCode());
        Object $contractorName = getContractorName();
        return (result12 * 59) + ($contractorName == null ? 43 : $contractorName.hashCode());
    }
    public String toString() {
        return "GatePeople(personId=" + getPersonId() + ", personCode=" + getPersonCode() + ", personPhoto=" + getPersonPhoto() + ", cardId=" + getCardId() + ", realName=" + getRealName() + ", deptName=" + getDeptName() + ", idNumber=" + getIdNumber() + ", personType=" + getPersonType() + ", staffType=" + getStaffType() + ", deptId=" + getDeptId() + ", postId=" + getPostId() + ", contractorId=" + getContractorId() + ", contractorName=" + getContractorName() + StringPool.RIGHT_BRACKET;
    }
    public Long getPersonId() {
        return this.personId;
    }
    public String getPersonCode() {
        return this.personCode;
    }
    public String getPersonPhoto() {
        return this.personPhoto;
    }
    public Long getCardId() {
        return this.cardId;
    }
    public String getRealName() {
        return this.realName;
    }
    public String getDeptName() {
        return this.deptName;
    }
    public String getIdNumber() {
        return this.idNumber;
    }
    public String getPersonType() {
        return this.personType;
    }
    public String getStaffType() {
        return this.staffType;
    }
    public Long getDeptId() {
        return this.deptId;
    }
    public Long getPostId() {
        return this.postId;
    }
    public Long getContractorId() {
        return this.contractorId;
    }
    public String getContractorName() {
        return this.contractorName;
    }
}
