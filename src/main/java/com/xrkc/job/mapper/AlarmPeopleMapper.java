package com.xrkc.job.mapper;
import com.xrkc.core.domain.alarm.CoreAlarmPeople;
import com.xrkc.core.mybatisplus.BaseMapperX;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
@Mapper
@Repository
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/AlarmPeopleMapper.class */
public interface AlarmPeopleMapper extends BaseMapperX<CoreAlarmPeople> {
    void updatepeopleAlarm(@Param("alarmIds") List<Long> list, @Param("list") Set<Long> set, @Param("time") LocalDateTime localDateTime);
    List<Long> selectAlarmId(@Param("areaId") Long l, @Param("alarmType") String str);
    List<CoreAlarmPeople> selectAlarmPeopleId(@Param("coreAlarmId") Long l);
    void updateONLeavepeopleAlarm(@Param("alarmIds") List<Long> list, @Param("list") Set<Long> set, @Param("time") LocalDateTime localDateTime);
    void updatePeople(CoreAlarmPeople coreAlarmPeople);
    List<CoreAlarmPeople> selectPersonLowPowerCardList(@Param("cardPower") int i);
}
