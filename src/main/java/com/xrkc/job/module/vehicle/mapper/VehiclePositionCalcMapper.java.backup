package com.xrkc.job.module.vehicle.mapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.xrkc.core.domain.vehicle.VehiclePositionCalc;
import com.xrkc.core.domain.vehicle.VehiclePositionCurrent;
import com.xrkc.core.mybatisplus.BaseMapperX;
import com.xrkc.core.mybatisplus.LambdaQueryWrapperX;
import java.lang.invoke.SerializedLambda;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.Mapper;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/module/vehicle/mapper/VehiclePositionCalcMapper.class */
public interface VehiclePositionCalcMapper extends BaseMapperX<VehiclePositionCalc> {
    default List<VehiclePositionCalc> selectListOfHistory(int second) {
        return selectList(new LambdaQueryWrapperX<VehiclePositionCalc>().gtIfPresent((v0) -> {
            return v0.getAcceptTime();
        }, LocalDateTime.now().minusSeconds(second)).orderByDesc((v0) -> {
            return v0.getId();
        }));
    }
    default Long findMaxId(int minute) {
        Optional<Long> optionalId = selectList(new LambdaQueryWrapperX<VehiclePositionCalc>().ltIfPresent((v0) -> {
            return v0.getAcceptTime();
        }, LocalDateTime.now().minusMinutes(minute)).orderByDesc((v0) -> {
            return v0.getId();
        })).stream().map((v0) -> {
            return v0.getId();
        }).max((v0, v1) -> {
            return v0.compareTo(v1);
        });
        if (optionalId.isPresent()) {
            return optionalId.get();
        }
        return null;
    }
    /* JADX WARN: Multi-variable type inference failed */
    default int delByMaxId(Long maxId) {
        return delete((Wrapper) new LambdaQueryWrapperX<VehiclePositionCalc>().lt((v0) -> {
            return v0.getId();
        }, maxId));
    }
    default List<VehiclePositionCalc> selectCurrentListByCurrent(VehiclePositionCurrent positionCurrent) {
        return selectList(new LambdaQueryWrapperX<VehiclePositionCalc>().eq((v0) -> {
            return v0.getVehicleId();
        }, (Object) positionCurrent.getVehicleId()).eq((v0) -> {
            return v0.getCardId();
        }, (Object) positionCurrent.getCardId()).eq((v0) -> {
            return v0.getLongitude();
        }, (Object) positionCurrent.getLongitude()).eq((v0) -> {
            return v0.getLatitude();
        }, (Object) positionCurrent.getLatitude()).orderByAsc((v0) -> {
            return v0.getAcceptTime();
        }));
    }
    default List<VehiclePositionCalc> selectCurrentListByTime(VehiclePositionCurrent positionCurrent) {
        return selectList(new LambdaQueryWrapperX<VehiclePositionCalc>().eq((v0) -> {
            return v0.getVehicleId();
        }, (Object) positionCurrent.getVehicleId()).eq((v0) -> {
            return v0.getCardId();
        }, (Object) positionCurrent.getCardId()).orderByAsc((v0) -> {
            return v0.getAcceptTime();
        }));
    }
}
