package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
@TableName("position_data_calc")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/PositionDataCalc.class */
public class PositionDataCalc implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId("id")
    private Long id;
    private LocalDateTime acceptTime;
    private Integer beaconId;
    private Double distance;
    private Long cardId;
    private String cardType;
    private BigDecimal longitude;
    private BigDecimal latitude;
    private String layerId;
    private Integer layerHeight;
    private LocalDateTime createTime;
    private Long personId;
    private String personType;
    private String staffType;
    private String realName;
    private Long deptId;
    private Long postId;
    private Long contractorId;
    private String positionType;
    private String positionStatus;
    @TableField(exist = false)
    private String orgLayerId;
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/PositionDataCalc$PositionDataCalcBuilder.class */
    public static class PositionDataCalcBuilder {
        private Long id;
        private LocalDateTime acceptTime;
        private Integer beaconId;
        private Double distance;
        private Long cardId;
        private String cardType;
        private BigDecimal longitude;
        private BigDecimal latitude;
        private String layerId;
        private Integer layerHeight;
        private LocalDateTime createTime;
        private Long personId;
        private String personType;
        private String staffType;
        private String realName;
        private Long deptId;
        private Long postId;
        private Long contractorId;
        private String positionType;
        private String positionStatus;
        private String orgLayerId;
        PositionDataCalcBuilder() {
        }
        public PositionDataCalcBuilder id(Long id) {
            this.id = id;
            return this;
        }
        public PositionDataCalcBuilder acceptTime(LocalDateTime acceptTime) {
            this.acceptTime = acceptTime;
            return this;
        }
        public PositionDataCalcBuilder beaconId(Integer beaconId) {
            this.beaconId = beaconId;
            return this;
        }
        public PositionDataCalcBuilder distance(Double distance) {
            this.distance = distance;
            return this;
        }
        public PositionDataCalcBuilder cardId(Long cardId) {
            this.cardId = cardId;
            return this;
        }
        public PositionDataCalcBuilder cardType(String cardType) {
            this.cardType = cardType;
            return this;
        }
        public PositionDataCalcBuilder longitude(BigDecimal longitude) {
            this.longitude = longitude;
            return this;
        }
        public PositionDataCalcBuilder latitude(BigDecimal latitude) {
            this.latitude = latitude;
            return this;
        }
        public PositionDataCalcBuilder layerId(String layerId) {
            this.layerId = layerId;
            return this;
        }
        public PositionDataCalcBuilder layerHeight(Integer layerHeight) {
            this.layerHeight = layerHeight;
            return this;
        }
        public PositionDataCalcBuilder createTime(LocalDateTime createTime) {
            this.createTime = createTime;
            return this;
        }
        public PositionDataCalcBuilder personId(Long personId) {
            this.personId = personId;
            return this;
        }
        public PositionDataCalcBuilder personType(String personType) {
            this.personType = personType;
            return this;
        }
        public PositionDataCalcBuilder staffType(String staffType) {
            this.staffType = staffType;
            return this;
        }
        public PositionDataCalcBuilder realName(String realName) {
            this.realName = realName;
            return this;
        }
        public PositionDataCalcBuilder deptId(Long deptId) {
            this.deptId = deptId;
            return this;
        }
        public PositionDataCalcBuilder postId(Long postId) {
            this.postId = postId;
            return this;
        }
        public PositionDataCalcBuilder contractorId(Long contractorId) {
            this.contractorId = contractorId;
            return this;
        }
        public PositionDataCalcBuilder positionType(String positionType) {
            this.positionType = positionType;
            return this;
        }
        public PositionDataCalcBuilder positionStatus(String positionStatus) {
            this.positionStatus = positionStatus;
            return this;
        }
        public PositionDataCalcBuilder orgLayerId(String orgLayerId) {
            this.orgLayerId = orgLayerId;
            return this;
        }
        public PositionDataCalc build() {
            return new PositionDataCalc(this.id, this.acceptTime, this.beaconId, this.distance, this.cardId, this.cardType, this.longitude, this.latitude, this.layerId, this.layerHeight, this.createTime, this.personId, this.personType, this.staffType, this.realName, this.deptId, this.postId, this.contractorId, this.positionType, this.positionStatus, this.orgLayerId);
        }
        public String toString() {
            return "PositionDataCalc.PositionDataCalcBuilder(id=" + this.id + ", acceptTime=" + this.acceptTime + ", beaconId=" + this.beaconId + ", distance=" + this.distance + ", cardId=" + this.cardId + ", cardType=" + this.cardType + ", longitude=" + this.longitude + ", latitude=" + this.latitude + ", layerId=" + this.layerId + ", layerHeight=" + this.layerHeight + ", createTime=" + this.createTime + ", personId=" + this.personId + ", personType=" + this.personType + ", staffType=" + this.staffType + ", realName=" + this.realName + ", deptId=" + this.deptId + ", postId=" + this.postId + ", contractorId=" + this.contractorId + ", positionType=" + this.positionType + ", positionStatus=" + this.positionStatus + ", orgLayerId=" + this.orgLayerId + StringPool.RIGHT_BRACKET;
        }
    }
    public void setId(Long id) {
        this.id = id;
    }
    public void setAcceptTime(LocalDateTime acceptTime) {
        this.acceptTime = acceptTime;
    }
    public void setBeaconId(Integer beaconId) {
        this.beaconId = beaconId;
    }
    public void setDistance(Double distance) {
        this.distance = distance;
    }
    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }
    public void setCardType(String cardType) {
        this.cardType = cardType;
    }
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
    public void setLayerHeight(Integer layerHeight) {
        this.layerHeight = layerHeight;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public void setPersonId(Long personId) {
        this.personId = personId;
    }
    public void setPersonType(String personType) {
        this.personType = personType;
    }
    public void setStaffType(String staffType) {
        this.staffType = staffType;
    }
    public void setRealName(String realName) {
        this.realName = realName;
    }
    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
    public void setPostId(Long postId) {
        this.postId = postId;
    }
    public void setContractorId(Long contractorId) {
        this.contractorId = contractorId;
    }
    public void setPositionType(String positionType) {
        this.positionType = positionType;
    }
    public void setPositionStatus(String positionStatus) {
        this.positionStatus = positionStatus;
    }
    public void setOrgLayerId(String orgLayerId) {
        this.orgLayerId = orgLayerId;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof PositionDataCalc)) {
            return false;
        }
        PositionDataCalc other = (PositionDataCalc) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$id = getId();
        Object other$id = other.getId();
        if (this$id == null) {
            if (other$id != null) {
                return false;
            }
        } else if (!this$id.equals(other$id)) {
            return false;
        }
        Object this$beaconId = getBeaconId();
        Object other$beaconId = other.getBeaconId();
        if (this$beaconId == null) {
            if (other$beaconId != null) {
                return false;
            }
        } else if (!this$beaconId.equals(other$beaconId)) {
            return false;
        }
        Object this$distance = getDistance();
        Object other$distance = other.getDistance();
        if (this$distance == null) {
            if (other$distance != null) {
                return false;
            }
        } else if (!this$distance.equals(other$distance)) {
            return false;
        }
        Object this$cardId = getCardId();
        Object other$cardId = other.getCardId();
        if (this$cardId == null) {
            if (other$cardId != null) {
                return false;
            }
        } else if (!this$cardId.equals(other$cardId)) {
            return false;
        }
        Object this$layerHeight = getLayerHeight();
        Object other$layerHeight = other.getLayerHeight();
        if (this$layerHeight == null) {
            if (other$layerHeight != null) {
                return false;
            }
        } else if (!this$layerHeight.equals(other$layerHeight)) {
            return false;
        }
        Object this$personId = getPersonId();
        Object other$personId = other.getPersonId();
        if (this$personId == null) {
            if (other$personId != null) {
                return false;
            }
        } else if (!this$personId.equals(other$personId)) {
            return false;
        }
        Object this$deptId = getDeptId();
        Object other$deptId = other.getDeptId();
        if (this$deptId == null) {
            if (other$deptId != null) {
                return false;
            }
        } else if (!this$deptId.equals(other$deptId)) {
            return false;
        }
        Object this$postId = getPostId();
        Object other$postId = other.getPostId();
        if (this$postId == null) {
            if (other$postId != null) {
                return false;
            }
        } else if (!this$postId.equals(other$postId)) {
            return false;
        }
        Object this$contractorId = getContractorId();
        Object other$contractorId = other.getContractorId();
        if (this$contractorId == null) {
            if (other$contractorId != null) {
                return false;
            }
        } else if (!this$contractorId.equals(other$contractorId)) {
            return false;
        }
        Object this$acceptTime = getAcceptTime();
        Object other$acceptTime = other.getAcceptTime();
        if (this$acceptTime == null) {
            if (other$acceptTime != null) {
                return false;
            }
        } else if (!this$acceptTime.equals(other$acceptTime)) {
            return false;
        }
        Object this$cardType = getCardType();
        Object other$cardType = other.getCardType();
        if (this$cardType == null) {
            if (other$cardType != null) {
                return false;
            }
        } else if (!this$cardType.equals(other$cardType)) {
            return false;
        }
        Object this$longitude = getLongitude();
        Object other$longitude = other.getLongitude();
        if (this$longitude == null) {
            if (other$longitude != null) {
                return false;
            }
        } else if (!this$longitude.equals(other$longitude)) {
            return false;
        }
        Object this$latitude = getLatitude();
        Object other$latitude = other.getLatitude();
        if (this$latitude == null) {
            if (other$latitude != null) {
                return false;
            }
        } else if (!this$latitude.equals(other$latitude)) {
            return false;
        }
        Object this$layerId = getLayerId();
        Object other$layerId = other.getLayerId();
        if (this$layerId == null) {
            if (other$layerId != null) {
                return false;
            }
        } else if (!this$layerId.equals(other$layerId)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        if (this$createTime == null) {
            if (other$createTime != null) {
                return false;
            }
        } else if (!this$createTime.equals(other$createTime)) {
            return false;
        }
        Object this$personType = getPersonType();
        Object other$personType = other.getPersonType();
        if (this$personType == null) {
            if (other$personType != null) {
                return false;
            }
        } else if (!this$personType.equals(other$personType)) {
            return false;
        }
        Object this$staffType = getStaffType();
        Object other$staffType = other.getStaffType();
        if (this$staffType == null) {
            if (other$staffType != null) {
                return false;
            }
        } else if (!this$staffType.equals(other$staffType)) {
            return false;
        }
        Object this$realName = getRealName();
        Object other$realName = other.getRealName();
        if (this$realName == null) {
            if (other$realName != null) {
                return false;
            }
        } else if (!this$realName.equals(other$realName)) {
            return false;
        }
        Object this$positionType = getPositionType();
        Object other$positionType = other.getPositionType();
        if (this$positionType == null) {
            if (other$positionType != null) {
                return false;
            }
        } else if (!this$positionType.equals(other$positionType)) {
            return false;
        }
        Object this$positionStatus = getPositionStatus();
        Object other$positionStatus = other.getPositionStatus();
        if (this$positionStatus == null) {
            if (other$positionStatus != null) {
                return false;
            }
        } else if (!this$positionStatus.equals(other$positionStatus)) {
            return false;
        }
        Object this$orgLayerId = getOrgLayerId();
        Object other$orgLayerId = other.getOrgLayerId();
        return this$orgLayerId == null ? other$orgLayerId == null : this$orgLayerId.equals(other$orgLayerId);
    }
    protected boolean canEqual(Object other) {
        return other instanceof PositionDataCalc;
    }
    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $beaconId = getBeaconId();
        int result2 = (result * 59) + ($beaconId == null ? 43 : $beaconId.hashCode());
        Object $distance = getDistance();
        int result3 = (result2 * 59) + ($distance == null ? 43 : $distance.hashCode());
        Object $cardId = getCardId();
        int result4 = (result3 * 59) + ($cardId == null ? 43 : $cardId.hashCode());
        Object $layerHeight = getLayerHeight();
        int result5 = (result4 * 59) + ($layerHeight == null ? 43 : $layerHeight.hashCode());
        Object $personId = getPersonId();
        int result6 = (result5 * 59) + ($personId == null ? 43 : $personId.hashCode());
        Object $deptId = getDeptId();
        int result7 = (result6 * 59) + ($deptId == null ? 43 : $deptId.hashCode());
        Object $postId = getPostId();
        int result8 = (result7 * 59) + ($postId == null ? 43 : $postId.hashCode());
        Object $contractorId = getContractorId();
        int result9 = (result8 * 59) + ($contractorId == null ? 43 : $contractorId.hashCode());
        Object $acceptTime = getAcceptTime();
        int result10 = (result9 * 59) + ($acceptTime == null ? 43 : $acceptTime.hashCode());
        Object $cardType = getCardType();
        int result11 = (result10 * 59) + ($cardType == null ? 43 : $cardType.hashCode());
        Object $longitude = getLongitude();
        int result12 = (result11 * 59) + ($longitude == null ? 43 : $longitude.hashCode());
        Object $latitude = getLatitude();
        int result13 = (result12 * 59) + ($latitude == null ? 43 : $latitude.hashCode());
        Object $layerId = getLayerId();
        int result14 = (result13 * 59) + ($layerId == null ? 43 : $layerId.hashCode());
        Object $createTime = getCreateTime();
        int result15 = (result14 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $personType = getPersonType();
        int result16 = (result15 * 59) + ($personType == null ? 43 : $personType.hashCode());
        Object $staffType = getStaffType();
        int result17 = (result16 * 59) + ($staffType == null ? 43 : $staffType.hashCode());
        Object $realName = getRealName();
        int result18 = (result17 * 59) + ($realName == null ? 43 : $realName.hashCode());
        Object $positionType = getPositionType();
        int result19 = (result18 * 59) + ($positionType == null ? 43 : $positionType.hashCode());
        Object $positionStatus = getPositionStatus();
        int result20 = (result19 * 59) + ($positionStatus == null ? 43 : $positionStatus.hashCode());
        Object $orgLayerId = getOrgLayerId();
        return (result20 * 59) + ($orgLayerId == null ? 43 : $orgLayerId.hashCode());
    }
    public String toString() {
        return "PositionDataCalc(id=" + getId() + ", acceptTime=" + getAcceptTime() + ", beaconId=" + getBeaconId() + ", distance=" + getDistance() + ", cardId=" + getCardId() + ", cardType=" + getCardType() + ", longitude=" + getLongitude() + ", latitude=" + getLatitude() + ", layerId=" + getLayerId() + ", layerHeight=" + getLayerHeight() + ", createTime=" + getCreateTime() + ", personId=" + getPersonId() + ", personType=" + getPersonType() + ", staffType=" + getStaffType() + ", realName=" + getRealName() + ", deptId=" + getDeptId() + ", postId=" + getPostId() + ", contractorId=" + getContractorId() + ", positionType=" + getPositionType() + ", positionStatus=" + getPositionStatus() + ", orgLayerId=" + getOrgLayerId() + StringPool.RIGHT_BRACKET;
    }
    public static PositionDataCalcBuilder builder() {
        return new PositionDataCalcBuilder();
    }
    public PositionDataCalc() {
    }
    public PositionDataCalc(Long id, LocalDateTime acceptTime, Integer beaconId, Double distance, Long cardId, String cardType, BigDecimal longitude, BigDecimal latitude, String layerId, Integer layerHeight, LocalDateTime createTime, Long personId, String personType, String staffType, String realName, Long deptId, Long postId, Long contractorId, String positionType, String positionStatus, String orgLayerId) {
        this.id = id;
        this.acceptTime = acceptTime;
        this.beaconId = beaconId;
        this.distance = distance;
        this.cardId = cardId;
        this.cardType = cardType;
        this.longitude = longitude;
        this.latitude = latitude;
        this.layerId = layerId;
        this.layerHeight = layerHeight;
        this.createTime = createTime;
        this.personId = personId;
        this.personType = personType;
        this.staffType = staffType;
        this.realName = realName;
        this.deptId = deptId;
        this.postId = postId;
        this.contractorId = contractorId;
        this.positionType = positionType;
        this.positionStatus = positionStatus;
        this.orgLayerId = orgLayerId;
    }
    public Long getId() {
        return this.id;
    }
    public LocalDateTime getAcceptTime() {
        return this.acceptTime;
    }
    public Integer getBeaconId() {
        return this.beaconId;
    }
    public Double getDistance() {
        return this.distance;
    }
    public Long getCardId() {
        return this.cardId;
    }
    public String getCardType() {
        return this.cardType;
    }
    public BigDecimal getLongitude() {
        return this.longitude;
    }
    public BigDecimal getLatitude() {
        return this.latitude;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public Integer getLayerHeight() {
        return this.layerHeight;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public Long getPersonId() {
        return this.personId;
    }
    public String getPersonType() {
        return this.personType;
    }
    public String getStaffType() {
        return this.staffType;
    }
    public String getRealName() {
        return this.realName;
    }
    public Long getDeptId() {
        return this.deptId;
    }
    public Long getPostId() {
        return this.postId;
    }
    public Long getContractorId() {
        return this.contractorId;
    }
    public String getPositionType() {
        return this.positionType;
    }
    public String getPositionStatus() {
        return this.positionStatus;
    }
    public String getOrgLayerId() {
        return this.orgLayerId;
    }
}
