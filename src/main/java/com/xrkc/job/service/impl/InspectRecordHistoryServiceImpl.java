package com.xrkc.job.service.impl;
import com.xrkc.core.domain.inspect.InspectRecord;
import com.xrkc.job.mapper.InspectRecordHistoryMapper;
import com.xrkc.job.service.IInspectRecordHistoryService;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/InspectRecordHistoryServiceImpl.class */
public class InspectRecordHistoryServiceImpl implements IInspectRecordHistoryService {
    @Autowired
    private InspectRecordHistoryMapper inspectRecordHistoryMapper;
    @Override // com.xrkc.job.service.IInspectRecordHistoryService
    public List<InspectRecord> selectCopyHistory() {
        LocalDateTime months = LocalDateTime.now().minusMonths(1L);
        return this.inspectRecordHistoryMapper.selectCopyHistory(months);
    }
    @Override // com.xrkc.job.service.IInspectRecordHistoryService
    public int batchSave(List<InspectRecord> list) {
        List<Long> recordIds = (List) list.stream().map((v0) -> {
            return v0.getRecordId();
        }).collect(Collectors.toList());
        this.inspectRecordHistoryMapper.delHistory(recordIds);
        return this.inspectRecordHistoryMapper.batchBakHistory(list);
    }
    @Override // com.xrkc.job.service.IInspectRecordHistoryService
    public int batchDelRecord(List<Long> recordIds) {
        return this.inspectRecordHistoryMapper.batchDelRecord(recordIds);
    }
    @Override // com.xrkc.job.service.IInspectRecordHistoryService
    public int batchDelRecordLocation(List<Long> recordIds) {
        return this.inspectRecordHistoryMapper.batchDelRecordLocation(recordIds);
    }
    @Override // com.xrkc.job.service.IInspectRecordHistoryService
    public int batchDelRecordItem(List<Long> recordIds) {
        return this.inspectRecordHistoryMapper.batchDelRecordItem(recordIds);
    }
    @Override // com.xrkc.job.service.IInspectRecordHistoryService
    public int batchDelRecordParam(List<Long> recordIds) {
        return this.inspectRecordHistoryMapper.batchDelRecordParam(recordIds);
    }
    @Override // com.xrkc.job.service.IInspectRecordHistoryService
    public int delHistory(List<Long> recordIds) {
        return this.inspectRecordHistoryMapper.delHistory(recordIds);
    }
    @Override // com.xrkc.job.service.IInspectRecordHistoryService
    public List<Long> findDelRecordIds() {
        LocalDateTime minusYears = LocalDateTime.now().minusYears(1L);
        return this.inspectRecordHistoryMapper.findDelRecordIds(minusYears);
    }
}
