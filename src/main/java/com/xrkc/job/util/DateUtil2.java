package com.xrkc.job.util;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/DateUtil2.class */
public class DateUtil2 {
    public static final String YYYY = "yyyy";
    public static final String YYYY_MM = "yyyy-MM";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String YEAR = "YEAR";
    public static final String MONTH = "MONTH";
    public static final String DAY = "DAY";
    private static final DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static Timestamp formatDayTimestamp(String date) {
        Timestamp t = Timestamp.valueOf(date);
        return t;
    }
    public static String formatDay(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(date);
    }
    public static String formatDay(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }
    public static Date formatDay(String date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.parse(date, new ParsePosition(0));
    }
    public static Date formatDay(String date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.parse(date, new ParsePosition(0));
    }
    public static LocalDate getCurMonthLastDay() {
        LocalDate today = LocalDate.now();
        return getMonthLastDay(today.getYear(), today.getMonthValue());
    }
    public static Integer getCurMonthLastDays() {
        LocalDate today = LocalDate.now();
        return Integer.valueOf(getMonthLastDay(today.getYear(), today.getMonthValue()).getDayOfMonth());
    }
    public static LocalDate getMonthLastDay(int year, int month) {
        new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        cal.set(1, year);
        cal.set(2, month - 1);
        int lastDay = cal.getActualMaximum(5);
        cal.set(5, lastDay);
        long timeInMillis = cal.getTimeInMillis();
        LocalDate localDate = LocalDateTime.ofEpochSecond(timeInMillis / 1000, 0, ZoneOffset.ofHours(8)).toLocalDate();
        return localDate;
    }
    public static String getCurrentDay() {
        Calendar cal = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(cal.getTime());
    }
    public static String getCurrentMonth() {
        Calendar cal = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        return sdf.format(cal.getTime());
    }
    public static String getCurrentYear() {
        Calendar cal = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
        return sdf.format(cal.getTime());
    }
    public static String getLastMonth() {
        Calendar cal = Calendar.getInstance();
        cal.add(2, -1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        return sdf.format(cal.getTime());
    }
    public static String getLastMonth(Date month, Integer addMonth) {
        Calendar cal = Calendar.getInstance();
        if (month != null) {
            cal.setTime(month);
        }
        cal.add(2, addMonth.intValue());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        return sdf.format(cal.getTime());
    }
    public static String getFirstDayOfMonth() {
        Calendar cal = Calendar.getInstance();
        cal.set(5, 1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(cal.getTime());
    }
    public static String getLastDayOfMonth(Date date) {
        Calendar cal = Calendar.getInstance();
        if (date != null) {
            cal.setTime(date);
        }
        cal.add(2, 1);
        cal.add(5, -1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(cal.getTime());
    }
    public static String getLastDayOfYear(Date date) {
        Calendar cal = Calendar.getInstance();
        if (date != null) {
            cal.setTime(date);
        }
        cal.add(1, 1);
        cal.add(5, -1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(cal.getTime());
    }
    public static Date getFirstDayZeroTimeOfMonth(String month) {
        String day = month + "-01";
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return formatter.parse(day);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }
    public static Date getFirstDayZeroTimeOfYear(String year) {
        String day = year + "-01-01";
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return formatter.parse(day);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }
    public static Date addYear(Date date, int amount) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(1, amount);
        return cal.getTime();
    }
    public static Date addMonth(Date date, int amount) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(2, amount);
        return cal.getTime();
    }
    public static Date addDay(Date date, int amount) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(5, amount);
        return cal.getTime();
    }
    public static boolean isFirstDayOfMonth() {
        Calendar cal = Calendar.getInstance();
        int day = cal.get(5);
        if (day == 1) {
            return true;
        }
        return false;
    }
    public static boolean isMoreThanDayZeroTime(String day, Date timeTag) {
        Date theDay = getZeroTime(day);
        if (timeTag.getTime() <= theDay.getTime()) {
            return true;
        }
        return false;
    }
    public static boolean isDayZeroTime(String day, Date timeTag) {
        Date theDay = getZeroTime(day);
        if (timeTag.getTime() == theDay.getTime()) {
            return true;
        }
        return false;
    }
    public static boolean isCurrentZeroTime(Date timeTag) {
        if (timeTag.getTime() == getCurrentZeroTime().getTime()) {
            return true;
        }
        return false;
    }
    public static Date getCurrentZeroTime() {
        Calendar cal = Calendar.getInstance();
        cal.set(11, 0);
        cal.set(12, 0);
        cal.set(13, 0);
        cal.set(14, 0);
        return cal.getTime();
    }
    public static Date getZeroTime(String day) {
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return formatter.parse(day);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }
    public static boolean isValidMonth(String month) throws ParseException {
        DateFormat formatter = new SimpleDateFormat("yyyy-MM");
        try {
            Date date = formatter.parse(month + "-01");
            if (formatter.format(date).equals(month)) {
                return true;
            }
            return false;
        } catch (ParseException e) {
            return false;
        }
    }
    public static boolean isValidDay(String day) throws ParseException {
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date date = formatter.parse(day);
            if (formatter.format(date).equals(day)) {
                return true;
            }
            return false;
        } catch (ParseException e) {
            return false;
        }
    }
    public static Long dayToTimestamp(String day) throws ParseException {
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date date = formatter.parse(day);
            return Long.valueOf(date.getTime());
        } catch (ParseException e) {
            e.printStackTrace();
            return 0L;
        }
    }
    public static void main(String[] args) {
        System.out.println(getDayOfInterval("2021-11-02", 15));
    }
    public static String getMonthOfDay(String day) {
        return day.substring(0, 7);
    }
    public static String getMonth(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        return sdf.format(date);
    }
    public static String getSpecifiedDayBefore(String specifiedDay, int d) {
        Calendar c = Calendar.getInstance();
        try {
            Date date = new SimpleDateFormat("yyyy-MM-dd").parse(specifiedDay);
            c.setTime(date);
            int day = c.get(5);
            c.set(5, day + d);
            String dayBefore = new SimpleDateFormat("yyyy-MM-dd").format(c.getTime());
            return dayBefore;
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }
    public static String getEndDayOfMonthStr(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(5, cal.getActualMaximum(5));
        String endtDate = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());
        return endtDate;
    }
    public static String getEndDayOfYearStr(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(6, cal.getActualMaximum(6));
        String endtDate = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());
        return endtDate;
    }
    public static List<String> getBetweenDateTime(String begin, String end) {
        String newend;
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        List<String> betweenList = new ArrayList<>();
        try {
            Calendar startDay = Calendar.getInstance();
            startDay.setTime(format.parse(begin));
            startDay.add(5, -1);
            do {
                startDay.add(5, 1);
                Date newDate = startDay.getTime();
                newend = format.format(newDate);
                betweenList.add(newend);
            } while (!end.equals(newend));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return betweenList;
    }
    public static String formatDate(Timestamp timestamp) {
        DateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String s = df2.format((Date) timestamp);
        return s;
    }
    public static String formatDate(Timestamp timestamp, String sdf) {
        DateFormat df2 = new SimpleDateFormat(sdf);
        String s = df2.format((Date) timestamp);
        return s;
    }
    public static String formatDate(Long timestamp) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH-mm-ss");
        Date date = new Date();
        date.setTime(timestamp.longValue());
        String s = simpleDateFormat.format(date);
        return s;
    }
    public static String formatDate2(Long timestamp) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date();
        date.setTime(timestamp.longValue());
        return simpleDateFormat.format(date);
    }
    public static String[] getDateSectionOfMonth(String date) {
        String startTime = date + "-01";
        Calendar cal = Calendar.getInstance();
        cal.setTime(formatDay(startTime, "yyyy-MM-dd"));
        cal.add(2, 1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String endTime = sdf.format(cal.getTime());
        return new String[]{startTime, endTime};
    }
    public static List<String> getDayOfMonth(String date) {
        List<String> time = new ArrayList<>();
        String startTime = date + "-01";
        Calendar cal = Calendar.getInstance();
        cal.setTime(formatDay(startTime, "yyyy-MM-dd"));
        cal.add(2, 1);
        cal.add(5, -1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String endTime = sdf.format(cal.getTime());
        int days = Integer.valueOf(endTime.substring(endTime.length() - 2)).intValue();
        int i = 1;
        while (i <= days) {
            time.add(date + "-" + (i < 10 ? "0" + i : Integer.valueOf(i)));
            i++;
        }
        return time;
    }
    public static List<String> getMonthOfYear(String date) {
        List<String> time = new ArrayList<>();
        String startTime = date + "-01-01";
        Calendar cal = Calendar.getInstance();
        cal.setTime(formatDay(startTime, "yyyy-MM-dd"));
        cal.add(1, 1);
        cal.add(2, -1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String endTime = sdf.format(cal.getTime());
        int days = Integer.valueOf(endTime.substring(5, 7)).intValue();
        int i = 1;
        while (i <= days) {
            time.add(date + "-" + (i < 10 ? "0" + i : Integer.valueOf(i)));
            i++;
        }
        return time;
    }
    public static List<String> getFirstDayofEveryMonth(String date) {
        List<String> time = new ArrayList<>();
        int i = 1;
        while (i <= 12) {
            time.add(date + "-" + (i < 10 ? "0" + i : Integer.valueOf(i)) + "-01");
            i++;
        }
        return time;
    }
    public static int differentDays(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        int day1 = cal1.get(6);
        int day2 = cal2.get(6);
        int year1 = cal1.get(1);
        int year2 = cal2.get(1);
        if (year1 != year2) {
            int timeDistance = 0;
            for (int i = year1; i < year2; i++) {
                if ((i % 4 == 0 && i % 100 != 0) || i % 400 == 0) {
                    timeDistance += 366;
                } else {
                    timeDistance += 365;
                }
            }
            return timeDistance + (day2 - day1);
        }
        System.out.println("判断day2 - day1 : " + (day2 - day1));
        return day2 - day1;
    }
    public static String searchDay(Date date) {
        String searchDay;
        Calendar calendar1 = Calendar.getInstance();
        int nowYear = calendar1.get(1);
        int nowMonth = calendar1.get(2) + 1;
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date);
        int year = calendar2.get(1);
        int month = calendar2.get(2) + 1;
        if (nowYear == year && month == nowMonth) {
            searchDay = getSpecifiedDayBefore(formatDay(new Date()), -1);
        } else {
            searchDay = getEndDayOfMonthStr(date);
        }
        return searchDay;
    }
    public static String searchYearDay(Date date) {
        String searchDay;
        Calendar calendar1 = Calendar.getInstance();
        int nowYear = calendar1.get(1);
        int i = calendar1.get(2) + 1;
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date);
        int year = calendar2.get(1);
        int i2 = calendar2.get(2) + 1;
        if (nowYear == year) {
            searchDay = getSpecifiedDayBefore(formatDay(new Date()), -1);
        } else {
            searchDay = getEndDayOfYearStr(date);
        }
        return searchDay;
    }
    public static boolean isThisMonth(Date date) {
        boolean isThisMonth = false;
        Calendar calendar1 = Calendar.getInstance();
        int nowYear = calendar1.get(1);
        int nowMonth = calendar1.get(2) + 1;
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date);
        int year = calendar2.get(1);
        int month = calendar2.get(2) + 1;
        if (nowYear == year && month == nowMonth) {
            isThisMonth = true;
        }
        return isThisMonth;
    }
    public static String searchTariffTime(Date date) {
        String searchDay;
        Calendar calendar1 = Calendar.getInstance();
        int nowYear = calendar1.get(1);
        int nowMonth = calendar1.get(2) + 1;
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date);
        int year = calendar2.get(1);
        int month = calendar2.get(2) + 1;
        if (nowYear == year && month == nowMonth) {
            searchDay = getSpecifiedDayBefore(formatDay(new Date()), -1);
        } else {
            searchDay = formatDay(addMonth(date, 1));
        }
        return searchDay;
    }
    public static String searchMonthTariffTime(Date date) {
        String searchDay;
        Calendar calendar1 = Calendar.getInstance();
        int nowYear = calendar1.get(1);
        int nowMonth = calendar1.get(2) + 1;
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date);
        int year = calendar2.get(1);
        int month = calendar2.get(2) + 1;
        if (nowYear == year && month == nowMonth) {
            searchDay = formatDay(new Date());
        } else {
            searchDay = formatDay(addMonth(date, 1));
        }
        return searchDay;
    }
    public static String searchTariffYearTime(Date date) {
        String searchDay;
        Calendar calendar1 = Calendar.getInstance();
        int nowYear = calendar1.get(1);
        int i = calendar1.get(2) + 1;
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date);
        int year = calendar2.get(1);
        int i2 = calendar2.get(2) + 1;
        if (nowYear == year) {
            searchDay = getSpecifiedDayBefore(formatDay(new Date()), 0);
        } else {
            searchDay = formatDay(addYear(date, 1));
        }
        return searchDay;
    }
    public static List<String> getDayOfInterval(String date, Integer interval) {
        Integer total;
        List<String> timeInterval = new ArrayList<>();
        if (date.equals(getCurrentDay())) {
            total = Integer.valueOf(Math.toIntExact((System.currentTimeMillis() - formatDay(date).getTime()) / (60000 * interval.intValue())) + 1);
        } else {
            total = Integer.valueOf(1440 / interval.intValue());
        }
        LocalTime localTime = LocalTime.of(0, 0);
        for (int i = 0; i < total.intValue(); i++) {
            timeInterval.add(date + " " + localTime.toString() + ":00");
            localTime = localTime.plusMinutes(interval.intValue());
        }
        return timeInterval;
    }
    public static String getYearOfFinalDay(String year) {
        String currentDay = getCurrentDay();
        if (currentDay.contains(year)) {
            return currentDay;
        }
        return (Integer.valueOf(year).intValue() + 1) + "-01-01";
    }
    public static List<String> getDayofTimeSection(String date) {
        List<String> days = new ArrayList<>();
        try {
            String[] dates = date.split(",");
            days = getBetweenDate(dates[0], dates[1]);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return days;
    }
    public static List<String> getBetweenDate(String startTime, String endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        List<String> list = new ArrayList<>();
        try {
            Date endDate = sdf.parse(endTime);
            Calendar calendar = Calendar.getInstance();
            for (Date startDate = sdf.parse(startTime); startDate.getTime() <= endDate.getTime(); startDate = calendar.getTime()) {
                list.add(sdf.format(startDate));
                calendar.setTime(startDate);
                calendar.add(5, 1);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }
    public static int getDayNum(String date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(formatDay(date));
        int maxDate = cal.getActualMaximum(5);
        return maxDate;
    }
    public static Date getYearFirst(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(1, year);
        Date currYearFirst = calendar.getTime();
        return currYearFirst;
    }
    public static Date getYearLast(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(1, year);
        calendar.roll(6, -1);
        Date currYearLast = calendar.getTime();
        return currYearLast;
    }
    public static boolean isDate(String strDate) {
        Pattern pattern = Pattern.compile("^((\\d{2}(([02468][048])|([13579][26]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|(1[0-9])|(2[0-8]))))))(\\s(((0?[0-9])|([1-2][0-3]))\\:([0-5]?[0-9])((\\s)|(\\:([0-5]?[0-9])))))?$");
        Matcher m = pattern.matcher(strDate);
        if (m.matches()) {
            return true;
        }
        return false;
    }
    public static Calendar getCalendar() {
        return Calendar.getInstance();
    }
    public static Timestamp truncTime(Timestamp timestamp) {
        Calendar calendar = getCalendar();
        calendar.setTime(timestamp);
        calendar.set(11, 0);
        calendar.set(12, 0);
        calendar.set(13, 0);
        calendar.set(14, 0);
        return new Timestamp(calendar.getTimeInMillis());
    }
    public static Timestamp addDate(Timestamp timestamp, int offset) {
        Calendar cal = getCalendar();
        cal.setTime(timestamp);
        cal.add(5, offset);
        return new Timestamp(cal.getTimeInMillis());
    }
    public static Timestamp addDate(Timestamp timestamp, int offset, int field) {
        Calendar cal = getCalendar();
        cal.setTime(timestamp);
        cal.add(field, offset);
        return new Timestamp(cal.getTimeInMillis());
    }
    public static String getFirstDayOnYear(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(6, 1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(cal.getTime());
    }
    public static String isNowMonth(Date date) {
        String searchDay;
        Calendar calendar1 = Calendar.getInstance();
        int nowYear = calendar1.get(1);
        int nowMonth = calendar1.get(2) + 1;
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date);
        int year = calendar2.get(1);
        int month = calendar2.get(2) + 1;
        if (nowYear == year && month == nowMonth) {
            searchDay = getSpecifiedDayBefore(formatDay(new Date()), -1);
        } else {
            searchDay = getLastDayOfMonth(date);
        }
        return searchDay;
    }
    public static String isNowYear(String year) {
        String currentDay = getCurrentDay();
        if (currentDay.contains(year)) {
            return getSpecifiedDayBefore(formatDay(new Date()), -1);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(1, Integer.valueOf(year).intValue());
        calendar.roll(6, -1);
        return formatDay(calendar.getTime());
    }
    public static List<LocalDate> get12MonthDate(String year) {
        List<LocalDate> dates = new ArrayList<>();
        if (year != null && !year.equals("")) {
            String dateStr = year + "-01-01";
            dates.add(LocalDate.parse(dateStr));
            Date date = formatDay(dateStr);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            for (int i = 0; i < LocalDateTime.now().getMonthValue() - 1; i++) {
                calendar.add(2, 1);
                Date time = calendar.getTime();
                String s = formatDay(time);
                dates.add(LocalDate.parse(s));
            }
        }
        return dates;
    }
    public static List<String> getDayListOfMonth(String date) {
        String strValueOf;
        if (date.length() != 7) {
            return null;
        }
        String currentDay = getCurrentDay();
        if (currentDay.contains(date)) {
            String startDate = date + "-01";
            return getBetweenDate(startDate, currentDay);
        }
        List<String> list = new ArrayList<>();
        String ystr = date.substring(0, 4);
        String mstr = date.substring(5, 7);
        Calendar a = Calendar.getInstance();
        a.set(1, Integer.parseInt(ystr));
        a.set(2, Integer.parseInt(mstr) - 1);
        a.set(5, 1);
        a.roll(5, -1);
        int maxDate = a.get(5);
        for (int i = 0; i < maxDate; i++) {
            int d = i + 1;
            if (d < 10) {
                strValueOf = "0" + String.valueOf(d);
            } else {
                strValueOf = String.valueOf(d);
            }
            String dstr = strValueOf;
            String day = ystr + "-" + mstr + "-" + dstr;
            list.add(day);
        }
        return list;
    }
    public static List<Date> getDayOfIntervalDate(String date, Integer interval) {
        Integer total;
        List<String> timeInterval = new ArrayList<>();
        if (date.equals(getCurrentDay())) {
            total = Integer.valueOf(Math.toIntExact((System.currentTimeMillis() - formatDay(date).getTime()) / (60000 * interval.intValue())) + 1);
        } else {
            total = Integer.valueOf(1440 / interval.intValue());
        }
        LocalTime localTime = LocalTime.of(0, 0);
        for (int i = 0; i < total.intValue(); i++) {
            timeInterval.add(date + " " + localTime.toString() + ":00");
            localTime = localTime.plusMinutes(interval.intValue());
        }
        SimpleDateFormat spf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<Date> dateList = new ArrayList<>();
        for (String t : timeInterval) {
            Date parse = null;
            try {
                parse = spf.parse(t);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            dateList.add(parse);
        }
        return dateList;
    }
    public static List<LocalDateTime> getDayListOfMonthDate(String date) {
        String strValueOf;
        if (date.length() != 7) {
            return null;
        }
        List<LocalDateTime> list = new ArrayList<>();
        String ystr = date.substring(0, 4);
        String mstr = date.substring(5, 7);
        Calendar a = Calendar.getInstance();
        a.set(1, Integer.parseInt(ystr));
        a.set(2, Integer.parseInt(mstr) - 1);
        a.set(5, 1);
        a.roll(5, -1);
        int maxDate = a.get(5);
        for (int i = 0; i < maxDate; i++) {
            int d = i + 1;
            if (d < 10) {
                strValueOf = "0" + String.valueOf(d);
            } else {
                strValueOf = String.valueOf(d);
            }
            String dstr = strValueOf;
            String day = ystr + "-" + mstr + "-" + dstr + " 23:59:59";
            LocalDateTime date1 = LocalDateTime.parse(day, df);
            list.add(date1);
        }
        return list;
    }
    public static List<String> getDayAndPlusMinuets(LocalDateTime beginTime, LocalDateTime endTime, int step, DateTimeFormatter dtf) {
        List<String> localDateTimes = new ArrayList<>();
        while (beginTime.isBefore(endTime)) {
            localDateTimes.add(beginTime.format(dtf));
            beginTime = beginTime.plusMinutes(step);
        }
        if (beginTime.isBefore(endTime) || beginTime.equals(endTime)) {
            localDateTimes.add(beginTime.format(dtf));
        }
        return localDateTimes;
    }
    public static List<LocalDateTime> getLastDayMonthList(String year) {
        List<LocalDateTime> lastDayList = new ArrayList<>();
        getCurrentDay();
        Integer m = 12;
        for (int i = 1; i <= m.intValue(); i++) {
            LocalDate localDate = LocalDate.of(Integer.parseInt(year), i, 1);
            int len = localDate.lengthOfMonth();
            if (i < 10) {
                lastDayList.add(LocalDateTime.parse(year + "-0" + i + "-" + len + " 23:59:59", df));
            } else {
                lastDayList.add(LocalDateTime.parse(year + "-" + i + "-" + len + " 23:59:59", df));
            }
        }
        return lastDayList;
    }
    public static String isNowYearMonth(String year) {
        String currentDay = getCurrentDay();
        if (currentDay.contains(year)) {
            return getSpecifiedDayBefore(formatDay(new Date()), -1);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(1, Integer.valueOf(year).intValue());
        calendar.roll(6, -1);
        return formatDay(calendar.getTime());
    }
    public static boolean isGreaterThanNow(String dateStr) {
        String[] date = dateStr.split("-");
        return LocalDate.now().isBefore(LocalDate.of(Integer.parseInt(date[0]), Integer.parseInt(date[1]), Integer.parseInt(date[2])));
    }
    public static String dateToStrLong(Date dateDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(dateDate);
        return dateString;
    }
    public static List<String> getBetweenDates(String start, String end) {
        List<String> result = new ArrayList<>();
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date start_date = sdf.parse(start);
            Date end_date = sdf.parse(end);
            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(start_date);
            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(end_date);
            while (true) {
                if (!tempStart.before(tempEnd) && !tempStart.equals(tempEnd)) {
                    break;
                }
                result.add(sdf.format(tempStart.getTime()));
                tempStart.add(6, 1);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return result;
    }
    public static List<LocalDateTime> get12PeriodTime() {
        List<LocalDateTime> before12TimeList = new ArrayList<>();
        int hour = LocalDateTime.now().getHour() + 1;
        String LocalData = LocalDate.now().toString();
        int curTime = 0;
        if (hour >= 12) {
            curTime = hour - 12;
        }
        for (int i = 0; i < 12; i++) {
            if (curTime < 10) {
                int i2 = curTime;
                curTime++;
                LocalDateTime data = LocalDateTime.parse(LocalData + " 0" + i2 + ":00:00", df);
                before12TimeList.add(data);
            } else {
                int i3 = curTime;
                curTime++;
                LocalDateTime data2 = LocalDateTime.parse(LocalData + " " + i3 + ":00:00", df);
                before12TimeList.add(data2);
            }
        }
        return before12TimeList;
    }
}
