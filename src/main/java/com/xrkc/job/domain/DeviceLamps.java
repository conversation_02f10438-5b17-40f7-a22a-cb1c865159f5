package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
@TableName("device_lamps")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/DeviceLamps.class */
public class DeviceLamps implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId("id")
    private Long id;
    @TableField("lamps_id")
    private Long lampsId;
    @TableField("layer_id")
    private String layerId;
    @TableField("layer_height")
    private Integer layerHeight;
    @TableField("longitude")
    private BigDecimal longitude;
    @TableField("latitude")
    private BigDecimal latitude;
    @TableField("lamps_status")
    private Integer lampsStatus;
    @TableField("lamps_luminance")
    private Integer lampsLuminance;
    @TableField("lamps_version")
    private Integer lampsVersion;
    @TableField("lamps_voltage")
    private BigDecimal lampsVoltage;
    @TableField("lamps_electricity")
    private BigDecimal lampsElectricity;
    @TableField("lamps_kw")
    private BigDecimal lampsKw;
    @TableField("heart_time")
    private LocalDateTime heartTime;
    @TableField("lamps_group")
    private Integer lampsGroup;
    @TableField("inductive_enable")
    private Integer inductiveEnable;
    @TableField("inductive_luminance")
    private Integer inductiveLuminance;
    @TableField("inductive_distance")
    private Integer inductiveDistance;
    @TableField("inductive_off_delay_time")
    private Integer inductiveOffDelayTime;
    @TableField("push_lamps_group")
    private Integer pushLampsGroup;
    @TableField("create_time")
    private Date createTime;
    @TableField("create_by")
    private String createBy;
    @TableField("update_time")
    private Date updateTime;
    @TableField("update_by")
    private String updateBy;
    @TableField("remark")
    private String remark;
    public void setId(Long id) {
        this.id = id;
    }
    public void setLampsId(Long lampsId) {
        this.lampsId = lampsId;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
    public void setLayerHeight(Integer layerHeight) {
        this.layerHeight = layerHeight;
    }
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    public void setLampsStatus(Integer lampsStatus) {
        this.lampsStatus = lampsStatus;
    }
    public void setLampsLuminance(Integer lampsLuminance) {
        this.lampsLuminance = lampsLuminance;
    }
    public void setLampsVersion(Integer lampsVersion) {
        this.lampsVersion = lampsVersion;
    }
    public void setLampsVoltage(BigDecimal lampsVoltage) {
        this.lampsVoltage = lampsVoltage;
    }
    public void setLampsElectricity(BigDecimal lampsElectricity) {
        this.lampsElectricity = lampsElectricity;
    }
    public void setLampsKw(BigDecimal lampsKw) {
        this.lampsKw = lampsKw;
    }
    public void setHeartTime(LocalDateTime heartTime) {
        this.heartTime = heartTime;
    }
    public void setLampsGroup(Integer lampsGroup) {
        this.lampsGroup = lampsGroup;
    }
    public void setInductiveEnable(Integer inductiveEnable) {
        this.inductiveEnable = inductiveEnable;
    }
    public void setInductiveLuminance(Integer inductiveLuminance) {
        this.inductiveLuminance = inductiveLuminance;
    }
    public void setInductiveDistance(Integer inductiveDistance) {
        this.inductiveDistance = inductiveDistance;
    }
    public void setInductiveOffDelayTime(Integer inductiveOffDelayTime) {
        this.inductiveOffDelayTime = inductiveOffDelayTime;
    }
    public void setPushLampsGroup(Integer pushLampsGroup) {
        this.pushLampsGroup = pushLampsGroup;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }
    public String toString() {
        return "DeviceLamps(id=" + getId() + ", lampsId=" + getLampsId() + ", layerId=" + getLayerId() + ", layerHeight=" + getLayerHeight() + ", longitude=" + getLongitude() + ", latitude=" + getLatitude() + ", lampsStatus=" + getLampsStatus() + ", lampsLuminance=" + getLampsLuminance() + ", lampsVersion=" + getLampsVersion() + ", lampsVoltage=" + getLampsVoltage() + ", lampsElectricity=" + getLampsElectricity() + ", lampsKw=" + getLampsKw() + ", heartTime=" + getHeartTime() + ", lampsGroup=" + getLampsGroup() + ", inductiveEnable=" + getInductiveEnable() + ", inductiveLuminance=" + getInductiveLuminance() + ", inductiveDistance=" + getInductiveDistance() + ", inductiveOffDelayTime=" + getInductiveOffDelayTime() + ", pushLampsGroup=" + getPushLampsGroup() + ", createTime=" + getCreateTime() + ", createBy=" + getCreateBy() + ", updateTime=" + getUpdateTime() + ", updateBy=" + getUpdateBy() + ", remark=" + getRemark() + StringPool.RIGHT_BRACKET;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof DeviceLamps)) {
            return false;
        }
        DeviceLamps other = (DeviceLamps) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$id = getId();
        Object other$id = other.getId();
        if (this$id == null) {
            if (other$id != null) {
                return false;
            }
        } else if (!this$id.equals(other$id)) {
            return false;
        }
        Object this$lampsId = getLampsId();
        Object other$lampsId = other.getLampsId();
        if (this$lampsId == null) {
            if (other$lampsId != null) {
                return false;
            }
        } else if (!this$lampsId.equals(other$lampsId)) {
            return false;
        }
        Object this$layerHeight = getLayerHeight();
        Object other$layerHeight = other.getLayerHeight();
        if (this$layerHeight == null) {
            if (other$layerHeight != null) {
                return false;
            }
        } else if (!this$layerHeight.equals(other$layerHeight)) {
            return false;
        }
        Object this$lampsStatus = getLampsStatus();
        Object other$lampsStatus = other.getLampsStatus();
        if (this$lampsStatus == null) {
            if (other$lampsStatus != null) {
                return false;
            }
        } else if (!this$lampsStatus.equals(other$lampsStatus)) {
            return false;
        }
        Object this$lampsLuminance = getLampsLuminance();
        Object other$lampsLuminance = other.getLampsLuminance();
        if (this$lampsLuminance == null) {
            if (other$lampsLuminance != null) {
                return false;
            }
        } else if (!this$lampsLuminance.equals(other$lampsLuminance)) {
            return false;
        }
        Object this$lampsVersion = getLampsVersion();
        Object other$lampsVersion = other.getLampsVersion();
        if (this$lampsVersion == null) {
            if (other$lampsVersion != null) {
                return false;
            }
        } else if (!this$lampsVersion.equals(other$lampsVersion)) {
            return false;
        }
        Object this$lampsGroup = getLampsGroup();
        Object other$lampsGroup = other.getLampsGroup();
        if (this$lampsGroup == null) {
            if (other$lampsGroup != null) {
                return false;
            }
        } else if (!this$lampsGroup.equals(other$lampsGroup)) {
            return false;
        }
        Object this$inductiveEnable = getInductiveEnable();
        Object other$inductiveEnable = other.getInductiveEnable();
        if (this$inductiveEnable == null) {
            if (other$inductiveEnable != null) {
                return false;
            }
        } else if (!this$inductiveEnable.equals(other$inductiveEnable)) {
            return false;
        }
        Object this$inductiveLuminance = getInductiveLuminance();
        Object other$inductiveLuminance = other.getInductiveLuminance();
        if (this$inductiveLuminance == null) {
            if (other$inductiveLuminance != null) {
                return false;
            }
        } else if (!this$inductiveLuminance.equals(other$inductiveLuminance)) {
            return false;
        }
        Object this$inductiveDistance = getInductiveDistance();
        Object other$inductiveDistance = other.getInductiveDistance();
        if (this$inductiveDistance == null) {
            if (other$inductiveDistance != null) {
                return false;
            }
        } else if (!this$inductiveDistance.equals(other$inductiveDistance)) {
            return false;
        }
        Object this$inductiveOffDelayTime = getInductiveOffDelayTime();
        Object other$inductiveOffDelayTime = other.getInductiveOffDelayTime();
        if (this$inductiveOffDelayTime == null) {
            if (other$inductiveOffDelayTime != null) {
                return false;
            }
        } else if (!this$inductiveOffDelayTime.equals(other$inductiveOffDelayTime)) {
            return false;
        }
        Object this$pushLampsGroup = getPushLampsGroup();
        Object other$pushLampsGroup = other.getPushLampsGroup();
        if (this$pushLampsGroup == null) {
            if (other$pushLampsGroup != null) {
                return false;
            }
        } else if (!this$pushLampsGroup.equals(other$pushLampsGroup)) {
            return false;
        }
        Object this$layerId = getLayerId();
        Object other$layerId = other.getLayerId();
        if (this$layerId == null) {
            if (other$layerId != null) {
                return false;
            }
        } else if (!this$layerId.equals(other$layerId)) {
            return false;
        }
        Object this$longitude = getLongitude();
        Object other$longitude = other.getLongitude();
        if (this$longitude == null) {
            if (other$longitude != null) {
                return false;
            }
        } else if (!this$longitude.equals(other$longitude)) {
            return false;
        }
        Object this$latitude = getLatitude();
        Object other$latitude = other.getLatitude();
        if (this$latitude == null) {
            if (other$latitude != null) {
                return false;
            }
        } else if (!this$latitude.equals(other$latitude)) {
            return false;
        }
        Object this$lampsVoltage = getLampsVoltage();
        Object other$lampsVoltage = other.getLampsVoltage();
        if (this$lampsVoltage == null) {
            if (other$lampsVoltage != null) {
                return false;
            }
        } else if (!this$lampsVoltage.equals(other$lampsVoltage)) {
            return false;
        }
        Object this$lampsElectricity = getLampsElectricity();
        Object other$lampsElectricity = other.getLampsElectricity();
        if (this$lampsElectricity == null) {
            if (other$lampsElectricity != null) {
                return false;
            }
        } else if (!this$lampsElectricity.equals(other$lampsElectricity)) {
            return false;
        }
        Object this$lampsKw = getLampsKw();
        Object other$lampsKw = other.getLampsKw();
        if (this$lampsKw == null) {
            if (other$lampsKw != null) {
                return false;
            }
        } else if (!this$lampsKw.equals(other$lampsKw)) {
            return false;
        }
        Object this$heartTime = getHeartTime();
        Object other$heartTime = other.getHeartTime();
        if (this$heartTime == null) {
            if (other$heartTime != null) {
                return false;
            }
        } else if (!this$heartTime.equals(other$heartTime)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        if (this$createTime == null) {
            if (other$createTime != null) {
                return false;
            }
        } else if (!this$createTime.equals(other$createTime)) {
            return false;
        }
        Object this$createBy = getCreateBy();
        Object other$createBy = other.getCreateBy();
        if (this$createBy == null) {
            if (other$createBy != null) {
                return false;
            }
        } else if (!this$createBy.equals(other$createBy)) {
            return false;
        }
        Object this$updateTime = getUpdateTime();
        Object other$updateTime = other.getUpdateTime();
        if (this$updateTime == null) {
            if (other$updateTime != null) {
                return false;
            }
        } else if (!this$updateTime.equals(other$updateTime)) {
            return false;
        }
        Object this$updateBy = getUpdateBy();
        Object other$updateBy = other.getUpdateBy();
        if (this$updateBy == null) {
            if (other$updateBy != null) {
                return false;
            }
        } else if (!this$updateBy.equals(other$updateBy)) {
            return false;
        }
        Object this$remark = getRemark();
        Object other$remark = other.getRemark();
        return this$remark == null ? other$remark == null : this$remark.equals(other$remark);
    }
    protected boolean canEqual(Object other) {
        return other instanceof DeviceLamps;
    }
    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $lampsId = getLampsId();
        int result2 = (result * 59) + ($lampsId == null ? 43 : $lampsId.hashCode());
        Object $layerHeight = getLayerHeight();
        int result3 = (result2 * 59) + ($layerHeight == null ? 43 : $layerHeight.hashCode());
        Object $lampsStatus = getLampsStatus();
        int result4 = (result3 * 59) + ($lampsStatus == null ? 43 : $lampsStatus.hashCode());
        Object $lampsLuminance = getLampsLuminance();
        int result5 = (result4 * 59) + ($lampsLuminance == null ? 43 : $lampsLuminance.hashCode());
        Object $lampsVersion = getLampsVersion();
        int result6 = (result5 * 59) + ($lampsVersion == null ? 43 : $lampsVersion.hashCode());
        Object $lampsGroup = getLampsGroup();
        int result7 = (result6 * 59) + ($lampsGroup == null ? 43 : $lampsGroup.hashCode());
        Object $inductiveEnable = getInductiveEnable();
        int result8 = (result7 * 59) + ($inductiveEnable == null ? 43 : $inductiveEnable.hashCode());
        Object $inductiveLuminance = getInductiveLuminance();
        int result9 = (result8 * 59) + ($inductiveLuminance == null ? 43 : $inductiveLuminance.hashCode());
        Object $inductiveDistance = getInductiveDistance();
        int result10 = (result9 * 59) + ($inductiveDistance == null ? 43 : $inductiveDistance.hashCode());
        Object $inductiveOffDelayTime = getInductiveOffDelayTime();
        int result11 = (result10 * 59) + ($inductiveOffDelayTime == null ? 43 : $inductiveOffDelayTime.hashCode());
        Object $pushLampsGroup = getPushLampsGroup();
        int result12 = (result11 * 59) + ($pushLampsGroup == null ? 43 : $pushLampsGroup.hashCode());
        Object $layerId = getLayerId();
        int result13 = (result12 * 59) + ($layerId == null ? 43 : $layerId.hashCode());
        Object $longitude = getLongitude();
        int result14 = (result13 * 59) + ($longitude == null ? 43 : $longitude.hashCode());
        Object $latitude = getLatitude();
        int result15 = (result14 * 59) + ($latitude == null ? 43 : $latitude.hashCode());
        Object $lampsVoltage = getLampsVoltage();
        int result16 = (result15 * 59) + ($lampsVoltage == null ? 43 : $lampsVoltage.hashCode());
        Object $lampsElectricity = getLampsElectricity();
        int result17 = (result16 * 59) + ($lampsElectricity == null ? 43 : $lampsElectricity.hashCode());
        Object $lampsKw = getLampsKw();
        int result18 = (result17 * 59) + ($lampsKw == null ? 43 : $lampsKw.hashCode());
        Object $heartTime = getHeartTime();
        int result19 = (result18 * 59) + ($heartTime == null ? 43 : $heartTime.hashCode());
        Object $createTime = getCreateTime();
        int result20 = (result19 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $createBy = getCreateBy();
        int result21 = (result20 * 59) + ($createBy == null ? 43 : $createBy.hashCode());
        Object $updateTime = getUpdateTime();
        int result22 = (result21 * 59) + ($updateTime == null ? 43 : $updateTime.hashCode());
        Object $updateBy = getUpdateBy();
        int result23 = (result22 * 59) + ($updateBy == null ? 43 : $updateBy.hashCode());
        Object $remark = getRemark();
        return (result23 * 59) + ($remark == null ? 43 : $remark.hashCode());
    }
    public Long getId() {
        return this.id;
    }
    public Long getLampsId() {
        return this.lampsId;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public Integer getLayerHeight() {
        return this.layerHeight;
    }
    public BigDecimal getLongitude() {
        return this.longitude;
    }
    public BigDecimal getLatitude() {
        return this.latitude;
    }
    public Integer getLampsStatus() {
        return this.lampsStatus;
    }
    public Integer getLampsLuminance() {
        return this.lampsLuminance;
    }
    public Integer getLampsVersion() {
        return this.lampsVersion;
    }
    public BigDecimal getLampsVoltage() {
        return this.lampsVoltage;
    }
    public BigDecimal getLampsElectricity() {
        return this.lampsElectricity;
    }
    public BigDecimal getLampsKw() {
        return this.lampsKw;
    }
    public LocalDateTime getHeartTime() {
        return this.heartTime;
    }
    public Integer getLampsGroup() {
        return this.lampsGroup;
    }
    public Integer getInductiveEnable() {
        return this.inductiveEnable;
    }
    public Integer getInductiveLuminance() {
        return this.inductiveLuminance;
    }
    public Integer getInductiveDistance() {
        return this.inductiveDistance;
    }
    public Integer getInductiveOffDelayTime() {
        return this.inductiveOffDelayTime;
    }
    public Integer getPushLampsGroup() {
        return this.pushLampsGroup;
    }
    public Date getCreateTime() {
        return this.createTime;
    }
    public String getCreateBy() {
        return this.createBy;
    }
    public Date getUpdateTime() {
        return this.updateTime;
    }
    public String getUpdateBy() {
        return this.updateBy;
    }
    public String getRemark() {
        return this.remark;
    }
}
