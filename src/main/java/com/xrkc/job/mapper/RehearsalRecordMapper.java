package com.xrkc.job.mapper;
import com.xrkc.core.mybatisplus.BaseMapperX;
import com.xrkc.job.domain.PositionHistory;
import com.xrkc.job.domain.RehearsalRecord;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/RehearsalRecordMapper.class */
public interface RehearsalRecordMapper extends BaseMapperX<RehearsalRecord> {
    List<RehearsalRecord> selectUnCompletePlan();
    List<RehearsalRecord> selectList(Map<String, Object> map);
    Integer batchUpdate(RehearsalRecord rehearsalRecord);
    List<PositionHistory> selectByLayerId(RehearsalRecord rehearsalRecord);
}
