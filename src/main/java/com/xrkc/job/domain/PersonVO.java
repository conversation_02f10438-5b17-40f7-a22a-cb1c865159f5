package com.xrkc.job.domain;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/PersonVO.class */
public class PersonVO implements Serializable {
    private static final long serialVersionUID = 1;
    private Long personId;
    private String realName;
    @JsonSerialize(using = ToStringSerializer.class)
    public Long getPersonId() {
        return this.personId;
    }
    public void setPersonId(Long personId) {
        this.personId = personId;
    }
    public String getRealName() {
        return this.realName;
    }
    public void setRealName(String realName) {
        this.realName = realName;
    }
}
