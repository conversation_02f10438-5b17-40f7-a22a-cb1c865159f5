package com.xrkc.job.domain;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/PositionHistory.class */
public class PositionHistory implements Serializable {
    private static final long serialVersionUID = 1;
    private Long id;
    private LocalDateTime acceptTime;
    private Integer beaconId;
    private Double distance;
    private Long cardId;
    private String cardType;
    private BigDecimal longitude;
    private BigDecimal latitude;
    private String layerId;
    private Integer layerHeight;
    private LocalDateTime createTime;
    private Long personId;
    private String personType;
    private String staffType;
    private String realName;
    private Long deptId;
    private Long postId;
    private Long contractorId;
    public LocalDateTime getAcceptTime() {
        return this.acceptTime;
    }
    public void setAcceptTime(LocalDateTime acceptTime) {
        this.acceptTime = acceptTime;
    }
    public Integer getBeaconId() {
        return this.beaconId;
    }
    public void setBeaconId(Integer beaconId) {
        this.beaconId = beaconId;
    }
    public Double getDistance() {
        return this.distance;
    }
    public void setDistance(Double distance) {
        this.distance = distance;
    }
    public Long getCardId() {
        return this.cardId;
    }
    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }
    public String getCardType() {
        return this.cardType;
    }
    public void setCardType(String cardType) {
        this.cardType = cardType;
    }
    public BigDecimal getLongitude() {
        return this.longitude;
    }
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    public BigDecimal getLatitude() {
        return this.latitude;
    }
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
    public Integer getLayerHeight() {
        return this.layerHeight;
    }
    public void setLayerHeight(Integer layerHeight) {
        this.layerHeight = layerHeight;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public Long getId() {
        return this.id;
    }
    public void setId(Long id) {
        this.id = id;
    }
    public Long getPersonId() {
        return this.personId;
    }
    public void setPersonId(Long personId) {
        this.personId = personId;
    }
    public String getPersonType() {
        return this.personType;
    }
    public void setPersonType(String personType) {
        this.personType = personType;
    }
    public String getStaffType() {
        return this.staffType;
    }
    public void setStaffType(String staffType) {
        this.staffType = staffType;
    }
    public String getRealName() {
        return this.realName;
    }
    public void setRealName(String realName) {
        this.realName = realName;
    }
    public Long getDeptId() {
        return this.deptId;
    }
    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
    public Long getPostId() {
        return this.postId;
    }
    public void setPostId(Long postId) {
        this.postId = postId;
    }
    public Long getContractorId() {
        return this.contractorId;
    }
    public void setContractorId(Long contractorId) {
        this.contractorId = contractorId;
    }
}
