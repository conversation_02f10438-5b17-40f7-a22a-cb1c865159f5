package com.xrkc.job.mapper;
import com.xrkc.job.domain.InspectRecord;
import com.xrkc.job.domain.InspectRecordItem;
import com.xrkc.job.domain.InspectRecordItemParam;
import com.xrkc.job.domain.InspectRecordLocation;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/InspectRecordMapper.class */
public interface InspectRecordMapper {
    List<InspectRecord> selectEverydayInspectTaskList(String str);
    List<InspectRecord> selectNotEverydayInspectTaskList(String str);
    int existInspectTask(InspectRecord inspectRecord);
    int insertInspectRecord(InspectRecord inspectRecord);
    List<InspectRecordLocation> selectInspectLocationList(List<Long> list);
    int batchInsertLocation(List<InspectRecordLocation> list);
    List<InspectRecordItem> selectInspectItemList(List<Long> list);
    int batchInsertItem(List<InspectRecordItem> list);
    int batchInsertItemParam(List<InspectRecordItemParam> list);
    List<InspectRecord> selectInspectRecord();
    int updateInspectRecord(InspectRecord inspectRecord);
    List<InspectRecord> selectYesterdayRecord(@Param("startTime") LocalDate localDate, @Param("endTime") LocalDateTime localDateTime);
    List<InspectRecordItem> selectInspectItem(@Param("locationId") Long l);
    List<InspectRecordItemParam> selectInspectItemParam(@Param("itemId") Long l);
    String findTeamBypersonId(@Param("personId") Long l);
    Set<String> findRoadLeaderNames();
    int existTeamPerson();
    Set<String> findLocationLeaderNames();
}
