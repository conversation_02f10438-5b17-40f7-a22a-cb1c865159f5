package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
@TableName("device_building")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/DeviceBuilding.class */
public class DeviceBuilding implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId("building_id")
    private Long buildingId;
    @TableField("building_name")
    private String buildingName;
    @TableField("building_enable")
    private String buildingEnable;
    @TableField("layer_id")
    private String layerId;
    @TableField("layer_height")
    private Integer layerHeight;
    @TableField("longitude")
    private BigDecimal longitude;
    @TableField("latitude")
    private BigDecimal latitude;
    @TableField("create_time")
    private Date createTime;
    @TableField("create_by")
    private String createBy;
    @TableField("update_time")
    private Date updateTime;
    @TableField("update_by")
    private String updateBy;
    @TableField("remark")
    private String remark;
    @TableField("rail_scope")
    private String railScope;
    @TableField("draw_type")
    private Integer drawType;
    @TableField("visible")
    private String visible;
    public DeviceBuilding setBuildingId(Long buildingId) {
        this.buildingId = buildingId;
        return this;
    }
    public DeviceBuilding setBuildingName(String buildingName) {
        this.buildingName = buildingName;
        return this;
    }
    public DeviceBuilding setBuildingEnable(String buildingEnable) {
        this.buildingEnable = buildingEnable;
        return this;
    }
    public DeviceBuilding setLayerId(String layerId) {
        this.layerId = layerId;
        return this;
    }
    public DeviceBuilding setLayerHeight(Integer layerHeight) {
        this.layerHeight = layerHeight;
        return this;
    }
    public DeviceBuilding setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
        return this;
    }
    public DeviceBuilding setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
        return this;
    }
    public DeviceBuilding setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }
    public DeviceBuilding setCreateBy(String createBy) {
        this.createBy = createBy;
        return this;
    }
    public DeviceBuilding setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }
    public DeviceBuilding setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
        return this;
    }
    public DeviceBuilding setRemark(String remark) {
        this.remark = remark;
        return this;
    }
    public DeviceBuilding setRailScope(String railScope) {
        this.railScope = railScope;
        return this;
    }
    public DeviceBuilding setDrawType(Integer drawType) {
        this.drawType = drawType;
        return this;
    }
    public DeviceBuilding setVisible(String visible) {
        this.visible = visible;
        return this;
    }
    public String toString() {
        return "DeviceBuilding(buildingId=" + getBuildingId() + ", buildingName=" + getBuildingName() + ", buildingEnable=" + getBuildingEnable() + ", layerId=" + getLayerId() + ", layerHeight=" + getLayerHeight() + ", longitude=" + getLongitude() + ", latitude=" + getLatitude() + ", createTime=" + getCreateTime() + ", createBy=" + getCreateBy() + ", updateTime=" + getUpdateTime() + ", updateBy=" + getUpdateBy() + ", remark=" + getRemark() + ", railScope=" + getRailScope() + ", drawType=" + getDrawType() + ", visible=" + getVisible() + StringPool.RIGHT_BRACKET;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof DeviceBuilding)) {
            return false;
        }
        DeviceBuilding other = (DeviceBuilding) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$buildingId = getBuildingId();
        Object other$buildingId = other.getBuildingId();
        if (this$buildingId == null) {
            if (other$buildingId != null) {
                return false;
            }
        } else if (!this$buildingId.equals(other$buildingId)) {
            return false;
        }
        Object this$layerHeight = getLayerHeight();
        Object other$layerHeight = other.getLayerHeight();
        if (this$layerHeight == null) {
            if (other$layerHeight != null) {
                return false;
            }
        } else if (!this$layerHeight.equals(other$layerHeight)) {
            return false;
        }
        Object this$drawType = getDrawType();
        Object other$drawType = other.getDrawType();
        if (this$drawType == null) {
            if (other$drawType != null) {
                return false;
            }
        } else if (!this$drawType.equals(other$drawType)) {
            return false;
        }
        Object this$buildingName = getBuildingName();
        Object other$buildingName = other.getBuildingName();
        if (this$buildingName == null) {
            if (other$buildingName != null) {
                return false;
            }
        } else if (!this$buildingName.equals(other$buildingName)) {
            return false;
        }
        Object this$buildingEnable = getBuildingEnable();
        Object other$buildingEnable = other.getBuildingEnable();
        if (this$buildingEnable == null) {
            if (other$buildingEnable != null) {
                return false;
            }
        } else if (!this$buildingEnable.equals(other$buildingEnable)) {
            return false;
        }
        Object this$layerId = getLayerId();
        Object other$layerId = other.getLayerId();
        if (this$layerId == null) {
            if (other$layerId != null) {
                return false;
            }
        } else if (!this$layerId.equals(other$layerId)) {
            return false;
        }
        Object this$longitude = getLongitude();
        Object other$longitude = other.getLongitude();
        if (this$longitude == null) {
            if (other$longitude != null) {
                return false;
            }
        } else if (!this$longitude.equals(other$longitude)) {
            return false;
        }
        Object this$latitude = getLatitude();
        Object other$latitude = other.getLatitude();
        if (this$latitude == null) {
            if (other$latitude != null) {
                return false;
            }
        } else if (!this$latitude.equals(other$latitude)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        if (this$createTime == null) {
            if (other$createTime != null) {
                return false;
            }
        } else if (!this$createTime.equals(other$createTime)) {
            return false;
        }
        Object this$createBy = getCreateBy();
        Object other$createBy = other.getCreateBy();
        if (this$createBy == null) {
            if (other$createBy != null) {
                return false;
            }
        } else if (!this$createBy.equals(other$createBy)) {
            return false;
        }
        Object this$updateTime = getUpdateTime();
        Object other$updateTime = other.getUpdateTime();
        if (this$updateTime == null) {
            if (other$updateTime != null) {
                return false;
            }
        } else if (!this$updateTime.equals(other$updateTime)) {
            return false;
        }
        Object this$updateBy = getUpdateBy();
        Object other$updateBy = other.getUpdateBy();
        if (this$updateBy == null) {
            if (other$updateBy != null) {
                return false;
            }
        } else if (!this$updateBy.equals(other$updateBy)) {
            return false;
        }
        Object this$remark = getRemark();
        Object other$remark = other.getRemark();
        if (this$remark == null) {
            if (other$remark != null) {
                return false;
            }
        } else if (!this$remark.equals(other$remark)) {
            return false;
        }
        Object this$railScope = getRailScope();
        Object other$railScope = other.getRailScope();
        if (this$railScope == null) {
            if (other$railScope != null) {
                return false;
            }
        } else if (!this$railScope.equals(other$railScope)) {
            return false;
        }
        Object this$visible = getVisible();
        Object other$visible = other.getVisible();
        return this$visible == null ? other$visible == null : this$visible.equals(other$visible);
    }
    protected boolean canEqual(Object other) {
        return other instanceof DeviceBuilding;
    }
    public int hashCode() {
        Object $buildingId = getBuildingId();
        int result = (1 * 59) + ($buildingId == null ? 43 : $buildingId.hashCode());
        Object $layerHeight = getLayerHeight();
        int result2 = (result * 59) + ($layerHeight == null ? 43 : $layerHeight.hashCode());
        Object $drawType = getDrawType();
        int result3 = (result2 * 59) + ($drawType == null ? 43 : $drawType.hashCode());
        Object $buildingName = getBuildingName();
        int result4 = (result3 * 59) + ($buildingName == null ? 43 : $buildingName.hashCode());
        Object $buildingEnable = getBuildingEnable();
        int result5 = (result4 * 59) + ($buildingEnable == null ? 43 : $buildingEnable.hashCode());
        Object $layerId = getLayerId();
        int result6 = (result5 * 59) + ($layerId == null ? 43 : $layerId.hashCode());
        Object $longitude = getLongitude();
        int result7 = (result6 * 59) + ($longitude == null ? 43 : $longitude.hashCode());
        Object $latitude = getLatitude();
        int result8 = (result7 * 59) + ($latitude == null ? 43 : $latitude.hashCode());
        Object $createTime = getCreateTime();
        int result9 = (result8 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $createBy = getCreateBy();
        int result10 = (result9 * 59) + ($createBy == null ? 43 : $createBy.hashCode());
        Object $updateTime = getUpdateTime();
        int result11 = (result10 * 59) + ($updateTime == null ? 43 : $updateTime.hashCode());
        Object $updateBy = getUpdateBy();
        int result12 = (result11 * 59) + ($updateBy == null ? 43 : $updateBy.hashCode());
        Object $remark = getRemark();
        int result13 = (result12 * 59) + ($remark == null ? 43 : $remark.hashCode());
        Object $railScope = getRailScope();
        int result14 = (result13 * 59) + ($railScope == null ? 43 : $railScope.hashCode());
        Object $visible = getVisible();
        return (result14 * 59) + ($visible == null ? 43 : $visible.hashCode());
    }
    public Long getBuildingId() {
        return this.buildingId;
    }
    public String getBuildingName() {
        return this.buildingName;
    }
    public String getBuildingEnable() {
        return this.buildingEnable;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public Integer getLayerHeight() {
        return this.layerHeight;
    }
    public BigDecimal getLongitude() {
        return this.longitude;
    }
    public BigDecimal getLatitude() {
        return this.latitude;
    }
    public Date getCreateTime() {
        return this.createTime;
    }
    public String getCreateBy() {
        return this.createBy;
    }
    public Date getUpdateTime() {
        return this.updateTime;
    }
    public String getUpdateBy() {
        return this.updateBy;
    }
    public String getRemark() {
        return this.remark;
    }
    public String getRailScope() {
        return this.railScope;
    }
    public Integer getDrawType() {
        return this.drawType;
    }
    public String getVisible() {
        return this.visible;
    }
}
