package com.xrkc.job.domain;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/FaceDispenserPersonVO.class */
public class FaceDispenserPersonVO {
    private Long personId;
    private String employeeNumber;
    private String realName;
    private String personPhoto;
    private String base64;
    private String nation;
    public void setPersonId(Long personId) {
        this.personId = personId;
    }
    public void setEmployeeNumber(String employeeNumber) {
        this.employeeNumber = employeeNumber;
    }
    public void setRealName(String realName) {
        this.realName = realName;
    }
    public void setPersonPhoto(String personPhoto) {
        this.personPhoto = personPhoto;
    }
    public void setBase64(String base64) {
        this.base64 = base64;
    }
    public void setNation(String nation) {
        this.nation = nation;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FaceDispenserPersonVO)) {
            return false;
        }
        FaceDispenserPersonVO other = (FaceDispenserPersonVO) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$personId = getPersonId();
        Object other$personId = other.getPersonId();
        if (this$personId == null) {
            if (other$personId != null) {
                return false;
            }
        } else if (!this$personId.equals(other$personId)) {
            return false;
        }
        Object this$employeeNumber = getEmployeeNumber();
        Object other$employeeNumber = other.getEmployeeNumber();
        if (this$employeeNumber == null) {
            if (other$employeeNumber != null) {
                return false;
            }
        } else if (!this$employeeNumber.equals(other$employeeNumber)) {
            return false;
        }
        Object this$realName = getRealName();
        Object other$realName = other.getRealName();
        if (this$realName == null) {
            if (other$realName != null) {
                return false;
            }
        } else if (!this$realName.equals(other$realName)) {
            return false;
        }
        Object this$personPhoto = getPersonPhoto();
        Object other$personPhoto = other.getPersonPhoto();
        if (this$personPhoto == null) {
            if (other$personPhoto != null) {
                return false;
            }
        } else if (!this$personPhoto.equals(other$personPhoto)) {
            return false;
        }
        Object this$base64 = getBase64();
        Object other$base64 = other.getBase64();
        if (this$base64 == null) {
            if (other$base64 != null) {
                return false;
            }
        } else if (!this$base64.equals(other$base64)) {
            return false;
        }
        Object this$nation = getNation();
        Object other$nation = other.getNation();
        return this$nation == null ? other$nation == null : this$nation.equals(other$nation);
    }
    protected boolean canEqual(Object other) {
        return other instanceof FaceDispenserPersonVO;
    }
    public int hashCode() {
        Object $personId = getPersonId();
        int result = (1 * 59) + ($personId == null ? 43 : $personId.hashCode());
        Object $employeeNumber = getEmployeeNumber();
        int result2 = (result * 59) + ($employeeNumber == null ? 43 : $employeeNumber.hashCode());
        Object $realName = getRealName();
        int result3 = (result2 * 59) + ($realName == null ? 43 : $realName.hashCode());
        Object $personPhoto = getPersonPhoto();
        int result4 = (result3 * 59) + ($personPhoto == null ? 43 : $personPhoto.hashCode());
        Object $base64 = getBase64();
        int result5 = (result4 * 59) + ($base64 == null ? 43 : $base64.hashCode());
        Object $nation = getNation();
        return (result5 * 59) + ($nation == null ? 43 : $nation.hashCode());
    }
    public String toString() {
        return "FaceDispenserPersonVO(personId=" + getPersonId() + ", employeeNumber=" + getEmployeeNumber() + ", realName=" + getRealName() + ", personPhoto=" + getPersonPhoto() + ", base64=" + getBase64() + ", nation=" + getNation() + StringPool.RIGHT_BRACKET;
    }
    public Long getPersonId() {
        return this.personId;
    }
    public String getEmployeeNumber() {
        return this.employeeNumber;
    }
    public String getRealName() {
        return this.realName;
    }
    public String getPersonPhoto() {
        return this.personPhoto;
    }
    public String getBase64() {
        return this.base64;
    }
    public String getNation() {
        return this.nation;
    }
}
