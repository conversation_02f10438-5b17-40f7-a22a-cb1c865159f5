package com.xrkc.job.domain;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/SystemRoleFacilityVO.class */
public class SystemRoleFacilityVO implements Serializable {
    private static final long serialVersionUID = 1;
    private Long facilityId;
    private String layerId;
    private Long railId;
    private String railScope;
    @JsonSerialize(using = ToStringSerializer.class)
    public Long getFacilityId() {
        return this.facilityId;
    }
    public void setFacilityId(Long facilityId) {
        this.facilityId = facilityId;
    }
    @JsonSerialize(using = ToStringSerializer.class)
    public Long getRailId() {
        return this.railId;
    }
    public void setRailId(Long railId) {
        this.railId = railId;
    }
    public String getRailScope() {
        return this.railScope;
    }
    public void setRailScope(String railScope) {
        this.railScope = railScope;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
}
