package com.xrkc.job.vo;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/vo/PushCardSenderVO.class */
public class PushCardSenderVO {
    private String deviceSn;
    private String deviceName;
    private Integer aimsCount;
    private List<PushCardSenderAimsVO> aimsVOList;
    public void setDeviceSn(String deviceSn) {
        this.deviceSn = deviceSn;
    }
    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }
    public void setAimsCount(Integer aimsCount) {
        this.aimsCount = aimsCount;
    }
    public void setAimsVOList(List<PushCardSenderAimsVO> aimsVOList) {
        this.aimsVOList = aimsVOList;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof PushCardSenderVO)) {
            return false;
        }
        PushCardSenderVO other = (PushCardSenderVO) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$aimsCount = getAimsCount();
        Object other$aimsCount = other.getAimsCount();
        if (this$aimsCount == null) {
            if (other$aimsCount != null) {
                return false;
            }
        } else if (!this$aimsCount.equals(other$aimsCount)) {
            return false;
        }
        Object this$deviceSn = getDeviceSn();
        Object other$deviceSn = other.getDeviceSn();
        if (this$deviceSn == null) {
            if (other$deviceSn != null) {
                return false;
            }
        } else if (!this$deviceSn.equals(other$deviceSn)) {
            return false;
        }
        Object this$deviceName = getDeviceName();
        Object other$deviceName = other.getDeviceName();
        if (this$deviceName == null) {
            if (other$deviceName != null) {
                return false;
            }
        } else if (!this$deviceName.equals(other$deviceName)) {
            return false;
        }
        Object this$aimsVOList = getAimsVOList();
        Object other$aimsVOList = other.getAimsVOList();
        return this$aimsVOList == null ? other$aimsVOList == null : this$aimsVOList.equals(other$aimsVOList);
    }
    protected boolean canEqual(Object other) {
        return other instanceof PushCardSenderVO;
    }
    public int hashCode() {
        Object $aimsCount = getAimsCount();
        int result = (1 * 59) + ($aimsCount == null ? 43 : $aimsCount.hashCode());
        Object $deviceSn = getDeviceSn();
        int result2 = (result * 59) + ($deviceSn == null ? 43 : $deviceSn.hashCode());
        Object $deviceName = getDeviceName();
        int result3 = (result2 * 59) + ($deviceName == null ? 43 : $deviceName.hashCode());
        Object $aimsVOList = getAimsVOList();
        return (result3 * 59) + ($aimsVOList == null ? 43 : $aimsVOList.hashCode());
    }
    public String toString() {
        return "PushCardSenderVO(deviceSn=" + getDeviceSn() + ", deviceName=" + getDeviceName() + ", aimsCount=" + getAimsCount() + ", aimsVOList=" + getAimsVOList() + StringPool.RIGHT_BRACKET;
    }
    public String getDeviceSn() {
        return this.deviceSn;
    }
    public String getDeviceName() {
        return this.deviceName;
    }
    public Integer getAimsCount() {
        return this.aimsCount;
    }
    public List<PushCardSenderAimsVO> getAimsVOList() {
        return this.aimsVOList;
    }
}
