package com.xrkc.job.domain;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/AlarmNotice.class */
public class AlarmNotice {
    private Long alarmNoticeId;
    private String alarmNoticeName;
    private String alarmNoticeEnable;
    private String noticeType;
    private String firstPersonId;
    private String firstRealName;
    private String secondPersonId;
    private String secondRealName;
    private Long secondIntervalSecond;
    private String thirdPersonId;
    private String thirdRealName;
    private Long thirdIntervalSecond;
    private List<Long> firstPersonIds;
    private List<PersonVO> firstPersonList;
    private List<Long> secondPersonIds;
    private List<PersonVO> secondPersonList;
    private List<Long> thirdPersonIds;
    private List<PersonVO> thirdPersonList;
    private Long vehicleSeetingId;
    @JsonSerialize(using = ToStringSerializer.class)
    public Long getAlarmNoticeId() {
        return this.alarmNoticeId;
    }
    public void setAlarmNoticeId(Long alarmNoticeId) {
        this.alarmNoticeId = alarmNoticeId;
    }
    public String getAlarmNoticeName() {
        return this.alarmNoticeName;
    }
    public void setAlarmNoticeName(String alarmNoticeName) {
        this.alarmNoticeName = alarmNoticeName;
    }
    public String getNoticeType() {
        return this.noticeType;
    }
    public void setNoticeType(String noticeType) {
        this.noticeType = noticeType;
    }
    public String getFirstPersonId() {
        return this.firstPersonId;
    }
    public void setFirstPersonId(String firstPersonId) {
        this.firstPersonId = firstPersonId;
    }
    public String getFirstRealName() {
        return this.firstRealName;
    }
    public void setFirstRealName(String firstRealName) {
        this.firstRealName = firstRealName;
    }
    public String getSecondPersonId() {
        return this.secondPersonId;
    }
    public void setSecondPersonId(String secondPersonId) {
        this.secondPersonId = secondPersonId;
    }
    public String getSecondRealName() {
        return this.secondRealName;
    }
    public void setSecondRealName(String secondRealName) {
        this.secondRealName = secondRealName;
    }
    public String getThirdPersonId() {
        return this.thirdPersonId;
    }
    public void setThirdPersonId(String thirdPersonId) {
        this.thirdPersonId = thirdPersonId;
    }
    public String getThirdRealName() {
        return this.thirdRealName;
    }
    public void setThirdRealName(String thirdRealName) {
        this.thirdRealName = thirdRealName;
    }
    public String getAlarmNoticeEnable() {
        return this.alarmNoticeEnable;
    }
    public void setAlarmNoticeEnable(String alarmNoticeEnable) {
        this.alarmNoticeEnable = alarmNoticeEnable;
    }
    public Long getSecondIntervalSecond() {
        return this.secondIntervalSecond;
    }
    public void setSecondIntervalSecond(Long secondIntervalSecond) {
        this.secondIntervalSecond = secondIntervalSecond;
    }
    public Long getThirdIntervalSecond() {
        return this.thirdIntervalSecond;
    }
    public void setThirdIntervalSecond(Long thirdIntervalSecond) {
        this.thirdIntervalSecond = thirdIntervalSecond;
    }
    public List<Long> getFirstPersonIds() {
        return this.firstPersonIds;
    }
    public void setFirstPersonIds(List<Long> firstPersonIds) {
        this.firstPersonIds = firstPersonIds;
    }
    public List<Long> getSecondPersonIds() {
        return this.secondPersonIds;
    }
    public void setSecondPersonIds(List<Long> secondPersonIds) {
        this.secondPersonIds = secondPersonIds;
    }
    public List<Long> getThirdPersonIds() {
        return this.thirdPersonIds;
    }
    public void setThirdPersonIds(List<Long> thirdPersonIds) {
        this.thirdPersonIds = thirdPersonIds;
    }
    public List<PersonVO> getFirstPersonList() {
        return this.firstPersonList;
    }
    public void setFirstPersonList(List<PersonVO> firstPersonList) {
        this.firstPersonList = firstPersonList;
    }
    public List<PersonVO> getSecondPersonList() {
        return this.secondPersonList;
    }
    public void setSecondPersonList(List<PersonVO> secondPersonList) {
        this.secondPersonList = secondPersonList;
    }
    public List<PersonVO> getThirdPersonList() {
        return this.thirdPersonList;
    }
    public void setThirdPersonList(List<PersonVO> thirdPersonList) {
        this.thirdPersonList = thirdPersonList;
    }
    public Long getVehicleSeetingId() {
        return this.vehicleSeetingId;
    }
    public void setVehicleSeetingId(Long vehicleSeetingId) {
        this.vehicleSeetingId = vehicleSeetingId;
    }
}
