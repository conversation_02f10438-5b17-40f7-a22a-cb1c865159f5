package com.xrkc.job.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xrkc.core.domain.system.SystemRoleFacility;
import com.xrkc.job.domain.SystemRoleFacilityVO;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/ISystemRoleFacilityService.class */
public interface ISystemRoleFacilityService extends IService<SystemRoleFacility> {
    List<SystemRoleFacilityVO> findByRoleIds(List<Long> list);
}
