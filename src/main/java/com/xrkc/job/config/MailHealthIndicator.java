package com.xrkc.job.config;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;
@Component
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/config/MailHealthIndicator.class */
public class MailHealthIndicator implements HealthIndicator {
    @Override // org.springframework.boot.actuate.health.HealthIndicator
    public Health health() {
        int errorCode = check();
        if (errorCode != 0) {
            return Health.down().withDetail("Error Code", Integer.valueOf(errorCode)).build();
        }
        return Health.up().build();
    }
    int check() {
        return 0;
    }
}
