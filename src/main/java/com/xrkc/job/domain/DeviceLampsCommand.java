package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
import java.time.LocalDateTime;
@TableName("device_lamps_command")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/DeviceLampsCommand.class */
public class DeviceLampsCommand implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId("command_id")
    private Long commandId;
    @TableField("command_type")
    private Integer commandType;
    @TableField("opcode")
    private String opcode;
    @TableField("lamps_status")
    private Integer lampsStatus;
    @TableField("lamps_luminance")
    private Integer lampsLuminance;
    @TableField("push_inductive_enable")
    private Integer pushInductiveEnable;
    @TableField("push_inductive_distance")
    private Integer pushInductiveDistance;
    @TableField("push_inductive_luminance")
    private Integer pushInductiveLuminance;
    private Integer pushInductiveOffLuminance;
    @TableField("push_inductive_off_delay_time")
    private Integer pushInductiveOffDelayTime;
    @TableField("command_status")
    private String commandStatus;
    private String commandSource;
    @TableField("create_time")
    private LocalDateTime createTime;
    @TableField("create_by")
    private String createBy;
    @TableField("update_time")
    private LocalDateTime updateTime;
    @TableField("update_by")
    private String updateBy;
    @TableField("remark")
    private String remark;
    public void setCommandId(Long commandId) {
        this.commandId = commandId;
    }
    public void setCommandType(Integer commandType) {
        this.commandType = commandType;
    }
    public void setOpcode(String opcode) {
        this.opcode = opcode;
    }
    public void setLampsStatus(Integer lampsStatus) {
        this.lampsStatus = lampsStatus;
    }
    public void setLampsLuminance(Integer lampsLuminance) {
        this.lampsLuminance = lampsLuminance;
    }
    public void setPushInductiveEnable(Integer pushInductiveEnable) {
        this.pushInductiveEnable = pushInductiveEnable;
    }
    public void setPushInductiveDistance(Integer pushInductiveDistance) {
        this.pushInductiveDistance = pushInductiveDistance;
    }
    public void setPushInductiveLuminance(Integer pushInductiveLuminance) {
        this.pushInductiveLuminance = pushInductiveLuminance;
    }
    public void setPushInductiveOffLuminance(Integer pushInductiveOffLuminance) {
        this.pushInductiveOffLuminance = pushInductiveOffLuminance;
    }
    public void setPushInductiveOffDelayTime(Integer pushInductiveOffDelayTime) {
        this.pushInductiveOffDelayTime = pushInductiveOffDelayTime;
    }
    public void setCommandStatus(String commandStatus) {
        this.commandStatus = commandStatus;
    }
    public void setCommandSource(String commandSource) {
        this.commandSource = commandSource;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }
    public String toString() {
        return "DeviceLampsCommand(commandId=" + getCommandId() + ", commandType=" + getCommandType() + ", opcode=" + getOpcode() + ", lampsStatus=" + getLampsStatus() + ", lampsLuminance=" + getLampsLuminance() + ", pushInductiveEnable=" + getPushInductiveEnable() + ", pushInductiveDistance=" + getPushInductiveDistance() + ", pushInductiveLuminance=" + getPushInductiveLuminance() + ", pushInductiveOffLuminance=" + getPushInductiveOffLuminance() + ", pushInductiveOffDelayTime=" + getPushInductiveOffDelayTime() + ", commandStatus=" + getCommandStatus() + ", commandSource=" + getCommandSource() + ", createTime=" + getCreateTime() + ", createBy=" + getCreateBy() + ", updateTime=" + getUpdateTime() + ", updateBy=" + getUpdateBy() + ", remark=" + getRemark() + StringPool.RIGHT_BRACKET;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof DeviceLampsCommand)) {
            return false;
        }
        DeviceLampsCommand other = (DeviceLampsCommand) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$commandId = getCommandId();
        Object other$commandId = other.getCommandId();
        if (this$commandId == null) {
            if (other$commandId != null) {
                return false;
            }
        } else if (!this$commandId.equals(other$commandId)) {
            return false;
        }
        Object this$commandType = getCommandType();
        Object other$commandType = other.getCommandType();
        if (this$commandType == null) {
            if (other$commandType != null) {
                return false;
            }
        } else if (!this$commandType.equals(other$commandType)) {
            return false;
        }
        Object this$lampsStatus = getLampsStatus();
        Object other$lampsStatus = other.getLampsStatus();
        if (this$lampsStatus == null) {
            if (other$lampsStatus != null) {
                return false;
            }
        } else if (!this$lampsStatus.equals(other$lampsStatus)) {
            return false;
        }
        Object this$lampsLuminance = getLampsLuminance();
        Object other$lampsLuminance = other.getLampsLuminance();
        if (this$lampsLuminance == null) {
            if (other$lampsLuminance != null) {
                return false;
            }
        } else if (!this$lampsLuminance.equals(other$lampsLuminance)) {
            return false;
        }
        Object this$pushInductiveEnable = getPushInductiveEnable();
        Object other$pushInductiveEnable = other.getPushInductiveEnable();
        if (this$pushInductiveEnable == null) {
            if (other$pushInductiveEnable != null) {
                return false;
            }
        } else if (!this$pushInductiveEnable.equals(other$pushInductiveEnable)) {
            return false;
        }
        Object this$pushInductiveDistance = getPushInductiveDistance();
        Object other$pushInductiveDistance = other.getPushInductiveDistance();
        if (this$pushInductiveDistance == null) {
            if (other$pushInductiveDistance != null) {
                return false;
            }
        } else if (!this$pushInductiveDistance.equals(other$pushInductiveDistance)) {
            return false;
        }
        Object this$pushInductiveLuminance = getPushInductiveLuminance();
        Object other$pushInductiveLuminance = other.getPushInductiveLuminance();
        if (this$pushInductiveLuminance == null) {
            if (other$pushInductiveLuminance != null) {
                return false;
            }
        } else if (!this$pushInductiveLuminance.equals(other$pushInductiveLuminance)) {
            return false;
        }
        Object this$pushInductiveOffLuminance = getPushInductiveOffLuminance();
        Object other$pushInductiveOffLuminance = other.getPushInductiveOffLuminance();
        if (this$pushInductiveOffLuminance == null) {
            if (other$pushInductiveOffLuminance != null) {
                return false;
            }
        } else if (!this$pushInductiveOffLuminance.equals(other$pushInductiveOffLuminance)) {
            return false;
        }
        Object this$pushInductiveOffDelayTime = getPushInductiveOffDelayTime();
        Object other$pushInductiveOffDelayTime = other.getPushInductiveOffDelayTime();
        if (this$pushInductiveOffDelayTime == null) {
            if (other$pushInductiveOffDelayTime != null) {
                return false;
            }
        } else if (!this$pushInductiveOffDelayTime.equals(other$pushInductiveOffDelayTime)) {
            return false;
        }
        Object this$opcode = getOpcode();
        Object other$opcode = other.getOpcode();
        if (this$opcode == null) {
            if (other$opcode != null) {
                return false;
            }
        } else if (!this$opcode.equals(other$opcode)) {
            return false;
        }
        Object this$commandStatus = getCommandStatus();
        Object other$commandStatus = other.getCommandStatus();
        if (this$commandStatus == null) {
            if (other$commandStatus != null) {
                return false;
            }
        } else if (!this$commandStatus.equals(other$commandStatus)) {
            return false;
        }
        Object this$commandSource = getCommandSource();
        Object other$commandSource = other.getCommandSource();
        if (this$commandSource == null) {
            if (other$commandSource != null) {
                return false;
            }
        } else if (!this$commandSource.equals(other$commandSource)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        if (this$createTime == null) {
            if (other$createTime != null) {
                return false;
            }
        } else if (!this$createTime.equals(other$createTime)) {
            return false;
        }
        Object this$createBy = getCreateBy();
        Object other$createBy = other.getCreateBy();
        if (this$createBy == null) {
            if (other$createBy != null) {
                return false;
            }
        } else if (!this$createBy.equals(other$createBy)) {
            return false;
        }
        Object this$updateTime = getUpdateTime();
        Object other$updateTime = other.getUpdateTime();
        if (this$updateTime == null) {
            if (other$updateTime != null) {
                return false;
            }
        } else if (!this$updateTime.equals(other$updateTime)) {
            return false;
        }
        Object this$updateBy = getUpdateBy();
        Object other$updateBy = other.getUpdateBy();
        if (this$updateBy == null) {
            if (other$updateBy != null) {
                return false;
            }
        } else if (!this$updateBy.equals(other$updateBy)) {
            return false;
        }
        Object this$remark = getRemark();
        Object other$remark = other.getRemark();
        return this$remark == null ? other$remark == null : this$remark.equals(other$remark);
    }
    protected boolean canEqual(Object other) {
        return other instanceof DeviceLampsCommand;
    }
    public int hashCode() {
        Object $commandId = getCommandId();
        int result = (1 * 59) + ($commandId == null ? 43 : $commandId.hashCode());
        Object $commandType = getCommandType();
        int result2 = (result * 59) + ($commandType == null ? 43 : $commandType.hashCode());
        Object $lampsStatus = getLampsStatus();
        int result3 = (result2 * 59) + ($lampsStatus == null ? 43 : $lampsStatus.hashCode());
        Object $lampsLuminance = getLampsLuminance();
        int result4 = (result3 * 59) + ($lampsLuminance == null ? 43 : $lampsLuminance.hashCode());
        Object $pushInductiveEnable = getPushInductiveEnable();
        int result5 = (result4 * 59) + ($pushInductiveEnable == null ? 43 : $pushInductiveEnable.hashCode());
        Object $pushInductiveDistance = getPushInductiveDistance();
        int result6 = (result5 * 59) + ($pushInductiveDistance == null ? 43 : $pushInductiveDistance.hashCode());
        Object $pushInductiveLuminance = getPushInductiveLuminance();
        int result7 = (result6 * 59) + ($pushInductiveLuminance == null ? 43 : $pushInductiveLuminance.hashCode());
        Object $pushInductiveOffLuminance = getPushInductiveOffLuminance();
        int result8 = (result7 * 59) + ($pushInductiveOffLuminance == null ? 43 : $pushInductiveOffLuminance.hashCode());
        Object $pushInductiveOffDelayTime = getPushInductiveOffDelayTime();
        int result9 = (result8 * 59) + ($pushInductiveOffDelayTime == null ? 43 : $pushInductiveOffDelayTime.hashCode());
        Object $opcode = getOpcode();
        int result10 = (result9 * 59) + ($opcode == null ? 43 : $opcode.hashCode());
        Object $commandStatus = getCommandStatus();
        int result11 = (result10 * 59) + ($commandStatus == null ? 43 : $commandStatus.hashCode());
        Object $commandSource = getCommandSource();
        int result12 = (result11 * 59) + ($commandSource == null ? 43 : $commandSource.hashCode());
        Object $createTime = getCreateTime();
        int result13 = (result12 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $createBy = getCreateBy();
        int result14 = (result13 * 59) + ($createBy == null ? 43 : $createBy.hashCode());
        Object $updateTime = getUpdateTime();
        int result15 = (result14 * 59) + ($updateTime == null ? 43 : $updateTime.hashCode());
        Object $updateBy = getUpdateBy();
        int result16 = (result15 * 59) + ($updateBy == null ? 43 : $updateBy.hashCode());
        Object $remark = getRemark();
        return (result16 * 59) + ($remark == null ? 43 : $remark.hashCode());
    }
    public Long getCommandId() {
        return this.commandId;
    }
    public Integer getCommandType() {
        return this.commandType;
    }
    public String getOpcode() {
        return this.opcode;
    }
    public Integer getLampsStatus() {
        return this.lampsStatus;
    }
    public Integer getLampsLuminance() {
        return this.lampsLuminance;
    }
    public Integer getPushInductiveEnable() {
        return this.pushInductiveEnable;
    }
    public Integer getPushInductiveDistance() {
        return this.pushInductiveDistance;
    }
    public Integer getPushInductiveLuminance() {
        return this.pushInductiveLuminance;
    }
    public Integer getPushInductiveOffLuminance() {
        return this.pushInductiveOffLuminance;
    }
    public Integer getPushInductiveOffDelayTime() {
        return this.pushInductiveOffDelayTime;
    }
    public String getCommandStatus() {
        return this.commandStatus;
    }
    public String getCommandSource() {
        return this.commandSource;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public String getCreateBy() {
        return this.createBy;
    }
    public LocalDateTime getUpdateTime() {
        return this.updateTime;
    }
    public String getUpdateBy() {
        return this.updateBy;
    }
    public String getRemark() {
        return this.remark;
    }
}
