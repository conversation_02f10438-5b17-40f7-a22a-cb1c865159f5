package com.xrkc.job.domain;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/FacilityRail.class */
public class FacilityRail {
    private Long facilityId;
    private String facilityName;
    private String layerId;
    private String railScope;
    private String drawType;
    private String statisticType;
    @JsonSerialize(using = ToStringSerializer.class)
    public Long getFacilityId() {
        return this.facilityId;
    }
    public void setFacilityId(Long facilityId) {
        this.facilityId = facilityId;
    }
    public String getRailScope() {
        return this.railScope;
    }
    public void setRailScope(String railScope) {
        this.railScope = railScope;
    }
    public String getFacilityName() {
        return this.facilityName;
    }
    public void setFacilityName(String facilityName) {
        this.facilityName = facilityName;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
    public String getStatisticType() {
        return this.statisticType;
    }
    public void setStatisticType(String statisticType) {
        this.statisticType = statisticType;
    }
    public String getDrawType() {
        return this.drawType;
    }
    public void setDrawType(String drawType) {
        this.drawType = drawType;
    }
}
