package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.time.LocalDateTime;
@TableName(value = "system_job_log", autoResultMap = true)
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/SystemJobLog.class */
public class SystemJobLog implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId
    private Long jobLogId;
    private Long jobId;
    private String jobName;
    private String jobGroup;
    private String jobTask;
    private Long jobTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime stopTime;
    private String jobMsg;
    private String jobStatus;
    private String exceptionInfo;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    public void setJobLogId(Long jobLogId) {
        this.jobLogId = jobLogId;
    }
    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }
    public void setJobName(String jobName) {
        this.jobName = jobName;
    }
    public void setJobGroup(String jobGroup) {
        this.jobGroup = jobGroup;
    }
    public void setJobTask(String jobTask) {
        this.jobTask = jobTask;
    }
    public void setJobTime(Long jobTime) {
        this.jobTime = jobTime;
    }
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public void setStopTime(LocalDateTime stopTime) {
        this.stopTime = stopTime;
    }
    public void setJobMsg(String jobMsg) {
        this.jobMsg = jobMsg;
    }
    public void setJobStatus(String jobStatus) {
        this.jobStatus = jobStatus;
    }
    public void setExceptionInfo(String exceptionInfo) {
        this.exceptionInfo = exceptionInfo;
    }
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof SystemJobLog)) {
            return false;
        }
        SystemJobLog other = (SystemJobLog) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$jobLogId = getJobLogId();
        Object other$jobLogId = other.getJobLogId();
        if (this$jobLogId == null) {
            if (other$jobLogId != null) {
                return false;
            }
        } else if (!this$jobLogId.equals(other$jobLogId)) {
            return false;
        }
        Object this$jobId = getJobId();
        Object other$jobId = other.getJobId();
        if (this$jobId == null) {
            if (other$jobId != null) {
                return false;
            }
        } else if (!this$jobId.equals(other$jobId)) {
            return false;
        }
        Object this$jobTime = getJobTime();
        Object other$jobTime = other.getJobTime();
        if (this$jobTime == null) {
            if (other$jobTime != null) {
                return false;
            }
        } else if (!this$jobTime.equals(other$jobTime)) {
            return false;
        }
        Object this$jobName = getJobName();
        Object other$jobName = other.getJobName();
        if (this$jobName == null) {
            if (other$jobName != null) {
                return false;
            }
        } else if (!this$jobName.equals(other$jobName)) {
            return false;
        }
        Object this$jobGroup = getJobGroup();
        Object other$jobGroup = other.getJobGroup();
        if (this$jobGroup == null) {
            if (other$jobGroup != null) {
                return false;
            }
        } else if (!this$jobGroup.equals(other$jobGroup)) {
            return false;
        }
        Object this$jobTask = getJobTask();
        Object other$jobTask = other.getJobTask();
        if (this$jobTask == null) {
            if (other$jobTask != null) {
                return false;
            }
        } else if (!this$jobTask.equals(other$jobTask)) {
            return false;
        }
        Object this$startTime = getStartTime();
        Object other$startTime = other.getStartTime();
        if (this$startTime == null) {
            if (other$startTime != null) {
                return false;
            }
        } else if (!this$startTime.equals(other$startTime)) {
            return false;
        }
        Object this$stopTime = getStopTime();
        Object other$stopTime = other.getStopTime();
        if (this$stopTime == null) {
            if (other$stopTime != null) {
                return false;
            }
        } else if (!this$stopTime.equals(other$stopTime)) {
            return false;
        }
        Object this$jobMsg = getJobMsg();
        Object other$jobMsg = other.getJobMsg();
        if (this$jobMsg == null) {
            if (other$jobMsg != null) {
                return false;
            }
        } else if (!this$jobMsg.equals(other$jobMsg)) {
            return false;
        }
        Object this$jobStatus = getJobStatus();
        Object other$jobStatus = other.getJobStatus();
        if (this$jobStatus == null) {
            if (other$jobStatus != null) {
                return false;
            }
        } else if (!this$jobStatus.equals(other$jobStatus)) {
            return false;
        }
        Object this$exceptionInfo = getExceptionInfo();
        Object other$exceptionInfo = other.getExceptionInfo();
        if (this$exceptionInfo == null) {
            if (other$exceptionInfo != null) {
                return false;
            }
        } else if (!this$exceptionInfo.equals(other$exceptionInfo)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        return this$createTime == null ? other$createTime == null : this$createTime.equals(other$createTime);
    }
    protected boolean canEqual(Object other) {
        return other instanceof SystemJobLog;
    }
    public int hashCode() {
        Object $jobLogId = getJobLogId();
        int result = (1 * 59) + ($jobLogId == null ? 43 : $jobLogId.hashCode());
        Object $jobId = getJobId();
        int result2 = (result * 59) + ($jobId == null ? 43 : $jobId.hashCode());
        Object $jobTime = getJobTime();
        int result3 = (result2 * 59) + ($jobTime == null ? 43 : $jobTime.hashCode());
        Object $jobName = getJobName();
        int result4 = (result3 * 59) + ($jobName == null ? 43 : $jobName.hashCode());
        Object $jobGroup = getJobGroup();
        int result5 = (result4 * 59) + ($jobGroup == null ? 43 : $jobGroup.hashCode());
        Object $jobTask = getJobTask();
        int result6 = (result5 * 59) + ($jobTask == null ? 43 : $jobTask.hashCode());
        Object $startTime = getStartTime();
        int result7 = (result6 * 59) + ($startTime == null ? 43 : $startTime.hashCode());
        Object $stopTime = getStopTime();
        int result8 = (result7 * 59) + ($stopTime == null ? 43 : $stopTime.hashCode());
        Object $jobMsg = getJobMsg();
        int result9 = (result8 * 59) + ($jobMsg == null ? 43 : $jobMsg.hashCode());
        Object $jobStatus = getJobStatus();
        int result10 = (result9 * 59) + ($jobStatus == null ? 43 : $jobStatus.hashCode());
        Object $exceptionInfo = getExceptionInfo();
        int result11 = (result10 * 59) + ($exceptionInfo == null ? 43 : $exceptionInfo.hashCode());
        Object $createTime = getCreateTime();
        return (result11 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
    }
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/SystemJobLog$SystemJobLogBuilder.class */
    public static class SystemJobLogBuilder {
        private Long jobLogId;
        private Long jobId;
        private String jobName;
        private String jobGroup;
        private String jobTask;
        private Long jobTime;
        private LocalDateTime startTime;
        private LocalDateTime stopTime;
        private String jobMsg;
        private String jobStatus;
        private String exceptionInfo;
        private LocalDateTime createTime;
        SystemJobLogBuilder() {
        }
        public SystemJobLogBuilder jobLogId(Long jobLogId) {
            this.jobLogId = jobLogId;
            return this;
        }
        public SystemJobLogBuilder jobId(Long jobId) {
            this.jobId = jobId;
            return this;
        }
        public SystemJobLogBuilder jobName(String jobName) {
            this.jobName = jobName;
            return this;
        }
        public SystemJobLogBuilder jobGroup(String jobGroup) {
            this.jobGroup = jobGroup;
            return this;
        }
        public SystemJobLogBuilder jobTask(String jobTask) {
            this.jobTask = jobTask;
            return this;
        }
        public SystemJobLogBuilder jobTime(Long jobTime) {
            this.jobTime = jobTime;
            return this;
        }
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        public SystemJobLogBuilder startTime(LocalDateTime startTime) {
            this.startTime = startTime;
            return this;
        }
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        public SystemJobLogBuilder stopTime(LocalDateTime stopTime) {
            this.stopTime = stopTime;
            return this;
        }
        public SystemJobLogBuilder jobMsg(String jobMsg) {
            this.jobMsg = jobMsg;
            return this;
        }
        public SystemJobLogBuilder jobStatus(String jobStatus) {
            this.jobStatus = jobStatus;
            return this;
        }
        public SystemJobLogBuilder exceptionInfo(String exceptionInfo) {
            this.exceptionInfo = exceptionInfo;
            return this;
        }
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        public SystemJobLogBuilder createTime(LocalDateTime createTime) {
            this.createTime = createTime;
            return this;
        }
        public SystemJobLog build() {
            return new SystemJobLog(this.jobLogId, this.jobId, this.jobName, this.jobGroup, this.jobTask, this.jobTime, this.startTime, this.stopTime, this.jobMsg, this.jobStatus, this.exceptionInfo, this.createTime);
        }
        public String toString() {
            return "SystemJobLog.SystemJobLogBuilder(jobLogId=" + this.jobLogId + ", jobId=" + this.jobId + ", jobName=" + this.jobName + ", jobGroup=" + this.jobGroup + ", jobTask=" + this.jobTask + ", jobTime=" + this.jobTime + ", startTime=" + this.startTime + ", stopTime=" + this.stopTime + ", jobMsg=" + this.jobMsg + ", jobStatus=" + this.jobStatus + ", exceptionInfo=" + this.exceptionInfo + ", createTime=" + this.createTime + StringPool.RIGHT_BRACKET;
        }
    }
    public String toString() {
        return "SystemJobLog(super=" + super.toString() + ", jobLogId=" + getJobLogId() + ", jobId=" + getJobId() + ", jobName=" + getJobName() + ", jobGroup=" + getJobGroup() + ", jobTask=" + getJobTask() + ", jobTime=" + getJobTime() + ", startTime=" + getStartTime() + ", stopTime=" + getStopTime() + ", jobMsg=" + getJobMsg() + ", jobStatus=" + getJobStatus() + ", exceptionInfo=" + getExceptionInfo() + ", createTime=" + getCreateTime() + StringPool.RIGHT_BRACKET;
    }
    public static SystemJobLogBuilder builder() {
        return new SystemJobLogBuilder();
    }
    public SystemJobLog(Long jobLogId, Long jobId, String jobName, String jobGroup, String jobTask, Long jobTime, LocalDateTime startTime, LocalDateTime stopTime, String jobMsg, String jobStatus, String exceptionInfo, LocalDateTime createTime) {
        this.jobLogId = jobLogId;
        this.jobId = jobId;
        this.jobName = jobName;
        this.jobGroup = jobGroup;
        this.jobTask = jobTask;
        this.jobTime = jobTime;
        this.startTime = startTime;
        this.stopTime = stopTime;
        this.jobMsg = jobMsg;
        this.jobStatus = jobStatus;
        this.exceptionInfo = exceptionInfo;
        this.createTime = createTime;
    }
    public SystemJobLog() {
    }
    public Long getJobLogId() {
        return this.jobLogId;
    }
    public Long getJobId() {
        return this.jobId;
    }
    public String getJobName() {
        return this.jobName;
    }
    public String getJobGroup() {
        return this.jobGroup;
    }
    public String getJobTask() {
        return this.jobTask;
    }
    public Long getJobTime() {
        return this.jobTime;
    }
    public LocalDateTime getStartTime() {
        return this.startTime;
    }
    public LocalDateTime getStopTime() {
        return this.stopTime;
    }
    public String getJobMsg() {
        return this.jobMsg;
    }
    public String getJobStatus() {
        return this.jobStatus;
    }
    public String getExceptionInfo() {
        return this.exceptionInfo;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
}
