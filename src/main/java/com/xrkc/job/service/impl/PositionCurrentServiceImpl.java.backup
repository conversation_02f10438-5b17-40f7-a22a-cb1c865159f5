package com.xrkc.job.service.impl;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.domain.rail.RailDrawType;
import com.xrkc.core.domain.statistics.StatisticsFacilityCurrentPersonList;
import com.xrkc.core.domain.system.SystemDept;
import com.xrkc.job.domain.AreaAlarmTempVO;
import com.xrkc.job.domain.PositionCurrent;
import com.xrkc.job.domain.PositionDataCalc;
import com.xrkc.job.domain.PositionVO;
import com.xrkc.job.mapper.PositionCurrentMapper;
import com.xrkc.job.mapper.SysConfigMapper;
import com.xrkc.job.service.IPositionCurrentService;
import com.xrkc.job.service.ISystemDeptService;
import com.xrkc.job.util.CommonUtil;
import com.xrkc.redis.utils.RedisCacheUtils;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/PositionCurrentServiceImpl.class */
public class PositionCurrentServiceImpl implements IPositionCurrentService {
    @Autowired
    private PositionCurrentMapper positionCurrentMapper;
    @Autowired
    private ISystemDeptService systemDeptService;
    @Autowired
    private SysConfigMapper sysConfigMapper;
    @Override // com.xrkc.job.service.IPositionCurrentService
    public int deleteByOfflineBeacons(String offlineBeacons) {
        return this.positionCurrentMapper.deleteByOfflineBeacons(offlineBeacons);
    }
    @Override // com.xrkc.job.service.IPositionCurrentService
    public int delDataByOfflineBeacons(String offlineBeacons) {
        return this.positionCurrentMapper.delDataByOfflineBeacons(offlineBeacons);
    }
    @Override // com.xrkc.job.service.IPositionCurrentService
    public List<String> countCurrentPerson(String layerId, String railScope, String drawType) {
        Integer intervalSecond = this.sysConfigMapper.getNewByIntervalSecond();
        LocalDateTime minusSeconds = LocalDateTime.now().minusSeconds(intervalSecond.intValue());
        List<PositionCurrent> currentList = this.positionCurrentMapper.findByLayerId(layerId, minusSeconds);
        List<String> personTypeList = new ArrayList<>();
        for (PositionCurrent current : currentList) {
            if (CommonUtil.containsRailScope(current.getLongitude(), current.getLatitude(), railScope, drawType)) {
                personTypeList.add(current.getPersonType());
            }
        }
        return personTypeList;
    }
    @Override // com.xrkc.job.service.IPositionCurrentService
    public int countCurrentFacilityPerson(List<RailDrawType> railScopeList) {
        if (CollectionUtils.isEmpty(railScopeList)) {
            return 0;
        }
        List<Long> personTypeList = new ArrayList<>();
        Integer intervalSecond = this.sysConfigMapper.getNewByIntervalSecond();
        LocalDateTime minusSeconds = LocalDateTime.now().minusSeconds(intervalSecond.intValue());
        List<PositionCurrent> currentList = this.positionCurrentMapper.findPersonId(minusSeconds);
        for (RailDrawType railScope : railScopeList) {
            for (PositionCurrent current : currentList) {
                if (CommonUtil.containsRailScope(current.getLongitude(), current.getLatitude(), railScope.getRailScope(), railScope.getDrawType()) && !personTypeList.contains(current.getPersonId())) {
                    personTypeList.add(current.getPersonId());
                }
            }
        }
        List<Long> resultList = (List) personTypeList.stream().distinct().collect(Collectors.toList());
        return resultList.size();
    }
    @Override // com.xrkc.job.service.IPositionCurrentService
    public int replaceVO(PositionVO t) {
        PositionCurrent positionCurrent = this.positionCurrentMapper.selectByCardId(t.getCardId());
        t.setCreateTime(LocalDateTime.now());
        if (ObjectUtil.isEmpty(positionCurrent)) {
            PositionCurrent entity = (PositionCurrent) BeanUtil.copyProperties((Object) t, PositionCurrent.class, new String[0]);
            return this.positionCurrentMapper.insert((PositionCurrentMapper) entity);
        }
        return this.positionCurrentMapper.updateByParams(t);
    }
    @Override // com.xrkc.job.service.IPositionCurrentService
    public List<PositionCurrent> selectList() {
        Map<String, String> configMap = RedisCacheUtils.getConfigRedisCache();
        Object offline_beacons = (String) configMap.get(ConfigValue.CONFIG_KEY_OFFLINE_BEACONS);
        String online_minute = configMap.get(ConfigValue.CONFIG_KEY_PERSON_ONLINE_MINUTE);
        int onlineMinute = StringUtils.isNotBlank(online_minute) ? Integer.parseInt(online_minute) : 10;
        Map<String, Object> query = new HashMap<>();
        query.put("offlineBeacons", offline_beacons);
        query.put("onlineSecond", Integer.valueOf(onlineMinute * 60));
        Integer onlineSecond = (Integer) query.get("onlineSecond");
        LocalDateTime minusSeconds = LocalDateTime.now().minusSeconds(onlineSecond.intValue());
        query.put("minusSeconds", minusSeconds);
        List<PositionCurrent> list = this.positionCurrentMapper.selectListByParams(query);
        if (!CollectionUtils.isEmpty(list)) {
            list.stream().filter(f -> {
                return com.xrkc.core.utils.CommonUtil.containsZeroWidthCharacters(f.getRealName());
            }).forEach(t -> {
                t.setRealName(com.xrkc.core.utils.CommonUtil.removeZeroWidthCharacters(t.getRealName()));
            });
        }
        return list;
    }
    @Override // com.xrkc.job.service.IPositionCurrentService
    public void notMovePosition(PositionVO lastPosition) {
        this.positionCurrentMapper.updateByParams(lastPosition);
    }
    @Override // com.xrkc.job.service.IPositionCurrentService
    public List<PositionCurrent> selectAlarmPerson(AreaAlarmTempVO areaVO) {
        if (CollUtil.isEmpty((Collection<?>) areaVO.getAlarmPersonIds()) && CollUtil.isEmpty((Collection<?>) areaVO.getDeptIds()) && CollUtil.isEmpty((Collection<?>) areaVO.getPostIds())) {
            areaVO.setParameterCondition(false);
        } else {
            areaVO.setParameterCondition(true);
        }
        Map<String, String> config = RedisCacheUtils.getConfigRedisCache();
        areaVO.setConfigurationRelation(config.getOrDefault(ConfigValue.CONFIGURATION_RELATION, "1"));
        List<PositionCurrent> resultCurrentList = new ArrayList<>();
        Integer intervalSecond = this.sysConfigMapper.getNewByIntervalSecond();
        LocalDateTime minusSeconds = LocalDateTime.now().minusSeconds(intervalSecond.intValue());
        areaVO.setMinusSeconds(minusSeconds);
        List<PositionCurrent> currentList = this.positionCurrentMapper.selectAlarmPerson(areaVO);
        if (CollUtil.isEmpty((Collection<?>) currentList)) {
            return resultCurrentList;
        }
        if (CollUtil.isNotEmpty((Collection<?>) areaVO.getRailDrawList())) {
            String alarmType = areaVO.getAlarmType();
            for (RailDrawType railScope : areaVO.getRailDrawList()) {
                for (PositionCurrent current : currentList) {
                    if (CommonUtil.sameLayerId(railScope.getLayerId(), current.getLayerId()) && CommonUtil.containsRailScope(current.getLongitude(), current.getLatitude(), railScope.getRailScope(), railScope.getDrawType()) && !resultCurrentList.contains(current)) {
                        resultCurrentList.add(current);
                    }
                }
            }
            if ("20".equals(alarmType)) {
                if (CollUtil.isNotEmpty((Collection<?>) resultCurrentList)) {
                    return (List) currentList.stream().filter(s -> {
                        return !resultCurrentList.contains(s);
                    }).collect(Collectors.toList());
                }
                return currentList;
            }
            return resultCurrentList;
        }
        return currentList;
    }
    @Override // com.xrkc.job.service.IPositionCurrentService
    public List<PositionCurrent> selectStopAlarmPerson(AreaAlarmTempVO areaVO, Long ruleDurationSecond) {
        if (CollUtil.isEmpty((Collection<?>) areaVO.getAlarmPersonIds()) && CollUtil.isEmpty((Collection<?>) areaVO.getDeptIds()) && CollUtil.isEmpty((Collection<?>) areaVO.getPostIds())) {
            areaVO.setParameterCondition(false);
        } else {
            areaVO.setParameterCondition(true);
        }
        Map<String, String> config = RedisCacheUtils.getConfigRedisCache();
        areaVO.setConfigurationRelation(config.getOrDefault(ConfigValue.CONFIGURATION_RELATION, "1"));
        LocalDateTime minusSeconds = LocalDateTime.now().minusSeconds(ruleDurationSecond.longValue());
        areaVO.setMinusSeconds(minusSeconds);
        return this.positionCurrentMapper.selectStopAlarmPerson(areaVO);
    }
    @Override // com.xrkc.job.service.IPositionCurrentService
    public List<Long> selectStopAlarmPositionId(AreaAlarmTempVO areaVO, Long ruleDurationSecond) {
        List<Long> longList = new ArrayList<>();
        LocalDateTime minusSeconds = LocalDateTime.now().minusSeconds(ruleDurationSecond.longValue());
        areaVO.setMinusSeconds(minusSeconds);
        if (CollUtil.isEmpty((Collection<?>) areaVO.getAlarmPersonIds()) && CollUtil.isEmpty((Collection<?>) areaVO.getDeptIds()) && CollUtil.isEmpty((Collection<?>) areaVO.getPostIds())) {
            areaVO.setParameterCondition(false);
        } else {
            areaVO.setParameterCondition(true);
        }
        Map<String, String> config = RedisCacheUtils.getConfigRedisCache();
        areaVO.setConfigurationRelation(config.getOrDefault(ConfigValue.CONFIGURATION_RELATION, "1"));
        List<PositionDataCalc> idList = this.positionCurrentMapper.selectId(areaVO);
        String alarmType = areaVO.getAlarmType();
        List<RailDrawType> railScopeList = areaVO.getRailDrawList();
        if ("70".equals(alarmType) && !CollectionUtils.isEmpty(railScopeList)) {
            for (RailDrawType railScope : railScopeList) {
                for (PositionDataCalc calc : idList) {
                    if (CommonUtil.containsRailScope(calc.getLongitude(), calc.getLatitude(), railScope.getRailScope(), railScope.getDrawType()) && !longList.contains(calc.getId())) {
                        longList.add(calc.getId());
                    }
                }
            }
        }
        return longList;
    }
    @Override // com.xrkc.job.service.IPositionCurrentService
    public List<PositionCurrent> selectRailAlarmPerson(AreaAlarmTempVO areaVO) {
        if (CollUtil.isEmpty((Collection<?>) areaVO.getAlarmPersonIds()) && CollUtil.isEmpty((Collection<?>) areaVO.getDeptIds()) && CollUtil.isEmpty((Collection<?>) areaVO.getPostIds())) {
            areaVO.setParameterCondition(false);
        } else {
            areaVO.setParameterCondition(true);
        }
        List<PositionCurrent> resultCurrentList = new ArrayList<>();
        Integer intervalSecond = this.sysConfigMapper.getNewByIntervalSecond();
        LocalDateTime minusSeconds = LocalDateTime.now().minusSeconds(intervalSecond.intValue());
        areaVO.setMinusSeconds(minusSeconds);
        Map<String, String> config = RedisCacheUtils.getConfigRedisCache();
        areaVO.setConfigurationRelation(config.getOrDefault(ConfigValue.CONFIGURATION_RELATION, "1"));
        List<PositionCurrent> currentList = this.positionCurrentMapper.selectRailAlarmPerson(areaVO);
        String railScope = areaVO.getRailScope();
        if (ObjectUtil.isEmpty(railScope)) {
            return currentList;
        }
        for (PositionCurrent current : currentList) {
            if (CommonUtil.containsRailScope(current.getLongitude(), current.getLatitude(), railScope, areaVO.getDrawType())) {
                resultCurrentList.add(current);
            }
        }
        return resultCurrentList;
    }
    @Override // com.xrkc.job.service.IPositionCurrentService
    public int deleteByOnlineMinute() {
        Integer IntervalSecond = this.sysConfigMapper.getNewByIntervalSecond();
        LocalDateTime minusSeconds = LocalDateTime.now().minusSeconds(IntervalSecond.intValue());
        return this.positionCurrentMapper.deleteByOnlineMinute(minusSeconds);
    }
    @Override // com.xrkc.job.service.IPositionCurrentService
    public List<PositionCurrent> selectAlarmPersonPosition(AreaAlarmTempVO areaVO) throws NumberFormatException {
        if (CollUtil.isEmpty((Collection<?>) areaVO.getAlarmPersonIds()) && CollUtil.isEmpty((Collection<?>) areaVO.getDeptIds()) && CollUtil.isEmpty((Collection<?>) areaVO.getPostIds())) {
            areaVO.setParameterCondition(false);
        } else {
            areaVO.setParameterCondition(true);
        }
        Long rule = Long.valueOf(areaVO.getRule());
        LocalDateTime minusSeconds = LocalDateTime.now().minusSeconds(rule.longValue() + 180);
        areaVO.setMinusSeconds(minusSeconds);
        Map<String, String> config = RedisCacheUtils.getConfigRedisCache();
        areaVO.setConfigurationRelation(config.getOrDefault(ConfigValue.CONFIGURATION_RELATION, "1"));
        List<PositionCurrent> currentList = this.positionCurrentMapper.selectAlarmPersonPosition(areaVO);
        for (PositionCurrent current : currentList) {
            if (CommonUtil.containsRailScope(current.getLongitude(), current.getLatitude(), areaVO.getRailScope(), areaVO.getDrawType())) {
                current.setInRailScope(true);
            } else {
                current.setInRailScope(false);
            }
        }
        return currentList;
    }
    @Override // com.xrkc.job.service.IPositionCurrentService
    public int offlineByCard(Long cardId, List<Long> cardIds) {
        if (Objects.isNull(cardId) && CollectionUtils.isEmpty(cardIds)) {
            return 0;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("cardId", cardId);
        map.put("cardIds", cardIds);
        this.positionCurrentMapper.delCurrentPosition2(map);
        this.positionCurrentMapper.delRssiPosition2(map);
        return this.positionCurrentMapper.delRssiTempPosition2(map);
    }
    @Override // com.xrkc.job.service.IPositionCurrentService
    public List<StatisticsFacilityCurrentPersonList> getCurrentPersonListByRails(String layerId, String railScope, String drawType) {
        Integer intervalSecond = this.sysConfigMapper.getNewByIntervalSecond();
        LocalDateTime minusSeconds = LocalDateTime.now().minusSeconds(intervalSecond.intValue());
        List<PositionCurrent> currentList = this.positionCurrentMapper.getCurrentPersonList(layerId, minusSeconds);
        Map<Long, SystemDept> deptInfoMap = this.systemDeptService.findDeptInfoMap();
        List<StatisticsFacilityCurrentPersonList> list = new ArrayList<>();
        for (PositionCurrent current : currentList) {
            if (CommonUtil.containsRailScope(current.getLongitude(), current.getLatitude(), railScope, drawType)) {
                StatisticsFacilityCurrentPersonList statisticsFacilityCurrentPerson = new StatisticsFacilityCurrentPersonList();
                BeanUtil.copyProperties(current, statisticsFacilityCurrentPerson, new String[0]);
                statisticsFacilityCurrentPerson.setId(null);
                statisticsFacilityCurrentPerson.setDeptName(null);
                Long deptId = current.getDeptId();
                if (Objects.nonNull(deptId) && !CollectionUtils.isEmpty(deptInfoMap)) {
                    String deptNamePath = com.xrkc.core.utils.CommonUtil.getDeptPath(deptId, deptInfoMap);
                    statisticsFacilityCurrentPerson.setDeptName(deptNamePath);
                }
                list.add(statisticsFacilityCurrentPerson);
            }
        }
        return list;
    }
    @Override // com.xrkc.job.service.IPositionCurrentService
    public List<Long> selectAllOnlinePersonId(List<Long> successPersonIds) {
        Map<String, String> configMap = RedisCacheUtils.getConfigRedisCache();
        String online_minute = configMap.get(ConfigValue.CONFIG_KEY_PERSON_ONLINE_MINUTE);
        int onlineMinute = StringUtils.isNotBlank(online_minute) ? Integer.parseInt(online_minute) : 10;
        LocalDateTime time = LocalDateTime.now().minusMinutes(onlineMinute);
        return this.positionCurrentMapper.selectAllOnlinePersonId(successPersonIds, time);
    }
}
