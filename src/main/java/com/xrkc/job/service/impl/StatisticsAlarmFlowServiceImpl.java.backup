package com.xrkc.job.service.impl;
import com.xrkc.job.domain.StatisticsAlarmFlow;
import com.xrkc.job.mapper.StatisticsAlarmFlowMapper;
import com.xrkc.job.service.IStatisticsAlarmFlowService;
import java.lang.invoke.SerializedLambda;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/StatisticsAlarmFlowServiceImpl.class */
public class StatisticsAlarmFlowServiceImpl implements IStatisticsAlarmFlowService {
    @Autowired
    private StatisticsAlarmFlowMapper statisticsAlarmFlowMapper;
    @Override // com.xrkc.job.service.IStatisticsAlarmFlowService
    public int batchReplace(LocalDate statisticsDate, List<StatisticsAlarmFlow> statisticsAlarmFlowList) {
        List<StatisticsAlarmFlow> oldList = this.statisticsAlarmFlowMapper.selectList((v0) -> {
            return v0.getStatisticsDate();
        }, statisticsDate);
        if (CollectionUtils.isEmpty(oldList)) {
            this.statisticsAlarmFlowMapper.insertBatch(statisticsAlarmFlowList, 1000);
            return statisticsAlarmFlowList.size();
        }
        int result = 0;
        Map<String, Long> map = (Map) oldList.stream().collect(Collectors.toMap((v0) -> {
            return v0.getAlarmType();
        }, (v0) -> {
            return v0.getStatisticsId();
        }));
        for (StatisticsAlarmFlow flow : statisticsAlarmFlowList) {
            if (map.containsKey(flow.getAlarmType())) {
                flow.setStatisticsId(map.get(flow.getAlarmType()));
                result += this.statisticsAlarmFlowMapper.updateById(flow);
            } else {
                result += this.statisticsAlarmFlowMapper.insert(flow);
            }
        }
        return result;
    }
}
