package com.xrkc.job.service.impl;
import com.xrkc.job.domain.Attendance;
import com.xrkc.job.mapper.AttendanceMapper;
import com.xrkc.job.service.IAttendanceService;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/AttendanceServiceImpl.class */
public class AttendanceServiceImpl implements IAttendanceService {
    @Autowired
    private AttendanceMapper attendanceMapper;
    @Override // com.xrkc.job.service.IAttendanceService
    public List<Attendance> queryNotAttendanceList(String attendanceType) {
        return this.attendanceMapper.queryNotAttendanceList(attendanceType);
    }
    @Override // com.xrkc.job.service.IAttendanceService
    public List<Attendance> selectPositionList(Map<String, Object> map) {
        return this.attendanceMapper.selectPositionList(map);
    }
    @Override // com.xrkc.job.service.IAttendanceService
    public int batchInsert(List<Attendance> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        this.attendanceMapper.insertBatch(list, 1000);
        return list.size();
    }
}
