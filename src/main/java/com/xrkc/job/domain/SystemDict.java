package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.xrkc.core.constant.CacheConstants;
import java.io.Serializable;
@TableName(value = CacheConstants.SYSTEM_DICT_KEY, autoResultMap = true)
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/SystemDict.class */
public class SystemDict implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId("dict_id")
    private Long dictId;
    private String dictType;
    private String dictValue;
    private String dictLabel;
    private Integer dictSort;
    private String dictDefault;
    private String dictEnable;
    private String dictAttribute;
    public void setDictAttribute(String dictAttribute) {
        this.dictAttribute = dictAttribute;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof SystemDict)) {
            return false;
        }
        SystemDict other = (SystemDict) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$dictId = getDictId();
        Object other$dictId = other.getDictId();
        if (this$dictId == null) {
            if (other$dictId != null) {
                return false;
            }
        } else if (!this$dictId.equals(other$dictId)) {
            return false;
        }
        Object this$dictSort = getDictSort();
        Object other$dictSort = other.getDictSort();
        if (this$dictSort == null) {
            if (other$dictSort != null) {
                return false;
            }
        } else if (!this$dictSort.equals(other$dictSort)) {
            return false;
        }
        Object this$dictType = getDictType();
        Object other$dictType = other.getDictType();
        if (this$dictType == null) {
            if (other$dictType != null) {
                return false;
            }
        } else if (!this$dictType.equals(other$dictType)) {
            return false;
        }
        Object this$dictValue = getDictValue();
        Object other$dictValue = other.getDictValue();
        if (this$dictValue == null) {
            if (other$dictValue != null) {
                return false;
            }
        } else if (!this$dictValue.equals(other$dictValue)) {
            return false;
        }
        Object this$dictLabel = getDictLabel();
        Object other$dictLabel = other.getDictLabel();
        if (this$dictLabel == null) {
            if (other$dictLabel != null) {
                return false;
            }
        } else if (!this$dictLabel.equals(other$dictLabel)) {
            return false;
        }
        Object this$dictDefault = getDictDefault();
        Object other$dictDefault = other.getDictDefault();
        if (this$dictDefault == null) {
            if (other$dictDefault != null) {
                return false;
            }
        } else if (!this$dictDefault.equals(other$dictDefault)) {
            return false;
        }
        Object this$dictEnable = getDictEnable();
        Object other$dictEnable = other.getDictEnable();
        if (this$dictEnable == null) {
            if (other$dictEnable != null) {
                return false;
            }
        } else if (!this$dictEnable.equals(other$dictEnable)) {
            return false;
        }
        Object this$dictAttribute = getDictAttribute();
        Object other$dictAttribute = other.getDictAttribute();
        return this$dictAttribute == null ? other$dictAttribute == null : this$dictAttribute.equals(other$dictAttribute);
    }
    protected boolean canEqual(Object other) {
        return other instanceof SystemDict;
    }
    public int hashCode() {
        Object $dictId = getDictId();
        int result = (1 * 59) + ($dictId == null ? 43 : $dictId.hashCode());
        Object $dictSort = getDictSort();
        int result2 = (result * 59) + ($dictSort == null ? 43 : $dictSort.hashCode());
        Object $dictType = getDictType();
        int result3 = (result2 * 59) + ($dictType == null ? 43 : $dictType.hashCode());
        Object $dictValue = getDictValue();
        int result4 = (result3 * 59) + ($dictValue == null ? 43 : $dictValue.hashCode());
        Object $dictLabel = getDictLabel();
        int result5 = (result4 * 59) + ($dictLabel == null ? 43 : $dictLabel.hashCode());
        Object $dictDefault = getDictDefault();
        int result6 = (result5 * 59) + ($dictDefault == null ? 43 : $dictDefault.hashCode());
        Object $dictEnable = getDictEnable();
        int result7 = (result6 * 59) + ($dictEnable == null ? 43 : $dictEnable.hashCode());
        Object $dictAttribute = getDictAttribute();
        return (result7 * 59) + ($dictAttribute == null ? 43 : $dictAttribute.hashCode());
    }
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/SystemDict$SystemDictBuilder.class */
    public static class SystemDictBuilder {
        private Long dictId;
        private String dictType;
        private String dictValue;
        private String dictLabel;
        private Integer dictSort;
        private String dictDefault;
        private String dictEnable;
        private String dictAttribute;
        SystemDictBuilder() {
        }
        public SystemDictBuilder dictId(Long dictId) {
            this.dictId = dictId;
            return this;
        }
        public SystemDictBuilder dictType(String dictType) {
            this.dictType = dictType;
            return this;
        }
        public SystemDictBuilder dictValue(String dictValue) {
            this.dictValue = dictValue;
            return this;
        }
        public SystemDictBuilder dictLabel(String dictLabel) {
            this.dictLabel = dictLabel;
            return this;
        }
        public SystemDictBuilder dictSort(Integer dictSort) {
            this.dictSort = dictSort;
            return this;
        }
        public SystemDictBuilder dictDefault(String dictDefault) {
            this.dictDefault = dictDefault;
            return this;
        }
        public SystemDictBuilder dictEnable(String dictEnable) {
            this.dictEnable = dictEnable;
            return this;
        }
        public SystemDictBuilder dictAttribute(String dictAttribute) {
            this.dictAttribute = dictAttribute;
            return this;
        }
        public SystemDict build() {
            return new SystemDict(this.dictId, this.dictType, this.dictValue, this.dictLabel, this.dictSort, this.dictDefault, this.dictEnable, this.dictAttribute);
        }
        public String toString() {
            return "SystemDict.SystemDictBuilder(dictId=" + this.dictId + ", dictType=" + this.dictType + ", dictValue=" + this.dictValue + ", dictLabel=" + this.dictLabel + ", dictSort=" + this.dictSort + ", dictDefault=" + this.dictDefault + ", dictEnable=" + this.dictEnable + ", dictAttribute=" + this.dictAttribute + StringPool.RIGHT_BRACKET;
        }
    }
    public String toString() {
        return "SystemDict(super=" + super.toString() + ", dictId=" + getDictId() + ", dictType=" + getDictType() + ", dictValue=" + getDictValue() + ", dictLabel=" + getDictLabel() + ", dictSort=" + getDictSort() + ", dictDefault=" + getDictDefault() + ", dictEnable=" + getDictEnable() + ", dictAttribute=" + getDictAttribute() + StringPool.RIGHT_BRACKET;
    }
    public static SystemDictBuilder builder() {
        return new SystemDictBuilder();
    }
    public SystemDict(Long dictId, String dictType, String dictValue, String dictLabel, Integer dictSort, String dictDefault, String dictEnable, String dictAttribute) {
        this.dictId = dictId;
        this.dictType = dictType;
        this.dictValue = dictValue;
        this.dictLabel = dictLabel;
        this.dictSort = dictSort;
        this.dictDefault = dictDefault;
        this.dictEnable = dictEnable;
        this.dictAttribute = dictAttribute;
    }
    public SystemDict() {
    }
    public String getDictAttribute() {
        return this.dictAttribute;
    }
    public Long getDictId() {
        return this.dictId;
    }
    public void setDictId(Long dictId) {
        this.dictId = dictId;
    }
    public String getDictType() {
        return this.dictType;
    }
    public void setDictType(String dictType) {
        this.dictType = dictType;
    }
    public String getDictValue() {
        return this.dictValue;
    }
    public void setDictValue(String dictValue) {
        this.dictValue = dictValue;
    }
    public String getDictLabel() {
        return this.dictLabel;
    }
    public void setDictLabel(String dictLabel) {
        this.dictLabel = dictLabel;
    }
    public Integer getDictSort() {
        return this.dictSort;
    }
    public void setDictSort(Integer dictSort) {
        this.dictSort = dictSort;
    }
    public String getDictDefault() {
        return this.dictDefault;
    }
    public void setDictDefault(String dictDefault) {
        this.dictDefault = dictDefault;
    }
    public String getDictEnable() {
        return this.dictEnable;
    }
    public void setDictEnable(String dictEnable) {
        this.dictEnable = dictEnable;
    }
}
