package com.xrkc.job.module.vehicle.service.impl;
import com.xrkc.core.domain.vehicle.VehicleAlarmSettingVehicle;
import com.xrkc.job.module.vehicle.mapper.VehicleAlarmSettingVehicleMapper;
import com.xrkc.job.module.vehicle.service.IVehicleAlarmSettingVehicleService;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/module/vehicle/service/impl/VehicleAlarmSettingVehicleServiceImpl.class */
public class VehicleAlarmSettingVehicleServiceImpl implements IVehicleAlarmSettingVehicleService {
    private final VehicleAlarmSettingVehicleMapper mapper;
    @Autowired
    public VehicleAlarmSettingVehicleServiceImpl(VehicleAlarmSettingVehicleMapper mapper) {
        this.mapper = mapper;
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehicleAlarmSettingVehicleService
    public void batchInsert(List<VehicleAlarmSettingVehicle> list, Long settingId) {
        if (Objects.nonNull(settingId)) {
            this.mapper.deleteBatch((v0) -> {
                return v0.getSettingId();
            }, settingId);
        }
        if (!CollectionUtils.isEmpty(list)) {
            this.mapper.insertBatch(list);
        }
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehicleAlarmSettingVehicleService
    public List<Long> getVehicleIdListBySettingId(Long settingId) {
        if (Objects.nonNull(settingId)) {
            return (List) this.mapper.selectList((v0) -> {
                return v0.getSettingId();
            }, settingId).stream().map((v0) -> {
                return v0.getVehicleId();
            }).collect(Collectors.toList());
        }
        return null;
    }
}
