package com.xrkc.job.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xrkc.job.domain.DeviceLayer;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;
@Mapper
@Repository
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/DeviceLayerMapper.class */
public interface DeviceLayerMapper extends BaseMapper<DeviceLayer> {
    DeviceLayer findOneLayer();
}
