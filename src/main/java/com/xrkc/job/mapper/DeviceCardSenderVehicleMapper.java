package com.xrkc.job.mapper;
import com.xrkc.core.domain.cardDispenser.DeviceCardSenderVehicle;
import com.xrkc.core.mybatisplus.BaseMapperX;
import com.xrkc.core.mybatisplus.LambdaQueryWrapperX;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/DeviceCardSenderVehicleMapper.class */
public interface DeviceCardSenderVehicleMapper extends BaseMapperX<DeviceCardSenderVehicle> {
    default LambdaQueryWrapperX<DeviceCardSenderVehicle> privateWrapper(List<String> notDeviceSnList, List<String> inDeviceSnList, int timeout) {
        return new LambdaQueryWrapperX<DeviceCardSenderVehicle>().eqIfPresent((v0) -> {
            return v0.getCardSenderType();
        }, 3).eqIfPresent((v0) -> {
            return v0.getResult();
        }, "成功").ltIfPresent((v0) -> {
            return v0.getCommandTime();
        }, LocalDateTime.now().minusSeconds(timeout)).notInIfPresent((v0) -> {
            return v0.getDeviceSn();
        }, notDeviceSnList).inIfPresent((v0) -> {
            return v0.getDeviceSn();
        }, inDeviceSnList).orderByDesc((v0) -> {
            return v0.getId();
        });
    }
    default List<DeviceCardSenderVehicle> selectTimeoutList(List<String> notDeviceSnList, List<String> inDeviceSnList, int timeout) {
        return selectList(privateWrapper(notDeviceSnList, inDeviceSnList, timeout));
    }
}
