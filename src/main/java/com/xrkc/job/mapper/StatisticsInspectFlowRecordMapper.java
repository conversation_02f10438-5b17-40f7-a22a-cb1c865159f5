package com.xrkc.job.mapper;
import com.xrkc.core.mybatisplus.BaseMapperX;
import com.xrkc.job.domain.StatisticsInspectFlowRecord;
import java.time.LocalDate;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
@Mapper
@Repository
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/StatisticsInspectFlowRecordMapper.class */
public interface StatisticsInspectFlowRecordMapper extends BaseMapperX<StatisticsInspectFlowRecord> {
    @Select({"select record_type , statistics_date from statistics_inspect_flow_record where statistics_date = #{statisticsDate};"})
    List<StatisticsInspectFlowRecord> selectByStatisticsDate(LocalDate localDate);
}
