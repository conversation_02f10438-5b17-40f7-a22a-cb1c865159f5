package com.xrkc.job.module.vehicle.service.impl;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.xrkc.core.constant.ConfigValue;
import com.xrkc.core.domain.vehicle.VehiclePositionCalc;
import com.xrkc.core.domain.vehicle.VehiclePositionCurrent;
import com.xrkc.core.mybatisplus.LambdaQueryWrapperX;
import com.xrkc.core.utils.CommonUtil;
import com.xrkc.job.api.VehicleLocationApi;
import com.xrkc.job.domain.SystemDict;
import com.xrkc.job.module.vehicle.mapper.VehiclePositionCurrentMapper;
import com.xrkc.job.module.vehicle.service.IVehicleAlarmService;
import com.xrkc.job.module.vehicle.service.IVehiclePositionCalcService;
import com.xrkc.job.module.vehicle.service.IVehiclePositionCurrentService;
import com.xrkc.job.service.ISystemDictService;
import com.xrkc.redis.utils.RedisCacheUtils;
import java.lang.invoke.SerializedLambda;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/module/vehicle/service/impl/VehiclePositionCurrentServiceImpl.class */
public class VehiclePositionCurrentServiceImpl implements IVehiclePositionCurrentService {
    private final VehiclePositionCurrentMapper mapper;
    private final IVehiclePositionCalcService vehiclePositionCalcService;
    private final ISystemDictService systemDictService;
    private final IVehicleAlarmService vehicleAlarmService;
    @Autowired
    public VehiclePositionCurrentServiceImpl(VehiclePositionCurrentMapper mapper, IVehiclePositionCalcService vehiclePositionCalcService, ISystemDictService systemDictService, IVehicleAlarmService vehicleAlarmService) {
        this.mapper = mapper;
        this.vehiclePositionCalcService = vehiclePositionCalcService;
        this.systemDictService = systemDictService;
        this.vehicleAlarmService = vehicleAlarmService;
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehiclePositionCurrentService
    public List<VehiclePositionCurrent> selectCurrentList() throws NumberFormatException {
        Long onlineMinute = Long.valueOf(RedisCacheUtils.getConfigRedisCache().getOrDefault(ConfigValue.CONFIG_KEY_VEHICLE_ONLINE_MINUTE, "30"));
        List<VehiclePositionCurrent> list = this.mapper.selectCurrentList(onlineMinute);
        Map<String, SystemDict> dictMap = this.systemDictService.findValEntityMap("vehicle_type");
        Map<Long, Long> vehicleAlarmMap = this.vehicleAlarmService.findUndisposedMap();
        list.forEach(t -> {
            if (StringUtils.isNotBlank(t.getVehicleType())) {
                SystemDict dict = (SystemDict) dictMap.get(t.getVehicleType());
                if (Objects.nonNull(dict)) {
                    t.setVehicleAttribute(dict.getDictAttribute());
                    t.setVehicleTypeName(dict.getDictLabel());
                }
            }
            if (CommonUtil.containsZeroWidthCharacters(t.getVehicleName())) {
                t.setVehicleName(CommonUtil.removeZeroWidthCharacters(t.getVehicleName()));
            }
            Long undisposedAlarmCount = Objects.nonNull(t.getVehicleId()) ? (Long) vehicleAlarmMap.getOrDefault(t.getVehicleId(), 0L) : 0L;
            t.setUndisposedAlarmCount(undisposedAlarmCount);
        });
        return list;
    }
    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.xrkc.job.module.vehicle.service.IVehiclePositionCurrentService
    public void batchInsert(List<VehiclePositionCurrent> currentList) {
        if (CollectionUtils.isEmpty(currentList)) {
            return;
        }
        List<VehiclePositionCurrent> list = this.mapper.selectList((Wrapper) new LambdaQueryWrapperX<VehiclePositionCurrent>().in((LambdaQueryWrapperX<VehiclePositionCurrent>) (v0) -> {
            return v0.getCardId();
        }, (Collection<?>) currentList.stream().map((v0) -> {
            return v0.getCardId();
        }).collect(Collectors.toList())));
        Map<Long, VehiclePositionCurrent> cardEntityMap = (Map) list.stream().collect(Collectors.toMap((v0) -> {
            return v0.getCardId();
        }, Function.identity()));
        List<VehiclePositionCalc> calcList = new ArrayList<>();
        currentList.stream().forEach(t -> {
            t.setId(null);
            t.setCreateTime(LocalDateTime.now());
            if (cardEntityMap.containsKey(t.getCardId())) {
                VehiclePositionCurrent oldCurrent = (VehiclePositionCurrent) cardEntityMap.get(t.getCardId());
                Long old = VehicleLocationApi.toSecondStamp(oldCurrent.getAcceptTime());
                Long current = VehicleLocationApi.toSecondStamp(t.getAcceptTime());
                if (old.compareTo(current) == 0) {
                    return;
                }
                t.setId(oldCurrent.getId());
                this.mapper.updateById(t);
            } else {
                this.mapper.insert(t);
            }
            VehiclePositionCalc calc = (VehiclePositionCalc) BeanUtil.copyProperties((Object) t, VehiclePositionCalc.class, new String[0]);
            calcList.add(calc);
        });
        this.vehiclePositionCalcService.batchInsert(calcList);
    }
}
