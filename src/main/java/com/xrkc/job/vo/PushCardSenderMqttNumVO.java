package com.xrkc.job.vo;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/vo/PushCardSenderMqttNumVO.class */
public class PushCardSenderMqttNumVO {
    private Integer aims;
    private Integer num;
    private Integer exist;
    private Long cardId;
    private Integer electricity;
    public void setAims(Integer aims) {
        this.aims = aims;
    }
    public void setNum(Integer num) {
        this.num = num;
    }
    public void setExist(Integer exist) {
        this.exist = exist;
    }
    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }
    public void setElectricity(Integer electricity) {
        this.electricity = electricity;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof PushCardSenderMqttNumVO)) {
            return false;
        }
        PushCardSenderMqttNumVO other = (PushCardSenderMqttNumVO) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$aims = getAims();
        Object other$aims = other.getAims();
        if (this$aims == null) {
            if (other$aims != null) {
                return false;
            }
        } else if (!this$aims.equals(other$aims)) {
            return false;
        }
        Object this$num = getNum();
        Object other$num = other.getNum();
        if (this$num == null) {
            if (other$num != null) {
                return false;
            }
        } else if (!this$num.equals(other$num)) {
            return false;
        }
        Object this$exist = getExist();
        Object other$exist = other.getExist();
        if (this$exist == null) {
            if (other$exist != null) {
                return false;
            }
        } else if (!this$exist.equals(other$exist)) {
            return false;
        }
        Object this$cardId = getCardId();
        Object other$cardId = other.getCardId();
        if (this$cardId == null) {
            if (other$cardId != null) {
                return false;
            }
        } else if (!this$cardId.equals(other$cardId)) {
            return false;
        }
        Object this$electricity = getElectricity();
        Object other$electricity = other.getElectricity();
        return this$electricity == null ? other$electricity == null : this$electricity.equals(other$electricity);
    }
    protected boolean canEqual(Object other) {
        return other instanceof PushCardSenderMqttNumVO;
    }
    public int hashCode() {
        Object $aims = getAims();
        int result = (1 * 59) + ($aims == null ? 43 : $aims.hashCode());
        Object $num = getNum();
        int result2 = (result * 59) + ($num == null ? 43 : $num.hashCode());
        Object $exist = getExist();
        int result3 = (result2 * 59) + ($exist == null ? 43 : $exist.hashCode());
        Object $cardId = getCardId();
        int result4 = (result3 * 59) + ($cardId == null ? 43 : $cardId.hashCode());
        Object $electricity = getElectricity();
        return (result4 * 59) + ($electricity == null ? 43 : $electricity.hashCode());
    }
    public String toString() {
        return "PushCardSenderMqttNumVO(aims=" + getAims() + ", num=" + getNum() + ", exist=" + getExist() + ", cardId=" + getCardId() + ", electricity=" + getElectricity() + StringPool.RIGHT_BRACKET;
    }
    public Integer getAims() {
        return this.aims;
    }
    public Integer getNum() {
        return this.num;
    }
    public Integer getExist() {
        return this.exist;
    }
    public Long getCardId() {
        return this.cardId;
    }
    public Integer getElectricity() {
        return this.electricity;
    }
}
