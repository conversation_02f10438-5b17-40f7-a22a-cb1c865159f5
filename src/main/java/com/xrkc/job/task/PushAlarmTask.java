package com.xrkc.job.task;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.xrkc.core.constant.Constants;
import com.xrkc.core.domain.system.SystemDept;
import com.xrkc.datascope.aspect.DataScopeAspect;
import com.xrkc.job.controller.WebSocketServer;
import com.xrkc.job.domain.Alarm;
import com.xrkc.job.domain.SystemRoleFacilityVO;
import com.xrkc.job.domain.SystemUser;
import com.xrkc.job.domain.SystemUserDataScopeVO;
import com.xrkc.job.service.IAlarmService;
import com.xrkc.job.service.IDeviceCameraService;
import com.xrkc.job.service.ISystemDeptService;
import com.xrkc.job.service.ISystemRoleFacilityService;
import com.xrkc.job.service.ISystemUserRoleService;
import com.xrkc.job.service.ISystemUserService;
import com.xrkc.job.util.CommonUtil;
import java.time.LocalDateTime;
import java.time.chrono.ChronoLocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import net.jodah.expiringmap.ExpiringMap;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
@Component("pushAlarmTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/PushAlarmTask.class */
public class PushAlarmTask {
    @Autowired
    private WebSocketServer webSocketServer;
    @Autowired
    private ISystemUserRoleService systemUserRoleService;
    @Autowired
    private ISystemUserService systemUserService;
    @Autowired
    private ISystemDeptService systemDeptService;
    @Autowired
    private ISystemRoleFacilityService systemRoleFacilityService;
    @Autowired
    private IAlarmService alarmService;
    @Autowired
    private IDeviceCameraService deviceCameraService;
    ExpiringMap<String, LocalDateTime> wsPushAlarmMap = ExpiringMap.builder().expiration(1, TimeUnit.DAYS).build();
    private static final Logger log = LoggerFactory.getLogger((Class<?>) PushAlarmTask.class);
    private static final DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public void pushCurrentPersonAlarm() {
        if (CollectionUtils.isEmpty(WebSocketServer.systemSessionMap)) {
            return;
        }
        Set<Long> wsUserIds = new HashSet<>();
        WebSocketServer.systemSessionMap.forEach((socketKey, session) -> {
            String userId = socketKey.split("_")[2];
            wsUserIds.add(Long.valueOf(userId));
        });
        List<SystemUser> systemUsers = this.systemUserService.findByUserIds(wsUserIds);
        systemUsers.removeIf(r -> {
            return !wsUserIds.contains(r.getUserId());
        });
        Set<Long> pushUserIds = (Set) systemUsers.stream().map((v0) -> {
            return v0.getUserId();
        }).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(pushUserIds)) {
            return;
        }
        List<Alarm> list = this.alarmService.selectUnDisposedAlarm(null);
        if (CollectionUtils.isEmpty(list)) {
            pushAlarmCount(null, 0);
            return;
        }
        JSONObject pushJson = new JSONObject();
        pushJson.put("msgType", "alarm");
        List<SystemDept> allDeptList = this.systemDeptService.selectList();
        Map<Long, List<SystemUserDataScopeVO>> dataScopeMap = (Map) this.systemUserRoleService.findDataScopeByUserIds(pushUserIds).stream().collect(Collectors.groupingBy((v0) -> {
            return v0.getUserId();
        }));
        systemUsers.stream().forEach(user -> {
            Long userId = user.getUserId();
            if ("xrkc_admin".equals(user.getUserName())) {
                pushAlarmCount(userId, list.size());
                pushAlarm(list, userId, pushJson);
                return;
            }
            List<SystemUserDataScopeVO> dataScopeVOList = (List) dataScopeMap.get(userId);
            if (CollectionUtils.isEmpty(dataScopeVOList)) {
                pushAlarmCount(userId, 0);
                return;
            }
            Set<String> userDataScopeSet = (Set) dataScopeVOList.stream().map((v0) -> {
                return v0.getDataScope();
            }).collect(Collectors.toSet());
            if (userDataScopeSet.contains("1")) {
                pushAlarmCount(userId, list.size());
                pushAlarm(list, userId, pushJson);
                return;
            }
            Long userDeptId = user.getDeptId();
            Long userPersonId = user.getPersonId();
            Set<Long> filterDeptIds = new HashSet<>();
            Set<Long> customDeptIds = (Set) dataScopeVOList.stream().filter(f -> {
                return "2".equals(f.getDataScope()) && Objects.nonNull(f.getDeptId());
            }).map((v0) -> {
                return v0.getDeptId();
            }).collect(Collectors.toSet());
            filterDeptIds.addAll(customDeptIds);
            if (userDataScopeSet.contains(DataScopeAspect.DATA_SCOPE_CUSTOM_AND_CHILD)) {
                Set<Long> cDeptIds = (Set) dataScopeVOList.stream().filter(f2 -> {
                    return DataScopeAspect.DATA_SCOPE_CUSTOM_AND_CHILD.equals(f2.getDataScope()) && Objects.nonNull(f2.getDeptId());
                }).map((v0) -> {
                    return v0.getDeptId();
                }).collect(Collectors.toSet());
                filterDeptIds.addAll(cDeptIds);
                cDeptIds.forEach(cId -> {
                    filterDeptIds.addAll(CommonUtil.getChildDeptIds(cId, allDeptList));
                });
            }
            if (userDataScopeSet.contains("3") && Objects.nonNull(userDeptId)) {
                filterDeptIds.add(userDeptId);
            }
            if (userDataScopeSet.contains("4")) {
                filterDeptIds.addAll(CommonUtil.getChildDeptIds(userDeptId, allDeptList));
            }
            List<Alarm> deptDataLocation = (List) list.stream().filter(f3 -> {
                return Objects.nonNull(f3.getDeptId()) && filterDeptIds.contains(f3.getDeptId());
            }).collect(Collectors.toList());
            List<Alarm> personDataLocation = new ArrayList<>();
            if (userDataScopeSet.contains("5") && Objects.nonNull(userPersonId)) {
                personDataLocation = (List) list.stream().filter(f4 -> {
                    return Objects.nonNull(f4.getPersonId()) && userPersonId.compareTo(f4.getPersonId()) == 0;
                }).collect(Collectors.toList());
            }
            List<Alarm> facilityDataLocation = new ArrayList<>();
            List<SystemRoleFacilityVO> systemRoleFacilityVOList = this.systemRoleFacilityService.findByRoleIds((List) dataScopeVOList.stream().map((v0) -> {
                return v0.getRoleId();
            }).collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(systemRoleFacilityVOList)) {
                facilityDataLocation = (List) list.stream().filter(f5 -> {
                    return CommonUtil.containsFacilityRailScope(f5.getLayerId(), f5.getLongitude(), f5.getLatitude(), systemRoleFacilityVOList);
                }).collect(Collectors.toList());
            }
            deptDataLocation.addAll(personDataLocation);
            deptDataLocation.addAll(facilityDataLocation);
            List<Alarm> deptDataLocation2 = (List) deptDataLocation.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> {
                return new TreeSet(Comparator.comparing((v0) -> {
                    return v0.getAlarmId();
                }));
            }), (v1) -> {
                return new ArrayList(v1);
            }));
            pushAlarmCount(userId, deptDataLocation2.size());
            pushAlarm(deptDataLocation2, userId, pushJson);
        });
    }
    private void pushAlarm(List<Alarm> list, Long userId, JSONObject pushJson) {
        list.stream().filter(f -> {
            return f.getCreateTime().plusSeconds(30L).compareTo((ChronoLocalDateTime<?>) LocalDateTime.now()) > -1;
        }).forEach(t -> {
            String pushKey = userId + "_" + t.getAlarmId();
            if (!this.wsPushAlarmMap.containsKey(pushKey)) {
                JSONObject data = new JSONObject();
                data.put("alarmId", t.getAlarmId());
                data.put("acceptTime", t.getAcceptTime().format(df));
                data.put("alarmName", t.getAlarmName());
                data.put("cardId", t.getCardId());
                data.put("realName", t.getRealName());
                data.put("layerId", t.getLayerId());
                data.put("alarmType", t.getAlarmType());
                data.put("alarmTypeName", t.getAlarmTypeName());
                log.debug("正在进行报警推送……");
                String rtsp = null;
                if (Constants.ALARM_TYPE_95.equals(t.getAlarmType())) {
                    rtsp = this.deviceCameraService.findRtspByParam(t.getLayerId(), t.getLongitude(), t.getLatitude());
                }
                data.put("rtsp", StringUtils.isNotBlank(rtsp) ? rtsp : "");
                if (Constants.ALARM_TYPE_80.equals(t.getAlarmType()) || "81".equals(t.getAlarmType())) {
                    pushJson.put("msgType", "senderAlarm");
                }
                pushJson.put("data", data);
                this.webSocketServer.sendMessageForSystemUser(userId, JSONObject.toJSONString(pushJson, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible));
                this.wsPushAlarmMap.put(pushKey, t.getAcceptTime());
            }
        });
    }
    private void pushAlarmCount(Long userId, int count) {
        JSONObject pushJson = new JSONObject();
        pushJson.put("msgType", "alarmCount");
        pushJson.put("data", Integer.valueOf(count));
        if (Objects.nonNull(userId)) {
            this.webSocketServer.sendMessageForSystemUser(userId, JSONObject.toJSONString(pushJson, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible));
        } else {
            this.webSocketServer.sendMessageForXR(null, JSONObject.toJSONString(pushJson, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.BrowserCompatible));
        }
    }
}
