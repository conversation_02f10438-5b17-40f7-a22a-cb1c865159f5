package com.xrkc.job.module.vehicle.service.impl;
import com.xrkc.core.domain.vehicle.VehicleAlarmSettingRail;
import com.xrkc.job.module.vehicle.mapper.VehicleAlarmSettingRailMapper;
import com.xrkc.job.module.vehicle.service.IVehicleAlarmSettingRailService;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/module/vehicle/service/impl/VehicleAlarmSettingRailServiceImpl.class */
public class VehicleAlarmSettingRailServiceImpl implements IVehicleAlarmSettingRailService {
    private final VehicleAlarmSettingRailMapper mapper;
    @Autowired
    public VehicleAlarmSettingRailServiceImpl(VehicleAlarmSettingRailMapper mapper) {
        this.mapper = mapper;
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehicleAlarmSettingRailService
    public void batchInsert(List<VehicleAlarmSettingRail> list, Long settingId) {
        if (Objects.nonNull(settingId)) {
            this.mapper.deleteBatch((v0) -> {
                return v0.getSettingId();
            }, settingId);
        }
        if (!CollectionUtils.isEmpty(list)) {
            this.mapper.insertBatch(list);
        }
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehicleAlarmSettingRailService
    public List<Long> getRailIdListBySettingId(Long settingId) {
        if (Objects.nonNull(settingId)) {
            return (List) this.mapper.selectList((v0) -> {
                return v0.getSettingId();
            }, settingId).stream().map((v0) -> {
                return v0.getRailId();
            }).collect(Collectors.toList());
        }
        return null;
    }
}
