package com.xrkc.job.domain;
import java.io.Serializable;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/Rail.class */
public class Rail implements Serializable {
    private static final long serialVersionUID = 1;
    private Long railId;
    private String railName;
    private String railType;
    private String layerId;
    private String color;
    private Integer layerHeight;
    private Integer railHeight;
    private String railScope;
    private String drawType;
    public Integer getRailHeight() {
        return this.railHeight;
    }
    public void setRailHeight(Integer railHeight) {
        this.railHeight = railHeight;
    }
    public String getColor() {
        return this.color;
    }
    public void setColor(String color) {
        this.color = color;
    }
    public String getRailType() {
        return this.railType;
    }
    public void setRailType(String railType) {
        this.railType = railType;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
    public Integer getLayerHeight() {
        return this.layerHeight;
    }
    public void setLayerHeight(Integer layerHeight) {
        this.layerHeight = layerHeight;
    }
    public String getRailScope() {
        return this.railScope;
    }
    public void setRailScope(String railScope) {
        this.railScope = railScope;
    }
    public Long getRailId() {
        return this.railId;
    }
    public void setRailId(Long railId) {
        this.railId = railId;
    }
    public String getRailName() {
        return this.railName;
    }
    public void setRailName(String railName) {
        this.railName = railName;
    }
    public String getDrawType() {
        return this.drawType;
    }
    public void setDrawType(String drawType) {
        this.drawType = drawType;
    }
}
