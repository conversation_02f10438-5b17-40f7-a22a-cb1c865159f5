package com.xrkc.job.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xrkc.core.domain.system.SystemRoleFacility;
import com.xrkc.job.domain.SystemRoleFacilityVO;
import java.util.List;
import org.apache.ibatis.annotations.Param;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/SystemRoleFacilityMapper.class */
public interface SystemRoleFacilityMapper extends BaseMapper<SystemRoleFacility> {
    List<SystemRoleFacilityVO> findByRoleIds(@Param("list") List<Long> list);
}
