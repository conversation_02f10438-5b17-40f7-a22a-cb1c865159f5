package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.job.domain.FacilityAccessRecord;
import com.xrkc.job.mapper.FacilityAccessRecordMapper;
import com.xrkc.job.service.IFacilityAccessRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/FacilityAccessRecordServiceImpl.class */
public class FacilityAccessRecordServiceImpl extends ServiceImpl<FacilityAccessRecordMapper, FacilityAccessRecord> implements IFacilityAccessRecordService {
    @Autowired
    private FacilityAccessRecordMapper facilityAccessRecordMapper;
}
