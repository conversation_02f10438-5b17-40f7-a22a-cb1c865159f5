package com.xrkc.job.service.impl;
import com.xrkc.job.domain.Rail;
import com.xrkc.job.mapper.RailMapper;
import com.xrkc.job.service.IRailService;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/RailServiceImpl.class */
public class RailServiceImpl implements IRailService {
    @Autowired
    private RailMapper railMapper;
    @Override // com.xrkc.job.service.IRailService
    public List<Rail> selectList(Map<String, Object> map) {
        return this.railMapper.selectList(map);
    }
    @Override // com.xrkc.job.service.IRailService
    public Rail selectOneByName(String railName) {
        return this.railMapper.selectOneByName(railName);
    }
    @Override // com.xrkc.job.service.IRailService
    public List<Rail> selectByIdList(List<Long> railIdList) {
        return this.railMapper.selectByIdList(railIdList);
    }
}
