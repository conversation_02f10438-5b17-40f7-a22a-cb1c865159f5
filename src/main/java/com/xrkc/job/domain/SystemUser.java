package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
@TableName("system_user")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/SystemUser.class */
public class SystemUser implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId("user_id")
    private Long userId;
    @TableField("user_name")
    private String userName;
    @TableField("password")
    private String password;
    @TableField("user_type")
    private String userType;
    @TableField("user_enable")
    private String userEnable;
    @TableField("create_by")
    private String createBy;
    @TableField("create_time")
    private LocalDateTime createTime;
    private Long deptId;
    private Long personId;
    public Long getUserId() {
        return this.userId;
    }
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    public String getUserName() {
        return this.userName;
    }
    public void setUserName(String userName) {
        this.userName = userName;
    }
    public String getPassword() {
        return this.password;
    }
    public void setPassword(String password) {
        this.password = password;
    }
    public String getUserType() {
        return this.userType;
    }
    public void setUserType(String userType) {
        this.userType = userType;
    }
    public String getUserEnable() {
        return this.userEnable;
    }
    public void setUserEnable(String userEnable) {
        this.userEnable = userEnable;
    }
    public String getCreateBy() {
        return this.createBy;
    }
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public Long getDeptId() {
        return this.deptId;
    }
    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
    public Long getPersonId() {
        return this.personId;
    }
    public void setPersonId(Long personId) {
        this.personId = personId;
    }
}
