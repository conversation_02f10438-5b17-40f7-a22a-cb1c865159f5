package com.xrkc.job.domain;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.xrkc.core.domain.rail.RailDrawType;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/AreaAlarmTempVO.class */
public class AreaAlarmTempVO implements Serializable {
    private static final long serialVersionUID = 1;
    private Long areaId;
    private String areaName;
    private String alarmType;
    private String rule;
    private String secondAlarmRule;
    private String thirdAlarmRule;
    private LocalDate validBeginDate;
    private LocalDate validEndDate;
    private LocalTime validBeginTime;
    private LocalTime validEndTime;
    private List<Long> railIds;
    private List<Long> deptIds;
    private List<Long> personIds;
    private List<RailDrawType> railDrawList;
    private Long railId;
    private String railName;
    private Integer railCount;
    private String layerId;
    private List<String> layerIds;
    private Integer layerHeight;
    private String railScope;
    private List<Rail> railList;
    private Long alarmNoticeId;
    private String alarmNoticeName;
    private String noticeType;
    private String firstPersonId;
    private String secondPersonId;
    private Long secondIntervalSecond;
    private String thirdPersonId;
    private Long thirdIntervalSecond;
    private LocalDateTime acceptTime;
    private LocalDateTime minusSeconds;
    private String areaType;
    private List<Long> postIds;
    private List<Long> alarmPersonIds;
    private Boolean parameterCondition;
    private Integer railHeight;
    private String configurationRelation;
    private String drawType;
    private String buildingName;
    private Long buildingId;
    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }
    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }
    public void setAlarmType(String alarmType) {
        this.alarmType = alarmType;
    }
    public void setRule(String rule) {
        this.rule = rule;
    }
    public void setSecondAlarmRule(String secondAlarmRule) {
        this.secondAlarmRule = secondAlarmRule;
    }
    public void setThirdAlarmRule(String thirdAlarmRule) {
        this.thirdAlarmRule = thirdAlarmRule;
    }
    public void setValidBeginDate(LocalDate validBeginDate) {
        this.validBeginDate = validBeginDate;
    }
    public void setValidEndDate(LocalDate validEndDate) {
        this.validEndDate = validEndDate;
    }
    public void setValidBeginTime(LocalTime validBeginTime) {
        this.validBeginTime = validBeginTime;
    }
    public void setValidEndTime(LocalTime validEndTime) {
        this.validEndTime = validEndTime;
    }
    public void setRailIds(List<Long> railIds) {
        this.railIds = railIds;
    }
    public void setDeptIds(List<Long> deptIds) {
        this.deptIds = deptIds;
    }
    public void setPersonIds(List<Long> personIds) {
        this.personIds = personIds;
    }
    public void setRailDrawList(List<RailDrawType> railDrawList) {
        this.railDrawList = railDrawList;
    }
    public void setRailId(Long railId) {
        this.railId = railId;
    }
    public void setRailName(String railName) {
        this.railName = railName;
    }
    public void setRailCount(Integer railCount) {
        this.railCount = railCount;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
    public void setLayerIds(List<String> layerIds) {
        this.layerIds = layerIds;
    }
    public void setLayerHeight(Integer layerHeight) {
        this.layerHeight = layerHeight;
    }
    public void setRailScope(String railScope) {
        this.railScope = railScope;
    }
    public void setRailList(List<Rail> railList) {
        this.railList = railList;
    }
    public void setAlarmNoticeId(Long alarmNoticeId) {
        this.alarmNoticeId = alarmNoticeId;
    }
    public void setAlarmNoticeName(String alarmNoticeName) {
        this.alarmNoticeName = alarmNoticeName;
    }
    public void setNoticeType(String noticeType) {
        this.noticeType = noticeType;
    }
    public void setFirstPersonId(String firstPersonId) {
        this.firstPersonId = firstPersonId;
    }
    public void setSecondPersonId(String secondPersonId) {
        this.secondPersonId = secondPersonId;
    }
    public void setSecondIntervalSecond(Long secondIntervalSecond) {
        this.secondIntervalSecond = secondIntervalSecond;
    }
    public void setThirdPersonId(String thirdPersonId) {
        this.thirdPersonId = thirdPersonId;
    }
    public void setThirdIntervalSecond(Long thirdIntervalSecond) {
        this.thirdIntervalSecond = thirdIntervalSecond;
    }
    public void setAcceptTime(LocalDateTime acceptTime) {
        this.acceptTime = acceptTime;
    }
    public void setMinusSeconds(LocalDateTime minusSeconds) {
        this.minusSeconds = minusSeconds;
    }
    public void setAreaType(String areaType) {
        this.areaType = areaType;
    }
    public void setPostIds(List<Long> postIds) {
        this.postIds = postIds;
    }
    public void setAlarmPersonIds(List<Long> alarmPersonIds) {
        this.alarmPersonIds = alarmPersonIds;
    }
    public void setParameterCondition(Boolean parameterCondition) {
        this.parameterCondition = parameterCondition;
    }
    public void setRailHeight(Integer railHeight) {
        this.railHeight = railHeight;
    }
    public void setConfigurationRelation(String configurationRelation) {
        this.configurationRelation = configurationRelation;
    }
    public void setDrawType(String drawType) {
        this.drawType = drawType;
    }
    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }
    public void setBuildingId(Long buildingId) {
        this.buildingId = buildingId;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof AreaAlarmTempVO)) {
            return false;
        }
        AreaAlarmTempVO other = (AreaAlarmTempVO) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$areaId = getAreaId();
        Object other$areaId = other.getAreaId();
        if (this$areaId == null) {
            if (other$areaId != null) {
                return false;
            }
        } else if (!this$areaId.equals(other$areaId)) {
            return false;
        }
        Object this$railId = getRailId();
        Object other$railId = other.getRailId();
        if (this$railId == null) {
            if (other$railId != null) {
                return false;
            }
        } else if (!this$railId.equals(other$railId)) {
            return false;
        }
        Object this$railCount = getRailCount();
        Object other$railCount = other.getRailCount();
        if (this$railCount == null) {
            if (other$railCount != null) {
                return false;
            }
        } else if (!this$railCount.equals(other$railCount)) {
            return false;
        }
        Object this$layerHeight = getLayerHeight();
        Object other$layerHeight = other.getLayerHeight();
        if (this$layerHeight == null) {
            if (other$layerHeight != null) {
                return false;
            }
        } else if (!this$layerHeight.equals(other$layerHeight)) {
            return false;
        }
        Object this$alarmNoticeId = getAlarmNoticeId();
        Object other$alarmNoticeId = other.getAlarmNoticeId();
        if (this$alarmNoticeId == null) {
            if (other$alarmNoticeId != null) {
                return false;
            }
        } else if (!this$alarmNoticeId.equals(other$alarmNoticeId)) {
            return false;
        }
        Object this$secondIntervalSecond = getSecondIntervalSecond();
        Object other$secondIntervalSecond = other.getSecondIntervalSecond();
        if (this$secondIntervalSecond == null) {
            if (other$secondIntervalSecond != null) {
                return false;
            }
        } else if (!this$secondIntervalSecond.equals(other$secondIntervalSecond)) {
            return false;
        }
        Object this$thirdIntervalSecond = getThirdIntervalSecond();
        Object other$thirdIntervalSecond = other.getThirdIntervalSecond();
        if (this$thirdIntervalSecond == null) {
            if (other$thirdIntervalSecond != null) {
                return false;
            }
        } else if (!this$thirdIntervalSecond.equals(other$thirdIntervalSecond)) {
            return false;
        }
        Object this$parameterCondition = getParameterCondition();
        Object other$parameterCondition = other.getParameterCondition();
        if (this$parameterCondition == null) {
            if (other$parameterCondition != null) {
                return false;
            }
        } else if (!this$parameterCondition.equals(other$parameterCondition)) {
            return false;
        }
        Object this$railHeight = getRailHeight();
        Object other$railHeight = other.getRailHeight();
        if (this$railHeight == null) {
            if (other$railHeight != null) {
                return false;
            }
        } else if (!this$railHeight.equals(other$railHeight)) {
            return false;
        }
        Object this$buildingId = getBuildingId();
        Object other$buildingId = other.getBuildingId();
        if (this$buildingId == null) {
            if (other$buildingId != null) {
                return false;
            }
        } else if (!this$buildingId.equals(other$buildingId)) {
            return false;
        }
        Object this$areaName = getAreaName();
        Object other$areaName = other.getAreaName();
        if (this$areaName == null) {
            if (other$areaName != null) {
                return false;
            }
        } else if (!this$areaName.equals(other$areaName)) {
            return false;
        }
        Object this$alarmType = getAlarmType();
        Object other$alarmType = other.getAlarmType();
        if (this$alarmType == null) {
            if (other$alarmType != null) {
                return false;
            }
        } else if (!this$alarmType.equals(other$alarmType)) {
            return false;
        }
        Object this$rule = getRule();
        Object other$rule = other.getRule();
        if (this$rule == null) {
            if (other$rule != null) {
                return false;
            }
        } else if (!this$rule.equals(other$rule)) {
            return false;
        }
        Object this$secondAlarmRule = getSecondAlarmRule();
        Object other$secondAlarmRule = other.getSecondAlarmRule();
        if (this$secondAlarmRule == null) {
            if (other$secondAlarmRule != null) {
                return false;
            }
        } else if (!this$secondAlarmRule.equals(other$secondAlarmRule)) {
            return false;
        }
        Object this$thirdAlarmRule = getThirdAlarmRule();
        Object other$thirdAlarmRule = other.getThirdAlarmRule();
        if (this$thirdAlarmRule == null) {
            if (other$thirdAlarmRule != null) {
                return false;
            }
        } else if (!this$thirdAlarmRule.equals(other$thirdAlarmRule)) {
            return false;
        }
        Object this$validBeginDate = getValidBeginDate();
        Object other$validBeginDate = other.getValidBeginDate();
        if (this$validBeginDate == null) {
            if (other$validBeginDate != null) {
                return false;
            }
        } else if (!this$validBeginDate.equals(other$validBeginDate)) {
            return false;
        }
        Object this$validEndDate = getValidEndDate();
        Object other$validEndDate = other.getValidEndDate();
        if (this$validEndDate == null) {
            if (other$validEndDate != null) {
                return false;
            }
        } else if (!this$validEndDate.equals(other$validEndDate)) {
            return false;
        }
        Object this$validBeginTime = getValidBeginTime();
        Object other$validBeginTime = other.getValidBeginTime();
        if (this$validBeginTime == null) {
            if (other$validBeginTime != null) {
                return false;
            }
        } else if (!this$validBeginTime.equals(other$validBeginTime)) {
            return false;
        }
        Object this$validEndTime = getValidEndTime();
        Object other$validEndTime = other.getValidEndTime();
        if (this$validEndTime == null) {
            if (other$validEndTime != null) {
                return false;
            }
        } else if (!this$validEndTime.equals(other$validEndTime)) {
            return false;
        }
        Object this$railIds = getRailIds();
        Object other$railIds = other.getRailIds();
        if (this$railIds == null) {
            if (other$railIds != null) {
                return false;
            }
        } else if (!this$railIds.equals(other$railIds)) {
            return false;
        }
        Object this$deptIds = getDeptIds();
        Object other$deptIds = other.getDeptIds();
        if (this$deptIds == null) {
            if (other$deptIds != null) {
                return false;
            }
        } else if (!this$deptIds.equals(other$deptIds)) {
            return false;
        }
        Object this$personIds = getPersonIds();
        Object other$personIds = other.getPersonIds();
        if (this$personIds == null) {
            if (other$personIds != null) {
                return false;
            }
        } else if (!this$personIds.equals(other$personIds)) {
            return false;
        }
        Object this$railDrawList = getRailDrawList();
        Object other$railDrawList = other.getRailDrawList();
        if (this$railDrawList == null) {
            if (other$railDrawList != null) {
                return false;
            }
        } else if (!this$railDrawList.equals(other$railDrawList)) {
            return false;
        }
        Object this$railName = getRailName();
        Object other$railName = other.getRailName();
        if (this$railName == null) {
            if (other$railName != null) {
                return false;
            }
        } else if (!this$railName.equals(other$railName)) {
            return false;
        }
        Object this$layerId = getLayerId();
        Object other$layerId = other.getLayerId();
        if (this$layerId == null) {
            if (other$layerId != null) {
                return false;
            }
        } else if (!this$layerId.equals(other$layerId)) {
            return false;
        }
        Object this$layerIds = getLayerIds();
        Object other$layerIds = other.getLayerIds();
        if (this$layerIds == null) {
            if (other$layerIds != null) {
                return false;
            }
        } else if (!this$layerIds.equals(other$layerIds)) {
            return false;
        }
        Object this$railScope = getRailScope();
        Object other$railScope = other.getRailScope();
        if (this$railScope == null) {
            if (other$railScope != null) {
                return false;
            }
        } else if (!this$railScope.equals(other$railScope)) {
            return false;
        }
        Object this$railList = getRailList();
        Object other$railList = other.getRailList();
        if (this$railList == null) {
            if (other$railList != null) {
                return false;
            }
        } else if (!this$railList.equals(other$railList)) {
            return false;
        }
        Object this$alarmNoticeName = getAlarmNoticeName();
        Object other$alarmNoticeName = other.getAlarmNoticeName();
        if (this$alarmNoticeName == null) {
            if (other$alarmNoticeName != null) {
                return false;
            }
        } else if (!this$alarmNoticeName.equals(other$alarmNoticeName)) {
            return false;
        }
        Object this$noticeType = getNoticeType();
        Object other$noticeType = other.getNoticeType();
        if (this$noticeType == null) {
            if (other$noticeType != null) {
                return false;
            }
        } else if (!this$noticeType.equals(other$noticeType)) {
            return false;
        }
        Object this$firstPersonId = getFirstPersonId();
        Object other$firstPersonId = other.getFirstPersonId();
        if (this$firstPersonId == null) {
            if (other$firstPersonId != null) {
                return false;
            }
        } else if (!this$firstPersonId.equals(other$firstPersonId)) {
            return false;
        }
        Object this$secondPersonId = getSecondPersonId();
        Object other$secondPersonId = other.getSecondPersonId();
        if (this$secondPersonId == null) {
            if (other$secondPersonId != null) {
                return false;
            }
        } else if (!this$secondPersonId.equals(other$secondPersonId)) {
            return false;
        }
        Object this$thirdPersonId = getThirdPersonId();
        Object other$thirdPersonId = other.getThirdPersonId();
        if (this$thirdPersonId == null) {
            if (other$thirdPersonId != null) {
                return false;
            }
        } else if (!this$thirdPersonId.equals(other$thirdPersonId)) {
            return false;
        }
        Object this$acceptTime = getAcceptTime();
        Object other$acceptTime = other.getAcceptTime();
        if (this$acceptTime == null) {
            if (other$acceptTime != null) {
                return false;
            }
        } else if (!this$acceptTime.equals(other$acceptTime)) {
            return false;
        }
        Object this$minusSeconds = getMinusSeconds();
        Object other$minusSeconds = other.getMinusSeconds();
        if (this$minusSeconds == null) {
            if (other$minusSeconds != null) {
                return false;
            }
        } else if (!this$minusSeconds.equals(other$minusSeconds)) {
            return false;
        }
        Object this$areaType = getAreaType();
        Object other$areaType = other.getAreaType();
        if (this$areaType == null) {
            if (other$areaType != null) {
                return false;
            }
        } else if (!this$areaType.equals(other$areaType)) {
            return false;
        }
        Object this$postIds = getPostIds();
        Object other$postIds = other.getPostIds();
        if (this$postIds == null) {
            if (other$postIds != null) {
                return false;
            }
        } else if (!this$postIds.equals(other$postIds)) {
            return false;
        }
        Object this$alarmPersonIds = getAlarmPersonIds();
        Object other$alarmPersonIds = other.getAlarmPersonIds();
        if (this$alarmPersonIds == null) {
            if (other$alarmPersonIds != null) {
                return false;
            }
        } else if (!this$alarmPersonIds.equals(other$alarmPersonIds)) {
            return false;
        }
        Object this$configurationRelation = getConfigurationRelation();
        Object other$configurationRelation = other.getConfigurationRelation();
        if (this$configurationRelation == null) {
            if (other$configurationRelation != null) {
                return false;
            }
        } else if (!this$configurationRelation.equals(other$configurationRelation)) {
            return false;
        }
        Object this$drawType = getDrawType();
        Object other$drawType = other.getDrawType();
        if (this$drawType == null) {
            if (other$drawType != null) {
                return false;
            }
        } else if (!this$drawType.equals(other$drawType)) {
            return false;
        }
        Object this$buildingName = getBuildingName();
        Object other$buildingName = other.getBuildingName();
        return this$buildingName == null ? other$buildingName == null : this$buildingName.equals(other$buildingName);
    }
    protected boolean canEqual(Object other) {
        return other instanceof AreaAlarmTempVO;
    }
    public int hashCode() {
        Object $areaId = getAreaId();
        int result = (1 * 59) + ($areaId == null ? 43 : $areaId.hashCode());
        Object $railId = getRailId();
        int result2 = (result * 59) + ($railId == null ? 43 : $railId.hashCode());
        Object $railCount = getRailCount();
        int result3 = (result2 * 59) + ($railCount == null ? 43 : $railCount.hashCode());
        Object $layerHeight = getLayerHeight();
        int result4 = (result3 * 59) + ($layerHeight == null ? 43 : $layerHeight.hashCode());
        Object $alarmNoticeId = getAlarmNoticeId();
        int result5 = (result4 * 59) + ($alarmNoticeId == null ? 43 : $alarmNoticeId.hashCode());
        Object $secondIntervalSecond = getSecondIntervalSecond();
        int result6 = (result5 * 59) + ($secondIntervalSecond == null ? 43 : $secondIntervalSecond.hashCode());
        Object $thirdIntervalSecond = getThirdIntervalSecond();
        int result7 = (result6 * 59) + ($thirdIntervalSecond == null ? 43 : $thirdIntervalSecond.hashCode());
        Object $parameterCondition = getParameterCondition();
        int result8 = (result7 * 59) + ($parameterCondition == null ? 43 : $parameterCondition.hashCode());
        Object $railHeight = getRailHeight();
        int result9 = (result8 * 59) + ($railHeight == null ? 43 : $railHeight.hashCode());
        Object $buildingId = getBuildingId();
        int result10 = (result9 * 59) + ($buildingId == null ? 43 : $buildingId.hashCode());
        Object $areaName = getAreaName();
        int result11 = (result10 * 59) + ($areaName == null ? 43 : $areaName.hashCode());
        Object $alarmType = getAlarmType();
        int result12 = (result11 * 59) + ($alarmType == null ? 43 : $alarmType.hashCode());
        Object $rule = getRule();
        int result13 = (result12 * 59) + ($rule == null ? 43 : $rule.hashCode());
        Object $secondAlarmRule = getSecondAlarmRule();
        int result14 = (result13 * 59) + ($secondAlarmRule == null ? 43 : $secondAlarmRule.hashCode());
        Object $thirdAlarmRule = getThirdAlarmRule();
        int result15 = (result14 * 59) + ($thirdAlarmRule == null ? 43 : $thirdAlarmRule.hashCode());
        Object $validBeginDate = getValidBeginDate();
        int result16 = (result15 * 59) + ($validBeginDate == null ? 43 : $validBeginDate.hashCode());
        Object $validEndDate = getValidEndDate();
        int result17 = (result16 * 59) + ($validEndDate == null ? 43 : $validEndDate.hashCode());
        Object $validBeginTime = getValidBeginTime();
        int result18 = (result17 * 59) + ($validBeginTime == null ? 43 : $validBeginTime.hashCode());
        Object $validEndTime = getValidEndTime();
        int result19 = (result18 * 59) + ($validEndTime == null ? 43 : $validEndTime.hashCode());
        Object $railIds = getRailIds();
        int result20 = (result19 * 59) + ($railIds == null ? 43 : $railIds.hashCode());
        Object $deptIds = getDeptIds();
        int result21 = (result20 * 59) + ($deptIds == null ? 43 : $deptIds.hashCode());
        Object $personIds = getPersonIds();
        int result22 = (result21 * 59) + ($personIds == null ? 43 : $personIds.hashCode());
        Object $railDrawList = getRailDrawList();
        int result23 = (result22 * 59) + ($railDrawList == null ? 43 : $railDrawList.hashCode());
        Object $railName = getRailName();
        int result24 = (result23 * 59) + ($railName == null ? 43 : $railName.hashCode());
        Object $layerId = getLayerId();
        int result25 = (result24 * 59) + ($layerId == null ? 43 : $layerId.hashCode());
        Object $layerIds = getLayerIds();
        int result26 = (result25 * 59) + ($layerIds == null ? 43 : $layerIds.hashCode());
        Object $railScope = getRailScope();
        int result27 = (result26 * 59) + ($railScope == null ? 43 : $railScope.hashCode());
        Object $railList = getRailList();
        int result28 = (result27 * 59) + ($railList == null ? 43 : $railList.hashCode());
        Object $alarmNoticeName = getAlarmNoticeName();
        int result29 = (result28 * 59) + ($alarmNoticeName == null ? 43 : $alarmNoticeName.hashCode());
        Object $noticeType = getNoticeType();
        int result30 = (result29 * 59) + ($noticeType == null ? 43 : $noticeType.hashCode());
        Object $firstPersonId = getFirstPersonId();
        int result31 = (result30 * 59) + ($firstPersonId == null ? 43 : $firstPersonId.hashCode());
        Object $secondPersonId = getSecondPersonId();
        int result32 = (result31 * 59) + ($secondPersonId == null ? 43 : $secondPersonId.hashCode());
        Object $thirdPersonId = getThirdPersonId();
        int result33 = (result32 * 59) + ($thirdPersonId == null ? 43 : $thirdPersonId.hashCode());
        Object $acceptTime = getAcceptTime();
        int result34 = (result33 * 59) + ($acceptTime == null ? 43 : $acceptTime.hashCode());
        Object $minusSeconds = getMinusSeconds();
        int result35 = (result34 * 59) + ($minusSeconds == null ? 43 : $minusSeconds.hashCode());
        Object $areaType = getAreaType();
        int result36 = (result35 * 59) + ($areaType == null ? 43 : $areaType.hashCode());
        Object $postIds = getPostIds();
        int result37 = (result36 * 59) + ($postIds == null ? 43 : $postIds.hashCode());
        Object $alarmPersonIds = getAlarmPersonIds();
        int result38 = (result37 * 59) + ($alarmPersonIds == null ? 43 : $alarmPersonIds.hashCode());
        Object $configurationRelation = getConfigurationRelation();
        int result39 = (result38 * 59) + ($configurationRelation == null ? 43 : $configurationRelation.hashCode());
        Object $drawType = getDrawType();
        int result40 = (result39 * 59) + ($drawType == null ? 43 : $drawType.hashCode());
        Object $buildingName = getBuildingName();
        return (result40 * 59) + ($buildingName == null ? 43 : $buildingName.hashCode());
    }
    public String toString() {
        return "AreaAlarmTempVO(areaId=" + getAreaId() + ", areaName=" + getAreaName() + ", alarmType=" + getAlarmType() + ", rule=" + getRule() + ", secondAlarmRule=" + getSecondAlarmRule() + ", thirdAlarmRule=" + getThirdAlarmRule() + ", validBeginDate=" + getValidBeginDate() + ", validEndDate=" + getValidEndDate() + ", validBeginTime=" + getValidBeginTime() + ", validEndTime=" + getValidEndTime() + ", railIds=" + getRailIds() + ", deptIds=" + getDeptIds() + ", personIds=" + getPersonIds() + ", railDrawList=" + getRailDrawList() + ", railId=" + getRailId() + ", railName=" + getRailName() + ", railCount=" + getRailCount() + ", layerId=" + getLayerId() + ", layerIds=" + getLayerIds() + ", layerHeight=" + getLayerHeight() + ", railScope=" + getRailScope() + ", railList=" + getRailList() + ", alarmNoticeId=" + getAlarmNoticeId() + ", alarmNoticeName=" + getAlarmNoticeName() + ", noticeType=" + getNoticeType() + ", firstPersonId=" + getFirstPersonId() + ", secondPersonId=" + getSecondPersonId() + ", secondIntervalSecond=" + getSecondIntervalSecond() + ", thirdPersonId=" + getThirdPersonId() + ", thirdIntervalSecond=" + getThirdIntervalSecond() + ", acceptTime=" + getAcceptTime() + ", minusSeconds=" + getMinusSeconds() + ", areaType=" + getAreaType() + ", postIds=" + getPostIds() + ", alarmPersonIds=" + getAlarmPersonIds() + ", parameterCondition=" + getParameterCondition() + ", railHeight=" + getRailHeight() + ", configurationRelation=" + getConfigurationRelation() + ", drawType=" + getDrawType() + ", buildingName=" + getBuildingName() + ", buildingId=" + getBuildingId() + StringPool.RIGHT_BRACKET;
    }
    public Long getAreaId() {
        return this.areaId;
    }
    public String getAreaName() {
        return this.areaName;
    }
    public String getAlarmType() {
        return this.alarmType;
    }
    public String getRule() {
        return this.rule;
    }
    public String getSecondAlarmRule() {
        return this.secondAlarmRule;
    }
    public String getThirdAlarmRule() {
        return this.thirdAlarmRule;
    }
    public LocalDate getValidBeginDate() {
        return this.validBeginDate;
    }
    public LocalDate getValidEndDate() {
        return this.validEndDate;
    }
    public LocalTime getValidBeginTime() {
        return this.validBeginTime;
    }
    public LocalTime getValidEndTime() {
        return this.validEndTime;
    }
    public List<Long> getRailIds() {
        return this.railIds;
    }
    public List<Long> getDeptIds() {
        return this.deptIds;
    }
    public List<Long> getPersonIds() {
        return this.personIds;
    }
    public List<RailDrawType> getRailDrawList() {
        return this.railDrawList;
    }
    public Long getRailId() {
        return this.railId;
    }
    public String getRailName() {
        return this.railName;
    }
    public Integer getRailCount() {
        return this.railCount;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public List<String> getLayerIds() {
        return this.layerIds;
    }
    public Integer getLayerHeight() {
        return this.layerHeight;
    }
    public String getRailScope() {
        return this.railScope;
    }
    public List<Rail> getRailList() {
        return this.railList;
    }
    public Long getAlarmNoticeId() {
        return this.alarmNoticeId;
    }
    public String getAlarmNoticeName() {
        return this.alarmNoticeName;
    }
    public String getNoticeType() {
        return this.noticeType;
    }
    public String getFirstPersonId() {
        return this.firstPersonId;
    }
    public String getSecondPersonId() {
        return this.secondPersonId;
    }
    public Long getSecondIntervalSecond() {
        return this.secondIntervalSecond;
    }
    public String getThirdPersonId() {
        return this.thirdPersonId;
    }
    public Long getThirdIntervalSecond() {
        return this.thirdIntervalSecond;
    }
    public LocalDateTime getAcceptTime() {
        return this.acceptTime;
    }
    public LocalDateTime getMinusSeconds() {
        return this.minusSeconds;
    }
    public String getAreaType() {
        return this.areaType;
    }
    public List<Long> getPostIds() {
        return this.postIds;
    }
    public List<Long> getAlarmPersonIds() {
        return this.alarmPersonIds;
    }
    public Boolean getParameterCondition() {
        return this.parameterCondition;
    }
    public Integer getRailHeight() {
        return this.railHeight;
    }
    public String getConfigurationRelation() {
        return this.configurationRelation;
    }
    public String getDrawType() {
        return this.drawType;
    }
    public String getBuildingName() {
        return this.buildingName;
    }
    public Long getBuildingId() {
        return this.buildingId;
    }
}
