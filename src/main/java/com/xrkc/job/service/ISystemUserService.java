package com.xrkc.job.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xrkc.job.domain.SystemUser;
import java.util.List;
import java.util.Set;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/ISystemUserService.class */
public interface ISystemUserService extends IService<SystemUser> {
    List<String> findAllUserName();
    Long getPersonIdByUserId(Long l);
    List<SystemUser> findByUserIds(Set<Long> set);
}
