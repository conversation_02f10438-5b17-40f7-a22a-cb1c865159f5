package com.xrkc.job.service.impl;
import com.xrkc.job.domain.AreaAlarmPost;
import com.xrkc.job.mapper.AreaAlarmPostMapper;
import com.xrkc.job.service.IAreaAlarmPostService;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/AreaAlarmPostServiceImpl.class */
public class AreaAlarmPostServiceImpl implements IAreaAlarmPostService {
    @Autowired
    AreaAlarmPostMapper mapper;
    @Override // com.xrkc.job.service.IAreaAlarmPostService
    public List<Long> findPostIdsByAreaId(Long areaId) {
        List<AreaAlarmPost> list = this.mapper.selectList((v0) -> {
            return v0.getAreaId();
        }, areaId);
        return (List) list.stream().map((v0) -> {
            return v0.getPostId();
        }).collect(Collectors.toList());
    }
}
