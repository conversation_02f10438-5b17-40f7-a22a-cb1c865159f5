package com.xrkc.job.task;
import cn.hutool.core.bean.BeanUtil;
import com.xrkc.core.constant.Constants;
import com.xrkc.core.domain.rail.RailDrawType;
import com.xrkc.core.domain.system.SystemApi;
import com.xrkc.core.domain.vehicle.VehicleInfo;
import com.xrkc.core.domain.vehicle.VehiclePositionCurrent;
import com.xrkc.core.domain.vehicle.VehiclePositionHistory;
import com.xrkc.core.encrypt.sms4.SM4Utils;
import com.xrkc.core.utils.CommonUtil;
import com.xrkc.job.api.DTO.VehicleInfoResVO;
import com.xrkc.job.api.DTO.VehiclePositionResVO;
import com.xrkc.job.api.VehicleLocationApi;
import com.xrkc.job.domain.DeviceCard;
import com.xrkc.job.domain.DeviceLayer;
import com.xrkc.job.module.vehicle.service.IVehicleInfoService;
import com.xrkc.job.module.vehicle.service.IVehiclePositionCurrentService;
import com.xrkc.job.module.vehicle.service.IVehiclePositionHistoryService;
import com.xrkc.job.service.IDeviceCardService;
import com.xrkc.job.service.IDeviceLayerService;
import com.xrkc.job.service.ISystemInfoService;
import com.xrkc.job.service.SystemApiService;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
@Component("vehicleApiTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/VehicleApiTask.class */
public class VehicleApiTask {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) VehicleApiTask.class);
    @Autowired
    private IVehiclePositionCurrentService vehiclePositionCurrentService;
    @Autowired
    private IVehiclePositionHistoryService vehiclePositionHistoryService;
    @Autowired
    private SystemApiService systemApiService;
    @Autowired
    private IVehicleInfoService vehicleInfoService;
    @Autowired
    private IDeviceCardService deviceCardService;
    @Autowired
    private IDeviceLayerService deviceLayerService;
    @Autowired
    private ISystemInfoService systemInfoService;
    @Value("${spring.datasource.driver-class-name}")
    private String driverClassName;
    @Autowired
    private VehicleApiTask vehicleApiTask;
    @PostConstruct
    public void init() {
        this.vehicleApiTask.generateApiAuth();
    }
    @Transactional(rollbackFor = {Exception.class})
    public void generateApiAuth() {
        SystemApi systemApi = this.systemApiService.getByApiKey("vehicle");
        if (Objects.isNull(systemApi) || StringUtils.isAnyBlank(systemApi.getUserName(), systemApi.getPassword())) {
            return;
        }
        try {
            String username = SM4Utils.decryptData_CBC(systemApi.getUserName());
            String password = SM4Utils.decryptData_CBC(systemApi.getPassword());
            String vehicleUrl = StringUtils.isNotBlank(systemApi.getHost()) ? systemApi.getHost() + "/vehicle_api" : VehicleLocationApi.DEFAULT_VEHICLE_URL;
            VehicleLocationApi.VEHICLE_URL_MAP.put("master", vehicleUrl);
            VehicleLocationApi.VEHICLE_SCOPE_MAP.put("master", Boolean.valueOf("1".equals(systemApi.getBasicTopic())));
            VehicleLocationApi.login(username, password);
            syncVehicleInfo();
        } catch (Exception e) {
            log.error("车载卡账号密码有误:{}", e.getMessage());
        }
    }
    private void syncVehicleInfo() {
        if (StringUtils.isBlank(VehicleLocationApi.getVehicleToken())) {
            return;
        }
        List<VehicleInfoResVO> list = VehicleLocationApi.queryVehicleInfo();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<Long, DeviceCard> allCardMap = (Map) this.deviceCardService.list().stream().collect(Collectors.toMap((v0) -> {
            return v0.getCardId();
        }, Function.identity()));
        List<Long> cardIdList = this.vehicleInfoService.findCardIdList();
        List<DeviceCard> syncCardList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        list.stream().forEach(t -> {
            DeviceCard card = DeviceCard.builder().cardId(t.getCardId()).cardType("vehicle").cardModel("S15").cardVersion(15).cardStatus(1).cardTransfer(1).statusTime(now).heartTime(now).cardEnable("Y").useStatus("N").createBy("task").createTime(now).updateBy("task").updateTime(now).build();
            if (allCardMap.containsKey(t.getCardId())) {
                DeviceCard old = (DeviceCard) allCardMap.get(t.getCardId());
                card.setId(old.getId());
                card.setUseStatus(old.getUseStatus());
                card.setCardEnable(old.getCardEnable());
            }
            card.setUseStatus(cardIdList.contains(t.getCardId()) ? "Y" : "N");
            syncCardList.add(card);
        });
        List<DeviceCard> insertList = (List) syncCardList.stream().filter(f -> {
            return Objects.isNull(f.getId());
        }).collect(Collectors.toList());
        List<DeviceCard> updateList = (List) syncCardList.stream().filter(f2 -> {
            return Objects.nonNull(f2.getId());
        }).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(insertList)) {
            this.deviceCardService.saveBatch(insertList);
        } else if (!CollectionUtils.isEmpty(updateList)) {
            this.deviceCardService.updateBatchById(updateList);
        }
    }
    public void generateApiHistoryPosition() {
        if (StringUtils.isBlank(VehicleLocationApi.getVehicleToken())) {
            return;
        }
        List<VehicleInfoResVO> list = VehicleLocationApi.queryVehicleInfo();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Long> vehicleCarIdList = (List) list.stream().map((v0) -> {
            return v0.getVehicleCarId();
        }).collect(Collectors.toList());
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        vehicleCarIdList.stream().forEach(vehicleCarId -> {
            List<VehiclePositionResVO> resList1 = VehicleLocationApi.queryHistoryLocation(vehicleCarId, startTime, endTime);
            if (!CollectionUtils.isEmpty(resList1)) {
                assignmentProcess(resList1, endTime);
            }
        });
    }
    private void assignmentProcess(List<VehiclePositionResVO> resList, LocalDateTime endTime) {
        String tableName;
        if (CollectionUtils.isEmpty(resList)) {
            return;
        }
        if (VehicleLocationApi.getVehicleScope().booleanValue()) {
            RailDrawType mapRailScope = this.systemInfoService.findMapRailScope(Constants.SYSTEM_ID_MONITOR);
            if (Objects.nonNull(mapRailScope)) {
                resList.removeIf(r -> {
                    return !CommonUtil.containsRailScope(r.getLongitude(), r.getLatitude(), mapRailScope.getRailScope(), mapRailScope.getDrawType());
                });
            } else {
                DeviceLayer deviceLayer = this.deviceLayerService.findOneLayer();
                if (Objects.nonNull(deviceLayer) && Objects.nonNull(deviceLayer.getMaxLongitude()) && Objects.nonNull(deviceLayer.getMinLongitude()) && Objects.nonNull(deviceLayer.getMaxLatitude()) && Objects.nonNull(deviceLayer.getMinLatitude())) {
                    resList.removeIf(r2 -> {
                        return !CommonUtil.inOneLayer(r2.getLongitude(), r2.getLatitude(), deviceLayer.getMinLongitude(), deviceLayer.getMaxLongitude(), deviceLayer.getMinLatitude(), deviceLayer.getMaxLatitude()).booleanValue();
                    });
                }
            }
        }
        if (CollectionUtils.isEmpty(resList)) {
            return;
        }
        List<VehiclePositionHistory> historyList = new ArrayList<>();
        Map<Long, VehicleInfo> map = this.vehicleInfoService.findCardIdEntityMap();
        resList.stream().filter(f -> {
            return map.containsKey(f.getCardId());
        }).forEach(t -> {
            VehiclePositionHistory history = (VehiclePositionHistory) BeanUtil.copyProperties((Object) t, VehiclePositionHistory.class, new String[0]);
            VehicleInfo info = (VehicleInfo) map.get(t.getCardId());
            BeanUtil.copyProperties((Object) info, (Object) history, true);
            history.setVehicleId(info.getId());
            historyList.add(history);
        });
        if (!historyList.isEmpty()) {
            if (this.driverClassName.contains("dm")) {
                tableName = "VEHICLE_POSITION_HISTORY_" + endTime.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            } else {
                tableName = "vehicle_position_history_" + endTime.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            }
            this.vehiclePositionHistoryService.batchBakHistory(tableName, historyList);
        }
    }
    public void generateApiCurrentPosition() {
        if (StringUtils.isBlank(VehicleLocationApi.getVehicleToken())) {
            return;
        }
        List<VehiclePositionResVO> resList = VehicleLocationApi.queryCurrentLocation();
        if (CollectionUtils.isEmpty(resList)) {
            return;
        }
        Map<Long, VehiclePositionResVO> resVOMap = (Map) resList.stream().collect(Collectors.toMap((v0) -> {
            return v0.getCardId();
        }, Function.identity()));
        List<Long> cardIdList = (List) resList.stream().map((v0) -> {
            return v0.getCardId();
        }).collect(Collectors.toList());
        List<DeviceCard> cardList = this.deviceCardService.selectListByCardIdList(cardIdList);
        if (!CollectionUtils.isEmpty(cardList)) {
            cardList.stream().forEach(t -> {
                VehiclePositionResVO resVO = (VehiclePositionResVO) resVOMap.get(t.getCardId());
                t.setCardPower(resVO.getElectricity());
                t.setHeartTime(resVO.getAcceptTime());
                t.setCardStatus(1);
                t.setCardVersion(15);
                t.setCardTransfer(1);
                t.setStatusTime(LocalDateTime.now());
            });
            this.deviceCardService.updateBatchById(cardList);
        }
        if (VehicleLocationApi.getVehicleScope().booleanValue()) {
            RailDrawType mapRailScope = this.systemInfoService.findMapRailScope(Constants.SYSTEM_ID_MONITOR);
            if (Objects.nonNull(mapRailScope)) {
                resList.removeIf(r -> {
                    return !CommonUtil.containsRailScope(r.getLongitude(), r.getLatitude(), mapRailScope.getRailScope(), mapRailScope.getDrawType());
                });
            } else {
                DeviceLayer deviceLayer = this.deviceLayerService.findOneLayer();
                if (Objects.nonNull(deviceLayer) && Objects.nonNull(deviceLayer.getMaxLongitude()) && Objects.nonNull(deviceLayer.getMinLongitude()) && Objects.nonNull(deviceLayer.getMaxLatitude()) && Objects.nonNull(deviceLayer.getMinLatitude())) {
                    resList.removeIf(r2 -> {
                        return !CommonUtil.inOneLayer(r2.getLongitude(), r2.getLatitude(), deviceLayer.getMinLongitude(), deviceLayer.getMaxLongitude(), deviceLayer.getMinLatitude(), deviceLayer.getMaxLatitude()).booleanValue();
                    });
                }
            }
        }
        if (CollectionUtils.isEmpty(resList)) {
            return;
        }
        List<VehiclePositionCurrent> currentList = new ArrayList<>();
        Map<Long, VehicleInfo> map = this.vehicleInfoService.findCardIdEntityMap();
        resList.stream().filter(f -> {
            return Objects.nonNull(f.getCardId()) && map.containsKey(f.getCardId());
        }).forEach(t2 -> {
            VehiclePositionCurrent history = (VehiclePositionCurrent) BeanUtil.copyProperties((Object) t2, VehiclePositionCurrent.class, new String[0]);
            VehicleInfo info = (VehicleInfo) map.get(t2.getCardId());
            BeanUtil.copyProperties((Object) info, (Object) history, true);
            history.setVehicleId(info.getId());
            history.setElectricity(t2.getElectricity());
            currentList.add(history);
        });
        this.vehiclePositionCurrentService.batchInsert(currentList);
    }
}
