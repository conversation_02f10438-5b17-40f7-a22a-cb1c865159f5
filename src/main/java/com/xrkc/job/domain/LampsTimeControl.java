package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
import java.time.LocalTime;
import java.util.Date;
@TableName("lamps_time_control")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/LampsTimeControl.class */
public class LampsTimeControl implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId("id")
    private Long id;
    @TableField("task_name")
    private String taskName;
    @TableField("task_enable")
    private Integer taskEnable;
    @TableField("start_time")
    private LocalTime startTime;
    @TableField("end_time")
    private LocalTime endTime;
    @TableField("lamps_status")
    private Integer lampsStatus;
    @TableField("lamps_luminance")
    private Integer lampsLuminance;
    @TableField("create_by")
    private String createBy;
    @TableField("update_by")
    private String updateBy;
    @TableField("create_time")
    private Date createTime;
    @TableField("update_time")
    private Date updateTime;
    @TableField("push_inductive_luminance")
    private Integer pushInductiveLuminance;
    @TableField("push_inductive_off_luminance")
    private Integer pushInductiveOffLuminance;
    @TableField("push_inductive_distance")
    private Integer pushInductiveDistance;
    @TableField("push_inductive_off_delay_time")
    private Integer pushInductiveOffDelayTime;
    public void setId(Long id) {
        this.id = id;
    }
    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }
    public void setTaskEnable(Integer taskEnable) {
        this.taskEnable = taskEnable;
    }
    public void setStartTime(LocalTime startTime) {
        this.startTime = startTime;
    }
    public void setEndTime(LocalTime endTime) {
        this.endTime = endTime;
    }
    public void setLampsStatus(Integer lampsStatus) {
        this.lampsStatus = lampsStatus;
    }
    public void setLampsLuminance(Integer lampsLuminance) {
        this.lampsLuminance = lampsLuminance;
    }
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    public void setPushInductiveLuminance(Integer pushInductiveLuminance) {
        this.pushInductiveLuminance = pushInductiveLuminance;
    }
    public void setPushInductiveOffLuminance(Integer pushInductiveOffLuminance) {
        this.pushInductiveOffLuminance = pushInductiveOffLuminance;
    }
    public void setPushInductiveDistance(Integer pushInductiveDistance) {
        this.pushInductiveDistance = pushInductiveDistance;
    }
    public void setPushInductiveOffDelayTime(Integer pushInductiveOffDelayTime) {
        this.pushInductiveOffDelayTime = pushInductiveOffDelayTime;
    }
    public String toString() {
        return "LampsTimeControl(id=" + getId() + ", taskName=" + getTaskName() + ", taskEnable=" + getTaskEnable() + ", startTime=" + getStartTime() + ", endTime=" + getEndTime() + ", lampsStatus=" + getLampsStatus() + ", lampsLuminance=" + getLampsLuminance() + ", createBy=" + getCreateBy() + ", updateBy=" + getUpdateBy() + ", createTime=" + getCreateTime() + ", updateTime=" + getUpdateTime() + ", pushInductiveLuminance=" + getPushInductiveLuminance() + ", pushInductiveOffLuminance=" + getPushInductiveOffLuminance() + ", pushInductiveDistance=" + getPushInductiveDistance() + ", pushInductiveOffDelayTime=" + getPushInductiveOffDelayTime() + StringPool.RIGHT_BRACKET;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof LampsTimeControl)) {
            return false;
        }
        LampsTimeControl other = (LampsTimeControl) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$id = getId();
        Object other$id = other.getId();
        if (this$id == null) {
            if (other$id != null) {
                return false;
            }
        } else if (!this$id.equals(other$id)) {
            return false;
        }
        Object this$taskEnable = getTaskEnable();
        Object other$taskEnable = other.getTaskEnable();
        if (this$taskEnable == null) {
            if (other$taskEnable != null) {
                return false;
            }
        } else if (!this$taskEnable.equals(other$taskEnable)) {
            return false;
        }
        Object this$lampsStatus = getLampsStatus();
        Object other$lampsStatus = other.getLampsStatus();
        if (this$lampsStatus == null) {
            if (other$lampsStatus != null) {
                return false;
            }
        } else if (!this$lampsStatus.equals(other$lampsStatus)) {
            return false;
        }
        Object this$lampsLuminance = getLampsLuminance();
        Object other$lampsLuminance = other.getLampsLuminance();
        if (this$lampsLuminance == null) {
            if (other$lampsLuminance != null) {
                return false;
            }
        } else if (!this$lampsLuminance.equals(other$lampsLuminance)) {
            return false;
        }
        Object this$pushInductiveLuminance = getPushInductiveLuminance();
        Object other$pushInductiveLuminance = other.getPushInductiveLuminance();
        if (this$pushInductiveLuminance == null) {
            if (other$pushInductiveLuminance != null) {
                return false;
            }
        } else if (!this$pushInductiveLuminance.equals(other$pushInductiveLuminance)) {
            return false;
        }
        Object this$pushInductiveOffLuminance = getPushInductiveOffLuminance();
        Object other$pushInductiveOffLuminance = other.getPushInductiveOffLuminance();
        if (this$pushInductiveOffLuminance == null) {
            if (other$pushInductiveOffLuminance != null) {
                return false;
            }
        } else if (!this$pushInductiveOffLuminance.equals(other$pushInductiveOffLuminance)) {
            return false;
        }
        Object this$pushInductiveDistance = getPushInductiveDistance();
        Object other$pushInductiveDistance = other.getPushInductiveDistance();
        if (this$pushInductiveDistance == null) {
            if (other$pushInductiveDistance != null) {
                return false;
            }
        } else if (!this$pushInductiveDistance.equals(other$pushInductiveDistance)) {
            return false;
        }
        Object this$pushInductiveOffDelayTime = getPushInductiveOffDelayTime();
        Object other$pushInductiveOffDelayTime = other.getPushInductiveOffDelayTime();
        if (this$pushInductiveOffDelayTime == null) {
            if (other$pushInductiveOffDelayTime != null) {
                return false;
            }
        } else if (!this$pushInductiveOffDelayTime.equals(other$pushInductiveOffDelayTime)) {
            return false;
        }
        Object this$taskName = getTaskName();
        Object other$taskName = other.getTaskName();
        if (this$taskName == null) {
            if (other$taskName != null) {
                return false;
            }
        } else if (!this$taskName.equals(other$taskName)) {
            return false;
        }
        Object this$startTime = getStartTime();
        Object other$startTime = other.getStartTime();
        if (this$startTime == null) {
            if (other$startTime != null) {
                return false;
            }
        } else if (!this$startTime.equals(other$startTime)) {
            return false;
        }
        Object this$endTime = getEndTime();
        Object other$endTime = other.getEndTime();
        if (this$endTime == null) {
            if (other$endTime != null) {
                return false;
            }
        } else if (!this$endTime.equals(other$endTime)) {
            return false;
        }
        Object this$createBy = getCreateBy();
        Object other$createBy = other.getCreateBy();
        if (this$createBy == null) {
            if (other$createBy != null) {
                return false;
            }
        } else if (!this$createBy.equals(other$createBy)) {
            return false;
        }
        Object this$updateBy = getUpdateBy();
        Object other$updateBy = other.getUpdateBy();
        if (this$updateBy == null) {
            if (other$updateBy != null) {
                return false;
            }
        } else if (!this$updateBy.equals(other$updateBy)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        if (this$createTime == null) {
            if (other$createTime != null) {
                return false;
            }
        } else if (!this$createTime.equals(other$createTime)) {
            return false;
        }
        Object this$updateTime = getUpdateTime();
        Object other$updateTime = other.getUpdateTime();
        return this$updateTime == null ? other$updateTime == null : this$updateTime.equals(other$updateTime);
    }
    protected boolean canEqual(Object other) {
        return other instanceof LampsTimeControl;
    }
    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $taskEnable = getTaskEnable();
        int result2 = (result * 59) + ($taskEnable == null ? 43 : $taskEnable.hashCode());
        Object $lampsStatus = getLampsStatus();
        int result3 = (result2 * 59) + ($lampsStatus == null ? 43 : $lampsStatus.hashCode());
        Object $lampsLuminance = getLampsLuminance();
        int result4 = (result3 * 59) + ($lampsLuminance == null ? 43 : $lampsLuminance.hashCode());
        Object $pushInductiveLuminance = getPushInductiveLuminance();
        int result5 = (result4 * 59) + ($pushInductiveLuminance == null ? 43 : $pushInductiveLuminance.hashCode());
        Object $pushInductiveOffLuminance = getPushInductiveOffLuminance();
        int result6 = (result5 * 59) + ($pushInductiveOffLuminance == null ? 43 : $pushInductiveOffLuminance.hashCode());
        Object $pushInductiveDistance = getPushInductiveDistance();
        int result7 = (result6 * 59) + ($pushInductiveDistance == null ? 43 : $pushInductiveDistance.hashCode());
        Object $pushInductiveOffDelayTime = getPushInductiveOffDelayTime();
        int result8 = (result7 * 59) + ($pushInductiveOffDelayTime == null ? 43 : $pushInductiveOffDelayTime.hashCode());
        Object $taskName = getTaskName();
        int result9 = (result8 * 59) + ($taskName == null ? 43 : $taskName.hashCode());
        Object $startTime = getStartTime();
        int result10 = (result9 * 59) + ($startTime == null ? 43 : $startTime.hashCode());
        Object $endTime = getEndTime();
        int result11 = (result10 * 59) + ($endTime == null ? 43 : $endTime.hashCode());
        Object $createBy = getCreateBy();
        int result12 = (result11 * 59) + ($createBy == null ? 43 : $createBy.hashCode());
        Object $updateBy = getUpdateBy();
        int result13 = (result12 * 59) + ($updateBy == null ? 43 : $updateBy.hashCode());
        Object $createTime = getCreateTime();
        int result14 = (result13 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $updateTime = getUpdateTime();
        return (result14 * 59) + ($updateTime == null ? 43 : $updateTime.hashCode());
    }
    public Long getId() {
        return this.id;
    }
    public String getTaskName() {
        return this.taskName;
    }
    public Integer getTaskEnable() {
        return this.taskEnable;
    }
    public LocalTime getStartTime() {
        return this.startTime;
    }
    public LocalTime getEndTime() {
        return this.endTime;
    }
    public Integer getLampsStatus() {
        return this.lampsStatus;
    }
    public Integer getLampsLuminance() {
        return this.lampsLuminance;
    }
    public String getCreateBy() {
        return this.createBy;
    }
    public String getUpdateBy() {
        return this.updateBy;
    }
    public Date getCreateTime() {
        return this.createTime;
    }
    public Date getUpdateTime() {
        return this.updateTime;
    }
    public Integer getPushInductiveLuminance() {
        return this.pushInductiveLuminance;
    }
    public Integer getPushInductiveOffLuminance() {
        return this.pushInductiveOffLuminance;
    }
    public Integer getPushInductiveDistance() {
        return this.pushInductiveDistance;
    }
    public Integer getPushInductiveOffDelayTime() {
        return this.pushInductiveOffDelayTime;
    }
}
