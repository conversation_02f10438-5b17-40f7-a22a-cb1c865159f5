package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
@TableName("device_beacon")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/DeviceBeacon.class */
public class DeviceBeacon implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId("id")
    private Long id;
    @TableField("beacon_id")
    private Integer beaconId;
    @TableField("beacon_power")
    private BigDecimal beaconPower;
    @TableField("beacon_status")
    private Integer beaconStatus;
    @TableField("beacon_enable")
    private String beaconEnable;
    @TableField("beacon_transfer")
    private Integer beaconTransfer;
    @TableField("heart_time")
    private Date heartTime;
    @TableField("layer_id")
    private String layerId;
    @TableField("layer_height")
    private Integer layerHeight;
    @TableField("longitude")
    private BigDecimal longitude;
    @TableField("latitude")
    private BigDecimal latitude;
    @TableField("decay")
    private BigDecimal decay;
    @TableField("meter_signal")
    private BigDecimal meterSignal;
    @TableField("create_by")
    private String createBy;
    @TableField("update_by")
    private String updateBy;
    @TableField("remark")
    private String remark;
    @TableField("building_id")
    private Long buildingId;
    @TableField("create_time")
    private LocalDateTime createTime = LocalDateTime.now();
    @TableField("update_time")
    private LocalDateTime updateTime = LocalDateTime.now();
    public void setId(Long id) {
        this.id = id;
    }
    public void setBeaconId(Integer beaconId) {
        this.beaconId = beaconId;
    }
    public void setBeaconPower(BigDecimal beaconPower) {
        this.beaconPower = beaconPower;
    }
    public void setBeaconStatus(Integer beaconStatus) {
        this.beaconStatus = beaconStatus;
    }
    public void setBeaconEnable(String beaconEnable) {
        this.beaconEnable = beaconEnable;
    }
    public void setBeaconTransfer(Integer beaconTransfer) {
        this.beaconTransfer = beaconTransfer;
    }
    public void setHeartTime(Date heartTime) {
        this.heartTime = heartTime;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
    public void setLayerHeight(Integer layerHeight) {
        this.layerHeight = layerHeight;
    }
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    public void setDecay(BigDecimal decay) {
        this.decay = decay;
    }
    public void setMeterSignal(BigDecimal meterSignal) {
        this.meterSignal = meterSignal;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }
    public void setBuildingId(Long buildingId) {
        this.buildingId = buildingId;
    }
    public String toString() {
        return "DeviceBeacon(id=" + getId() + ", beaconId=" + getBeaconId() + ", beaconPower=" + getBeaconPower() + ", beaconStatus=" + getBeaconStatus() + ", beaconEnable=" + getBeaconEnable() + ", beaconTransfer=" + getBeaconTransfer() + ", heartTime=" + getHeartTime() + ", layerId=" + getLayerId() + ", layerHeight=" + getLayerHeight() + ", longitude=" + getLongitude() + ", latitude=" + getLatitude() + ", decay=" + getDecay() + ", meterSignal=" + getMeterSignal() + ", createTime=" + getCreateTime() + ", createBy=" + getCreateBy() + ", updateTime=" + getUpdateTime() + ", updateBy=" + getUpdateBy() + ", remark=" + getRemark() + ", buildingId=" + getBuildingId() + StringPool.RIGHT_BRACKET;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof DeviceBeacon)) {
            return false;
        }
        DeviceBeacon other = (DeviceBeacon) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$id = getId();
        Object other$id = other.getId();
        if (this$id == null) {
            if (other$id != null) {
                return false;
            }
        } else if (!this$id.equals(other$id)) {
            return false;
        }
        Object this$beaconId = getBeaconId();
        Object other$beaconId = other.getBeaconId();
        if (this$beaconId == null) {
            if (other$beaconId != null) {
                return false;
            }
        } else if (!this$beaconId.equals(other$beaconId)) {
            return false;
        }
        Object this$beaconStatus = getBeaconStatus();
        Object other$beaconStatus = other.getBeaconStatus();
        if (this$beaconStatus == null) {
            if (other$beaconStatus != null) {
                return false;
            }
        } else if (!this$beaconStatus.equals(other$beaconStatus)) {
            return false;
        }
        Object this$beaconTransfer = getBeaconTransfer();
        Object other$beaconTransfer = other.getBeaconTransfer();
        if (this$beaconTransfer == null) {
            if (other$beaconTransfer != null) {
                return false;
            }
        } else if (!this$beaconTransfer.equals(other$beaconTransfer)) {
            return false;
        }
        Object this$layerHeight = getLayerHeight();
        Object other$layerHeight = other.getLayerHeight();
        if (this$layerHeight == null) {
            if (other$layerHeight != null) {
                return false;
            }
        } else if (!this$layerHeight.equals(other$layerHeight)) {
            return false;
        }
        Object this$buildingId = getBuildingId();
        Object other$buildingId = other.getBuildingId();
        if (this$buildingId == null) {
            if (other$buildingId != null) {
                return false;
            }
        } else if (!this$buildingId.equals(other$buildingId)) {
            return false;
        }
        Object this$beaconPower = getBeaconPower();
        Object other$beaconPower = other.getBeaconPower();
        if (this$beaconPower == null) {
            if (other$beaconPower != null) {
                return false;
            }
        } else if (!this$beaconPower.equals(other$beaconPower)) {
            return false;
        }
        Object this$beaconEnable = getBeaconEnable();
        Object other$beaconEnable = other.getBeaconEnable();
        if (this$beaconEnable == null) {
            if (other$beaconEnable != null) {
                return false;
            }
        } else if (!this$beaconEnable.equals(other$beaconEnable)) {
            return false;
        }
        Object this$heartTime = getHeartTime();
        Object other$heartTime = other.getHeartTime();
        if (this$heartTime == null) {
            if (other$heartTime != null) {
                return false;
            }
        } else if (!this$heartTime.equals(other$heartTime)) {
            return false;
        }
        Object this$layerId = getLayerId();
        Object other$layerId = other.getLayerId();
        if (this$layerId == null) {
            if (other$layerId != null) {
                return false;
            }
        } else if (!this$layerId.equals(other$layerId)) {
            return false;
        }
        Object this$longitude = getLongitude();
        Object other$longitude = other.getLongitude();
        if (this$longitude == null) {
            if (other$longitude != null) {
                return false;
            }
        } else if (!this$longitude.equals(other$longitude)) {
            return false;
        }
        Object this$latitude = getLatitude();
        Object other$latitude = other.getLatitude();
        if (this$latitude == null) {
            if (other$latitude != null) {
                return false;
            }
        } else if (!this$latitude.equals(other$latitude)) {
            return false;
        }
        Object this$decay = getDecay();
        Object other$decay = other.getDecay();
        if (this$decay == null) {
            if (other$decay != null) {
                return false;
            }
        } else if (!this$decay.equals(other$decay)) {
            return false;
        }
        Object this$meterSignal = getMeterSignal();
        Object other$meterSignal = other.getMeterSignal();
        if (this$meterSignal == null) {
            if (other$meterSignal != null) {
                return false;
            }
        } else if (!this$meterSignal.equals(other$meterSignal)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        if (this$createTime == null) {
            if (other$createTime != null) {
                return false;
            }
        } else if (!this$createTime.equals(other$createTime)) {
            return false;
        }
        Object this$createBy = getCreateBy();
        Object other$createBy = other.getCreateBy();
        if (this$createBy == null) {
            if (other$createBy != null) {
                return false;
            }
        } else if (!this$createBy.equals(other$createBy)) {
            return false;
        }
        Object this$updateTime = getUpdateTime();
        Object other$updateTime = other.getUpdateTime();
        if (this$updateTime == null) {
            if (other$updateTime != null) {
                return false;
            }
        } else if (!this$updateTime.equals(other$updateTime)) {
            return false;
        }
        Object this$updateBy = getUpdateBy();
        Object other$updateBy = other.getUpdateBy();
        if (this$updateBy == null) {
            if (other$updateBy != null) {
                return false;
            }
        } else if (!this$updateBy.equals(other$updateBy)) {
            return false;
        }
        Object this$remark = getRemark();
        Object other$remark = other.getRemark();
        return this$remark == null ? other$remark == null : this$remark.equals(other$remark);
    }
    protected boolean canEqual(Object other) {
        return other instanceof DeviceBeacon;
    }
    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $beaconId = getBeaconId();
        int result2 = (result * 59) + ($beaconId == null ? 43 : $beaconId.hashCode());
        Object $beaconStatus = getBeaconStatus();
        int result3 = (result2 * 59) + ($beaconStatus == null ? 43 : $beaconStatus.hashCode());
        Object $beaconTransfer = getBeaconTransfer();
        int result4 = (result3 * 59) + ($beaconTransfer == null ? 43 : $beaconTransfer.hashCode());
        Object $layerHeight = getLayerHeight();
        int result5 = (result4 * 59) + ($layerHeight == null ? 43 : $layerHeight.hashCode());
        Object $buildingId = getBuildingId();
        int result6 = (result5 * 59) + ($buildingId == null ? 43 : $buildingId.hashCode());
        Object $beaconPower = getBeaconPower();
        int result7 = (result6 * 59) + ($beaconPower == null ? 43 : $beaconPower.hashCode());
        Object $beaconEnable = getBeaconEnable();
        int result8 = (result7 * 59) + ($beaconEnable == null ? 43 : $beaconEnable.hashCode());
        Object $heartTime = getHeartTime();
        int result9 = (result8 * 59) + ($heartTime == null ? 43 : $heartTime.hashCode());
        Object $layerId = getLayerId();
        int result10 = (result9 * 59) + ($layerId == null ? 43 : $layerId.hashCode());
        Object $longitude = getLongitude();
        int result11 = (result10 * 59) + ($longitude == null ? 43 : $longitude.hashCode());
        Object $latitude = getLatitude();
        int result12 = (result11 * 59) + ($latitude == null ? 43 : $latitude.hashCode());
        Object $decay = getDecay();
        int result13 = (result12 * 59) + ($decay == null ? 43 : $decay.hashCode());
        Object $meterSignal = getMeterSignal();
        int result14 = (result13 * 59) + ($meterSignal == null ? 43 : $meterSignal.hashCode());
        Object $createTime = getCreateTime();
        int result15 = (result14 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $createBy = getCreateBy();
        int result16 = (result15 * 59) + ($createBy == null ? 43 : $createBy.hashCode());
        Object $updateTime = getUpdateTime();
        int result17 = (result16 * 59) + ($updateTime == null ? 43 : $updateTime.hashCode());
        Object $updateBy = getUpdateBy();
        int result18 = (result17 * 59) + ($updateBy == null ? 43 : $updateBy.hashCode());
        Object $remark = getRemark();
        return (result18 * 59) + ($remark == null ? 43 : $remark.hashCode());
    }
    public Long getId() {
        return this.id;
    }
    public Integer getBeaconId() {
        return this.beaconId;
    }
    public BigDecimal getBeaconPower() {
        return this.beaconPower;
    }
    public Integer getBeaconStatus() {
        return this.beaconStatus;
    }
    public String getBeaconEnable() {
        return this.beaconEnable;
    }
    public Integer getBeaconTransfer() {
        return this.beaconTransfer;
    }
    public Date getHeartTime() {
        return this.heartTime;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public Integer getLayerHeight() {
        return this.layerHeight;
    }
    public BigDecimal getLongitude() {
        return this.longitude;
    }
    public BigDecimal getLatitude() {
        return this.latitude;
    }
    public BigDecimal getDecay() {
        return this.decay;
    }
    public BigDecimal getMeterSignal() {
        return this.meterSignal;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public String getCreateBy() {
        return this.createBy;
    }
    public LocalDateTime getUpdateTime() {
        return this.updateTime;
    }
    public String getUpdateBy() {
        return this.updateBy;
    }
    public String getRemark() {
        return this.remark;
    }
    public Long getBuildingId() {
        return this.buildingId;
    }
}
