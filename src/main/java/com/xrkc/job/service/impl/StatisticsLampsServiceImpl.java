package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.core.domain.statistics.StatisticsLamps;
import com.xrkc.job.domain.DeviceLampsStatus;
import com.xrkc.job.mapper.StatisticsLampsMapper;
import com.xrkc.job.service.IDeviceLampsStatusService;
import com.xrkc.job.service.IStatisticsLampsService;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.chrono.ChronoLocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/StatisticsLampsServiceImpl.class */
public class StatisticsLampsServiceImpl extends ServiceImpl<StatisticsLampsMapper, StatisticsLamps> implements IStatisticsLampsService {
    @Autowired
    private StatisticsLampsMapper statisticsLampsMapper;
    @Autowired
    private IDeviceLampsStatusService deviceLampsStatusService;
    private static final Logger log = LoggerFactory.getLogger((Class<?>) StatisticsLampsServiceImpl.class);
    private static final Double HOURS_MS = Double.valueOf(3600000.0d);
    private static final BigDecimal BD_1000 = new BigDecimal(1000);
    private static final BigDecimal HOURS_1 = new BigDecimal("0.01666666666666");
    private static boolean compareTime(LocalDateTime dateTime1, LocalDateTime dateTime2) {
        return (Objects.isNull(dateTime1) || Objects.isNull(dateTime2) || dateTime1.truncatedTo(ChronoUnit.MINUTES).compareTo((ChronoLocalDateTime<?>) dateTime2.truncatedTo(ChronoUnit.MINUTES)) != 0) ? false : true;
    }
    @Override // com.xrkc.job.service.IStatisticsLampsService
    public void addStatistLamps(LocalDateTime now) {
        LocalDateTime minTime = now.withMinute(0).withSecond(0);
        LocalDateTime maxTime = now.withMinute(59).withSecond(59);
        LambdaQueryWrapper<DeviceLampsStatus> wrapper = Wrappers.lambdaQuery(StatisticsLamps.class);
        wrapper.isNotNull((v0) -> {
            return v0.getLampsGroup();
        });
        wrapper.between((v0) -> {
            return v0.getHeartTime();
        }, minTime, maxTime);
        List<DeviceLampsStatus> lampsList = this.deviceLampsStatusService.list(wrapper);
        if (CollectionUtils.isEmpty(lampsList)) {
            return;
        }
        Map<Integer, List<DeviceLampsStatus>> lampGroupMap = (Map) lampsList.stream().collect(Collectors.groupingBy((v0) -> {
            return v0.getLampsGroup();
        }));
        lampGroupMap.forEach((lampsGroup, groupList) -> {
            long luminanceMillisecond = 0;
            BigDecimal lampsEnergy = BigDecimal.ZERO;
            Map<Long, List<DeviceLampsStatus>> lampsIdMap = (Map) groupList.stream().collect(Collectors.groupingBy((v0) -> {
                return v0.getLampsId();
            }));
            for (Long lampsId : lampsIdMap.keySet()) {
                List<DeviceLampsStatus> list = lampsIdMap.get(lampsId);
                list.sort(Comparator.comparing((v0) -> {
                    return v0.getHeartTime();
                }));
                LocalDateTime luminanceLastTime = null;
                LocalDateTime energyLastTime = null;
                for (DeviceLampsStatus currentLampsStatus : list) {
                    LocalDateTime currentHeartTime = currentLampsStatus.getHeartTime();
                    if (currentLampsStatus.getLampsStatus().compareTo((Integer) 1) == 0) {
                        if (Objects.nonNull(luminanceLastTime)) {
                            long runMs = Duration.between(luminanceLastTime, currentHeartTime).toMillis();
                            luminanceMillisecond += runMs;
                        }
                        luminanceLastTime = currentHeartTime;
                    } else {
                        luminanceLastTime = null;
                    }
                    BigDecimal kw = currentLampsStatus.getLampsKw().divide(BD_1000);
                    if (currentLampsStatus.getLampsStatus().compareTo((Integer) 1) == 0) {
                        if (Objects.nonNull(energyLastTime) && !compareTime(energyLastTime, currentHeartTime)) {
                            long s = Duration.between(energyLastTime, currentHeartTime).toMillis() / 1000;
                            if (s > 600) {
                                log.info("[灯具]id:{},组：{},超过10分钟没有上报状态，不累计时间", lampsId, lampsGroup);
                                lampsEnergy = lampsEnergy.add(kw.multiply(HOURS_1));
                            } else if (s < 120) {
                                lampsEnergy = lampsEnergy.add(kw.multiply(HOURS_1));
                            } else {
                                long d = s / 60;
                                BigDecimal h = HOURS_1.multiply(BigDecimal.valueOf(d));
                                lampsEnergy = lampsEnergy.add(kw.multiply(h));
                            }
                        }
                        energyLastTime = currentHeartTime;
                    } else {
                        energyLastTime = null;
                    }
                }
            }
            double hours = luminanceMillisecond / HOURS_MS.doubleValue();
            BigDecimal lampsLuminanceTime = BigDecimal.valueOf(hours);
            StatisticsLamps statisticsLamps = new StatisticsLamps();
            statisticsLamps.setLampsGroupId(lampsGroup);
            statisticsLamps.setStatisticsDate(now);
            statisticsLamps.setCreateTime(now);
            statisticsLamps.setLampsEnergy(lampsEnergy.setScale(3, 2));
            statisticsLamps.setLampsLuminanceTime(lampsLuminanceTime.setScale(3, 2));
            LambdaQueryWrapper<StatisticsLamps> queryWrapper = Wrappers.lambdaQuery(StatisticsLamps.class);
            queryWrapper.eq((v0) -> {
                return v0.getLampsGroupId();
            }, lampsGroup);
            queryWrapper.between((v0) -> {
                return v0.getStatisticsDate();
            }, minTime, maxTime);
            List<StatisticsLamps> statisticsLampsList = this.statisticsLampsMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(statisticsLampsList)) {
                this.statisticsLampsMapper.insert((StatisticsLampsMapper) statisticsLamps);
            } else {
                statisticsLamps.setStatisticsId(statisticsLampsList.get(0).getStatisticsId());
                this.statisticsLampsMapper.updateById((StatisticsLampsMapper) statisticsLamps);
            }
        });
    }
}
