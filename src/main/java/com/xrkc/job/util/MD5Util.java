package com.xrkc.job.util;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import org.apache.commons.codec.binary.Hex;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/MD5Util.class */
public class MD5Util {
    public static String md5Hex(String src) throws NoSuchAlgorithmException {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] bs = md5.digest(src.getBytes());
            return new String(new Hex().encode(bs));
        } catch (Exception e) {
            return null;
        }
    }
}
