package com.xrkc.job.mapper;
import com.xrkc.core.mybatisplus.BaseMapperX;
import com.xrkc.job.domain.DeviceCardSenderLog;
import java.time.LocalDateTime;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/DeviceCardSenderLogMapper.class */
public interface DeviceCardSenderLogMapper extends BaseMapperX<DeviceCardSenderLog> {
    Long findDelMaxId(@Param("minusDays") LocalDateTime localDateTime);
    int delByMaxId(@Param("maxId") Long l, @Param("delRow") int i);
}
