package com.xrkc.job.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xrkc.job.domain.HiddenDanger;
import com.xrkc.job.mapper.HiddenDangerMapper;
import com.xrkc.job.service.IHiddenDangerService;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/HiddenDangerServiceImpl.class */
public class HiddenDangerServiceImpl extends ServiceImpl<HiddenDangerMapper, HiddenDanger> implements IHiddenDangerService {
    @Autowired
    private HiddenDangerMapper hiddenDangerMapper;
    @Override // com.xrkc.job.service.IHiddenDangerService
    public List<HiddenDanger> selectDangerList(LocalDate now, LocalDateTime nowTime) {
        return this.hiddenDangerMapper.selectDangerList(now, nowTime);
    }
    @Override // com.xrkc.job.service.IHiddenDangerService
    public List<HiddenDanger> selectPushDangerList() {
        LocalDateTime minutes = LocalDateTime.now().minusMinutes(2L);
        return this.hiddenDangerMapper.selectPushDangerList(minutes);
    }
}
