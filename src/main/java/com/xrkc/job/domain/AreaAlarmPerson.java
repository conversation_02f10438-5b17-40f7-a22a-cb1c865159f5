package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
@TableName(value = "core_area_alarm_person", autoResultMap = true)
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/AreaAlarmPerson.class */
public class AreaAlarmPerson implements Serializable {
    private static final long serialVersionUID = 1;
    private Long areaId;
    private Long personId;
    @TableField(exist = false)
    private String realName;
    public static final String AREA_ID = "area_id";
    public static final String PERSON_ID = "person_id";
    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }
    public void setPersonId(Long personId) {
        this.personId = personId;
    }
    public void setRealName(String realName) {
        this.realName = realName;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof AreaAlarmPerson)) {
            return false;
        }
        AreaAlarmPerson other = (AreaAlarmPerson) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$areaId = getAreaId();
        Object other$areaId = other.getAreaId();
        if (this$areaId == null) {
            if (other$areaId != null) {
                return false;
            }
        } else if (!this$areaId.equals(other$areaId)) {
            return false;
        }
        Object this$personId = getPersonId();
        Object other$personId = other.getPersonId();
        if (this$personId == null) {
            if (other$personId != null) {
                return false;
            }
        } else if (!this$personId.equals(other$personId)) {
            return false;
        }
        Object this$realName = getRealName();
        Object other$realName = other.getRealName();
        return this$realName == null ? other$realName == null : this$realName.equals(other$realName);
    }
    protected boolean canEqual(Object other) {
        return other instanceof AreaAlarmPerson;
    }
    public int hashCode() {
        Object $areaId = getAreaId();
        int result = (1 * 59) + ($areaId == null ? 43 : $areaId.hashCode());
        Object $personId = getPersonId();
        int result2 = (result * 59) + ($personId == null ? 43 : $personId.hashCode());
        Object $realName = getRealName();
        return (result2 * 59) + ($realName == null ? 43 : $realName.hashCode());
    }
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/AreaAlarmPerson$AreaAlarmPersonBuilder.class */
    public static class AreaAlarmPersonBuilder {
        private Long areaId;
        private Long personId;
        private String realName;
        AreaAlarmPersonBuilder() {
        }
        public AreaAlarmPersonBuilder areaId(Long areaId) {
            this.areaId = areaId;
            return this;
        }
        public AreaAlarmPersonBuilder personId(Long personId) {
            this.personId = personId;
            return this;
        }
        public AreaAlarmPersonBuilder realName(String realName) {
            this.realName = realName;
            return this;
        }
        public AreaAlarmPerson build() {
            return new AreaAlarmPerson(this.areaId, this.personId, this.realName);
        }
        public String toString() {
            return "AreaAlarmPerson.AreaAlarmPersonBuilder(areaId=" + this.areaId + ", personId=" + this.personId + ", realName=" + this.realName + StringPool.RIGHT_BRACKET;
        }
    }
    public String toString() {
        return "AreaAlarmPerson(super=" + super.toString() + ", areaId=" + getAreaId() + ", personId=" + getPersonId() + ", realName=" + getRealName() + StringPool.RIGHT_BRACKET;
    }
    public static AreaAlarmPersonBuilder builder() {
        return new AreaAlarmPersonBuilder();
    }
    public AreaAlarmPerson(Long areaId, Long personId, String realName) {
        this.areaId = areaId;
        this.personId = personId;
        this.realName = realName;
    }
    public AreaAlarmPerson() {
    }
    public Long getAreaId() {
        return this.areaId;
    }
    public Long getPersonId() {
        return this.personId;
    }
    public String getRealName() {
        return this.realName;
    }
    public AreaAlarmPerson(Long areaId, Long personId) {
        this.areaId = areaId;
        this.personId = personId;
    }
}
