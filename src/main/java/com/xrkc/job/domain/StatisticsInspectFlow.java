package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
@TableName("statistics_inspect_flow")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/StatisticsInspectFlow.class */
public class StatisticsInspectFlow implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId("statistics_id")
    private Long statisticsId;
    @TableField("statistics_date")
    private LocalDate statisticsDate;
    @TableField("inspect_type")
    private String inspectType;
    @TableField("inspect_type_name")
    private String inspectTypeName;
    @TableField("inspect_count")
    private Integer inspectCount;
    @TableField("create_time")
    private LocalDateTime createTime;
    @TableField("update_time")
    private LocalDateTime updateTime;
    public void setStatisticsId(Long statisticsId) {
        this.statisticsId = statisticsId;
    }
    public void setStatisticsDate(LocalDate statisticsDate) {
        this.statisticsDate = statisticsDate;
    }
    public void setInspectType(String inspectType) {
        this.inspectType = inspectType;
    }
    public void setInspectTypeName(String inspectTypeName) {
        this.inspectTypeName = inspectTypeName;
    }
    public void setInspectCount(Integer inspectCount) {
        this.inspectCount = inspectCount;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    public String toString() {
        return "StatisticsInspectFlow(statisticsId=" + getStatisticsId() + ", statisticsDate=" + getStatisticsDate() + ", inspectType=" + getInspectType() + ", inspectTypeName=" + getInspectTypeName() + ", inspectCount=" + getInspectCount() + ", createTime=" + getCreateTime() + ", updateTime=" + getUpdateTime() + StringPool.RIGHT_BRACKET;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof StatisticsInspectFlow)) {
            return false;
        }
        StatisticsInspectFlow other = (StatisticsInspectFlow) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$statisticsId = getStatisticsId();
        Object other$statisticsId = other.getStatisticsId();
        if (this$statisticsId == null) {
            if (other$statisticsId != null) {
                return false;
            }
        } else if (!this$statisticsId.equals(other$statisticsId)) {
            return false;
        }
        Object this$inspectCount = getInspectCount();
        Object other$inspectCount = other.getInspectCount();
        if (this$inspectCount == null) {
            if (other$inspectCount != null) {
                return false;
            }
        } else if (!this$inspectCount.equals(other$inspectCount)) {
            return false;
        }
        Object this$statisticsDate = getStatisticsDate();
        Object other$statisticsDate = other.getStatisticsDate();
        if (this$statisticsDate == null) {
            if (other$statisticsDate != null) {
                return false;
            }
        } else if (!this$statisticsDate.equals(other$statisticsDate)) {
            return false;
        }
        Object this$inspectType = getInspectType();
        Object other$inspectType = other.getInspectType();
        if (this$inspectType == null) {
            if (other$inspectType != null) {
                return false;
            }
        } else if (!this$inspectType.equals(other$inspectType)) {
            return false;
        }
        Object this$inspectTypeName = getInspectTypeName();
        Object other$inspectTypeName = other.getInspectTypeName();
        if (this$inspectTypeName == null) {
            if (other$inspectTypeName != null) {
                return false;
            }
        } else if (!this$inspectTypeName.equals(other$inspectTypeName)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        if (this$createTime == null) {
            if (other$createTime != null) {
                return false;
            }
        } else if (!this$createTime.equals(other$createTime)) {
            return false;
        }
        Object this$updateTime = getUpdateTime();
        Object other$updateTime = other.getUpdateTime();
        return this$updateTime == null ? other$updateTime == null : this$updateTime.equals(other$updateTime);
    }
    protected boolean canEqual(Object other) {
        return other instanceof StatisticsInspectFlow;
    }
    public int hashCode() {
        Object $statisticsId = getStatisticsId();
        int result = (1 * 59) + ($statisticsId == null ? 43 : $statisticsId.hashCode());
        Object $inspectCount = getInspectCount();
        int result2 = (result * 59) + ($inspectCount == null ? 43 : $inspectCount.hashCode());
        Object $statisticsDate = getStatisticsDate();
        int result3 = (result2 * 59) + ($statisticsDate == null ? 43 : $statisticsDate.hashCode());
        Object $inspectType = getInspectType();
        int result4 = (result3 * 59) + ($inspectType == null ? 43 : $inspectType.hashCode());
        Object $inspectTypeName = getInspectTypeName();
        int result5 = (result4 * 59) + ($inspectTypeName == null ? 43 : $inspectTypeName.hashCode());
        Object $createTime = getCreateTime();
        int result6 = (result5 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $updateTime = getUpdateTime();
        return (result6 * 59) + ($updateTime == null ? 43 : $updateTime.hashCode());
    }
    public Long getStatisticsId() {
        return this.statisticsId;
    }
    public LocalDate getStatisticsDate() {
        return this.statisticsDate;
    }
    public String getInspectType() {
        return this.inspectType;
    }
    public String getInspectTypeName() {
        return this.inspectTypeName;
    }
    public Integer getInspectCount() {
        return this.inspectCount;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public LocalDateTime getUpdateTime() {
        return this.updateTime;
    }
}
