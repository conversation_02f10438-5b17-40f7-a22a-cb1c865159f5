package com.xrkc.job.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xrkc.job.domain.InspectRecordLocation;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IInspectRecordLocationService.class */
public interface IInspectRecordLocationService extends IService<InspectRecordLocation> {
    List<InspectRecordLocation> selectLocationList(LocalDate localDate, LocalDateTime localDateTime);
}
