package com.xrkc.job.util;
import cn.hutool.core.date.DatePattern;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Calendar;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/TimeSectionUtils.class */
public class TimeSectionUtils {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) TimeSectionUtils.class);
    public static void main(String[] args) {
        LocalTime startTime = LocalTime.parse("00:02:00");
        LocalTime endTime = LocalTime.parse("00:03:00");
        System.out.println(currentTimeBetween(startTime, endTime));
        LocalTime startTime2 = LocalTime.parse("23:50:21");
        LocalTime endTime2 = LocalTime.parse("00:30:52");
        System.out.println(currentTimeBetween(startTime2, endTime2));
    }
    public static boolean currentTimeBetween(LocalTime startTime, LocalTime endTime) {
        LocalDateTime beginTime;
        LocalDateTime finalTime;
        LocalTime nowTime = LocalTime.now();
        if (startTime.isBefore(endTime)) {
            return startTime.isBefore(nowTime) && endTime.isAfter(nowTime);
        }
        LocalTime minTime = LocalTime.MIN;
        LocalTime maxTime = LocalTime.MAX;
        LocalDate today = LocalDate.now();
        LocalDateTime now = LocalDateTime.now();
        if (startTime.isBefore(nowTime) && maxTime.isAfter(nowTime)) {
            beginTime = startTime.atDate(today);
            finalTime = endTime.atDate(today.plusDays(1L));
        } else if (minTime.isBefore(nowTime) && endTime.isAfter(nowTime)) {
            beginTime = startTime.atDate(today.minusDays(1L));
            finalTime = endTime.atDate(today);
        } else {
            return false;
        }
        return beginTime.isBefore(now) && finalTime.isAfter(now);
    }
    public static boolean isBelongPeriodTime(String date1, String date2) {
        Date startTimeDate = null;
        Date endTimeDate = null;
        SimpleDateFormat df = new SimpleDateFormat(DatePattern.NORM_TIME_PATTERN);
        Date currentTime = new Date(System.currentTimeMillis());
        Calendar date = Calendar.getInstance();
        Calendar begin = Calendar.getInstance();
        Calendar end = Calendar.getInstance();
        try {
            date.setTime(df.parse(df.format(currentTime)));
            startTimeDate = df.parse(date1);
            endTimeDate = df.parse(date2);
            begin.setTime(startTimeDate);
            end.setTime(endTimeDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (endTimeDate.getHours() < startTimeDate.getHours()) {
            if (date.after(begin) || date.before(end)) {
                return true;
            }
            return false;
        }
        if (endTimeDate.getHours() == startTimeDate.getHours() && endTimeDate.getMinutes() < startTimeDate.getMinutes()) {
            if (date.after(begin) || date.before(end)) {
                return true;
            }
            return false;
        }
        if (date.after(begin) && date.before(end)) {
            return true;
        }
        return false;
    }
    private boolean isAfterTime(String time) {
        SimpleDateFormat df = new SimpleDateFormat(DatePattern.NORM_TIME_PATTERN);
        Date currentTime = new Date(System.currentTimeMillis());
        Calendar curTime = Calendar.getInstance();
        Calendar scaTime = Calendar.getInstance();
        try {
            Date timeScale = df.parse(time);
            curTime.setTime(df.parse(df.format(currentTime)));
            scaTime.setTime(timeScale);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (curTime.after(scaTime)) {
            return true;
        }
        return false;
    }
}
