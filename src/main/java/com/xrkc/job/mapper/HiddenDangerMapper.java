package com.xrkc.job.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xrkc.job.domain.HiddenDanger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.postgresql.jdbc.EscapedFunctions;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/HiddenDangerMapper.class */
public interface HiddenDangerMapper extends BaseMapper<HiddenDanger> {
    @Select({"select hidden_danger_result from hidden_danger where create_time>#{now} and create_time<#{nowTime}"})
    List<HiddenDanger> selectDangerList(@Param(EscapedFunctions.NOW_FUNC) LocalDate localDate, @Param("nowTime") LocalDateTime localDateTime);
    @Select({"SELECT hidden_danger_id,location_name,create_time FROM hidden_danger WHERE create_time > #{minutes}"})
    List<HiddenDanger> selectPushDangerList(@Param("minutes") LocalDateTime localDateTime);
}
