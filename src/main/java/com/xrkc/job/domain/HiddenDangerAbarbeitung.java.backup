package com.xrkc.job.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.time.LocalDateTime;
//import org.springframework.cache.interceptor.CacheOperationExpressionEvaluator;

@TableName("hidden_danger_abarbeitung")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/HiddenDangerAbarbeitung.class */
public class HiddenDangerAbarbeitung implements Serializable {
    private static final long serialVersionUID = 1;

    @TableId("id")
    private Long id;

    @TableField("person_id")
    private Long personId;

    @TableField("person_name")
    private String personName;

    @TableField("abarbeitung_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime abarbeitungTime;

    @TableField("abarbeitung_deadline")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime abarbeitungDeadline;

    @TableField("deadline_remark")
    private String deadlineRemark;

    @TableField("feedback")
    private String feedback;

   // @TableField(CacheOperationExpressionEvaluator.RESULT_VARIABLE)
    private Boolean result;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/HiddenDangerAbarbeitung$HiddenDangerAbarbeitungBuilder.class */
    public static class HiddenDangerAbarbeitungBuilder {
        private Long id;
        private Long personId;
        private String personName;
        private LocalDateTime abarbeitungTime;
        private LocalDateTime abarbeitungDeadline;
        private String deadlineRemark;
        private String feedback;
        private Boolean result;
        private LocalDateTime createTime;
        private LocalDateTime updateTime;

        HiddenDangerAbarbeitungBuilder() {
        }

        public HiddenDangerAbarbeitungBuilder id(Long id) {
            this.id = id;
            return this;
        }

        public HiddenDangerAbarbeitungBuilder personId(Long personId) {
            this.personId = personId;
            return this;
        }

        public HiddenDangerAbarbeitungBuilder personName(String personName) {
            this.personName = personName;
            return this;
        }

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        public HiddenDangerAbarbeitungBuilder abarbeitungTime(LocalDateTime abarbeitungTime) {
            this.abarbeitungTime = abarbeitungTime;
            return this;
        }

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        public HiddenDangerAbarbeitungBuilder abarbeitungDeadline(LocalDateTime abarbeitungDeadline) {
            this.abarbeitungDeadline = abarbeitungDeadline;
            return this;
        }

        public HiddenDangerAbarbeitungBuilder deadlineRemark(String deadlineRemark) {
            this.deadlineRemark = deadlineRemark;
            return this;
        }

        public HiddenDangerAbarbeitungBuilder feedback(String feedback) {
            this.feedback = feedback;
            return this;
        }

        public HiddenDangerAbarbeitungBuilder result(Boolean result) {
            this.result = result;
            return this;
        }

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        public HiddenDangerAbarbeitungBuilder createTime(LocalDateTime createTime) {
            this.createTime = createTime;
            return this;
        }

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        public HiddenDangerAbarbeitungBuilder updateTime(LocalDateTime updateTime) {
            this.updateTime = updateTime;
            return this;
        }

        public HiddenDangerAbarbeitung build() {
            return new HiddenDangerAbarbeitung(this.id, this.personId, this.personName, this.abarbeitungTime, this.abarbeitungDeadline, this.deadlineRemark, this.feedback, this.result, this.createTime, this.updateTime);
        }

        public String toString() {
            return "HiddenDangerAbarbeitung.HiddenDangerAbarbeitungBuilder(id=" + this.id + ", personId=" + this.personId + ", personName=" + this.personName + ", abarbeitungTime=" + this.abarbeitungTime + ", abarbeitungDeadline=" + this.abarbeitungDeadline + ", deadlineRemark=" + this.deadlineRemark + ", feedback=" + this.feedback + ", result=" + this.result + ", createTime=" + this.createTime + ", updateTime=" + this.updateTime + StringPool.RIGHT_BRACKET;
        }
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setPersonId(Long personId) {
        this.personId = personId;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public void setAbarbeitungTime(LocalDateTime abarbeitungTime) {
        this.abarbeitungTime = abarbeitungTime;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public void setAbarbeitungDeadline(LocalDateTime abarbeitungDeadline) {
        this.abarbeitungDeadline = abarbeitungDeadline;
    }

    public void setDeadlineRemark(String deadlineRemark) {
        this.deadlineRemark = deadlineRemark;
    }

    public void setFeedback(String feedback) {
        this.feedback = feedback;
    }

    public void setResult(Boolean result) {
        this.result = result;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof HiddenDangerAbarbeitung)) {
            return false;
        }
        HiddenDangerAbarbeitung other = (HiddenDangerAbarbeitung) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$id = getId();
        Object other$id = other.getId();
        if (this$id == null) {
            if (other$id != null) {
                return false;
            }
        } else if (!this$id.equals(other$id)) {
            return false;
        }
        Object this$personId = getPersonId();
        Object other$personId = other.getPersonId();
        if (this$personId == null) {
            if (other$personId != null) {
                return false;
            }
        } else if (!this$personId.equals(other$personId)) {
            return false;
        }
        Object this$result = getResult();
        Object other$result = other.getResult();
        if (this$result == null) {
            if (other$result != null) {
                return false;
            }
        } else if (!this$result.equals(other$result)) {
            return false;
        }
        Object this$personName = getPersonName();
        Object other$personName = other.getPersonName();
        if (this$personName == null) {
            if (other$personName != null) {
                return false;
            }
        } else if (!this$personName.equals(other$personName)) {
            return false;
        }
        Object this$abarbeitungTime = getAbarbeitungTime();
        Object other$abarbeitungTime = other.getAbarbeitungTime();
        if (this$abarbeitungTime == null) {
            if (other$abarbeitungTime != null) {
                return false;
            }
        } else if (!this$abarbeitungTime.equals(other$abarbeitungTime)) {
            return false;
        }
        Object this$abarbeitungDeadline = getAbarbeitungDeadline();
        Object other$abarbeitungDeadline = other.getAbarbeitungDeadline();
        if (this$abarbeitungDeadline == null) {
            if (other$abarbeitungDeadline != null) {
                return false;
            }
        } else if (!this$abarbeitungDeadline.equals(other$abarbeitungDeadline)) {
            return false;
        }
        Object this$deadlineRemark = getDeadlineRemark();
        Object other$deadlineRemark = other.getDeadlineRemark();
        if (this$deadlineRemark == null) {
            if (other$deadlineRemark != null) {
                return false;
            }
        } else if (!this$deadlineRemark.equals(other$deadlineRemark)) {
            return false;
        }
        Object this$feedback = getFeedback();
        Object other$feedback = other.getFeedback();
        if (this$feedback == null) {
            if (other$feedback != null) {
                return false;
            }
        } else if (!this$feedback.equals(other$feedback)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        if (this$createTime == null) {
            if (other$createTime != null) {
                return false;
            }
        } else if (!this$createTime.equals(other$createTime)) {
            return false;
        }
        Object this$updateTime = getUpdateTime();
        Object other$updateTime = other.getUpdateTime();
        return this$updateTime == null ? other$updateTime == null : this$updateTime.equals(other$updateTime);
    }

    protected boolean canEqual(Object other) {
        return other instanceof HiddenDangerAbarbeitung;
    }

    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $personId = getPersonId();
        int result2 = (result * 59) + ($personId == null ? 43 : $personId.hashCode());
        Object $result = getResult();
        int result3 = (result2 * 59) + ($result == null ? 43 : $result.hashCode());
        Object $personName = getPersonName();
        int result4 = (result3 * 59) + ($personName == null ? 43 : $personName.hashCode());
        Object $abarbeitungTime = getAbarbeitungTime();
        int result5 = (result4 * 59) + ($abarbeitungTime == null ? 43 : $abarbeitungTime.hashCode());
        Object $abarbeitungDeadline = getAbarbeitungDeadline();
        int result6 = (result5 * 59) + ($abarbeitungDeadline == null ? 43 : $abarbeitungDeadline.hashCode());
        Object $deadlineRemark = getDeadlineRemark();
        int result7 = (result6 * 59) + ($deadlineRemark == null ? 43 : $deadlineRemark.hashCode());
        Object $feedback = getFeedback();
        int result8 = (result7 * 59) + ($feedback == null ? 43 : $feedback.hashCode());
        Object $createTime = getCreateTime();
        int result9 = (result8 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
        Object $updateTime = getUpdateTime();
        return (result9 * 59) + ($updateTime == null ? 43 : $updateTime.hashCode());
    }

    public String toString() {
        return "HiddenDangerAbarbeitung(id=" + getId() + ", personId=" + getPersonId() + ", personName=" + getPersonName() + ", abarbeitungTime=" + getAbarbeitungTime() + ", abarbeitungDeadline=" + getAbarbeitungDeadline() + ", deadlineRemark=" + getDeadlineRemark() + ", feedback=" + getFeedback() + ", result=" + getResult() + ", createTime=" + getCreateTime() + ", updateTime=" + getUpdateTime() + StringPool.RIGHT_BRACKET;
    }

    HiddenDangerAbarbeitung(Long id, Long personId, String personName, LocalDateTime abarbeitungTime, LocalDateTime abarbeitungDeadline, String deadlineRemark, String feedback, Boolean result, LocalDateTime createTime, LocalDateTime updateTime) {
        this.id = id;
        this.personId = personId;
        this.personName = personName;
        this.abarbeitungTime = abarbeitungTime;
        this.abarbeitungDeadline = abarbeitungDeadline;
        this.deadlineRemark = deadlineRemark;
        this.feedback = feedback;
        this.result = result;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }

    public static HiddenDangerAbarbeitungBuilder builder() {
        return new HiddenDangerAbarbeitungBuilder();
    }

    public Long getId() {
        return this.id;
    }

    public Long getPersonId() {
        return this.personId;
    }

    public String getPersonName() {
        return this.personName;
    }

    public LocalDateTime getAbarbeitungTime() {
        return this.abarbeitungTime;
    }

    public LocalDateTime getAbarbeitungDeadline() {
        return this.abarbeitungDeadline;
    }

    public String getDeadlineRemark() {
        return this.deadlineRemark;
    }

    public String getFeedback() {
        return this.feedback;
    }

    public Boolean getResult() {
        return this.result;
    }

    public LocalDateTime getCreateTime() {
        return this.createTime;
    }

    public LocalDateTime getUpdateTime() {
        return this.updateTime;
    }
}
