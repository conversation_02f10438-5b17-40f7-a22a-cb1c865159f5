package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
@TableName(value = "statistics_alarm_flow", autoResultMap = true)
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/StatisticsAlarmFlow.class */
public class StatisticsAlarmFlow implements Serializable {
    private static final long serialVersionUID = 1;
    @TableId
    private Long statisticsId;
    private LocalDate statisticsDate;
    private String alarmType;
    private String alarmTypeName;
    private Integer alarmCount;
    private LocalDateTime createTime;
    public void setStatisticsId(Long statisticsId) {
        this.statisticsId = statisticsId;
    }
    public void setStatisticsDate(LocalDate statisticsDate) {
        this.statisticsDate = statisticsDate;
    }
    public void setAlarmType(String alarmType) {
        this.alarmType = alarmType;
    }
    public void setAlarmTypeName(String alarmTypeName) {
        this.alarmTypeName = alarmTypeName;
    }
    public void setAlarmCount(Integer alarmCount) {
        this.alarmCount = alarmCount;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof StatisticsAlarmFlow)) {
            return false;
        }
        StatisticsAlarmFlow other = (StatisticsAlarmFlow) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$statisticsId = getStatisticsId();
        Object other$statisticsId = other.getStatisticsId();
        if (this$statisticsId == null) {
            if (other$statisticsId != null) {
                return false;
            }
        } else if (!this$statisticsId.equals(other$statisticsId)) {
            return false;
        }
        Object this$alarmCount = getAlarmCount();
        Object other$alarmCount = other.getAlarmCount();
        if (this$alarmCount == null) {
            if (other$alarmCount != null) {
                return false;
            }
        } else if (!this$alarmCount.equals(other$alarmCount)) {
            return false;
        }
        Object this$statisticsDate = getStatisticsDate();
        Object other$statisticsDate = other.getStatisticsDate();
        if (this$statisticsDate == null) {
            if (other$statisticsDate != null) {
                return false;
            }
        } else if (!this$statisticsDate.equals(other$statisticsDate)) {
            return false;
        }
        Object this$alarmType = getAlarmType();
        Object other$alarmType = other.getAlarmType();
        if (this$alarmType == null) {
            if (other$alarmType != null) {
                return false;
            }
        } else if (!this$alarmType.equals(other$alarmType)) {
            return false;
        }
        Object this$alarmTypeName = getAlarmTypeName();
        Object other$alarmTypeName = other.getAlarmTypeName();
        if (this$alarmTypeName == null) {
            if (other$alarmTypeName != null) {
                return false;
            }
        } else if (!this$alarmTypeName.equals(other$alarmTypeName)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        return this$createTime == null ? other$createTime == null : this$createTime.equals(other$createTime);
    }
    protected boolean canEqual(Object other) {
        return other instanceof StatisticsAlarmFlow;
    }
    public int hashCode() {
        Object $statisticsId = getStatisticsId();
        int result = (1 * 59) + ($statisticsId == null ? 43 : $statisticsId.hashCode());
        Object $alarmCount = getAlarmCount();
        int result2 = (result * 59) + ($alarmCount == null ? 43 : $alarmCount.hashCode());
        Object $statisticsDate = getStatisticsDate();
        int result3 = (result2 * 59) + ($statisticsDate == null ? 43 : $statisticsDate.hashCode());
        Object $alarmType = getAlarmType();
        int result4 = (result3 * 59) + ($alarmType == null ? 43 : $alarmType.hashCode());
        Object $alarmTypeName = getAlarmTypeName();
        int result5 = (result4 * 59) + ($alarmTypeName == null ? 43 : $alarmTypeName.hashCode());
        Object $createTime = getCreateTime();
        return (result5 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
    }
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/StatisticsAlarmFlow$StatisticsAlarmFlowBuilder.class */
    public static class StatisticsAlarmFlowBuilder {
        private Long statisticsId;
        private LocalDate statisticsDate;
        private String alarmType;
        private String alarmTypeName;
        private Integer alarmCount;
        private LocalDateTime createTime;
        StatisticsAlarmFlowBuilder() {
        }
        public StatisticsAlarmFlowBuilder statisticsId(Long statisticsId) {
            this.statisticsId = statisticsId;
            return this;
        }
        public StatisticsAlarmFlowBuilder statisticsDate(LocalDate statisticsDate) {
            this.statisticsDate = statisticsDate;
            return this;
        }
        public StatisticsAlarmFlowBuilder alarmType(String alarmType) {
            this.alarmType = alarmType;
            return this;
        }
        public StatisticsAlarmFlowBuilder alarmTypeName(String alarmTypeName) {
            this.alarmTypeName = alarmTypeName;
            return this;
        }
        public StatisticsAlarmFlowBuilder alarmCount(Integer alarmCount) {
            this.alarmCount = alarmCount;
            return this;
        }
        public StatisticsAlarmFlowBuilder createTime(LocalDateTime createTime) {
            this.createTime = createTime;
            return this;
        }
        public StatisticsAlarmFlow build() {
            return new StatisticsAlarmFlow(this.statisticsId, this.statisticsDate, this.alarmType, this.alarmTypeName, this.alarmCount, this.createTime);
        }
        public String toString() {
            return "StatisticsAlarmFlow.StatisticsAlarmFlowBuilder(statisticsId=" + this.statisticsId + ", statisticsDate=" + this.statisticsDate + ", alarmType=" + this.alarmType + ", alarmTypeName=" + this.alarmTypeName + ", alarmCount=" + this.alarmCount + ", createTime=" + this.createTime + StringPool.RIGHT_BRACKET;
        }
    }
    public String toString() {
        return "StatisticsAlarmFlow(super=" + super.toString() + ", statisticsId=" + getStatisticsId() + ", statisticsDate=" + getStatisticsDate() + ", alarmType=" + getAlarmType() + ", alarmTypeName=" + getAlarmTypeName() + ", alarmCount=" + getAlarmCount() + ", createTime=" + getCreateTime() + StringPool.RIGHT_BRACKET;
    }
    public static StatisticsAlarmFlowBuilder builder() {
        return new StatisticsAlarmFlowBuilder();
    }
    public StatisticsAlarmFlow(Long statisticsId, LocalDate statisticsDate, String alarmType, String alarmTypeName, Integer alarmCount, LocalDateTime createTime) {
        this.statisticsId = statisticsId;
        this.statisticsDate = statisticsDate;
        this.alarmType = alarmType;
        this.alarmTypeName = alarmTypeName;
        this.alarmCount = alarmCount;
        this.createTime = createTime;
    }
    public StatisticsAlarmFlow() {
    }
    public Long getStatisticsId() {
        return this.statisticsId;
    }
    public LocalDate getStatisticsDate() {
        return this.statisticsDate;
    }
    public String getAlarmType() {
        return this.alarmType;
    }
    public String getAlarmTypeName() {
        return this.alarmTypeName;
    }
    public Integer getAlarmCount() {
        return this.alarmCount;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
}
