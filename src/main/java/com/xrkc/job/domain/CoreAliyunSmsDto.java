package com.xrkc.job.domain;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/CoreAliyunSmsDto.class */
public class CoreAliyunSmsDto {
    private Long accessKeyId;
    private String accessKeySecret;
    private String signName;
    private String templateCode;
    private String endpoint;
    private int numberSms;
    public void setAccessKeyId(Long accessKeyId) {
        this.accessKeyId = accessKeyId;
    }
    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }
    public void setSignName(String signName) {
        this.signName = signName;
    }
    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }
    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }
    public void setNumberSms(int numberSms) {
        this.numberSms = numberSms;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof CoreAliyunSmsDto)) {
            return false;
        }
        CoreAliyunSmsDto other = (CoreAliyunSmsDto) o;
        if (!other.canEqual(this) || getNumberSms() != other.getNumberSms()) {
            return false;
        }
        Object this$accessKeyId = getAccessKeyId();
        Object other$accessKeyId = other.getAccessKeyId();
        if (this$accessKeyId == null) {
            if (other$accessKeyId != null) {
                return false;
            }
        } else if (!this$accessKeyId.equals(other$accessKeyId)) {
            return false;
        }
        Object this$accessKeySecret = getAccessKeySecret();
        Object other$accessKeySecret = other.getAccessKeySecret();
        if (this$accessKeySecret == null) {
            if (other$accessKeySecret != null) {
                return false;
            }
        } else if (!this$accessKeySecret.equals(other$accessKeySecret)) {
            return false;
        }
        Object this$signName = getSignName();
        Object other$signName = other.getSignName();
        if (this$signName == null) {
            if (other$signName != null) {
                return false;
            }
        } else if (!this$signName.equals(other$signName)) {
            return false;
        }
        Object this$templateCode = getTemplateCode();
        Object other$templateCode = other.getTemplateCode();
        if (this$templateCode == null) {
            if (other$templateCode != null) {
                return false;
            }
        } else if (!this$templateCode.equals(other$templateCode)) {
            return false;
        }
        Object this$endpoint = getEndpoint();
        Object other$endpoint = other.getEndpoint();
        return this$endpoint == null ? other$endpoint == null : this$endpoint.equals(other$endpoint);
    }
    protected boolean canEqual(Object other) {
        return other instanceof CoreAliyunSmsDto;
    }
    public int hashCode() {
        int result = (1 * 59) + getNumberSms();
        Object $accessKeyId = getAccessKeyId();
        int result2 = (result * 59) + ($accessKeyId == null ? 43 : $accessKeyId.hashCode());
        Object $accessKeySecret = getAccessKeySecret();
        int result3 = (result2 * 59) + ($accessKeySecret == null ? 43 : $accessKeySecret.hashCode());
        Object $signName = getSignName();
        int result4 = (result3 * 59) + ($signName == null ? 43 : $signName.hashCode());
        Object $templateCode = getTemplateCode();
        int result5 = (result4 * 59) + ($templateCode == null ? 43 : $templateCode.hashCode());
        Object $endpoint = getEndpoint();
        return (result5 * 59) + ($endpoint == null ? 43 : $endpoint.hashCode());
    }
    public String toString() {
        return "CoreAliyunSmsDto(accessKeyId=" + getAccessKeyId() + ", accessKeySecret=" + getAccessKeySecret() + ", signName=" + getSignName() + ", templateCode=" + getTemplateCode() + ", endpoint=" + getEndpoint() + ", numberSms=" + getNumberSms() + StringPool.RIGHT_BRACKET;
    }
    public Long getAccessKeyId() {
        return this.accessKeyId;
    }
    public String getAccessKeySecret() {
        return this.accessKeySecret;
    }
    public String getSignName() {
        return this.signName;
    }
    public String getTemplateCode() {
        return this.templateCode;
    }
    public String getEndpoint() {
        return this.endpoint;
    }
    public int getNumberSms() {
        return this.numberSms;
    }
}
