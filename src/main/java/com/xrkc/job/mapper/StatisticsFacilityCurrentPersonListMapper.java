package com.xrkc.job.mapper;
import com.xrkc.core.domain.statistics.StatisticsFacilityCurrentPersonList;
import com.xrkc.core.mybatisplus.BaseMapperX;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/StatisticsFacilityCurrentPersonListMapper.class */
public interface StatisticsFacilityCurrentPersonListMapper extends BaseMapperX<StatisticsFacilityCurrentPersonList> {
    @Delete({"TRUNCATE TABLE statistics_facility_current_person_list"})
    void deleteAll();
}
