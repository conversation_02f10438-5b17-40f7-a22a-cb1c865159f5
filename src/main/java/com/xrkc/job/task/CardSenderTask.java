package com.xrkc.job.task;
import com.xrkc.job.remote.RemoteDeviceCardSenderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
@Component("cardSenderTask")
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/task/CardSenderTask.class */
public class CardSenderTask {
    private static final Logger log = LoggerFactory.getLogger((Class<?>) CardSenderTask.class);
    @Autowired
    private RemoteDeviceCardSenderService deviceCardSenderService;
    public void syncWhiteList() {
        this.deviceCardSenderService.syncWhiteList();
    }
}
