package com.xrkc.job.domain;
import java.io.Serializable;
import java.time.LocalDateTime;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/StatisticsRailCurrentPerson.class */
public class StatisticsRailCurrentPerson implements Serializable {
    private static final long serialVersionUID = 1;
    private Long statisticsId;
    private Long railId;
    private String railName;
    private String railType;
    private String layerId;
    private Integer layerHeight;
    private String railScope;
    private LocalDateTime statisticsTime;
    private String personType;
    private String personTypeName;
    private Long personCount;
    public String getRailType() {
        return this.railType;
    }
    public void setRailType(String railType) {
        this.railType = railType;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public void setLayerId(String layerId) {
        this.layerId = layerId;
    }
    public Integer getLayerHeight() {
        return this.layerHeight;
    }
    public void setLayerHeight(Integer layerHeight) {
        this.layerHeight = layerHeight;
    }
    public String getRailScope() {
        return this.railScope;
    }
    public void setRailScope(String railScope) {
        this.railScope = railScope;
    }
    public Long getRailId() {
        return this.railId;
    }
    public void setRailId(Long railId) {
        this.railId = railId;
    }
    public String getRailName() {
        return this.railName;
    }
    public void setRailName(String railName) {
        this.railName = railName;
    }
    public Long getStatisticsId() {
        return this.statisticsId;
    }
    public void setStatisticsId(Long statisticsId) {
        this.statisticsId = statisticsId;
    }
    public LocalDateTime getStatisticsTime() {
        return this.statisticsTime;
    }
    public void setStatisticsTime(LocalDateTime statisticsTime) {
        this.statisticsTime = statisticsTime;
    }
    public String getPersonType() {
        return this.personType;
    }
    public void setPersonType(String personType) {
        this.personType = personType;
    }
    public String getPersonTypeName() {
        return this.personTypeName;
    }
    public void setPersonTypeName(String personTypeName) {
        this.personTypeName = personTypeName;
    }
    public Long getPersonCount() {
        return this.personCount;
    }
    public void setPersonCount(Long personCount) {
        this.personCount = personCount;
    }
}
