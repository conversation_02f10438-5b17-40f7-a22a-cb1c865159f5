package com.xrkc.job.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xrkc.job.domain.StatisticsInspectFlowDanger;
import java.time.LocalDate;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
@Mapper
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/mapper/StatisticsInspectFlowDangerMapper.class */
public interface StatisticsInspectFlowDangerMapper extends BaseMapper<StatisticsInspectFlowDanger> {
    int batchReplace(@Param("list") List<StatisticsInspectFlowDanger> list);
    @Select({"select danger_type ,statistics_date  from statistics_inspect_flow_danger where statistics_date =#{statisticsDate};"})
    List<StatisticsInspectFlowDanger> selectByStatisticsDate(LocalDate localDate);
}
