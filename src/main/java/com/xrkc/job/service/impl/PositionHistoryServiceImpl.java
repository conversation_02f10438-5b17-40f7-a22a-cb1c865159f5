package com.xrkc.job.service.impl;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import com.xrkc.core.constant.CacheConstants;
import com.xrkc.job.domain.PositionDataCalc;
import com.xrkc.job.mapper.PositionHistoryMapper;
import com.xrkc.job.mapper.SysConfigMapper;
import com.xrkc.job.service.IPositionHistoryService;
import com.xrkc.redis.service.RedisService;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/PositionHistoryServiceImpl.class */
public class PositionHistoryServiceImpl implements IPositionHistoryService {
    @Autowired
    private PositionHistoryMapper positionHistoryMapper;
    @Autowired
    private SysConfigMapper sysConfigMapper;
    @Autowired
    private RedisService redisService;
    @Value("${spring.datasource.driver-class-name}")
    private String driverClassName;
    private static final Logger log = LoggerFactory.getLogger((Class<?>) PositionHistoryServiceImpl.class);
    private static final DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter df_22 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SS");
    private static final DateTimeFormatter df_21 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
    private static final DateTimeFormatter df_23 = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_MS_PATTERN);
    @Override // com.xrkc.job.service.IPositionHistoryService
    public boolean existTable(String tableName) {
        return this.driverClassName.contains("dm") ? this.positionHistoryMapper.existTableDm(tableName) > 0 : this.driverClassName.contains("uxdb") ? this.positionHistoryMapper.existTableUxdb(tableName) > 0 : this.positionHistoryMapper.existTableMysql(tableName) > 0;
    }
    @Override // com.xrkc.job.service.IPositionHistoryService
    public int createTable(String tableName) {
        return this.positionHistoryMapper.createTable(tableName);
    }
    @Override // com.xrkc.job.service.IPositionHistoryService
    public int batchBakHistory(String tableName, List<PositionDataCalc> list) {
        if (StringUtils.isBlank(tableName) || CollectionUtils.isEmpty(list)) {
            return 0;
        }
        String datasourceId = "master";
        Map<String, LocalDateTime> lastCardTimeMap = new HashMap<>();
        LocalDate yesterday = LocalDate.now().minusDays(1L);
        ArrayList arrayList = new ArrayList();
        ArrayList<PositionDataCalc> arrayList2 = new ArrayList();
        list.forEach(f -> {
            String key = CacheConstants.personTrackKeyPrefix + datasourceId + ":" + f.getCardId();
            LocalDateTime lastTime = (LocalDateTime) lastCardTimeMap.get(key);
            if (Objects.isNull(lastTime)) {
                String cacheObject = (String) this.redisService.getCacheObject(key);
                if (StringUtils.isNotBlank(cacheObject)) {
                    lastTime = LocalDateTime.parse(cacheObject, cacheObject.length() == 21 ? df_21 : cacheObject.length() == 22 ? df_22 : cacheObject.length() == 23 ? df_23 : df);
                }
            }
            if (Objects.isNull(lastTime) || lastTime.isBefore(f.getAcceptTime())) {
                lastCardTimeMap.put(key, f.getAcceptTime());
                if (yesterday.equals(f.getAcceptTime().toLocalDate())) {
                    arrayList.add(f);
                } else {
                    arrayList2.add(f);
                }
            }
        });
        int rows = 0;
        if (!CollectionUtils.isEmpty(arrayList)) {
            String baseTableName = this.driverClassName.contains("dm") ? "POSITION_HISTORY_" : "position_history_";
            Object yesterdayTableName = baseTableName + yesterday.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            Map<String, Object> params = new HashMap<>();
            params.put("list", arrayList);
            params.put("tableName", yesterdayTableName);
            rows = 0 + this.positionHistoryMapper.batchBakHistory(params);
        }
        if (!CollectionUtils.isEmpty(arrayList2)) {
            Map<String, Object> params2 = new HashMap<>();
            params2.put("list", arrayList2);
            params2.put("tableName", tableName);
            int r = this.positionHistoryMapper.batchBakHistory(params2);
            if (r > 0) {
                Map<Long, Optional<PositionDataCalc>> map =  arrayList2.stream().collect(Collectors.groupingBy(v0 -> {
                    return v0.getCardId();
                }, Collectors.maxBy(Comparator.comparing((v0) -> {
                    return v0.getAcceptTime();
                }))));
                map.forEach((cardId, entity) -> {
                    if (entity.isPresent()) {
                        String key = CacheConstants.personTrackKeyPrefix + datasourceId + ":" + cardId;
                        this.redisService.set(key, entity.get().getAcceptTime(), 420L);
                    }
                });
            }
            rows += r;
        }
        return rows;
    }
    @Override // com.xrkc.job.service.IPositionHistoryService
    public int updateOrInsert(String tableName, List<PositionDataCalc> positionDataList, int batchSize) {
        if (CollUtil.isNotEmpty((Collection<?>) positionDataList)) {
            int listSize = positionDataList.size();
            int i = 0;
            while (true) {
                int i2 = i;
                if (i2 >= listSize) {
                    break;
                }
                List<PositionDataCalc> batch = positionDataList.subList(i2, Math.min(i2 + batchSize, listSize));
                try {
                    batchBakHistory(tableName, batch);
                } catch (Exception e) {
                    log.error("保存人员轨迹失败，批次：{}，表：{}，条数：{}，msg：{}", Integer.valueOf(i2), tableName, Integer.valueOf(batch.size()), e.getMessage());
                    log.error(e.getMessage(), (Throwable) e);
                }
                i = i2 + batchSize;
            }
        }
        return positionDataList.size();
    }
    @Override // com.xrkc.job.service.IPositionHistoryService
    public List<String> selectDeleteHistoryTable() {
        Integer intervalDay = this.sysConfigMapper.getByIntervalDay();
        LocalDateTime minusDays = LocalDateTime.now().minusDays(intervalDay.intValue());
        if (this.driverClassName.contains("dm")) {
            return this.positionHistoryMapper.selectDeleteHistoryTableByDM(minusDays);
        }
        return this.positionHistoryMapper.selectDeleteHistoryTableByMysql(minusDays);
    }
    @Override // com.xrkc.job.service.IPositionHistoryService
    public int deleteTable(String tableName) {
        return this.positionHistoryMapper.deleteTable(tableName);
    }
}
