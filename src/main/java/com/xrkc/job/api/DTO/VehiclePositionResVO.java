package com.xrkc.job.api.DTO;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.math.BigDecimal;
import java.time.LocalDateTime;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/api/DTO/VehiclePositionResVO.class */
public class VehiclePositionResVO {
    private LocalDateTime acceptTime;
    private Long cardId;
    private BigDecimal longitude;
    private BigDecimal latitude;
    private Integer stillStatus;
    private Integer speed;
    private Integer direction;
    private Integer electricity;
    private String pointType;
    private String pointRemark;
    public void setAcceptTime(LocalDateTime acceptTime) {
        this.acceptTime = acceptTime;
    }
    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    public void setStillStatus(Integer stillStatus) {
        this.stillStatus = stillStatus;
    }
    public void setSpeed(Integer speed) {
        this.speed = speed;
    }
    public void setDirection(Integer direction) {
        this.direction = direction;
    }
    public void setElectricity(Integer electricity) {
        this.electricity = electricity;
    }
    public void setPointType(String pointType) {
        this.pointType = pointType;
    }
    public void setPointRemark(String pointRemark) {
        this.pointRemark = pointRemark;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof VehiclePositionResVO)) {
            return false;
        }
        VehiclePositionResVO other = (VehiclePositionResVO) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$cardId = getCardId();
        Object other$cardId = other.getCardId();
        if (this$cardId == null) {
            if (other$cardId != null) {
                return false;
            }
        } else if (!this$cardId.equals(other$cardId)) {
            return false;
        }
        Object this$stillStatus = getStillStatus();
        Object other$stillStatus = other.getStillStatus();
        if (this$stillStatus == null) {
            if (other$stillStatus != null) {
                return false;
            }
        } else if (!this$stillStatus.equals(other$stillStatus)) {
            return false;
        }
        Object this$speed = getSpeed();
        Object other$speed = other.getSpeed();
        if (this$speed == null) {
            if (other$speed != null) {
                return false;
            }
        } else if (!this$speed.equals(other$speed)) {
            return false;
        }
        Object this$direction = getDirection();
        Object other$direction = other.getDirection();
        if (this$direction == null) {
            if (other$direction != null) {
                return false;
            }
        } else if (!this$direction.equals(other$direction)) {
            return false;
        }
        Object this$electricity = getElectricity();
        Object other$electricity = other.getElectricity();
        if (this$electricity == null) {
            if (other$electricity != null) {
                return false;
            }
        } else if (!this$electricity.equals(other$electricity)) {
            return false;
        }
        Object this$acceptTime = getAcceptTime();
        Object other$acceptTime = other.getAcceptTime();
        if (this$acceptTime == null) {
            if (other$acceptTime != null) {
                return false;
            }
        } else if (!this$acceptTime.equals(other$acceptTime)) {
            return false;
        }
        Object this$longitude = getLongitude();
        Object other$longitude = other.getLongitude();
        if (this$longitude == null) {
            if (other$longitude != null) {
                return false;
            }
        } else if (!this$longitude.equals(other$longitude)) {
            return false;
        }
        Object this$latitude = getLatitude();
        Object other$latitude = other.getLatitude();
        if (this$latitude == null) {
            if (other$latitude != null) {
                return false;
            }
        } else if (!this$latitude.equals(other$latitude)) {
            return false;
        }
        Object this$pointType = getPointType();
        Object other$pointType = other.getPointType();
        if (this$pointType == null) {
            if (other$pointType != null) {
                return false;
            }
        } else if (!this$pointType.equals(other$pointType)) {
            return false;
        }
        Object this$pointRemark = getPointRemark();
        Object other$pointRemark = other.getPointRemark();
        return this$pointRemark == null ? other$pointRemark == null : this$pointRemark.equals(other$pointRemark);
    }
    protected boolean canEqual(Object other) {
        return other instanceof VehiclePositionResVO;
    }
    public int hashCode() {
        Object $cardId = getCardId();
        int result = (1 * 59) + ($cardId == null ? 43 : $cardId.hashCode());
        Object $stillStatus = getStillStatus();
        int result2 = (result * 59) + ($stillStatus == null ? 43 : $stillStatus.hashCode());
        Object $speed = getSpeed();
        int result3 = (result2 * 59) + ($speed == null ? 43 : $speed.hashCode());
        Object $direction = getDirection();
        int result4 = (result3 * 59) + ($direction == null ? 43 : $direction.hashCode());
        Object $electricity = getElectricity();
        int result5 = (result4 * 59) + ($electricity == null ? 43 : $electricity.hashCode());
        Object $acceptTime = getAcceptTime();
        int result6 = (result5 * 59) + ($acceptTime == null ? 43 : $acceptTime.hashCode());
        Object $longitude = getLongitude();
        int result7 = (result6 * 59) + ($longitude == null ? 43 : $longitude.hashCode());
        Object $latitude = getLatitude();
        int result8 = (result7 * 59) + ($latitude == null ? 43 : $latitude.hashCode());
        Object $pointType = getPointType();
        int result9 = (result8 * 59) + ($pointType == null ? 43 : $pointType.hashCode());
        Object $pointRemark = getPointRemark();
        return (result9 * 59) + ($pointRemark == null ? 43 : $pointRemark.hashCode());
    }
    public String toString() {
        return "VehiclePositionResVO(acceptTime=" + getAcceptTime() + ", cardId=" + getCardId() + ", longitude=" + getLongitude() + ", latitude=" + getLatitude() + ", stillStatus=" + getStillStatus() + ", speed=" + getSpeed() + ", direction=" + getDirection() + ", electricity=" + getElectricity() + ", pointType=" + getPointType() + ", pointRemark=" + getPointRemark() + StringPool.RIGHT_BRACKET;
    }
    public LocalDateTime getAcceptTime() {
        return this.acceptTime;
    }
    public Long getCardId() {
        return this.cardId;
    }
    public BigDecimal getLongitude() {
        return this.longitude;
    }
    public BigDecimal getLatitude() {
        return this.latitude;
    }
    public Integer getStillStatus() {
        return this.stillStatus;
    }
    public Integer getSpeed() {
        return this.speed;
    }
    public Integer getDirection() {
        return this.direction;
    }
    public Integer getElectricity() {
        return this.electricity;
    }
    public String getPointType() {
        return this.pointType;
    }
    public String getPointRemark() {
        return this.pointRemark;
    }
}
