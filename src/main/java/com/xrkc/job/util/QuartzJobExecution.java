package com.xrkc.job.util;
import com.xrkc.job.domain.SystemJob;
import org.quartz.JobExecutionContext;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/util/QuartzJobExecution.class */
public class QuartzJobExecution extends AbstractQuartzJob {
    @Override // com.xrkc.job.util.AbstractQuartzJob
    protected void doExecute(JobExecutionContext context, SystemJob sysJob) throws Exception {
        JobInvokeUtil.invokeMethod(sysJob);
    }
}
