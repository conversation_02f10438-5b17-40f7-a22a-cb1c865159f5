package com.xrkc.job.service.impl;
import com.xrkc.job.domain.RehearsalPerson;
import com.xrkc.job.mapper.RehearsalPersonMapper;
import com.xrkc.job.service.IRehearsalPersonService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/RehearsalPersonServiceImpl.class */
public class RehearsalPersonServiceImpl implements IRehearsalPersonService {
    @Autowired
    private RehearsalPersonMapper rehearsalPersonMapper;
    @Override // com.xrkc.job.service.IRehearsalPersonService
    public int batchInsert(List<RehearsalPerson> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        this.rehearsalPersonMapper.insertBatch(list, 1000);
        return list.size();
    }
    @Override // com.xrkc.job.service.IRehearsalPersonService
    public List<RehearsalPerson> findRehearsalPersonList(List<Long> deptIds) {
        return this.rehearsalPersonMapper.findRehearsalPersonList(deptIds);
    }
    @Override // com.xrkc.job.service.IRehearsalPersonService
    public List<RehearsalPerson> findUnCompletePersonByRehearsalId(Long rehearsalId) {
        return this.rehearsalPersonMapper.findUnCompletePersonByRehearsalId(rehearsalId);
    }
    @Override // com.xrkc.job.service.IRehearsalPersonService
    public int batchCompleteUpdate(List<Long> completeIds) {
        if (CollectionUtils.isEmpty(completeIds)) {
            return 0;
        }
        return this.rehearsalPersonMapper.batchCompleteUpdate(completeIds);
    }
}
