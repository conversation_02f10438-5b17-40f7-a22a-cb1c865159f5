package com.xrkc.job.module.vehicle.service.impl;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.xrkc.core.domain.vehicle.VehicleAlarmSetting;
import com.xrkc.core.domain.vehicle.VehicleInfo;
import com.xrkc.core.domain.vehicle.dto.VehicleAlarmRailVO;
import com.xrkc.core.domain.vehicle.dto.VehicleAlarmSettingReq;
import com.xrkc.job.domain.Rail;
import com.xrkc.job.module.vehicle.mapper.VehicleAlarmSettingMapper;
import com.xrkc.job.module.vehicle.service.IVehicleAlarmSettingRailService;
import com.xrkc.job.module.vehicle.service.IVehicleAlarmSettingService;
import com.xrkc.job.module.vehicle.service.IVehicleAlarmSettingVehicleService;
import com.xrkc.job.module.vehicle.service.IVehicleInfoService;
import com.xrkc.job.service.IRailService;
import java.lang.invoke.SerializedLambda;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/module/vehicle/service/impl/VehicleAlarmSettingServiceImpl.class */
public class VehicleAlarmSettingServiceImpl implements IVehicleAlarmSettingService {
    private final VehicleAlarmSettingMapper mapper;
    private final IVehicleAlarmSettingVehicleService vehicleAlarmSettingVehicleService;
    private final IVehicleAlarmSettingRailService vehicleAlarmSettingRailService;
    private final IRailService railService;
    private final IVehicleInfoService vehicleInfoService;
    @Autowired
    public VehicleAlarmSettingServiceImpl(VehicleAlarmSettingMapper mapper, IVehicleAlarmSettingVehicleService vehicleAlarmSettingVehicleService, IVehicleAlarmSettingRailService vehicleAlarmSettingRailService, IRailService railService, IVehicleInfoService vehicleInfoService) {
        this.mapper = mapper;
        this.vehicleAlarmSettingVehicleService = vehicleAlarmSettingVehicleService;
        this.vehicleAlarmSettingRailService = vehicleAlarmSettingRailService;
        this.railService = railService;
        this.vehicleInfoService = vehicleInfoService;
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehicleAlarmSettingService
    public List<VehicleAlarmSettingReq> selectEnableListByType(String vehicleAlarmType) {
        List<VehicleAlarmSetting> list = this.mapper.selectList((v0) -> {
            return v0.getEnableStatus();
        }, "Y", (v0) -> {
            return v0.getVehicleAlarmType();
        }, vehicleAlarmType);
        return buildList(list);
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehicleAlarmSettingService
    public List<Long> selectEnableSettingId() {
        return (List) this.mapper.selectEnableSettingId().stream().map((v0) -> {
            return v0.getId();
        }).collect(Collectors.toList());
    }
    @Override // com.xrkc.job.module.vehicle.service.IVehicleAlarmSettingService
    public List<VehicleAlarmSettingReq> selectEnableList(List<String> vehicleAlarmTypeList) {
        List<VehicleAlarmSetting> list = this.mapper.selectListIn("Y", vehicleAlarmTypeList);
        return buildList(list);
    }
    public List<VehicleAlarmSettingReq> buildList(List<VehicleAlarmSetting> list) {
        List<VehicleAlarmSettingReq> reqList = CollectionUtils.isEmpty(list) ? null : BeanUtil.copyToList(list, VehicleAlarmSettingReq.class);
        if (!CollectionUtils.isEmpty(reqList)) {
            reqList.stream().forEach(req -> {
                List<VehicleInfo> vehicleInfoList;
                List<Long> railIdList = this.vehicleAlarmSettingRailService.getRailIdListBySettingId(req.getId());
                if (!CollectionUtils.isEmpty(railIdList)) {
                    List<Rail> railList = this.railService.selectByIdList(railIdList);
                    List<VehicleAlarmRailVO> railVOList = BeanUtil.copyToList(railList, VehicleAlarmRailVO.class);
                    req.setRailList(railVOList);
                    req.setRailIdList((List) railVOList.stream().map((v0) -> {
                        return v0.getRailId();
                    }).collect(Collectors.toList()));
                }
                List<Long> vehicleIdList = this.vehicleAlarmSettingVehicleService.getVehicleIdListBySettingId(req.getId());
                if (CollectionUtils.isEmpty(vehicleIdList)) {
                    vehicleInfoList = this.vehicleInfoService.selectList();
                } else {
                    List<VehicleInfo> queryVehicleInfoList = this.vehicleInfoService.selectListByIdList(vehicleIdList);
                    if (CollectionUtils.isEmpty(queryVehicleInfoList)) {
                        vehicleInfoList = this.vehicleInfoService.selectList();
                    } else {
                        vehicleInfoList = queryVehicleInfoList;
                    }
                }
                if (CollectionUtil.isNotEmpty((Collection<?>) vehicleInfoList)) {
                    vehicleInfoList.removeIf(vehicleInfo -> {
                        return vehicleInfo.getCardId() == null;
                    });
                }
                req.setVehicleInfoList(vehicleInfoList);
                if (!CollectionUtils.isEmpty(vehicleInfoList)) {
                    req.setVehicleIdList((List) vehicleInfoList.stream().map((v0) -> {
                        return v0.getId();
                    }).collect(Collectors.toList()));
                }
            });
        }
        return reqList;
    }
}
