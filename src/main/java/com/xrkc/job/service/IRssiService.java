package com.xrkc.job.service;
import com.xrkc.job.domain.PositionVO;
import java.util.List;
import java.util.Map;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/IRssiService.class */
public interface IRssiService {
    int batchReplace(Map<String, Object> map);
    List<PositionVO> selectRssiList(Map<String, Object> map);
    void batchDelete(Long l);
    void batchDeleteBak(int i);
    List<Long> selectCardIdListByMod(int i);
}
