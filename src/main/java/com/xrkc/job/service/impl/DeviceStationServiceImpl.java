package com.xrkc.job.service.impl;
import com.xrkc.job.domain.DeviceStation;
import com.xrkc.job.mapper.DeviceStationMapper;
import com.xrkc.job.service.IDeviceStationService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/service/impl/DeviceStationServiceImpl.class */
public class DeviceStationServiceImpl implements IDeviceStationService {
    @Autowired
    private DeviceStationMapper deviceStationMapper;
    @Override // com.xrkc.job.service.IDeviceStationService
    public List<DeviceStation> selectList() {
        return this.deviceStationMapper.selectList();
    }
}
