package com.xrkc.job.vo;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/vo/PushCardSenderNumVO.class */
public class PushCardSenderNumVO {
    private Integer deviceNum;
    private Integer deviceExist;
    private Long cardId;
    private Integer electricity;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    private Integer aims;
    public void setDeviceNum(Integer deviceNum) {
        this.deviceNum = deviceNum;
    }
    public void setDeviceExist(Integer deviceExist) {
        this.deviceExist = deviceExist;
    }
    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }
    public void setElectricity(Integer electricity) {
        this.electricity = electricity;
    }
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public void setAims(Integer aims) {
        this.aims = aims;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof PushCardSenderNumVO)) {
            return false;
        }
        PushCardSenderNumVO other = (PushCardSenderNumVO) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$deviceNum = getDeviceNum();
        Object other$deviceNum = other.getDeviceNum();
        if (this$deviceNum == null) {
            if (other$deviceNum != null) {
                return false;
            }
        } else if (!this$deviceNum.equals(other$deviceNum)) {
            return false;
        }
        Object this$deviceExist = getDeviceExist();
        Object other$deviceExist = other.getDeviceExist();
        if (this$deviceExist == null) {
            if (other$deviceExist != null) {
                return false;
            }
        } else if (!this$deviceExist.equals(other$deviceExist)) {
            return false;
        }
        Object this$cardId = getCardId();
        Object other$cardId = other.getCardId();
        if (this$cardId == null) {
            if (other$cardId != null) {
                return false;
            }
        } else if (!this$cardId.equals(other$cardId)) {
            return false;
        }
        Object this$electricity = getElectricity();
        Object other$electricity = other.getElectricity();
        if (this$electricity == null) {
            if (other$electricity != null) {
                return false;
            }
        } else if (!this$electricity.equals(other$electricity)) {
            return false;
        }
        Object this$aims = getAims();
        Object other$aims = other.getAims();
        if (this$aims == null) {
            if (other$aims != null) {
                return false;
            }
        } else if (!this$aims.equals(other$aims)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        return this$createTime == null ? other$createTime == null : this$createTime.equals(other$createTime);
    }
    protected boolean canEqual(Object other) {
        return other instanceof PushCardSenderNumVO;
    }
    public int hashCode() {
        Object $deviceNum = getDeviceNum();
        int result = (1 * 59) + ($deviceNum == null ? 43 : $deviceNum.hashCode());
        Object $deviceExist = getDeviceExist();
        int result2 = (result * 59) + ($deviceExist == null ? 43 : $deviceExist.hashCode());
        Object $cardId = getCardId();
        int result3 = (result2 * 59) + ($cardId == null ? 43 : $cardId.hashCode());
        Object $electricity = getElectricity();
        int result4 = (result3 * 59) + ($electricity == null ? 43 : $electricity.hashCode());
        Object $aims = getAims();
        int result5 = (result4 * 59) + ($aims == null ? 43 : $aims.hashCode());
        Object $createTime = getCreateTime();
        return (result5 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
    }
    public String toString() {
        return "PushCardSenderNumVO(deviceNum=" + getDeviceNum() + ", deviceExist=" + getDeviceExist() + ", cardId=" + getCardId() + ", electricity=" + getElectricity() + ", createTime=" + getCreateTime() + ", aims=" + getAims() + StringPool.RIGHT_BRACKET;
    }
    public Integer getDeviceNum() {
        return this.deviceNum;
    }
    public Integer getDeviceExist() {
        return this.deviceExist;
    }
    public Long getCardId() {
        return this.cardId;
    }
    public Integer getElectricity() {
        return this.electricity;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
    public Integer getAims() {
        return this.aims;
    }
}
