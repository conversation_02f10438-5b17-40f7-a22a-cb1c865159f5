package com.xrkc.job.rabbitmq.constant;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/rabbitmq/constant/RabbitMqConstant.class */
public class RabbitMqConstant {
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/rabbitmq/constant/RabbitMqConstant$Binding.class */
    public static class Binding {
        /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/rabbitmq/constant/RabbitMqConstant$Binding$DirectExchangeToQueue.class */
        public static class DirectExchangeToQueue {
            /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/rabbitmq/constant/RabbitMqConstant$Binding$DirectExchangeToQueue$Common.class */
            public static class Common {
                public static final String MESSAGE_BINDING = "messageBinding";
            }
        }
    }
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/rabbitmq/constant/RabbitMqConstant$DirectExchange.class */
    public static class DirectExchange {
        /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/rabbitmq/constant/RabbitMqConstant$DirectExchange$Common.class */
        public static class Common {
            public static final String MESSAGE_EXCHANGE = "messageExchange";
        }
    }
    /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/rabbitmq/constant/RabbitMqConstant$Queue.class */
    public static class Queue {
        /* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/rabbitmq/constant/RabbitMqConstant$Queue$Log.class */
        public static class Log {
            public static final String MESSAGE_QUEUE = "messageQueue";
        }
    }
}
