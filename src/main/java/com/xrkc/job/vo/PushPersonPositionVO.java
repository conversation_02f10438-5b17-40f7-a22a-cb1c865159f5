package com.xrkc.job.vo;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.math.BigDecimal;
import java.util.List;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/vo/PushPersonPositionVO.class */
public class PushPersonPositionVO {
    private Long personId;
    private String personNo;
    private Long cardNo;
    private Long timestamp;
    private String cardType;
    private String personName;
    private String personType;
    private String personTypeName;
    private String personAttribute;
    private String personPhoto;
    private String idNumber;
    private String phone;
    private Long deptId;
    private String deptName;
    private Integer deviceElectric;
    private BigDecimal longitude;
    private BigDecimal latitude;
    private String floor;
    private String build;
    private List<String> alarmType;
    private Integer speed;
    private Integer alt;
    private Boolean alarm;
    private String jobNumber;
    public void setPersonId(Long personId) {
        this.personId = personId;
    }
    public void setCardType(String cardType) {
        this.cardType = cardType;
    }
    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof PushPersonPositionVO)) {
            return false;
        }
        PushPersonPositionVO other = (PushPersonPositionVO) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$personId = getPersonId();
        Object other$personId = other.getPersonId();
        if (this$personId == null) {
            if (other$personId != null) {
                return false;
            }
        } else if (!this$personId.equals(other$personId)) {
            return false;
        }
        Object this$cardNo = getCardNo();
        Object other$cardNo = other.getCardNo();
        if (this$cardNo == null) {
            if (other$cardNo != null) {
                return false;
            }
        } else if (!this$cardNo.equals(other$cardNo)) {
            return false;
        }
        Object this$timestamp = getTimestamp();
        Object other$timestamp = other.getTimestamp();
        if (this$timestamp == null) {
            if (other$timestamp != null) {
                return false;
            }
        } else if (!this$timestamp.equals(other$timestamp)) {
            return false;
        }
        Object this$deptId = getDeptId();
        Object other$deptId = other.getDeptId();
        if (this$deptId == null) {
            if (other$deptId != null) {
                return false;
            }
        } else if (!this$deptId.equals(other$deptId)) {
            return false;
        }
        Object this$deviceElectric = getDeviceElectric();
        Object other$deviceElectric = other.getDeviceElectric();
        if (this$deviceElectric == null) {
            if (other$deviceElectric != null) {
                return false;
            }
        } else if (!this$deviceElectric.equals(other$deviceElectric)) {
            return false;
        }
        Object this$speed = getSpeed();
        Object other$speed = other.getSpeed();
        if (this$speed == null) {
            if (other$speed != null) {
                return false;
            }
        } else if (!this$speed.equals(other$speed)) {
            return false;
        }
        Object this$alt = getAlt();
        Object other$alt = other.getAlt();
        if (this$alt == null) {
            if (other$alt != null) {
                return false;
            }
        } else if (!this$alt.equals(other$alt)) {
            return false;
        }
        Object this$alarm = getAlarm();
        Object other$alarm = other.getAlarm();
        if (this$alarm == null) {
            if (other$alarm != null) {
                return false;
            }
        } else if (!this$alarm.equals(other$alarm)) {
            return false;
        }
        Object this$personNo = getPersonNo();
        Object other$personNo = other.getPersonNo();
        if (this$personNo == null) {
            if (other$personNo != null) {
                return false;
            }
        } else if (!this$personNo.equals(other$personNo)) {
            return false;
        }
        Object this$cardType = getCardType();
        Object other$cardType = other.getCardType();
        if (this$cardType == null) {
            if (other$cardType != null) {
                return false;
            }
        } else if (!this$cardType.equals(other$cardType)) {
            return false;
        }
        Object this$personName = getPersonName();
        Object other$personName = other.getPersonName();
        if (this$personName == null) {
            if (other$personName != null) {
                return false;
            }
        } else if (!this$personName.equals(other$personName)) {
            return false;
        }
        Object this$personType = getPersonType();
        Object other$personType = other.getPersonType();
        if (this$personType == null) {
            if (other$personType != null) {
                return false;
            }
        } else if (!this$personType.equals(other$personType)) {
            return false;
        }
        Object this$personTypeName = getPersonTypeName();
        Object other$personTypeName = other.getPersonTypeName();
        if (this$personTypeName == null) {
            if (other$personTypeName != null) {
                return false;
            }
        } else if (!this$personTypeName.equals(other$personTypeName)) {
            return false;
        }
        Object this$personAttribute = getPersonAttribute();
        Object other$personAttribute = other.getPersonAttribute();
        if (this$personAttribute == null) {
            if (other$personAttribute != null) {
                return false;
            }
        } else if (!this$personAttribute.equals(other$personAttribute)) {
            return false;
        }
        Object this$personPhoto = getPersonPhoto();
        Object other$personPhoto = other.getPersonPhoto();
        if (this$personPhoto == null) {
            if (other$personPhoto != null) {
                return false;
            }
        } else if (!this$personPhoto.equals(other$personPhoto)) {
            return false;
        }
        Object this$idNumber = getIdNumber();
        Object other$idNumber = other.getIdNumber();
        if (this$idNumber == null) {
            if (other$idNumber != null) {
                return false;
            }
        } else if (!this$idNumber.equals(other$idNumber)) {
            return false;
        }
        Object this$phone = getPhone();
        Object other$phone = other.getPhone();
        if (this$phone == null) {
            if (other$phone != null) {
                return false;
            }
        } else if (!this$phone.equals(other$phone)) {
            return false;
        }
        Object this$deptName = getDeptName();
        Object other$deptName = other.getDeptName();
        if (this$deptName == null) {
            if (other$deptName != null) {
                return false;
            }
        } else if (!this$deptName.equals(other$deptName)) {
            return false;
        }
        Object this$longitude = getLongitude();
        Object other$longitude = other.getLongitude();
        if (this$longitude == null) {
            if (other$longitude != null) {
                return false;
            }
        } else if (!this$longitude.equals(other$longitude)) {
            return false;
        }
        Object this$latitude = getLatitude();
        Object other$latitude = other.getLatitude();
        if (this$latitude == null) {
            if (other$latitude != null) {
                return false;
            }
        } else if (!this$latitude.equals(other$latitude)) {
            return false;
        }
        Object this$floor = getFloor();
        Object other$floor = other.getFloor();
        if (this$floor == null) {
            if (other$floor != null) {
                return false;
            }
        } else if (!this$floor.equals(other$floor)) {
            return false;
        }
        Object this$build = getBuild();
        Object other$build = other.getBuild();
        if (this$build == null) {
            if (other$build != null) {
                return false;
            }
        } else if (!this$build.equals(other$build)) {
            return false;
        }
        Object this$alarmType = getAlarmType();
        Object other$alarmType = other.getAlarmType();
        if (this$alarmType == null) {
            if (other$alarmType != null) {
                return false;
            }
        } else if (!this$alarmType.equals(other$alarmType)) {
            return false;
        }
        Object this$jobNumber = getJobNumber();
        Object other$jobNumber = other.getJobNumber();
        return this$jobNumber == null ? other$jobNumber == null : this$jobNumber.equals(other$jobNumber);
    }
    protected boolean canEqual(Object other) {
        return other instanceof PushPersonPositionVO;
    }
    public int hashCode() {
        Object $personId = getPersonId();
        int result = (1 * 59) + ($personId == null ? 43 : $personId.hashCode());
        Object $cardNo = getCardNo();
        int result2 = (result * 59) + ($cardNo == null ? 43 : $cardNo.hashCode());
        Object $timestamp = getTimestamp();
        int result3 = (result2 * 59) + ($timestamp == null ? 43 : $timestamp.hashCode());
        Object $deptId = getDeptId();
        int result4 = (result3 * 59) + ($deptId == null ? 43 : $deptId.hashCode());
        Object $deviceElectric = getDeviceElectric();
        int result5 = (result4 * 59) + ($deviceElectric == null ? 43 : $deviceElectric.hashCode());
        Object $speed = getSpeed();
        int result6 = (result5 * 59) + ($speed == null ? 43 : $speed.hashCode());
        Object $alt = getAlt();
        int result7 = (result6 * 59) + ($alt == null ? 43 : $alt.hashCode());
        Object $alarm = getAlarm();
        int result8 = (result7 * 59) + ($alarm == null ? 43 : $alarm.hashCode());
        Object $personNo = getPersonNo();
        int result9 = (result8 * 59) + ($personNo == null ? 43 : $personNo.hashCode());
        Object $cardType = getCardType();
        int result10 = (result9 * 59) + ($cardType == null ? 43 : $cardType.hashCode());
        Object $personName = getPersonName();
        int result11 = (result10 * 59) + ($personName == null ? 43 : $personName.hashCode());
        Object $personType = getPersonType();
        int result12 = (result11 * 59) + ($personType == null ? 43 : $personType.hashCode());
        Object $personTypeName = getPersonTypeName();
        int result13 = (result12 * 59) + ($personTypeName == null ? 43 : $personTypeName.hashCode());
        Object $personAttribute = getPersonAttribute();
        int result14 = (result13 * 59) + ($personAttribute == null ? 43 : $personAttribute.hashCode());
        Object $personPhoto = getPersonPhoto();
        int result15 = (result14 * 59) + ($personPhoto == null ? 43 : $personPhoto.hashCode());
        Object $idNumber = getIdNumber();
        int result16 = (result15 * 59) + ($idNumber == null ? 43 : $idNumber.hashCode());
        Object $phone = getPhone();
        int result17 = (result16 * 59) + ($phone == null ? 43 : $phone.hashCode());
        Object $deptName = getDeptName();
        int result18 = (result17 * 59) + ($deptName == null ? 43 : $deptName.hashCode());
        Object $longitude = getLongitude();
        int result19 = (result18 * 59) + ($longitude == null ? 43 : $longitude.hashCode());
        Object $latitude = getLatitude();
        int result20 = (result19 * 59) + ($latitude == null ? 43 : $latitude.hashCode());
        Object $floor = getFloor();
        int result21 = (result20 * 59) + ($floor == null ? 43 : $floor.hashCode());
        Object $build = getBuild();
        int result22 = (result21 * 59) + ($build == null ? 43 : $build.hashCode());
        Object $alarmType = getAlarmType();
        int result23 = (result22 * 59) + ($alarmType == null ? 43 : $alarmType.hashCode());
        Object $jobNumber = getJobNumber();
        return (result23 * 59) + ($jobNumber == null ? 43 : $jobNumber.hashCode());
    }
    public String toString() {
        return "PushPersonPositionVO(personId=" + getPersonId() + ", personNo=" + getPersonNo() + ", cardNo=" + getCardNo() + ", timestamp=" + getTimestamp() + ", cardType=" + getCardType() + ", personName=" + getPersonName() + ", personType=" + getPersonType() + ", personTypeName=" + getPersonTypeName() + ", personAttribute=" + getPersonAttribute() + ", personPhoto=" + getPersonPhoto() + ", idNumber=" + getIdNumber() + ", phone=" + getPhone() + ", deptId=" + getDeptId() + ", deptName=" + getDeptName() + ", deviceElectric=" + getDeviceElectric() + ", longitude=" + getLongitude() + ", latitude=" + getLatitude() + ", floor=" + getFloor() + ", build=" + getBuild() + ", alarmType=" + getAlarmType() + ", speed=" + getSpeed() + ", alt=" + getAlt() + ", alarm=" + getAlarm() + ", jobNumber=" + getJobNumber() + StringPool.RIGHT_BRACKET;
    }
    public Long getPersonId() {
        return this.personId;
    }
    public String getCardType() {
        return this.cardType;
    }
    public String getJobNumber() {
        return this.jobNumber;
    }
    public String getPersonNo() {
        return this.personNo;
    }
    public void setPersonNo(String personNo) {
        this.personNo = personNo;
    }
    public Integer getAlt() {
        return this.alt;
    }
    public void setAlt(Integer alt) {
        this.alt = alt;
    }
    public Long getCardNo() {
        return this.cardNo;
    }
    public void setCardNo(Long cardNo) {
        this.cardNo = cardNo;
    }
    public Long getTimestamp() {
        return this.timestamp;
    }
    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
    public String getPersonName() {
        return this.personName;
    }
    public void setPersonName(String personName) {
        this.personName = personName;
    }
    public String getPersonType() {
        return this.personType;
    }
    public void setPersonType(String personType) {
        this.personType = personType;
    }
    public String getPersonTypeName() {
        return this.personTypeName;
    }
    public void setPersonTypeName(String personTypeName) {
        this.personTypeName = personTypeName;
    }
    public String getPersonAttribute() {
        return this.personAttribute;
    }
    public void setPersonAttribute(String personAttribute) {
        this.personAttribute = personAttribute;
    }
    public Integer getDeviceElectric() {
        return this.deviceElectric;
    }
    public void setDeviceElectric(Integer deviceElectric) {
        this.deviceElectric = deviceElectric;
    }
    public BigDecimal getLongitude() {
        return this.longitude;
    }
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    public BigDecimal getLatitude() {
        return this.latitude;
    }
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    public String getFloor() {
        return this.floor;
    }
    public void setFloor(String floor) {
        this.floor = floor;
    }
    public String getBuild() {
        return this.build;
    }
    public void setBuild(String build) {
        this.build = build;
    }
    public List<String> getAlarmType() {
        return this.alarmType;
    }
    public void setAlarmType(List<String> alarmType) {
        this.alarmType = alarmType;
    }
    public Integer getSpeed() {
        return this.speed;
    }
    public void setSpeed(Integer speed) {
        this.speed = speed;
    }
    public Boolean getAlarm() {
        return this.alarm;
    }
    public void setAlarm(Boolean alarm) {
        this.alarm = alarm;
    }
    public String getPersonPhoto() {
        return this.personPhoto;
    }
    public void setPersonPhoto(String personPhoto) {
        this.personPhoto = personPhoto;
    }
    public Long getDeptId() {
        return this.deptId;
    }
    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
    public String getDeptName() {
        return this.deptName;
    }
    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    public String getIdNumber() {
        return this.idNumber;
    }
    public void setIdNumber(String idNumber) {
        this.idNumber = idNumber;
    }
    public String getPhone() {
        return this.phone;
    }
    public void setPhone(String phone) {
        this.phone = phone;
    }
}
