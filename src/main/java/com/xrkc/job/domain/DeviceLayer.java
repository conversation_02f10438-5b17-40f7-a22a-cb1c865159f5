package com.xrkc.job.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
/* loaded from: job-service.jar:BOOT-INF/classes/com/xrkc/job/domain/DeviceLayer.class */
public class DeviceLayer implements Serializable {
    private static final long serialVersionUID = 1;
    private Long id;
    private String mapType;
    private String mapUrl;
    private String layerEnable;
    private String layerName;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long buildingId;
    @TableField(exist = false)
    private String buildingName;
    private String layerId;
    private Integer layerHeight;
    private BigDecimal minLongitude;
    private BigDecimal maxLongitude;
    private BigDecimal minLatitude;
    private BigDecimal maxLatitude;
    @TableField("create_time")
    private LocalDateTime createTime;
    public DeviceLayer setId(Long id) {
        this.id = id;
        return this;
    }
    public DeviceLayer setMapType(String mapType) {
        this.mapType = mapType;
        return this;
    }
    public DeviceLayer setMapUrl(String mapUrl) {
        this.mapUrl = mapUrl;
        return this;
    }
    public DeviceLayer setLayerEnable(String layerEnable) {
        this.layerEnable = layerEnable;
        return this;
    }
    public DeviceLayer setLayerName(String layerName) {
        this.layerName = layerName;
        return this;
    }
    public DeviceLayer setBuildingId(Long buildingId) {
        this.buildingId = buildingId;
        return this;
    }
    public DeviceLayer setBuildingName(String buildingName) {
        this.buildingName = buildingName;
        return this;
    }
    public DeviceLayer setLayerId(String layerId) {
        this.layerId = layerId;
        return this;
    }
    public DeviceLayer setLayerHeight(Integer layerHeight) {
        this.layerHeight = layerHeight;
        return this;
    }
    public DeviceLayer setMinLongitude(BigDecimal minLongitude) {
        this.minLongitude = minLongitude;
        return this;
    }
    public DeviceLayer setMaxLongitude(BigDecimal maxLongitude) {
        this.maxLongitude = maxLongitude;
        return this;
    }
    public DeviceLayer setMinLatitude(BigDecimal minLatitude) {
        this.minLatitude = minLatitude;
        return this;
    }
    public DeviceLayer setMaxLatitude(BigDecimal maxLatitude) {
        this.maxLatitude = maxLatitude;
        return this;
    }
    public DeviceLayer setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
        return this;
    }
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof DeviceLayer)) {
            return false;
        }
        DeviceLayer other = (DeviceLayer) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$id = getId();
        Object other$id = other.getId();
        if (this$id == null) {
            if (other$id != null) {
                return false;
            }
        } else if (!this$id.equals(other$id)) {
            return false;
        }
        Object this$buildingId = getBuildingId();
        Object other$buildingId = other.getBuildingId();
        if (this$buildingId == null) {
            if (other$buildingId != null) {
                return false;
            }
        } else if (!this$buildingId.equals(other$buildingId)) {
            return false;
        }
        Object this$layerHeight = getLayerHeight();
        Object other$layerHeight = other.getLayerHeight();
        if (this$layerHeight == null) {
            if (other$layerHeight != null) {
                return false;
            }
        } else if (!this$layerHeight.equals(other$layerHeight)) {
            return false;
        }
        Object this$mapType = getMapType();
        Object other$mapType = other.getMapType();
        if (this$mapType == null) {
            if (other$mapType != null) {
                return false;
            }
        } else if (!this$mapType.equals(other$mapType)) {
            return false;
        }
        Object this$mapUrl = getMapUrl();
        Object other$mapUrl = other.getMapUrl();
        if (this$mapUrl == null) {
            if (other$mapUrl != null) {
                return false;
            }
        } else if (!this$mapUrl.equals(other$mapUrl)) {
            return false;
        }
        Object this$layerEnable = getLayerEnable();
        Object other$layerEnable = other.getLayerEnable();
        if (this$layerEnable == null) {
            if (other$layerEnable != null) {
                return false;
            }
        } else if (!this$layerEnable.equals(other$layerEnable)) {
            return false;
        }
        Object this$layerName = getLayerName();
        Object other$layerName = other.getLayerName();
        if (this$layerName == null) {
            if (other$layerName != null) {
                return false;
            }
        } else if (!this$layerName.equals(other$layerName)) {
            return false;
        }
        Object this$buildingName = getBuildingName();
        Object other$buildingName = other.getBuildingName();
        if (this$buildingName == null) {
            if (other$buildingName != null) {
                return false;
            }
        } else if (!this$buildingName.equals(other$buildingName)) {
            return false;
        }
        Object this$layerId = getLayerId();
        Object other$layerId = other.getLayerId();
        if (this$layerId == null) {
            if (other$layerId != null) {
                return false;
            }
        } else if (!this$layerId.equals(other$layerId)) {
            return false;
        }
        Object this$minLongitude = getMinLongitude();
        Object other$minLongitude = other.getMinLongitude();
        if (this$minLongitude == null) {
            if (other$minLongitude != null) {
                return false;
            }
        } else if (!this$minLongitude.equals(other$minLongitude)) {
            return false;
        }
        Object this$maxLongitude = getMaxLongitude();
        Object other$maxLongitude = other.getMaxLongitude();
        if (this$maxLongitude == null) {
            if (other$maxLongitude != null) {
                return false;
            }
        } else if (!this$maxLongitude.equals(other$maxLongitude)) {
            return false;
        }
        Object this$minLatitude = getMinLatitude();
        Object other$minLatitude = other.getMinLatitude();
        if (this$minLatitude == null) {
            if (other$minLatitude != null) {
                return false;
            }
        } else if (!this$minLatitude.equals(other$minLatitude)) {
            return false;
        }
        Object this$maxLatitude = getMaxLatitude();
        Object other$maxLatitude = other.getMaxLatitude();
        if (this$maxLatitude == null) {
            if (other$maxLatitude != null) {
                return false;
            }
        } else if (!this$maxLatitude.equals(other$maxLatitude)) {
            return false;
        }
        Object this$createTime = getCreateTime();
        Object other$createTime = other.getCreateTime();
        return this$createTime == null ? other$createTime == null : this$createTime.equals(other$createTime);
    }
    protected boolean canEqual(Object other) {
        return other instanceof DeviceLayer;
    }
    public int hashCode() {
        Object $id = getId();
        int result = (1 * 59) + ($id == null ? 43 : $id.hashCode());
        Object $buildingId = getBuildingId();
        int result2 = (result * 59) + ($buildingId == null ? 43 : $buildingId.hashCode());
        Object $layerHeight = getLayerHeight();
        int result3 = (result2 * 59) + ($layerHeight == null ? 43 : $layerHeight.hashCode());
        Object $mapType = getMapType();
        int result4 = (result3 * 59) + ($mapType == null ? 43 : $mapType.hashCode());
        Object $mapUrl = getMapUrl();
        int result5 = (result4 * 59) + ($mapUrl == null ? 43 : $mapUrl.hashCode());
        Object $layerEnable = getLayerEnable();
        int result6 = (result5 * 59) + ($layerEnable == null ? 43 : $layerEnable.hashCode());
        Object $layerName = getLayerName();
        int result7 = (result6 * 59) + ($layerName == null ? 43 : $layerName.hashCode());
        Object $buildingName = getBuildingName();
        int result8 = (result7 * 59) + ($buildingName == null ? 43 : $buildingName.hashCode());
        Object $layerId = getLayerId();
        int result9 = (result8 * 59) + ($layerId == null ? 43 : $layerId.hashCode());
        Object $minLongitude = getMinLongitude();
        int result10 = (result9 * 59) + ($minLongitude == null ? 43 : $minLongitude.hashCode());
        Object $maxLongitude = getMaxLongitude();
        int result11 = (result10 * 59) + ($maxLongitude == null ? 43 : $maxLongitude.hashCode());
        Object $minLatitude = getMinLatitude();
        int result12 = (result11 * 59) + ($minLatitude == null ? 43 : $minLatitude.hashCode());
        Object $maxLatitude = getMaxLatitude();
        int result13 = (result12 * 59) + ($maxLatitude == null ? 43 : $maxLatitude.hashCode());
        Object $createTime = getCreateTime();
        return (result13 * 59) + ($createTime == null ? 43 : $createTime.hashCode());
    }
    public String toString() {
        return "DeviceLayer(id=" + getId() + ", mapType=" + getMapType() + ", mapUrl=" + getMapUrl() + ", layerEnable=" + getLayerEnable() + ", layerName=" + getLayerName() + ", buildingId=" + getBuildingId() + ", buildingName=" + getBuildingName() + ", layerId=" + getLayerId() + ", layerHeight=" + getLayerHeight() + ", minLongitude=" + getMinLongitude() + ", maxLongitude=" + getMaxLongitude() + ", minLatitude=" + getMinLatitude() + ", maxLatitude=" + getMaxLatitude() + ", createTime=" + getCreateTime() + StringPool.RIGHT_BRACKET;
    }
    public Long getId() {
        return this.id;
    }
    public String getMapType() {
        return this.mapType;
    }
    public String getMapUrl() {
        return this.mapUrl;
    }
    public String getLayerEnable() {
        return this.layerEnable;
    }
    public String getLayerName() {
        return this.layerName;
    }
    public Long getBuildingId() {
        return this.buildingId;
    }
    public String getBuildingName() {
        return this.buildingName;
    }
    public String getLayerId() {
        return this.layerId;
    }
    public Integer getLayerHeight() {
        return this.layerHeight;
    }
    public BigDecimal getMinLongitude() {
        return this.minLongitude;
    }
    public BigDecimal getMaxLongitude() {
        return this.maxLongitude;
    }
    public BigDecimal getMinLatitude() {
        return this.minLatitude;
    }
    public BigDecimal getMaxLatitude() {
        return this.maxLatitude;
    }
    public LocalDateTime getCreateTime() {
        return this.createTime;
    }
}
